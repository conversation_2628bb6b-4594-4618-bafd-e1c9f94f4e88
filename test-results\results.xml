<testsuites id="" name="" tests="30" failures="2" skipped="0" errors="0" time="136.58567">
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:01:15.335Z" hostname="chromium" tests="6" failures="0" skipped="0" time="82.942" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="13.263">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should detect FAQ section presence" classname="faq-system.spec.ts" time="16.89">
<system-out>
<![CDATA[Found FAQ indicator: Preguntas Frecuentes
]]>
</system-out>
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should handle page navigation" classname="faq-system.spec.ts" time="13.188">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should handle protected route access" classname="faq-system.spec.ts" time="13.996">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should display login form when accessing protected routes" classname="faq-system.spec.ts" time="12.819">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport" classname="faq-system.spec.ts" time="12.786">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:01:15.335Z" hostname="firefox" tests="6" failures="0" skipped="0" time="85.382" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="12.966">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should detect FAQ section presence" classname="faq-system.spec.ts" time="17.603">
<system-out>
<![CDATA[Found FAQ indicator: Preguntas Frecuentes
]]>
</system-out>
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should handle page navigation" classname="faq-system.spec.ts" time="13.541">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should handle protected route access" classname="faq-system.spec.ts" time="15.385">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should display login form when accessing protected routes" classname="faq-system.spec.ts" time="13.625">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport" classname="faq-system.spec.ts" time="12.262">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:01:15.335Z" hostname="webkit" tests="6" failures="1" skipped="0" time="76.19" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="11.887">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should detect FAQ section presence" classname="faq-system.spec.ts" time="17.212">
<system-out>
<![CDATA[Found FAQ indicator: Preguntas Frecuentes
]]>
</system-out>
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should handle page navigation" classname="faq-system.spec.ts" time="9.744">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should handle protected route access" classname="faq-system.spec.ts" time="10.738">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should display login form when accessing protected routes" classname="faq-system.spec.ts" time="10.659">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport" classname="faq-system.spec.ts" time="15.95">
<failure message="faq-system.spec.ts:139:9 should work on mobile viewport" type="FAILURE">
<![CDATA[  [webkit] › faq-system.spec.ts:139:9 › FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport 

    Error: Timed out 5000ms waiting for expect(locator).toHaveValue(expected)

    Locator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')
    Expected string: "certificado"
    Received string: ""
    Call log:
      - Expect "toHaveValue" with timeout 5000ms
      - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')
        2 × locator resolved to <input value="" type="text" role="combobox" aria-expanded="false" aria-haspopup="listbox" aria-label="Búsqueda inteligente de trámites" placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..." class="flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>
          - unexpected value ""
        6 × locator resolved to <input value="" type="text" role="combobox" aria-expanded="true" aria-haspopup="listbox" aria-label="Búsqueda inteligente de trámites" placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..." class="flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>
          - unexpected value ""


      151 |       // Test search functionality on mobile
      152 |       await searchInput.fill('certificado')
    > 153 |       await expect(searchInput).toHaveValue('certificado')
          |                                 ^
      154 |     })
      155 |   })
      156 | })
        at C:\Users\<USER>\Documents\augment-projects\chia-tramites\tests\e2e\faq-system.spec.ts:153:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\test-failed-1.png]]

[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\video.webm]]

[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:01:15.335Z" hostname="Mobile Chrome" tests="6" failures="0" skipped="0" time="80.864" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="12.163">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should detect FAQ section presence" classname="faq-system.spec.ts" time="19.194">
<system-out>
<![CDATA[Found FAQ indicator: Preguntas Frecuentes
]]>
</system-out>
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should handle page navigation" classname="faq-system.spec.ts" time="12.959">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should handle protected route access" classname="faq-system.spec.ts" time="14.081">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should display login form when accessing protected routes" classname="faq-system.spec.ts" time="10.837">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport" classname="faq-system.spec.ts" time="11.63">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:01:15.335Z" hostname="Mobile Safari" tests="6" failures="1" skipped="0" time="77.793" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="11.03">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should detect FAQ section presence" classname="faq-system.spec.ts" time="18.875">
<system-out>
<![CDATA[Found FAQ indicator: Preguntas Frecuentes
]]>
</system-out>
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should handle page navigation" classname="faq-system.spec.ts" time="8.736">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should handle protected route access" classname="faq-system.spec.ts" time="12.215">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Authentication and Protected Routes › should display login form when accessing protected routes" classname="faq-system.spec.ts" time="11.213">
</testcase>
<testcase name="FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport" classname="faq-system.spec.ts" time="15.724">
<failure message="faq-system.spec.ts:139:9 should work on mobile viewport" type="FAILURE">
<![CDATA[  [Mobile Safari] › faq-system.spec.ts:139:9 › FAQ System E2E Tests - Simplified › Responsive Design › should work on mobile viewport 

    Error: Timed out 5000ms waiting for expect(locator).toHaveValue(expected)

    Locator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')
    Expected string: "certificado"
    Received string: ""
    Call log:
      - Expect "toHaveValue" with timeout 5000ms
      - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')
        - locator resolved to <input value="" type="text" role="combobox" aria-expanded="false" aria-haspopup="listbox" aria-label="Búsqueda inteligente de trámites" placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..." class="flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>
        7 × unexpected value ""
          - locator resolved to <input value="" type="text" role="combobox" aria-expanded="true" aria-haspopup="listbox" aria-label="Búsqueda inteligente de trámites" placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..." class="flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>
        - unexpected value ""


      151 |       // Test search functionality on mobile
      152 |       await searchInput.fill('certificado')
    > 153 |       await expect(searchInput).toHaveValue('certificado')
          |                                 ^
      154 |     })
      155 |   })
      156 | })
        at C:\Users\<USER>\Documents\augment-projects\chia-tramites\tests\e2e\faq-system.spec.ts:153:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\video.webm]]

[[ATTACHMENT|faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>