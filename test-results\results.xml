<testsuites id="" name="" tests="5" failures="0" skipped="0" errors="0" time="50.532472999999996">
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:52:53.102Z" hostname="chromium" tests="1" failures="0" skipped="0" time="20.795" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="20.795">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:52:53.102Z" hostname="firefox" tests="1" failures="0" skipped="0" time="20.135" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="20.135">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:52:53.102Z" hostname="webkit" tests="1" failures="0" skipped="0" time="17.42" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="17.42">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:52:53.102Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="0" time="21.798" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="21.798">
</testcase>
</testsuite>
<testsuite name="faq-system.spec.ts" timestamp="2025-07-01T21:52:53.102Z" hostname="Mobile Safari" tests="1" failures="0" skipped="0" time="9.051" errors="0">
<testcase name="FAQ System E2E Tests - Simplified › Basic Application Functionality › should load home page with main search bar" classname="faq-system.spec.ts" time="9.051">
</testcase>
</testsuite>
</testsuites>