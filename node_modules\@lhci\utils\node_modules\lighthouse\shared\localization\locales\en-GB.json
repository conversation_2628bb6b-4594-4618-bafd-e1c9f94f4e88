{"core/audits/accessibility/accesskeys.js | description": {"message": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` values are not unique"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` values are unique"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` attributes do not match their roles"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` attributes match their roles"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link` and `menuitem` elements do not have accessible names."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link` and `menuitem` elements have accessible names"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` is present on the document `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` is not present on the document `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Focusable descendants within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` elements contain focusable descendents"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA input fields do not have accessible names"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA input fields have accessible names"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` elements do not have accessible names."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` elements have accessible names"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` elements do not have accessible names."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` elements have accessible names"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`s do not have all required `[aria-*]` attributes"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`s have all required `[aria-*]` attributes"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` are missing some or all of those required children."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`s are not contained by their required parent element"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`s are contained by their required parent element"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` values are not valid"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` values are valid"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA toggle fields do not have accessible names"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA toggle fields have accessible names"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` elements do not have accessible names."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` elements have accessible names"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labelling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` elements do not have accessible names."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` elements have accessible names"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Assistive technologies, such as screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` attributes do not have valid values"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` attributes have valid values"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Assistive technologies, such as screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` attributes are not valid or misspelled"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` attributes are valid and not misspelled"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Failing elements"}, "core/audits/accessibility/button-name.js | description": {"message": "When a button doesn't have an accessible name, screen readers announce it as 'button', making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Buttons do not have an accessible name"}, "core/audits/accessibility/button-name.js | title": {"message": "Buttons have an accessible name"}, "core/audits/accessibility/bypass.js | description": {"message": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "The page does not contain a heading, skip link or landmark region"}, "core/audits/accessibility/bypass.js | title": {"message": "The page contains a heading, skip link or landmark region"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient colour contrast](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Background and foreground colours do not have a sufficient contrast ratio."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Background and foreground colours have a sufficient contrast ratio"}, "core/audits/accessibility/definition-list.js | description": {"message": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`'s do not contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements."}, "core/audits/accessibility/dlitem.js | description": {"message": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definition list items are not wrapped in `<dl>` elements"}, "core/audits/accessibility/dlitem.js | title": {"message": "Definition list items are wrapped in `<dl>` elements"}, "core/audits/accessibility/document-title.js | description": {"message": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Document doesn't have a `<title>` element"}, "core/audits/accessibility/document-title.js | title": {"message": "Document has a `<title>` element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "All focusable elements must have a unique `id` to ensure that they're visible to assistive technologies. [Learn how to fix duplicate `id`s](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]` attributes on active, focusable elements are not unique"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]` attributes on active, focusable elements are unique"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA IDs are not unique"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA IDs are unique"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Form fields with multiple labels can be confusingly announced by assistive technologies, like screen readers, which use either the first, the last or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Form fields have multiple labels"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "No form fields have multiple labels"}, "core/audits/accessibility/frame-title.js | description": {"message": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` or `<iframe>` elements do not have a title"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` or `<iframe>` elements have a title"}, "core/audits/accessibility/heading-order.js | description": {"message": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Heading elements are not in a sequentially-descending order"}, "core/audits/accessibility/heading-order.js | title": {"message": "Heading elements appear in a sequentially-descending order"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` element does not have a `[lang]` attribute"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` element has a `[lang]` attribute"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` element does not have a valid value for its `[lang]` attribute."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` element has a valid value for its `[lang]` attribute"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informative elements should aim for short, descriptive alternative text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Image elements do not have `[alt]` attributes"}, "core/audits/accessibility/image-alt.js | title": {"message": "Image elements have `[alt]` attributes"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` elements do not have `[alt]` text"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` elements have `[alt]` text"}, "core/audits/accessibility/label.js | description": {"message": "Labels ensure that form controls are announced properly by assistive technologies, such as screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Form elements do not have associated labels"}, "core/audits/accessibility/label.js | title": {"message": "Form elements have associated labels"}, "core/audits/accessibility/link-name.js | description": {"message": "Link text (and alternative text for images, when used as links) that is discernible, unique and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Links do not have a discernible name"}, "core/audits/accessibility/link-name.js | title": {"message": "Links have a discernible name"}, "core/audits/accessibility/list.js | description": {"message": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Lists do not contain only `<li>` elements and script supporting elements (`<script>` and `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "List items (`<li>`) are not contained within `<ul>`, `<ol>` or `<menu>` parent elements."}, "core/audits/accessibility/listitem.js | title": {"message": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Users do not expect a page to refresh automatically and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "The document uses `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "The document does not use `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` is used in the `<meta name=\"viewport\">` element or the `[maximum-scale]` attribute is less than 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Screen readers cannot translate non-text content. Adding alternative text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` elements do not have alternative text"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` elements have alternative text"}, "core/audits/accessibility/tabindex.js | description": {"message": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Some elements have a `[tabindex]` value greater than 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "No element has a `[tabindex]` value greater than 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Screen readers have features to make navigating tables easier. Ensuring that`<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Cells in a `<table>` element that use the `[headers]` attribute refer to an element `id` not found within the same table."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Screen readers have features to make navigating tables easier. Ensuring that table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` do not have data cells they describe."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` attributes do not have a valid value"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` attributes have a valid value"}, "core/audits/accessibility/video-caption.js | description": {"message": "When a video provides a caption it is easier for deaf and hearing-impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` elements do not contain a `<track>` element with `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Current value"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Suggested token"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` helps users submit forms more quickly. To reduce user effort, consider enabling by setting the `autocomplete` attribute to a valid value. [Learn more about `autocomplete` in forms](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` elements do not have correct `autocomplete` attributes"}, "core/audits/autocomplete.js | manualReview": {"message": "Requires manual review"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Review order of tokens"}, "core/audits/autocomplete.js | title": {"message": "`<input>` elements correctly use `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` token(s): '{token}' is invalid in {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Review order of tokens: '{tokens}' in {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Actionable"}, "core/audits/bf-cache.js | description": {"message": "Many navigations are performed by going back to a previous page, or forwards again. The back-forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 failure reason}other{# failure reasons}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Reason for failure"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON> prevented back-forward cache restoration"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Failure type"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Not actionable"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Pending browser support"}, "core/audits/bf-cache.js | title": {"message": "<PERSON> didn't prevent back-forward cache restoration"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome extensions negatively affected this page's load performance. Try auditing the page in incognito mode or from a Chrome profile without extensions."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Script Evaluation"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON> Parse"}, "core/audits/bootup-time.js | columnTotal": {"message": "Total CPU Time"}, "core/audits/bootup-time.js | description": {"message": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduce JavaScript execution time"}, "core/audits/bootup-time.js | title": {"message": "JavaScript execution time"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Remove duplicate modules in JavaScript bundles"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Use video formats for animated content"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. For your bundled JavaScript, adopt a modern script deployment strategy using module/nomodule feature detection to reduce the amount of code delivered to modern browsers, while retaining support for legacy browsers. [Learn how to use modern JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Avoid serving legacy JavaScript to modern browsers"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Serve images in next-gen formats"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Consider lazy loading offscreen and hidden images after all critical resources have finished loading to lower Time to Interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Defer off-screen images"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Eliminate render-blocking resources"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Total size was {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Avoid enormous network payloads"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Avoids enormous network payloads"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minify CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minify JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduce unused CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduce unused JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 resource found}other{# resources found}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Serve static assets with an efficient cache policy"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Uses efficient cache policy on static assets"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimised images load faster and consume less mobile data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efficiently encode images"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Actual dimensions"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Displayed dimensions"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Images were larger than their displayed size"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Images were appropriate for their displayed size"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Serve images that are appropriately-sized to save mobile data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Properly size images"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimise total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Enable text compression"}, "core/audits/content-width.js | description": {"message": "If the width of your app's content doesn't match the width of the viewport, your app might not be optimised for mobile screens. [Learn how to size content for the viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "The viewport size of {innerWidth}px does not match the window size of {outerWidth}px."}, "core/audits/content-width.js | failureTitle": {"message": "Content is not sized correctly for the viewport"}, "core/audits/content-width.js | title": {"message": "Content is sized correctly for the viewport"}, "core/audits/critical-request-chains.js | description": {"message": "The critical request chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 chain found}other{# chains found}}"}, "core/audits/critical-request-chains.js | title": {"message": "Avoid chaining critical requests"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directive"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Severity"}, "core/audits/csp-xss.js | description": {"message": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntax"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "The page contains a CSP defined in a <meta> tag. Consider moving the CSP to an HTTP header or defining another strict CSP in an HTTP header."}, "core/audits/csp-xss.js | noCsp": {"message": "No CSP found in enforcement mode"}, "core/audits/csp-xss.js | title": {"message": "Ensure CSP is effective against XSS attacks"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Deprecation/Warning"}, "core/audits/deprecations.js | columnLine": {"message": "Line"}, "core/audits/deprecations.js | description": {"message": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 warning found}other{# warnings found}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Uses deprecated APIs"}, "core/audits/deprecations.js | title": {"message": "Avoids deprecated APIs"}, "core/audits/dobetterweb/charset.js | description": {"message": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1,024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Charset declaration is missing or occurs too late in the HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON> defines charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Specifying a DOCTYPE prevents the browser from switching to quirks mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype name must be the string `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Document contains a `doctype` that triggers `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Document must contain a doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Expected publicId to be an empty string"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Expected systemId to be an empty string"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Document contains a `doctype` that triggers `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "<PERSON> lacks the HTML doctype, thus triggering quirks mode"}, "core/audits/dobetterweb/doctype.js | title": {"message": "<PERSON> has the HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistic"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Value"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}other{# elements}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Avoid an excessive DOM size"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximum DOM Depth"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total DOM Elements"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximum Child Elements"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Avoids an excessive DOM size"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Requests the geolocation permission on page load"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Avoids requesting the geolocation permission on page load"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Issue type"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Issues logged to the `Issues` panel in Chrome DevTools indicate unresolved problems. They can come from network request failures, insufficient security controls and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Issues were logged in the `Issues` panel in Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blocked by cross-origin policy"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Heavy resource usage by ads"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "No issues in the `Issues` panel in Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Detected JavaScript libraries"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Avoid `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Avoids `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Requests the notification permission on page load"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Avoids requesting the notification permission on page load"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 request not served via HTTP/2}other{# requests not served via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Use HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Does not use passive listeners to improve scrolling performance"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Uses passive listeners to improve scrolling performance"}, "core/audits/errors-in-console.js | description": {"message": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Browser errors were logged to the console"}, "core/audits/errors-in-console.js | title": {"message": "No browser errors logged to the console"}, "core/audits/font-display.js | description": {"message": "Leverage the `font-display` CSS feature to ensure that text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Ensure text remains visible during webfont load"}, "core/audits/font-display.js | title": {"message": "All text remains visible during webfont loads"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse was unable to automatically check the `font-display` value for the origin {fontOrigin}.}other{Lighthouse was unable to automatically check the `font-display` values for the origin {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Aspect ratio (actual)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Aspect ratio (displayed)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Displays images with incorrect aspect ratio"}, "core/audits/image-aspect-ratio.js | title": {"message": "Displays images with correct aspect ratio"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Actual size"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Displayed size"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Expected size"}, "core/audits/image-size-responsive.js | description": {"message": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximise image clarity. [Learn how to provide responsive images](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Serves images with low resolution"}, "core/audits/image-size-responsive.js | title": {"message": "Serves images with appropriate resolution"}, "core/audits/installable-manifest.js | already-installed": {"message": "The app is already installed"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Could not download a required icon from the manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "Reason for failure"}, "core/audits/installable-manifest.js | description": {"message": "Service worker is the technology that enables your app to use many progressive web app features, such as offline, add to homescreen and push notifications. With proper service worker and manifest implementations, browsers can proactively prompt users to add your app to their homescreen, which can lead to higher engagement. [Learn more about manifest installability requirements](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 reason}other{# reasons}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Web app manifest or service worker do not meet the installability requirements"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "The Play Store app URL and Play Store ID do not match"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Page is loaded in an incognito window"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Manifest 'display' property must be one of 'standalone', 'fullscreen' or 'minimal-UI'"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest contains 'display_override' field, and the first supported display mode must be one of 'standalone', 'fullscreen' or 'minimal-ui'"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest could not be fetched, is empty or could not be parsed"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifest URL changed while the manifest was being fetched."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest does not contain a 'name' or 'short_name' field"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest does not contain a suitable icon – PNG, SVG or WebP format of at least {value0} px is required, the sizes attribute must be set, and the purpose attribute, if set, must include 'any'."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "No supplied icon is at least {value0} px square in PNG, SVG or WebP format, with the purpose attribute unset or set to 'any'"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Downloaded icon was empty or corrupted"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "No Play Store ID provided"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Page has no manifest <link> URL"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "No matching service worker detected. You may need to reload the page, or check that the scope of the service worker for the current page encloses the scope and start URL from the manifest."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Could not check service worker without a 'start_url' field in the manifest"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Installability error ID '{errorId}' is not recognised"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Page is not served from a secure origin"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Page is not loaded in the main frame"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Page does not work offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA has been uninstalled and installability checks resetting."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "The specified application platform is not supported on Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest specifies prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications is only supported on Chrome Beta and Stable channels on Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse could not determine if there was a service worker. Please try with a newer version of Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "The manifest URL scheme ({scheme}) is not supported on Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Manifest start URL is not valid"}, "core/audits/installable-manifest.js | title": {"message": "Web app manifest and service worker meet the installability requirements"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "A URL in the manifest contains a username, password or port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Page does not work offline. The page will not be regarded as installable after Chrome 93, stable release August 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Allowed"}, "core/audits/is-on-https.js | blocked": {"message": "Blocked"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Insecure URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Request resolution"}, "core/audits/is-on-https.js | description": {"message": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 insecure request found}other{# insecure requests found}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Does not use HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Uses HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatically upgraded to HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Allowed with warning"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Largest contentful paint element"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS contribution"}, "core/audits/layout-shift-elements.js | description": {"message": "These DOM elements contribute most to the CLS of the page. [Learn how to improve CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Avoid large layout shifts"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the Largest Contentful Paint. [Learn more about optimal lazy loading](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Largest contentful paint image was lazily loaded"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Largest contentful paint image was not lazily loaded"}, "core/audits/long-tasks.js | description": {"message": "Lists the longest tasks on the main thread – useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# long task found}other{# long tasks found}}"}, "core/audits/long-tasks.js | title": {"message": "Avoid long main-thread tasks"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Category"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimise main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimise main-thread work"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimises main-thread work"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "To reach the most users, sites should work across every major browser. [Learn about cross-browser compatibility](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Site works cross-browser"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Ensure individual pages are deep linkable via URL and that URLs are unique for the purpose of shareability on social media. [Learn more about providing deep links](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Each page has a URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Transitions should feel snappy as you tap around, even on a slow network. This experience is key to a user's perception of performance. [Learn more about page transitions](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Page transitions don't feel like they block on the network"}, "core/audits/maskable-icon.js | description": {"message": "A maskable icon ensures that the image fills the entire shape without being letterboxed when installing the app on a device. [Learn about maskable manifest icons](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest doesn't have a maskable icon"}, "core/audits/maskable-icon.js | title": {"message": "Manifest has a maskable icon"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative layout shift measures the movement of visible elements within the viewport. [Learn more about the cumulative layout shift metric](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint measures page responsiveness, how long it takes the page to visibly respond to user input. [Learn more about the Interaction to Next Paint metric](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Time to Interactive is the amount of time that it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the maximum potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Network round-trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the round-trip time](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Network Return Times"}, "core/audits/network-server-latency.js | description": {"message": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication that the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Server Back-end Latencies"}, "core/audits/no-unload-listeners.js | description": {"message": "The `unload` event does not fire reliably and listening for it can prevent browser optimisations like the back-forward cache. Use `pagehide` or `visibilitychange` events instead. [Learn more about unload event listeners](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registers an `unload` listener"}, "core/audits/no-unload-listeners.js | title": {"message": "Avoids `unload` event listeners"}, "core/audits/non-composited-animations.js | description": {"message": "Animations that are not composited can be poor, slow and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animated element found}other{# animated elements found}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Filter-related property may move pixels"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Target has another animation which is incompatible"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effect has composite mode other than 'replace'"}, "core/audits/non-composited-animations.js | title": {"message": "Avoid non-composited animations"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Transform-related property depends on box size"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Unsupported CSS property: {properties}}other{Unsupported CSS properties: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Effect has unsupported timing parameters"}, "core/audits/performance-budget.js | description": {"message": "Keep the quantity and size of network requests under the targets set by the provided performance budget. [Learn more about performance budgets](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 request}other{# requests}}"}, "core/audits/performance-budget.js | title": {"message": "Performance budget"}, "core/audits/preload-fonts.js | description": {"message": "Preload `optional` fonts so that first-time visitors may use them. [Learn more about preloading fonts](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Fonts with `font-display: optional` are not preloaded"}, "core/audits/preload-fonts.js | title": {"message": "Fonts with `font-display: optional` are preloaded"}, "core/audits/prioritize-lcp-image.js | description": {"message": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Preload largest contentful paint image"}, "core/audits/redirects.js | description": {"message": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Avoid multiple page redirects"}, "core/audits/resource-summary.js | description": {"message": "To set budgets for the quantity and size of page resources, add a budget.json file. [Learn more about performance budgets](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 request • {byteCount, number, bytes} KiB}other{# requests • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Keep request counts low and transfer sizes small"}, "core/audits/seo/canonical.js | description": {"message": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Multiple conflicting URLs ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Invalid URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Points to another `hreflang` location ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Is not an absolute URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Points to the domain's root URL (the home page), instead of an equivalent page of content"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Document does not have a valid `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Document has a valid `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Uncrawlable link"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination so that more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Links are not crawlable"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Links are crawlable"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Add'l illegible text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Font size"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% of page text"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Font sizes less than 12px are too small to be legible and require mobile visitors to 'pinch to zoom' in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} legible text"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Text is illegible because there's no viewport meta tag optimised for mobile screens."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Document doesn't use legible font sizes"}, "core/audits/seo/font-size.js | legibleText": {"message": "Legible text"}, "core/audits/seo/font-size.js | title": {"message": "Document uses legible font sizes"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Document doesn't have a valid `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relative href value"}, "core/audits/seo/hreflang.js | title": {"message": "Document has a valid `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Unexpected language code"}, "core/audits/seo/http-status-code.js | description": {"message": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Page has unsuccessful HTTP status code"}, "core/audits/seo/http-status-code.js | title": {"message": "Page has successful HTTP status code"}, "core/audits/seo/is-crawlable.js | description": {"message": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Page is blocked from indexing"}, "core/audits/seo/is-crawlable.js | title": {"message": "Page isn’t blocked from indexing"}, "core/audits/seo/link-text.js | description": {"message": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link found}other{# links found}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Links do not have descriptive text"}, "core/audits/seo/link-text.js | title": {"message": "Links have descriptive text"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about structured data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Structured data is valid"}, "core/audits/seo/meta-description.js | description": {"message": "Meta descriptions may be included in search results to concisely summarise page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Description text is empty."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Document does not have a meta description"}, "core/audits/seo/meta-description.js | title": {"message": "Document has a meta description"}, "core/audits/seo/plugins.js | description": {"message": "Search engines can't index plug-in content and many devices restrict plug-ins or don't support them. [Learn more about avoiding plugins](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Document uses plugins"}, "core/audits/seo/plugins.js | title": {"message": "Document avoids plugins"}, "core/audits/seo/robots-txt.js | description": {"message": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Request for robots.txt returned HTTP status: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 error found}other{# errors found}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse was unable to download a robots.txt file"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt is not valid"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt is valid"}, "core/audits/seo/tap-targets.js | description": {"message": "Interactive elements such as buttons and links should be large enough (48 x 48px) and have enough space around them to be easy enough to tap without overlapping onto other elements. [Learn more about tap targets](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} appropriately sized tap targets"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Tap targets are too small because there's no viewport meta tag optimised for mobile screens"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Tap targets are not sized appropriately"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Overlapping target"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Tap target"}, "core/audits/seo/tap-targets.js | title": {"message": "Tap targets are sized appropriately"}, "core/audits/server-response-time.js | description": {"message": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Root document took {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reduce initial server response time"}, "core/audits/server-response-time.js | title": {"message": "Initial server response time was short"}, "core/audits/service-worker.js | description": {"message": "The service worker is the technology that enables your app to use many progressive web app features, such as offline, add to home screen and push notifications. [Learn more about service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "This page is controlled by a service worker, however no `start_url` was found because manifest failed to parse as valid JSON"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "This page is controlled by a service worker, however the `start_url` ({startUrl}) is not in the service worker's scope ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "This page is controlled by a service worker, however no `start_url` was found because no manifest was fetched."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "This origin has one or more service workers, however the page ({pageUrl}) is not in scope."}, "core/audits/service-worker.js | failureTitle": {"message": "Does not register a service worker that controls page and `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registers a service worker that controls page and `start_url`"}, "core/audits/splash-screen.js | description": {"message": "A themed splash screen ensures a high-quality experience when users launch your app from their home screens. [Learn more about splash screens](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Is not configured for a custom splash screen"}, "core/audits/splash-screen.js | title": {"message": "Configured for a custom splash screen"}, "core/audits/themed-omnibox.js | description": {"message": "The browser address bar can be themed to match your site. [Learn more about theming the address bar](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Does not set a theme colour for the address bar."}, "core/audits/themed-omnibox.js | title": {"message": "Sets a theme colour for the address bar."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Customer success)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Product"}, "core/audits/third-party-facades.js | description": {"message": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# facade alternative available}other{# facade alternatives available}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Some third-party resources can be lazy loaded with a facade"}, "core/audits/third-party-facades.js | title": {"message": "Lazy load third-party resources with facades"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Third-party"}, "core/audits/third-party-summary.js | description": {"message": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimise third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Third-party code blocked the main thread for {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduce the impact of third-party code"}, "core/audits/third-party-summary.js | title": {"message": "Minimise third-party usage"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Measurement"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Metric"}, "core/audits/timing-budget.js | description": {"message": "Set a timing budget to help you keep an eye on the performance of your site. Performant sites load fast and respond to user input events quickly. [Learn more about performance budgets](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Timing budget"}, "core/audits/unsized-images.js | description": {"message": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Image elements do not have explicit `width` and `height`"}, "core/audits/unsized-images.js | title": {"message": "Image elements have explicit `width` and `height`"}, "core/audits/user-timings.js | columnType": {"message": "Type"}, "core/audits/user-timings.js | description": {"message": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 user timing}other{# user timings}}"}, "core/audits/user-timings.js | title": {"message": "User Timing marks and measures"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "A `<link rel=preconnect>` was found for '{securityOrigin}' but was not used by the browser. Check that you are using the `crossorigin` attribute properly."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Pre-connect to required origins"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "More than two `<link rel=preconnect>` connections were found. These should be used sparingly and only to the most important origins."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "A `<link rel=preconnect>` was found for '{securityOrigin}' but was not used by the browser. Only use `preconnect` for important origins that the page will certainly request."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "A preload `<link>` was found for '{preloadURL}' but was not used by the browser. Check that you are using the `crossorigin` attribute properly."}, "core/audits/uses-rel-preload.js | description": {"message": "Consider using `<link rel=preload>` to prioritise fetching resources that are currently requested later in page load. [Learn how to preload key requests](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Pre-load key requests"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Map URL"}, "core/audits/valid-source-maps.js | description": {"message": "Source maps translate minified code to the original source code. This helps developers to debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Missing source maps for large first-party JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Large JavaScript file is missing a source map"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Warning: missing 1 item in `.sourcesContent`}other{Warning: missing # items in `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Page has valid source maps"}, "core/audits/viewport.js | description": {"message": "A `<meta name=\"viewport\">` not only optimises your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "No `<meta name=\"viewport\">` tag found"}, "core/audits/viewport.js | failureTitle": {"message": "Does not have a `<meta name=\"viewport\">` tag with `width` or `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "This is the thread-blocking work occurring during the Interaction to Next Paint measurement. [Learn more about the Interaction to Next Paint metric](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms spent on event ‘{interactionType}’"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Event target"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimise work during key interaction"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Input delay"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Presentation delay"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Processing time"}, "core/audits/work-during-interaction.js | title": {"message": "Minimises work during key interaction"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, such as a screen reader."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio and video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "These items highlight common accessibility best practices."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Best practices"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Only a subset of accessibility issues can be automatically detected so manual testing is also encouraged."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accessibility"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "These are opportunities to improve the legibility of your content."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "These are opportunities to improve the interpretation of your content by users in different locales."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisation and localisation"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, such as a screen reader."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Names and labels"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "These are opportunities to improve keyboard navigation in your application."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tables and lists"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Browser compatibility"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Best Practices"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "General"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Trust and safety"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "User experience"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Performance budgets set standards for the performance of your site."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budgets"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the performance score."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostics"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "The most critical aspect of performance is how quickly pixels are rendered onscreen. Key metrics: First Contentful Paint, First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "First Paint Improvements"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "These suggestions can help your page load faster. They don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the performance score."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Opportunities"}, "core/config/default-config.js | metricGroupTitle": {"message": "Metrics"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Enhance the overall loading experience, so the page is responsive and ready to use as soon as possible. Key metrics: Time to Interactive, Speed Index"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Overall Improvements"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performance"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "These checks validate the aspects of a progressive web app. [Learn what makes a good progressive web app](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "These checks are required by the baseline [PWA checklist](https://web.dev/pwa-checklist/) but are not automatically checked by Lighthouse. They do not affect your score but it's important that you verify them manually."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installable"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA Optimised"}, "core/config/default-config.js | seoCategoryDescription": {"message": "These checks ensure that your page is following basic search engine optimisation advice. There are many additional factors that Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/learn-core-web-vitals/). [Learn more about Google Search essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Run these additional validators on your site to check additional SEO best practices."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Content Best Practices"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "To appear in search results, crawlers need access to your app."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawling and Indexing"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Make sure that your pages are mobile-friendly so that users don’t have to pinch or zoom to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobile friendly"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "The tested device appears to have a slower CPU than Lighthouse expects. This can negatively affect your performance score. Learn more about [calibrating an appropriate CPU slowdown multiplier](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "The page may not be loading as expected because your test URL ({requested}) was redirected to {final}. Try testing the second URL directly."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "The page loaded too slowly to finish within the time limit. Results may be incomplete."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Clearing the browser cache timed out. Try auditing this page again and file a bug if the issue persists."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{There may be stored data affecting loading performance in this location: {locations}. Audit this page in an incognito window to prevent those resources from affecting your scores.}other{There may be stored data affecting loading performance in these locations: {locations}. Audit this page in an incognito window to prevent those resources from affecting your scores.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Clearing the origin data timed out. Try auditing this page again and file a bug if the issue persists."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Only pages loaded via a GET request are eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Only pages with a status code of 2XX can be cached."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome detected an attempt to execute JavaScript while in the cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Pages that requested an AppBanner are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Back-forward cache is disabled by flags. Visit chrome://flags/#back-forward-cache to enable it locally on this device."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Back-forward cache is disabled by the command line."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Back-forward cache is disabled due to insufficient memory."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Back-forward cache is not supported by delegate."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Back-forward cache is disabled for pre-renderer."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "The page cannot be cached because it has a BroadcastChannel instance with registered listeners."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Pages with cache-control:no-store header cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "The cache was intentionally cleared."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "The page was evicted from the cache to allow another page to be cached."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Pages containing plug-ins are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Pages that use FileChooser API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Pages that use file system access API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Pages that use media device dispatcher are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "A media player was playing upon navigating away."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Pages that use MediaSession API and set a playback state are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Pages that use MediaSession API and set action handlers are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Back-forward cache is disabled due to screen reader."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Pages that use SecurityHandler are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Pages that use Serial API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Pages that use WebAuthentication API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Pages that use WebBluetooth API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Pages that use WebUSB API are not eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Pages that use a dedicated worker or worklet are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "The document did not finish loading before navigating away."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "App banner was present upon navigating away."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome Password Manager was present upon navigating away."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM distillation was in progress upon navigating away."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM distiller viewer was present upon navigating away."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Back-forward cache is disabled due to extensions using messaging API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Extensions with long-lived connection should close the connection before entering back-forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Extensions with long-lived connection attempted to send messages to frames in back-forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Back-forward cache is disabled due to extensions."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Modal dialogue such as form resubmission or HTTP password dialogue was shown for the page upon navigating away."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "The offline page was shown upon navigating away."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Out-of-memory intervention bar was present upon navigating away."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "There were permission requests upon navigating away."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Pop-up blocker was present upon navigating away."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing details were shown upon navigating away."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing considered this page to be abusive and blocked the pop-up."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "A service worker was activated while the page was in back-forward cache."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Back-forward cache is disabled due to a document error."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Pages using FencedFrames cannot be stored in bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "The page was evicted from the cache to allow another page to be cached."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Pages that have granted media stream access are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Pages that use portals are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Pages that use IdleManager are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Pages that have an open IndexedDB connection are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Ineligible APIs were used."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Pages that JavaScript is injected into by extensions are not currently eligible for back/forward cache."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Pages that StyleSheet is injected into by extensions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Internal error."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Back-forward cache is disabled due to a keepalive request."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Pages that use keyboard lock are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | loading": {"message": "The page did not finish loading before navigating away."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Pages whose main resource has cache-control:no-cache cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Pages whose main resource has cache-control:no-store cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigation was cancelled before the page could be restored from back-forward cache."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "The page was evicted from the cache because an active network connection received too much data. Chrome limits the amount of data that a page may receive while cached."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Pages that have inflight fetch() or XHR are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "The page was evicted from back-forward cache because an active network request involved a redirect."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "The page was evicted from the cache because a network connection was open too long. Chrome limits the amount of time that a page may receive data while cached."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Pages that do not have a valid response head cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigation happened in a frame other than the main frame."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Pages with ongoing indexed DB transactions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Pages with an in-flight network request are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Pages with an in-flight fetch network request are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Pages with an in-flight network request are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Pages with an in-flight XHR network request are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Pages that use PaymentManager are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Pages that use picture-in-picture are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | portal": {"message": "Pages that use portals are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | printing": {"message": "Pages that show Printing UI are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "The page was opened using '`window.open()`' and another tab has a reference to it, or the page opened a window."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "The renderer process for the page in back-forward cache crashed."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "The renderer process for the page in back-forward cache was killed."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Pages that have requested audio capture permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Pages that have requested sensor permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Pages that have requested background sync or fetch permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Pages that have requested MIDI permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Pages that have requested notifications permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Pages that have requested storage access are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Pages that have requested video capture permissions are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Only pages whose URL scheme is HTTP/HTTPS can be cached."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "The page was claimed by a service worker while it is in back-forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "A service worker attempted to send the page in back-forward cache a `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorker was unregistered while a page was in back-forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "The page was evicted from back-forward cache due to a service worker activation."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome restarted and cleared the back-forward cache entries."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Pages that use SharedWorker are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Pages that use SpeechRecogniser are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Pages that use SpeechSynthesis are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "An iframe on the page started a navigation that did not complete."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Pages whose subresource has cache-control:no-cache cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Pages whose subresource has cache-control:no-store cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | timeout": {"message": "The page exceeded the maximum time in back-forward cache and expired."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "The page timed out entering back-forward cache (likely due to long-running page-hide handlers)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "The page has an unload handler in the main frame."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "The page has an unload handler in a subframe."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON> has changed the user agent override header."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Pages that have granted access to record video or audio are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Pages that use WebDatabase are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Pages that use WebHID are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Pages that use WebLocks are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Pages that use WebNfc are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Pages that use WebOTPService are not currently eligible for bfcache."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Pages with WebRTC cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Pages that use WebShare are not currently eligible for back-forward cache."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Pages with WebSocket cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Pages with WebTransport cannot enter back-forward cache."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Pages that use WebXR are not currently eligible for back-forward cache."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Consider adding https: and http: URL schemes (ignored by browsers supporting 'strict-dynamic') to be backward compatible with older browsers."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener is deprecated since CSP3. Please use the Cross-Origin-Opener-Policy header instead."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer is deprecated since CSP2. Please use the Referrer-Policy header instead."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss is deprecated since CSP2. Please use the X-XSS-Protection header instead."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Missing base-uri allows injected <base> tags to set the base URL for all relative URLs (e.g. scripts) to an attacker-controlled domain. Consider setting base-uri to 'none' or 'self'."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Missing object-src allows the injection of plug-ins that execute unsafe scripts. Consider setting object-src to 'none' if you can."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src directive is missing. This can allow the execution of unsafe scripts."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Did you forget the semicolon? {keyword} seems to be a directive, not a keyword."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "<PERSON><PERSON> should use the base64 charset."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces should be at least eight characters long."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Avoid using plain URL schemes ({keyword}) in this directive. Plain URL schemes allow scripts to be sourced from an unsafe domain."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Avoid using plain wildcards ({keyword}) in this directive. Plain wildcards allow scripts to be sourced from an unsafe domain."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "The reporting destination is only configured via the report-to directive. This directive is only supported in Chromium-based browsers so the use of a report-uri directive is also recommended."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "No CSP configures a reporting destination. This makes it difficult to maintain the CSP over time and monitor for any breakages."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Host allowlists can frequently be bypassed. Consider using CSP nonces or hashes instead, along with 'strict-dynamic' if necessary."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Unknown CSP directive."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} seems to be an invalid keyword."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "'unsafe-inline' allows the execution of unsafe in-page scripts and event handlers. Consider using CSP nonces or hashes to allow scripts individually."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Consider adding 'unsafe-inline' (ignored by browsers supporting nonces/hashes) to be backward compatible with older browsers."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Authorisation will not be covered by the wildcard symbol (*) in CORS `Access-Control-Allow-Headers` handling."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace `(n|r|t)` characters and less-than characters (`<`) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` is deprecated. Instead, use standardised API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` is deprecated. Instead, use standardised API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` is deprecated. Instead, use standardised API: `nextHopProtocol` in Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Cookies containing a `(0|r|n)` character will be rejected instead of truncated."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Relaxing the same-origin policy by setting `document.domain` is deprecated and will be disabled by default. This deprecation warning is for a cross-origin access that was enabled by setting `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Triggering {PH1} from cross-origin iframes has been deprecated and will be removed in the future."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "The `disableRemotePlayback` attribute should be used in order to disable the default cast integration instead of using `-internal-media-controls-overlay-cast-button` selector."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} is deprecated. Please use {PH2} instead."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "This is an example of a translated deprecation issue message."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Relaxing the same-origin policy by setting `document.domain` is deprecated and will be disabled by default. To continue using this feature, please opt out of origin-keyed agent clusters by sending an `Origin-Agent-Cluster: ?0` header along with the HTTP response for the document and frames. See https://developer.chrome.com/blog/immutable-document-domain/ for more details."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` is deprecated and will be removed. Please use `Event.composedPath()` instead."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "The `Expect-CT` header is deprecated and will be removed. Chrome requires Certificate Transparency for all publicly trusted certificates issued after 30 April 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Check the feature status page for more details."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` and `watchPosition()` no longer work on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` and `watchPosition()` are deprecated on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` is deprecated. Please use `RTCPeerConnectionIceErrorEvent.address` or `RTCPeerConnectionIceErrorEvent.port` instead."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "The merchant origin and arbitrary data from the `canmakepayment` service worker event are deprecated and will be removed: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the Internet, increasing the risk of a cross-site request forgery (CSRF) attack and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts and will start blocking them."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS cannot be loaded from `file:` URLs unless they end in a `.css` file extension."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Using `SourceBuffer.abort()` to abort `remove()`'s asynchronous range removal is deprecated due to specification change. Support will be removed in the future. You should listen to the `updateend` event instead. `abort()` is intended to only abort an asynchronous media append or reset parser state."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Setting `MediaSource.duration` below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit `remove(newDuration, oldDuration)` on all `sourceBuffers`, where `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "This change will go into effect with milestone {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask permission to use even if the sysex is not specified in the `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "The notification API may no longer be used from insecure origins. You should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Permission for the notification API may no longer be requested from a cross-origin iframe. You should consider requesting permission from a top-level frame or opening a new window instead."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL in non-secure contexts is deprecated and will be removed soon. Please use web storage or indexed database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Specifying `overflow: visible` on img, video and canvas tags may cause them to produce visual content outside of the element bounds. See https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` is deprecated. Please use just-in-time install for payment handlers instead."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Your `PaymentRequest` call bypassed Content-Security-Policy (CSP) `connect-src` directive. This bypass is deprecated. Please add the payment method identifier from the `PaymentRequest` API (in `supportedMethods` field) to your CSP `connect-src` directive."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` is deprecated. Please use standardised `navigator.storage` instead."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` with a `<picture>` parent is invalid and, therefore, ignored. Please use `<source srcset>` instead."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` is deprecated. Please use standardised `navigator.storage` instead."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. `**********************/`) are blocked."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "The constraint `DtlsSrtpKeyAgreement` is removed. You have specified a `false` value for this constraint, which is interpreted as an attempt to use the removed `SDES key negotiation` method. This functionality is removed; use a service that supports `DTLS key negotiation` instead."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "The constraint `DtlsSrtpKeyAgreement` is removed. You have specified a `true` value for this constraint, which had no effect, but you can remove this constraint for tidiness."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` detected. This dialect of the `Session Description Protocol` is no longer supported. Please use `Unified Plan SDP` instead."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, which is used when constructing an `RTCPeerConnection` with `{sdpSemantics:plan-b}`, is a legacy non-standard version of the `Session Description Protocol` that has been permanently deleted from the web platform. It is still available when building with `IS_FUCHSIA`, but we intend to delete it as soon as possible. Stop depending on it. See https://crbug.com/1302249 for status."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "The `rtcpMuxPolicy` option is deprecated and will be removed."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` without user activation is deprecated and will be removed."}, "core/lib/deprecations-strings.js | title": {"message": "Deprecated feature used"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt in to cross-origin isolation to continue using `SharedArrayBuffer`. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} is vendor-specific. Please use the standard {PH2} instead."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 is not supported by response JSON in `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchronous `XMLHttpRequest` on the main thread is deprecated because of its detrimental effects on the end user’s experience. For more help, check https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` is deprecated. Please use `isSessionSupported()` and check the resolved Boolean value instead."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Main-thread blocking time"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Description"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Duration"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Failing elements"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Location"}, "core/lib/i18n/i18n.js | columnName": {"message": "Name"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Over budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Requests"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Resource size"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Resource type"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Size"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Source"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Start Time"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Time Spent"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Transfer size"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potential savings"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potential Savings"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potential savings of {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element found}other{# elements found}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potential savings of {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Image"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "High"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Low"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Medium"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Max potential first input delay"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Other"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Other resources"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Third-party"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Something went wrong with recording the trace over your page load. Please run Lighthouse again. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Timeout waiting for initial Debugger Protocol connection."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome didn't collect any screenshots during the page load. Please make sure that there is content visible on the page, and then try re-running Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS servers could not resolve the provided domain."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Required {artifactName} gatherer encountered an error: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "An internal Chrome error occurred. Please restart Chrome and try re-running Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Required {artifact<PERSON><PERSON>} gatherer did not run."}, "core/lib/lh-error.js | noFcp": {"message": "The page did not paint any content. Please ensure that you keep the browser window in the foreground during the load and try again. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "The page did not display content that qualifies as a largest contentful paint (LCP). Ensure that the page has a valid LCP element and then try again. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "The page provided is not HTML (served as MIME type {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "This version of Chrome is too old to support '{featureName}'. Use a newer version to see full results."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse was unable to reliably load the page that you requested. Make sure that you are testing the correct URL and that the server is properly responding to all requests."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse was unable to reliably load the URL that you requested because the page stopped responding."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "The URL you have provided does not have a valid security certificate. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome prevented page load with an interstitial. Make sure that you are testing the correct URL and that the server is properly responding to all requests."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse was unable to reliably load the page that you requested. Make sure that you are testing the correct URL and that the server is properly responding to all requests. (Details: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse was unable to reliably load the page that you requested. Make sure that you are testing the correct URL and that the server is properly responding to all requests. (Status code: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Your page took too long to load. Please follow the opportunities in the report to reduce your page load time, and then try re-running Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Waiting for DevTools protocol response has exceeded the allotted time. (Method: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Fetching resource content has exceeded the allotted time"}, "core/lib/lh-error.js | urlInvalid": {"message": "The URL that you have provided appears to be invalid."}, "core/lib/navigation-error.js | warningXhtml": {"message": "The page MIME type is XHTML: Lighthouse does not explicitly support this document type"}, "core/user-flow.js | defaultFlowName": {"message": "User flow ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navigation report ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Snapshot report ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Timespan report ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "All reports"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categories"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accessibility"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Best Practices"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performance"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "progressive web app"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Desktop"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Understanding the Lighthouse flow report"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Understanding flows"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Use Navigation reports to…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Use Snapshot reports to…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Use Timespan reports to…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obtain a Lighthouse performance score."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Measure page load performance metrics, such as largest contentful paint and speed index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Assess progressive web app capabilities."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Find accessibility issues in single-page applications or complex forms."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Evaluate best practices of menus and UI elements hidden behind interaction."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Measure layout shifts and JavaScript execution time on a series of interactions."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Discover performance opportunities to improve the experience for long-lived pages and single-page applications."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Highest impact"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informative audit}other{{numInformative} informative audits}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobile"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Page load"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Navigation reports analyse a single page load, exactly like the original Lighthouse reports."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigation report"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navigation report}other{{numNavigation} navigation reports}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} passable audit}other{{numPassableAudits} passable audits}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audit passed}other{{numPassed} audits passed}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Average"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Poor"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Good"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Save"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Captured state of page"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Snapshot reports analyse the page in a particular state, typically after user interactions."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Snapshot report"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} snapshot report}other{{numSnapshot} snapshot reports}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Summary"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "User interactions"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Timespan reports analyse an arbitrary period of time, typically containing user interactions."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Timespan report"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} timespan report}other{{numTimespan} timespan reports}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse user flow report"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "For animated content, use [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) to minimise CPU usage when the content is offscreen."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Consider displaying all [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) components in WebP formats while specifying an appropriate fallback for other browsers. [Learn more](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Ensure that you are using [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) for images to automatically lazy load. [Learn more](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Use tools such as [AMP Optimiser](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) to [server-side render AMP layouts](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Refer to the [AMP documentation](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) to ensure that all styles are supported."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "The [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) component supports the [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) attribute to specify which image assets to use based on the screen size. [Learn more](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Consider virtual scrolling with the Component Dev Kit (CDK) if very large lists are being rendered. [Learn more](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Apply [route-level code splitting](https://web.dev/route-level-code-splitting-in-angular/) to minimise the size of your JavaScript bundles. Also, consider precaching assets with the [Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "If you are using Angular CLI, ensure that builds are generated in production mode. [Learn more](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "If you are using Angular CLI, include source maps in your production build to inspect your bundles. [Learn more](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Preload routes ahead of time to speed up navigation. [Learn more](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Consider using the `BreakpointObserver` utility in the Component Dev Kit (CDK) to manage image breakpoints. [Learn more](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Consider uploading your GIF to a service that will make it available to embed as an HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Specify `@font-display` when defining custom fonts in your theme."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Consider configuring [WebP image formats with a Convert image style](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) on your site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Install [a Drupal module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) that can lazy load images. Such modules provide the ability to defer any offscreen images to improve performance."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Consider using a module to inline critical CSS and JavaScript, or potentially load assets asynchronously via JavaScript such as the [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg) module. Beware that optimisations provided by this module may break your site, so you will likely need to make code changes."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Themes, modules and server specifications all contribute to server response time. Consider finding a more optimised theme, carefully selecting an optimisation module and/or upgrading your server. Your hosting servers should make use of PHP opcode caching, memory-caching to reduce database query times such as Redis or Memcached, as well as optimised application logic to prepare pages faster."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Consider using [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) to reduce the size of images loaded on your page. If you are using Views to show multiple content items on a page, consider implementing pagination to limit the number of content items shown on a given page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Ensure you have enabled 'Aggregate CSS files' in the 'Administration » Configuration » Development' page. You can also configure more advanced aggregation options through [additional modules](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) to speed up your site by concatenating, minifying and compressing your CSS styles."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Ensure that you have enabled 'Aggregate JavaScript files' in the 'Administration » Configuration » Development' page. You can also configure more advanced aggregation options through [additional modules](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) to speed up your site by concatenating, minifying and compressing your JavaScript assets."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Consider removing unused CSS rules and only attach the needed Drupal libraries to the relevant page or component in a page. See the [Drupal documentation link](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) for details. To identify attached libraries that are adding extraneous CSS, try running [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. You can identify the theme/module responsible from the URL of the stylesheet when CSS aggregation is disabled in your Drupal site. Look out for themes/modules that have many stylesheets in the list which have a lot of red in code coverage. A theme/module should only enqueue a stylesheet if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Consider removing unused JavaScript assets and only attach the needed Drupal libraries to the relevant page or component in a page. See the [Drupal documentation link](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) for details. To identify attached libraries that are adding extraneous JavaScript, try running [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. You can identify the theme/module responsible from the URL of the script when JavaScript aggregation is disabled in your Drupal site. Look out for themes/modules that have many scripts in the list which have a lot of red in code coverage. A theme/module should only enqueue a script if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Set the 'Browser and proxy cache maximum age' in the 'Administration » Configuration » Development' page. Read about [Drupal cache and optimising for performance](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Consider using [a module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) that automatically optimises and reduces the size of images uploaded through the site while retaining quality. Also, ensure that you are using the native [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) provided from Drupal (available in Drupal 8 and above) for all images rendered on the site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Preconnect or dns-prefetch resource hints can be added by installing and configuring [a module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) that provides facilities for user agent resource hints."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Ensure that you are using the native [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) provided from Drupal (available in Drupal 8 and above). Use the Responsive Image Styles when rendering image fields through view modes, views or images uploaded through the WYSIWYG editor."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Use [<PERSON><PERSON>](https://pubdash.ezoic.com/speed) and enable `Optimize Fonts` to automatically leverage the `font-display` CSS feature to ensure that text is user-visible while webfonts are loading."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Next-Gen Formats` to convert images to WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Use [E<PERSON> Leap](https://pubdash.ezoic.com/speed) and enable `Lazy Load Images` to defer loading off-screen images until they are needed."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Critical CSS` and `Script Delay` to defer non-critical JS/CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Use [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) to cache your content across our world wide network, improving time to first byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Minify CSS` to automatically minify your CSS to reduce network payload sizes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Minify Javascript` to automatically minify your JS to reduce network payload sizes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Remove Unused CSS` to help with this issue. It will identify the CSS classes that are actually used on each page of your site, and remove any others to keep the file size small."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Efficient Static Cache Policy` to set recommended values in the caching header for static assets."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Next-Gen Formats` to convert images to WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Pre-Connect Origins` to automatically add `preconnect` resource hints to establish early connections to important third-party origins."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Use [Ezoic Leap](https://pubdash.ezoic.com/speed) and enable `Preload Fonts` and `Preload Background Images` to add `preload` links to prioritise fetching resources that are currently requested later in page load."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Use [E<PERSON> Leap](https://pubdash.ezoic.com/speed) and enable `Resize Images` to resize images to a device-appropriate size, reducing network payload sizes."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Consider uploading your GIF to a service that will make it available to embed as an HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Consider using a [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) or service that will automatically convert your uploaded images to the optimal formats."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Install a [lazy-load <PERSON><PERSON><PERSON> plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) that provides the ability to defer any offscreen images, or switch to a template that provides that functionality. Starting with Joomla 4.0, all new images will [automatically](https://github.com/joomla/joomla-cms/pull/30748) get the `loading` attribute from the core."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "There are a number of Joomla plug-ins that can help you [inline critical assets](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) or [defer less important resources](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Beware that optimisations provided by these plug-ins may break features of your templates or plug-ins, so you will need to test these thoroughly."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Templates, extensions and server specifications all contribute to server response time. Consider finding a more optimised template, carefully selecting an optimisation extension and/or upgrading your server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Consider showing excerpts in your article categories (e.g. via the read more link), reducing the number of articles shown on a given page, breaking your long posts into multiple pages or using a plug-in to lazy-load comments."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "A number of [Joomla extensions](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) can speed up your site by concatenating, minifying and compressing your css styles. There are also templates that provide this functionality."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "A number of [Joomla extensions](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) can speed up your site by concatenating, minifying and compressing your scripts. There are also templates that provide this functionality."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Consider reducing, or switching, the number of [Joomla extensions](https://extensions.joomla.org/) loading unused CSS in your page. To identify extensions that are adding extraneous CSS, try running [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. You can identify the theme/plug-in responsible from the URL of the stylesheet. Look out for plug-ins that have many stylesheets in the list which have a lot of red in code coverage. A plug-in should only enqueue a stylesheet if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Consider reducing, or switching, the number of [Joomla extensions](https://extensions.joomla.org/) loading unused JavaScript in your page. To identify plug-ins that are adding extraneous JS, try running [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. You can identify the extension responsible from the URL of the script. Look out for extensions that have many scripts in the list which have a lot of red in code coverage. An extension should only enqueue a script if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Read about [browser caching in Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Consider using an [image optimisation plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) that compresses your images while retaining quality."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Consider using a [responsive images plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) to use responsive images in your content."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "You can enable text compression by enabling Gzip Page Compression in Joomla (System > Global configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "If you are not bundling your JavaScript assets, consider using [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Disable Magento's built-in [JavaScript bundling and minification](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), and consider using [baler](https://github.com/magento/baler/) instead."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Specify `@font-display` when [defining custom fonts](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Consider searching the [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) for a variety of third-party extensions to leverage newer image formats."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Consider modifying your product and catalogue templates to make use of the web platform's [lazy loading](https://web.dev/native-lazy-loading) feature."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Use Magento's [Varnish integration](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Enable the 'Minify CSS Files' option in your store's Developer settings. [Learn more](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Use [Terser](https://www.npmjs.com/package/terser) to minify all JavaScript assets from static content deployment and disable the built-in minification feature."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Disable Magento's built-in [JavaScript bundling](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Consider searching the [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) for a variety of third-party extensions to optimise images."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Preconnect or dns-prefetch resource hints can be added by [modifying a themes's layout](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` tags can be added by [modifying a themes's layout](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Use the `next/image` component instead of `<img>` to automatically optimise image format. [Learn more](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Use the `next/image` component instead of `<img>` to automatically lazy-load images. [Learn more](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Use the `next/image` component and set 'priority' to true to preload an LCP image. [Learn more](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Use the `next/script` component to defer loading of non-critical third-party scripts. [Learn more](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Use the `next/image` component to make sure that images are always sized appropriately. [Learn more](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Consider setting up `PurgeCSS` in `Next.js` configuration to remove unused rules from stylesheets. [Learn more](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Use `Webpack Bundle Analyzer` to detect unused JavaScript code. [Learn more](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Consider using `Next.js Analytics` to measure your app's real-world performance. [Learn more](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configure caching for immutable assets and `Server-side Rendered` (SSR) pages. [Learn more](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Use the `next/image` component instead of `<img>` to adjust image quality. [Learn more](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Use the `next/image` component to set the appropriate `sizes`. [Learn more](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Enable compression on your Next.js server. [Learn more](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Use the `nuxt/image` component and set `format=\"webp\"`. [Learn more](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Use the `nuxt/image` component and set `loading=\"lazy\"` for offscreen images. [Learn more](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Use the `nuxt/image` component and specify `preload` for LCP image. [Learn more](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Use the `nuxt/image` component and specify explicit `width` and `height`. [Learn more](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Use the `nuxt/image` component and set the appropriate `quality`. [Learn more](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Use the `nuxt/image` component and set the appropriate `sizes`. [Learn more](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Replace animated GIFs with video](https://web.dev/replace-gifs-with-videos/) for faster web page loads, and consider using modern file formats, such as [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) or [AV1,](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) to improve compression efficiency by greater than 30% over the current state-of-the-art video codec, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Consider using a [plug-in](https://octobercms.com/plugins?search=image) or service that will automatically convert the uploaded images to the optimal formats. [WebP lossless images](https://developers.google.com/speed/webp) are 26% smaller in size compared to PNGs, and 25–34% smaller than comparable JPEG images at the equivalent SSIM quality index. Another next-gen image format to consider is [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Consider installing an [image lazy-loading plug-in](https://octobercms.com/plugins?search=lazy) that provides the ability to defer any offscreen images, or switch to a theme that provides that functionality. Also consider using [the AMP plug-in](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "There are many plug-ins that help [inline critical assets](https://octobercms.com/plugins?search=css). These plug-ins may break other plug-ins, so you should test thoroughly."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Themes, plug-ins and server specifications all contribute to the server response time. Consider finding a more optimised theme, carefully selecting an optimisation plug-in and/or upgrading your server. October CMS also allows developers to use [`Queues`](https://octobercms.com/docs/services/queues) to defer the processing of a time consuming task, such as sending an email. This drastically speeds up web requests."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Consider showing excerpts in the post lists (e.g. using a `show more` button), reducing the number of posts shown on a given web page, breaking long posts into multiple web pages or using a plug-in to lazy-load comments."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "There are many [plug-ins](https://octobercms.com/plugins?search=css) that can speed up a website by concatenating, minifying and compressing the styles. Using a build process to do this minification upfront can speed up development."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "There are many [plug-ins](https://octobercms.com/plugins?search=javascript) that can speed up a website by concatenating, minifying and compressing the scripts. Using a build process to do this minification upfront can speed up development."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Consider reviewing the [plug-ins](https://octobercms.com/plugins) loading unused CSS on the website. To identify plug-ins that add unnecessary CSS, run [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. Identify the theme/plug-in responsible from the stylesheet URL. Look for plug-ins with many stylesheets that have a lot of red in code coverage. A plug-in should only add a stylesheet if it is actually used on the web page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Consider reviewing the [plug-ins](https://octobercms.com/plugins?search=javascript) that load unused JavaScript in the web page. To identify plug-ins that add unnecessary JavaScript, run [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. Identify the theme/plug-in responsible from the URL of the script. Look for plug-ins that have many scripts with a lot of red in code coverage. A plug-in should only add a script if it is actually used on the web page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Read about [preventing unnecessary network requests with the HTTP cache](https://web.dev/http-cache/#caching-checklist). There are many [plug-ins](https://octobercms.com/plugins?search=Caching) that can be used to speed up caching."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Consider using an [image optimisation plug-in](https://octobercms.com/plugins?search=image) that compresses images while retaining the quality."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Upload images directly in the media manager to ensure that the required image sizes are available. Consider using the [resize filter](https://octobercms.com/docs/markup/filter-resize) or an [image resizing plug-in](https://octobercms.com/plugins?search=image) to ensure that the optimal image sizes are used."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Enable text compression in the web server configuration."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Consider using a 'windowing' library, like `react-window`, to minimise the number of DOM nodes created if you are rendering many repeated elements on the page. [Learn more](https://web.dev/virtualize-long-lists-react-window/). Also, minimise unnecessary re-renders using [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) or [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) and [skip effects](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) only until certain dependencies have changed if you are using the `Effect` hook to improve runtime performance."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "If you are using React Router, minimise usage of the `<Redirect>` component for [route navigations](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "If you are server-side rendering any React components, consider using `renderToPipeableStream()` or `renderToStaticNodeStream()` to allow the client to receive and hydrate different parts of the markup instead of all at once. [Learn more](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "If your build system minifies CSS files automatically, ensure that you are deploying the production build of your application. You can check this with the React developer tools extension. [Learn more](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "If your build system minifies JS files automatically, ensure that you are deploying the production build of your application. You can check this with the React developer tools extension. [Learn more](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "If you are not server-side rendering, [split your JavaScript bundles](https://web.dev/code-splitting-suspense/) with `React.lazy()`. Otherwise, code-split using a third-party library such as [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Use the React DevTools Profiler, which makes use of the Profiler API, to measure the rendering performance of your components. [Learn more.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Consider uploading your GIF to a service that will make it available to embed as an HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Consider using the [Performance Lab](https://wordpress.org/plugins/performance-lab/) plugin to automatically convert your uploaded JPEG images into WebP, wherever supported."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Install a [lazy load WordPress plug-in](https://wordpress.org/plugins/search/lazy+load/) that provides the ability to defer any offscreen images, or switch to a theme that provides that functionality. Also consider using [the AMP plug-in](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "There are a number of WordPress plug-ins that can help you [inline critical assets](https://wordpress.org/plugins/search/critical+css/) or [defer less important resources](https://wordpress.org/plugins/search/defer+css+javascript/). Beware that optimisations provided by these plug-ins may break features of your theme or plug-ins, so you will likely need to make code changes."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Themes, plug-ins and server specifications all contribute to server response time. Consider finding a more optimised theme, carefully selecting an optimisation plug-in, and/or upgrading your server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Consider showing excerpts in your post lists (e.g. via the more tag), reducing the number of posts shown on a given page, breaking your long posts into multiple pages, or using a plug-in to lazy-load comments."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "A number of [WordPress plug-ins](https://wordpress.org/plugins/search/minify+css/) can speed up your site by concatenating, minifying and compressing your styles. You may also want to use a build process to do this minification up-front if possible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "A number of [WordPress plug-ins](https://wordpress.org/plugins/search/minify+javascript/) can speed up your site by concatenating, minifying and compressing your scripts. You may also want to use a build process to do this minification up front if possible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Consider reducing, or switching, the number of [WordPress plug-ins](https://wordpress.org/plugins/) loading unused CSS in your page. To identify plug-ins that are adding extraneous CSS, try running [code coverage](https://developer.chrome.com/docs/devtools/coverage/) in Chrome DevTools. You can identify the theme/plug-in responsible from the URL of the stylesheet. Look out for plug-ins that have many stylesheets in the list which have a lot of red in code coverage. A plug-in should only enqueue a stylesheet if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Consider reducing, or switching, the number of [WordPress plug-ins](https://wordpress.org/plugins/) loading unused JavaScript in your page. To identify plug-ins that are adding extraneous JS, try running [code coverage](https://developer.chrome.com/docs/devtools/coverage/) in Chrome DevTools. You can identify the theme/plug-in responsible from the URL of the script. Look out for plug-ins that have many scripts in the list which have a lot of red in code coverage. A plug-in should only enqueue a script if it is actually used on the page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Read about [browser caching in WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Consider using an [image optimisation WordPress plug-in](https://wordpress.org/plugins/search/optimize+images/) that compresses your images while retaining quality."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Upload images directly through the [media library](https://wordpress.org/support/article/media-library-screen/) to ensure that the required image sizes are available, and then insert them from the media library or use the image widget to ensure that the optimal image sizes are used (including those for the responsive breakpoints). Avoid using `Full Size` images unless the dimensions are adequate for their usage. [Learn more](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "You can enable text compression in your web server configuration."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Enable 'Imagify' from the Image optimisation tab in 'WP Rocket' to convert your images to WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Enable [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) in WP Rocket to fix this recommendation. This feature delays the loading of the images until the visitor scrolls down the page and actually needs to see them."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Enable [Remove unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) and [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) in 'WP Rocket' to address this recommendation. These features will respectively optimise the CSS and JavaScript files so that they don't block the rendering of your page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Enable [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) in 'WP Rocket' to fix this issue. Any spaces and comments in your site's CSS files will be removed to make the file size smaller and faster to download."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Enable [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) in 'WP Rocket' to fix this issue. Empty spaces and comments will be removed from JavaScript files to make their size smaller and faster to download."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Enable [Remove unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) in 'WP Rocket' to fix this issue. It reduces page size by removing all CSS and stylesheets that are not used while keeping only the used CSS for each page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Enable [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) in 'WP Rocket' to fix this problem. It will improve the loading of your page by delaying the execution of scripts until user interaction. If your site has iframes, you can use WP Rocket's [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) and [replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) as well."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Enable 'Imagify' from the Image optimisation tab in 'WP Rocket' and run bulk optimisation to compress your images."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Use [Prefetch DNS requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) in 'WP Rocket' to add 'dns-prefetch' and speed up the connection with external domains. Also, 'WP Rocket' automatically adds 'preconnect' to [Google Fonts domain](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) and any CNAME(S) added via the [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) feature."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "To fix this issue for fonts, enable [Remove unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) in 'WP Rocket'. Your site's critical fonts will be preloaded with priority."}, "report/renderer/report-utils.js | calculatorLink": {"message": "See calculator."}, "report/renderer/report-utils.js | collapseView": {"message": "Collapse view"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Initial Navigation"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximum critical path latency:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copy JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Toggle Dark theme"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Print expanded"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Print summary"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Save as gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Save as HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Save as JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Open in Viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "Error!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Report error: no audit information"}, "report/renderer/report-utils.js | expandView": {"message": "Expand view"}, "report/renderer/report-utils.js | footerIssue": {"message": "File an issue"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON>de"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Lab Data"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Additional items to manually check"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Not applicable"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Opportunity"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Estimated Savings"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Passed audits"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Initial page load"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Custom throttling"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emulated desktop"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "No emulation"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe version"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Unthrottled CPU/memory power"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU throttling"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Network throttling"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Screen emulation"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User agent (network)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Single page load"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "This data is taken from a single page load, as opposed to field data summarising many sessions."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Slow 4G throttling"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Unknown"}, "report/renderer/report-utils.js | show": {"message": "Show"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Show audits relevant to:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Collapse snippet"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Expand snippet"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Show 3rd-party resources"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Provided by environment"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "There were issues affecting this run of Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "View original trace"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "View trace"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "View Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Passed audits but with warnings"}, "report/renderer/report-utils.js | warningHeader": {"message": "Warnings: "}, "treemap/app/src/util.js | allLabel": {"message": "All"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "All scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Coverage"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Duplicate modules"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Resource bytes"}, "treemap/app/src/util.js | tableColumnName": {"message": "Name"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Toggle table"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Unused bytes"}}