{"version": 3, "file": "MainThreadActivity.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/extras/MainThreadActivity.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAC7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,MAAM,wBAAwB,GAAG,IAAI,GAAG,CAAC;IACvC,WAAW;IACX,QAAQ;IACR,QAAQ;CACT,CAAC,CAAC;AAEH,MAAM,UAAU,eAAe,CAC3B,WAAiD,EACjD,iBAAmE;IACrE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QAC9B,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9D,IAAI,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;YACtC,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;YAClG,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,IAAI,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxC,OAAO,WAAW,CAAC;IACrB,CAAC;IACD;;;QAGI;IACJ,SAAS,wBAAwB,CAAC,UAAkB,EAAE,SAAiB;QACrE,MAAM,SAAS,GAAG,GAAG,CAAC;QAEtB,IAAI,QAAQ,GAAG,UAAU,CAAC;QAC1B,MAAM,UAAU,GAAG,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QACpE,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;QAC/C,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,QAAQ,GAAG,CAAC,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC;YAC9C,IAAI,QAAQ,GAAG,SAAS,GAAG,QAAQ,EAAE,CAAC;gBACpC,QAAQ,GAAG,CAAC,CAAC;gBACb,OAAO,GAAG,QAAQ,CAAC;gBACnB,QAAQ,GAAG,CAAC,CAAC;YACf,CAAC;YACD,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC;QACnC,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,MAAM,UAAU,GAAG,wBAAwB,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAClF,MAAM,SAAS,GAAG,wBAAwB,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/F,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjG,IAAI,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC;IACrC,IAAI,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;IACrC,MAAM,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;IAE1C,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;QAC3C,yEAAyE;QACzE,wEAAwE;QACxE,gEAAgE;QAChE,qEAAqE;QACrE,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,wEAAwE;IACxE,qEAAqE;IACrE,kEAAkE;IAClE,oCAAoC;IACpC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,GAAG,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IAChG,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IAElG,OAAO;QACL,GAAG,EAAE,QAAQ;QACb,GAAG,EAAE,SAAS;QACd,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ,CAAC;KACvD,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nconst IDLE_FUNCTION_CALL_NAMES = new Set([\n  '(program)',\n  '(idle)',\n  '(root)',\n]);\n\nexport function calculateWindow(\n    traceBounds: Types.Timing.TraceWindowMicroSeconds,\n    mainThreadEntries: readonly Types.TraceEvents.SyntheticTraceEntry[]): Types.Timing.TraceWindowMicroSeconds {\n  if (!mainThreadEntries.length) {\n    return traceBounds;\n  }\n  const entriesWithIdleRemoved = mainThreadEntries.filter(entry => {\n    if (Types.TraceEvents.isProfileCall(entry) &&\n        (IDLE_FUNCTION_CALL_NAMES.has(entry.callFrame.functionName) || !entry.callFrame.functionName)) {\n      return false;\n    }\n    return true;\n  });\n\n  if (entriesWithIdleRemoved.length === 0) {\n    return traceBounds;\n  }\n  /**\n   * Calculates regions of low utilization and returns the index of the event\n   * that is the first event that should be included.\n   **/\n  function findLowUtilizationRegion(startIndex: number, stopIndex: number): number {\n    const threshold = 0.1;\n\n    let cutIndex = startIndex;\n    const entryAtCut = entriesWithIdleRemoved[cutIndex];\n    const timings = Helpers.Timing.eventTimingsMicroSeconds(entryAtCut);\n    let cutTime = (timings.startTime + timings.endTime) / 2;\n    let usedTime = 0;\n    const step = Math.sign(stopIndex - startIndex);\n    for (let i = startIndex; i !== stopIndex; i += step) {\n      const task = entriesWithIdleRemoved[i];\n      const taskTimings = Helpers.Timing.eventTimingsMicroSeconds(task);\n      const taskTime = (taskTimings.startTime + taskTimings.endTime) / 2;\n      const interval = Math.abs(cutTime - taskTime);\n      if (usedTime < threshold * interval) {\n        cutIndex = i;\n        cutTime = taskTime;\n        usedTime = 0;\n      }\n      usedTime += taskTimings.duration;\n    }\n    return cutIndex;\n  }\n  const rightIndex = findLowUtilizationRegion(entriesWithIdleRemoved.length - 1, 0);\n  const leftIndex = findLowUtilizationRegion(0, rightIndex);\n  const leftTimings = Helpers.Timing.eventTimingsMicroSeconds(entriesWithIdleRemoved[leftIndex]);\n  const rightTimings = Helpers.Timing.eventTimingsMicroSeconds(entriesWithIdleRemoved[rightIndex]);\n\n  let leftTime = leftTimings.startTime;\n  let rightTime = rightTimings.endTime;\n  const zoomedInSpan = rightTime - leftTime;\n\n  if (zoomedInSpan < traceBounds.range * 0.1) {\n    // If the area we have chosen to zoom into is less than 10% of the entire\n    // span, we bail and show the entire trace. It would not be so useful to\n    // the user to zoom in on such a small area; we assume they have\n    // purposefully recorded a trace that contains empty periods of time.\n    return traceBounds;\n  }\n\n  // Adjust the left time down by 5%, and the right time up by 5%, so that\n  // we give the range we want to zoom a bit of breathing space. At the\n  // same time, ensure that we do not stray beyond the bounds of the\n  // min/max time of the entire trace.\n  leftTime = Types.Timing.MicroSeconds(Math.max(leftTime - 0.05 * zoomedInSpan, traceBounds.min));\n  rightTime = Types.Timing.MicroSeconds(Math.min(rightTime + 0.05 * zoomedInSpan, traceBounds.max));\n\n  return {\n    min: leftTime,\n    max: rightTime,\n    range: Types.Timing.MicroSeconds(rightTime - leftTime),\n  };\n}\n"]}