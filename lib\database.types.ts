export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      audit_logs: {
        Row: {
          action: string
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          record_id: string | null
          table_name: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_conversations: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          session_id: string | null
          title: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          session_id?: string | null
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          session_id?: string | null
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_conversations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string | null
          id: string
          response_time_ms: number | null
          role: string
          sources: Json | null
          tokens_used: number | null
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string | null
          id?: string
          response_time_ms?: number | null
          role: string
          sources?: Json | null
          tokens_used?: number | null
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string | null
          id?: string
          response_time_ms?: number | null
          role?: string
          sources?: Json | null
          tokens_used?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      citizen_procedures: {
        Row: {
          actual_completion: string | null
          assigned_to: string | null
          attachments: string[] | null
          citizen_id: string
          created_at: string | null
          estimated_completion: string | null
          id: string
          notes: string | null
          priority: number | null
          procedure_id: string
          reference_number: string
          status_id: string
          submitted_data: Json | null
          updated_at: string | null
        }
        Insert: {
          actual_completion?: string | null
          assigned_to?: string | null
          attachments?: string[] | null
          citizen_id: string
          created_at?: string | null
          estimated_completion?: string | null
          id?: string
          notes?: string | null
          priority?: number | null
          procedure_id: string
          reference_number: string
          status_id: string
          submitted_data?: Json | null
          updated_at?: string | null
        }
        Update: {
          actual_completion?: string | null
          assigned_to?: string | null
          attachments?: string[] | null
          citizen_id?: string
          created_at?: string | null
          estimated_completion?: string | null
          id?: string
          notes?: string | null
          priority?: number | null
          procedure_id?: string
          reference_number?: string
          status_id?: string
          submitted_data?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "citizen_procedures_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "citizen_procedures_citizen_id_fkey"
            columns: ["citizen_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "citizen_procedures_procedure_id_fkey"
            columns: ["procedure_id"]
            isOneToOne: false
            referencedRelation: "procedures"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "citizen_procedures_status_id_fkey"
            columns: ["status_id"]
            isOneToOne: false
            referencedRelation: "procedure_statuses"
            referencedColumns: ["id"]
          },
        ]
      }
      dependencies: {
        Row: {
          acronym: string | null
          address: string | null
          code: string
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          acronym?: string | null
          address?: string | null
          code: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          acronym?: string | null
          address?: string | null
          code?: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dependencies_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "dependencies"
            referencedColumns: ["id"]
          },
        ]
      }
      knowledge_base: {
        Row: {
          content: string
          created_at: string | null
          embedding: string | null
          id: string
          is_active: boolean | null
          metadata: Json | null
          source_id: string | null
          source_type: string
          tags: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          embedding?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          embedding?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          category: string | null
          created_at: string | null
          id: string
          is_read: boolean | null
          message: string
          related_id: string | null
          sent_at: string | null
          title: string
          type: string | null
          user_id: string
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message: string
          related_id?: string | null
          sent_at?: string | null
          title: string
          type?: string | null
          user_id: string
        }
        Update: {
          category?: string | null
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string
          related_id?: string | null
          sent_at?: string | null
          title?: string
          type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      opas: {
        Row: {
          code: string
          created_at: string | null
          dependency_id: string
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          subdependency_id: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          dependency_id: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          subdependency_id?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          dependency_id?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          subdependency_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "opas_dependency_id_fkey"
            columns: ["dependency_id"]
            isOneToOne: false
            referencedRelation: "dependencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "opas_subdependency_id_fkey"
            columns: ["subdependency_id"]
            isOneToOne: false
            referencedRelation: "subdependencies"
            referencedColumns: ["id"]
          },
        ]
      }
      procedure_statuses: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          display_name: string
          id: string
          is_final: boolean | null
          name: string
          order_index: number | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          display_name: string
          id?: string
          is_final?: boolean | null
          name: string
          order_index?: number | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          display_name?: string
          id?: string
          is_final?: boolean | null
          name?: string
          order_index?: number | null
        }
        Relationships: []
      }
      procedures: {
        Row: {
          category: string | null
          contact_info: Json | null
          cost: number | null
          created_at: string | null
          dependency_id: string | null
          description: string | null
          difficulty_level: number | null
          documents_required: string[] | null
          id: string
          is_active: boolean | null
          legal_framework: string | null
          name: string
          online_available: boolean | null
          popularity_score: number | null
          procedure_type: string | null
          process_steps: string[] | null
          requirements: string[] | null
          response_time: string | null
          subdependency_id: string | null
          tags: string[] | null
          updated_at: string | null
        }
        Insert: {
          category?: string | null
          contact_info?: Json | null
          cost?: number | null
          created_at?: string | null
          dependency_id?: string | null
          description?: string | null
          difficulty_level?: number | null
          documents_required?: string[] | null
          id?: string
          is_active?: boolean | null
          legal_framework?: string | null
          name: string
          online_available?: boolean | null
          popularity_score?: number | null
          procedure_type?: string | null
          process_steps?: string[] | null
          requirements?: string[] | null
          response_time?: string | null
          subdependency_id?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Update: {
          category?: string | null
          contact_info?: Json | null
          cost?: number | null
          created_at?: string | null
          dependency_id?: string | null
          description?: string | null
          difficulty_level?: number | null
          documents_required?: string[] | null
          id?: string
          is_active?: boolean | null
          legal_framework?: string | null
          name?: string
          online_available?: boolean | null
          popularity_score?: number | null
          procedure_type?: string | null
          process_steps?: string[] | null
          requirements?: string[] | null
          response_time?: string | null
          subdependency_id?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "procedures_dependency_id_fkey"
            columns: ["dependency_id"]
            isOneToOne: false
            referencedRelation: "dependencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "procedures_subdependency_id_fkey"
            columns: ["subdependency_id"]
            isOneToOne: false
            referencedRelation: "subdependencies"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          city: string | null
          created_at: string | null
          department: string | null
          dependency_id: string | null
          document_number: string | null
          document_type: string | null
          email: string
          email_verified: boolean | null
          full_name: string | null
          id: string
          is_active: boolean | null
          last_login: string | null
          phone: string | null
          phone_verified: boolean | null
          preferences: Json | null
          role_id: string
          subdependency_id: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          created_at?: string | null
          department?: string | null
          dependency_id?: string | null
          document_number?: string | null
          document_type?: string | null
          email: string
          email_verified?: boolean | null
          full_name?: string | null
          id: string
          is_active?: boolean | null
          last_login?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          preferences?: Json | null
          role_id: string
          subdependency_id?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          created_at?: string | null
          department?: string | null
          dependency_id?: string | null
          document_number?: string | null
          document_type?: string | null
          email?: string
          email_verified?: boolean | null
          full_name?: string | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          preferences?: Json | null
          role_id?: string
          subdependency_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_dependency_id_fkey"
            columns: ["dependency_id"]
            isOneToOne: false
            referencedRelation: "dependencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_subdependency_id_fkey"
            columns: ["subdependency_id"]
            isOneToOne: false
            referencedRelation: "subdependencies"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          description: string | null
          display_name: string
          id: string
          is_active: boolean | null
          name: string
          permissions: Json | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_name: string
          id?: string
          is_active?: boolean | null
          name: string
          permissions?: Json | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_name?: string
          id?: string
          is_active?: boolean | null
          name?: string
          permissions?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subdependencies: {
        Row: {
          acronym: string | null
          code: string
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          dependency_id: string
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          acronym?: string | null
          code: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          dependency_id: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          acronym?: string | null
          code?: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          dependency_id?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subdependencies_dependency_id_fkey"
            columns: ["dependency_id"]
            isOneToOne: false
            referencedRelation: "dependencies"
            referencedColumns: ["id"]
          },
        ]
      }
      faq_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          icon: string | null
          color: string | null
          display_order: number | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          icon?: string | null
          color?: string | null
          display_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          icon?: string | null
          color?: string | null
          display_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      faqs: {
        Row: {
          id: string
          question: string
          answer: string
          category_id: string | null
          tags: string[] | null
          related_procedures: string[] | null
          popularity: number | null
          view_count: number | null
          helpful_votes: number | null
          unhelpful_votes: number | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          question: string
          answer: string
          category_id?: string | null
          tags?: string[] | null
          related_procedures?: string[] | null
          popularity?: number | null
          view_count?: number | null
          helpful_votes?: number | null
          unhelpful_votes?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          question?: string
          answer?: string
          category_id?: string | null
          tags?: string[] | null
          related_procedures?: string[] | null
          popularity?: number | null
          view_count?: number | null
          helpful_votes?: number | null
          unhelpful_votes?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "faqs_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "faq_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      faq_analytics: {
        Row: {
          id: string
          session_id: string | null
          event_type: string
          faq_id: string | null
          category_id: string | null
          search_query: string | null
          results_count: number | null
          response_time: number | null
          context: string | null
          user_agent: string | null
          ip_address: unknown | null
          metadata: Json | null
          created_at: string | null
        }
        Insert: {
          id?: string
          session_id?: string | null
          event_type: string
          faq_id?: string | null
          category_id?: string | null
          search_query?: string | null
          results_count?: number | null
          response_time?: number | null
          context?: string | null
          user_agent?: string | null
          ip_address?: unknown | null
          metadata?: Json | null
          created_at?: string | null
        }
        Update: {
          id?: string
          session_id?: string | null
          event_type?: string
          faq_id?: string | null
          category_id?: string | null
          search_query?: string | null
          results_count?: number | null
          response_time?: number | null
          context?: string | null
          user_agent?: string | null
          ip_address?: unknown | null
          metadata?: Json | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "faq_analytics_faq_id_fkey"
            columns: ["faq_id"]
            isOneToOne: false
            referencedRelation: "faqs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "faq_analytics_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "faq_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      faq_search_history: {
        Row: {
          id: string
          session_id: string | null
          search_query: string
          results_count: number | null
          response_time: number | null
          context: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          session_id?: string | null
          search_query: string
          results_count?: number | null
          response_time?: number | null
          context?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          session_id?: string | null
          search_query?: string
          results_count?: number | null
          response_time?: number | null
          context?: string | null
          created_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      all_procedures: {
        Row: {
          category: string | null
          contact_info: Json | null
          cost: number | null
          created_at: string | null
          dependency_code: string | null
          dependency_name: string | null
          description: string | null
          documents_required: string[] | null
          id: string | null
          is_active: boolean | null
          legal_framework: string | null
          name: string | null
          online_available: boolean | null
          process_steps: string[] | null
          requirements: string[] | null
          response_time: string | null
          subdependency_code: string | null
          subdependency_name: string | null
          type: string | null
          updated_at: string | null
        }
        Relationships: []
      }
      vista_procedimientos_completa: {
        Row: {
          categoria: string | null
          costo: string | null
          dependencia: string | null
          descripcion: string | null
          estado: string | null
          fecha_creacion: string | null
          modalidad: string | null
          nombre: string | null
          subdependencia: string | null
          tiempo_respuesta: string | null
          tipo_procedimiento: string | null
        }
        Relationships: []
      }
    }
    Enums: {
      document_type: "cedula" | "pasaporte" | "cedula_extranjeria" | "nit"
      procedure_status: "draft" | "active" | "inactive" | "archived"
      tramite_status:
        | "pending"
        | "in_progress"
        | "completed"
        | "rejected"
        | "cancelled"
      user_role: "ciudadano" | "admin" | "super_admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      document_type: ["cedula", "pasaporte", "cedula_extranjeria", "nit"],
      procedure_status: ["draft", "active", "inactive", "archived"],
      tramite_status: [
        "pending",
        "in_progress",
        "completed",
        "rejected",
        "cancelled",
      ],
      user_role: ["ciudadano", "admin", "super_admin"],
    },
  },
} as const
