import colors from 'colors';
import type { AxeResults, UnlabelledFrameSelector } from 'axe-core';
export declare const CHROME_TEST_PATH: string | undefined, CHROMEDRIVER_TEST_PATH: string | undefined;
export declare const saveOutcome: (outcome: AxeResults | AxeResults[], fileName?: string, dir?: string) => string;
export declare const parseUrl: (url: string) => string;
export declare const parseBrowser: (browser?: string) => string | Error;
export declare const getAxeSource: (axePath?: string, dirname?: string) => string | void;
export declare const getAxeVersion: (source: string) => string;
export declare const splitList: (val: string) => string[];
export declare const selectorToString: (selectors: UnlabelledFrameSelector, separator?: string) => string;
export declare const reporter: (noReporter: boolean, silentMode: boolean) => (() => void);
export declare const link: colors.Color;
export declare const error: colors.Color;
export declare const bold: colors.Color;
export declare const green: colors.Color;
export declare const italics: colors.Color;
