{"version": 3, "file": "FilmStrip.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/extras/FilmStrip.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAC7B,EAAE;AACF,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAyB/D,8BAA8B;AAC9B,kCAAkC;AAClC,qBAAqB;AACrB,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoE,CAAC;AAEnG,MAAM,UAAU,aAAa,CAAC,SAAqC,EAAE,cAA0C;IAC7G,MAAM,MAAM,GAAY,EAAE,CAAC;IAE3B,MAAM,QAAQ,GAAG,OAAO,cAAc,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IACzG,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAClD,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/D,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,MAAM,eAAe,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,eAAe,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;YAClC,SAAS;QACX,CAAC;QACD,MAAM,KAAK,GAAU;YACnB,KAAK,EAAE,MAAM,CAAC,MAAM;YACpB,eAAe,EAAE,eAAe;SACjC,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,MAAM,MAAM,GAAS;QACnB,QAAQ;QACR,QAAQ;QACR,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;KAC3B,CAAC;IAEF,MAAM,aAAa,GACf,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAmC,CAAC,CAAC;IACtH,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEpC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,SAAe,EAAE,eAA0C;IACjG,MAAM,gCAAgC,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAChF,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,CAAC;IAC3E,IAAI,gCAAgC,KAAK,IAAI,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,SAAS,CAAC,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n//\nimport * as Platform from '../../../core/platform/platform.js';\nimport type * as Handlers from '../handlers/handlers.js';\nimport type * as Types from '../types/types.js';\n\nexport interface Data {\n  zeroTime: Types.Timing.MicroSeconds;\n  spanTime: Types.Timing.MicroSeconds;\n  frames: readonly Frame[];\n}\n\nexport interface Frame {\n  screenshotEvent: Types.TraceEvents.SyntheticScreenshot;\n  index: number;\n}\n\nexport type HandlersWithFilmStrip = Handlers.Types.HandlersWithMeta<{\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  Screenshots: typeof Handlers.ModelHandlers.Screenshots,\n}>;\n\nexport type HandlerDataWithScreenshots = Handlers.Types.EnabledHandlerDataWithMeta<{\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  Screenshots: typeof Handlers.ModelHandlers.Screenshots,\n}>;\n\n// Cache film strips based on:\n// 1. The trace parsed data object\n// 2. The start time.\nconst filmStripCache = new Map<HandlerDataWithScreenshots, Map<Types.Timing.MicroSeconds, Data>>();\n\nexport function fromTraceData(traceData: HandlerDataWithScreenshots, customZeroTime?: Types.Timing.MicroSeconds): Data {\n  const frames: Frame[] = [];\n\n  const zeroTime = typeof customZeroTime !== 'undefined' ? customZeroTime : traceData.Meta.traceBounds.min;\n  const spanTime = traceData.Meta.traceBounds.range;\n  const fromCache = filmStripCache.get(traceData)?.get(zeroTime);\n  if (fromCache) {\n    return fromCache;\n  }\n\n  for (const screenshotEvent of traceData.Screenshots) {\n    if (screenshotEvent.ts < zeroTime) {\n      continue;\n    }\n    const frame: Frame = {\n      index: frames.length,\n      screenshotEvent: screenshotEvent,\n    };\n    frames.push(frame);\n  }\n\n  const result: Data = {\n    zeroTime,\n    spanTime,\n    frames: Array.from(frames),\n  };\n\n  const cachedForData =\n      Platform.MapUtilities.getWithDefault(filmStripCache, traceData, () => new Map<Types.Timing.MicroSeconds, Data>());\n  cachedForData.set(zeroTime, result);\n\n  return result;\n}\n\nexport function frameClosestToTimestamp(filmStrip: Data, searchTimestamp: Types.Timing.MicroSeconds): Frame|null {\n  const closestFrameIndexBeforeTimestamp = Platform.ArrayUtilities.nearestIndexFromEnd(\n      filmStrip.frames, frame => frame.screenshotEvent.ts < searchTimestamp);\n  if (closestFrameIndexBeforeTimestamp === null) {\n    return null;\n  }\n  return filmStrip.frames[closestFrameIndexBeforeTimestamp];\n}\n"]}