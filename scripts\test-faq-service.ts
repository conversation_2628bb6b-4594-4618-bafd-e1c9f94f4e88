/**
 * Script de prueba para verificar la conexión del FAQ Service con Supabase
 * Ejecutar con: npx tsx scripts/test-faq-service.ts
 */

import { createClient } from '@supabase/supabase-js'
import type { Database } from '../lib/database.types'

// Configurar cliente de Supabase directamente para el script
const supabaseUrl = 'https://zeieudvbhlrlnfkwejoh.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDk1MDEsImV4cCI6MjA2Njg4NTUwMX0.sOImH-XXxxVjjUZhWwYt6KK6dpfCBK2wvT2rnPmlC50'

const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

async function testFAQDatabase() {
  console.log('🧪 Iniciando pruebas de la base de datos FAQ...\n')

  try {
    // Test 1: Obtener categorías directamente
    console.log('📂 Test 1: Obtener categorías desde Supabase')
    const { data: categories, error: categoriesError } = await supabase
      .from('faq_categories')
      .select('*')
      .eq('is_active', true)
      .order('display_order')

    if (categoriesError) {
      console.error('❌ Error obteniendo categorías:', categoriesError)
      return
    }

    console.log(`✅ Categorías obtenidas: ${categories?.length || 0}`)
    categories?.forEach(cat => {
      console.log(`   - ${cat.name} (ID: ${cat.id})`)
    })
    console.log()

    // Test 2: Obtener FAQs directamente
    console.log('⭐ Test 2: Obtener FAQs desde Supabase')
    const { data: faqs, error: faqsError } = await supabase
      .from('faqs')
      .select(`
        *,
        faq_categories!inner(name)
      `)
      .eq('is_active', true)
      .order('popularity', { ascending: false })
      .limit(5)

    if (faqsError) {
      console.error('❌ Error obteniendo FAQs:', faqsError)
      return
    }

    console.log(`✅ FAQs obtenidas: ${faqs?.length || 0}`)
    faqs?.forEach(faq => {
      console.log(`   - ${faq.question} (Popularidad: ${faq.popularity})`)
      console.log(`     Categoría: ${faq.faq_categories?.name}`)
    })
    console.log()

    // Test 3: Buscar FAQs
    console.log('🔍 Test 3: Buscar FAQs con "impuesto"')
    const { data: searchResults, error: searchError } = await supabase
      .from('faqs')
      .select(`
        *,
        faq_categories!inner(name)
      `)
      .eq('is_active', true)
      .or('question.ilike.%impuesto%,answer.ilike.%impuesto%')
      .order('popularity', { ascending: false })

    if (searchError) {
      console.error('❌ Error en búsqueda:', searchError)
      return
    }

    console.log(`✅ Resultados de búsqueda: ${searchResults?.length || 0}`)
    searchResults?.forEach(faq => {
      console.log(`   - ${faq.question}`)
      console.log(`     Categoría: ${faq.faq_categories?.name}`)
    })
    console.log()

    // Test 4: Contar FAQs por categoría
    console.log('📊 Test 4: Contar FAQs por categoría')
    const { data: faqCounts, error: countError } = await supabase
      .from('faqs')
      .select('category_id')
      .eq('is_active', true)

    if (countError) {
      console.error('❌ Error contando FAQs:', countError)
      return
    }

    const countMap = new Map<string, number>()
    faqCounts?.forEach(faq => {
      if (faq.category_id) {
        countMap.set(faq.category_id, (countMap.get(faq.category_id) || 0) + 1)
      }
    })

    console.log('✅ Conteo por categoría:')
    categories?.forEach(cat => {
      const count = countMap.get(cat.id) || 0
      console.log(`   - ${cat.name}: ${count} FAQs`)
    })
    console.log()

    console.log('🎉 Todas las pruebas de base de datos completadas exitosamente!')

  } catch (error) {
    console.error('❌ Error durante las pruebas:', error)
  }
}

// Ejecutar las pruebas
testFAQDatabase()
