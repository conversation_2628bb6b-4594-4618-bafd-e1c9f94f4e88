{"version": 3, "file": "JSHandle.js", "sourceRoot": "", "sources": ["../../../../src/common/JSHandle.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAIH,oDAA4C;AAC5C,iDAAyC;AAMzC,uCAA+E;AAI/E;;GAEG;AACH,MAAa,WAAyB,SAAQ,sBAAW;IAUvD,IAAa,QAAQ;QACnB,OAAO,uBAAA,IAAI,6BAAU,CAAC;IACxB,CAAC;IAED,YACE,OAAyB,EACzB,YAA2C;QAE3C,KAAK,EAAE,CAAC;QAZV,gCAAY,KAAK,EAAC;QAClB,uCAA2B;QAC3B,4CAA6C;QAW3C,uBAAA,IAAI,wBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,6BAAiB,YAAY,MAAA,CAAC;IACpC,CAAC;IAEQ,gBAAgB;QACvB,OAAO,uBAAA,IAAI,4BAAS,CAAC;IACvB,CAAC;IAED,IAAa,MAAM;QACjB,OAAO,uBAAA,IAAI,4BAAS,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,QAAQ,CAIrB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,cAAc,CAI3B,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,cAAc,CACjD,YAAY,EACZ,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;IACJ,CAAC;IAMQ,KAAK,CAAC,WAAW,CACxB,YAAyB;QAEzB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;YAClD,OAAO,MAAM,CAAC,YAAiB,CAAC,CAAC;QACnC,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,IAAA,kBAAM,EAAC,uBAAA,IAAI,iCAAc,CAAC,QAAQ,CAAC,CAAC;QACpC,0EAA0E;QAC1E,wDAAwD;QACxD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/D,QAAQ,EAAE,uBAAA,IAAI,iCAAc,CAAC,QAAQ;YACrC,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;QAC3C,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC3C,SAAS;aACV;YACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAA,wBAAc,EAAC,uBAAA,IAAI,4BAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEQ,KAAK,CAAC,SAAS;QACtB,IAAI,CAAC,uBAAA,IAAI,iCAAc,CAAC,QAAQ,EAAE;YAChC,OAAO,IAAA,+BAAqB,EAAC,uBAAA,IAAI,iCAAc,CAAC,CAAC;SAClD;QACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACM,SAAS;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,IAAI,uBAAA,IAAI,6BAAU,EAAE;YAClB,OAAO;SACR;QACD,uBAAA,IAAI,yBAAa,IAAI,MAAA,CAAC;QACtB,MAAM,IAAA,uBAAa,EAAC,IAAI,CAAC,MAAM,EAAE,uBAAA,IAAI,iCAAc,CAAC,CAAC;IACvD,CAAC;IAEQ,QAAQ;QACf,IAAI,CAAC,uBAAA,IAAI,iCAAc,CAAC,QAAQ,EAAE;YAChC,OAAO,WAAW,GAAG,IAAA,+BAAqB,EAAC,uBAAA,IAAI,iCAAc,CAAC,CAAC;SAChE;QACD,MAAM,IAAI,GAAG,uBAAA,IAAI,iCAAc,CAAC,OAAO,IAAI,uBAAA,IAAI,iCAAc,CAAC,IAAI,CAAC;QACnE,OAAO,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,IAAa,EAAE;QACb,OAAO,uBAAA,IAAI,iCAAc,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEQ,YAAY;QACnB,OAAO,uBAAA,IAAI,iCAAc,CAAC;IAC5B,CAAC;CACF;AAvID,kCAuIC"}