{"version": 3, "file": "ElementHandle.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/ElementHandle.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,MAAM,EACN,KAAK,EACL,YAAY,EACb,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAO,iBAAiB,EAAC,MAAM,gBAAgB,CAAC;AAIvD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAGjC,OAAO,EAAC,sBAAsB,EAAC,MAAM,oBAAoB,CAAC;AAE1D,OAAO,EAAC,WAAW,EAAC,MAAM,eAAe,CAAC;AAG1C,OAAO,EAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAC,MAAM,YAAY,CAAC;AAC5E,OAAO,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAa/C;;;;;;GAMG;AACH,qBAAa,gBAAgB,CAC3B,WAAW,SAAS,IAAI,GAAG,OAAO,CAClC,SAAQ,aAAa,CAAC,WAAW,CAAC;;IAE1B,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;gBAGvC,OAAO,EAAE,gBAAgB,EACzB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,EAC3C,KAAK,EAAE,KAAK;IAMd;;OAEG;IACM,gBAAgB,IAAI,gBAAgB;IAI7C;;OAEG;IACH,IAAa,MAAM,IAAI,UAAU,CAEhC;IAEQ,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY;IAYtD,IAAa,KAAK,IAAI,KAAK,CAE1B;IAEc,CAAC,CAAC,QAAQ,SAAS,MAAM,EACtC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IASvC,EAAE,CAAC,QAAQ,SAAS,MAAM,EACvC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAQvC,KAAK,CAClB,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACzE,OAAO,CAAC,QAAQ,CAAC,EACjB,MAAM,CACP,EAED,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAYtB,MAAM,CACnB,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAC3B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EACxB,MAAM,CACP,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAEtD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAetB,EAAE,CACf,UAAU,EAAE,MAAM,GACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IAO1B,eAAe,CAAC,QAAQ,SAAS,MAAM,EACpD,QAAQ,EAAE,QAAQ,EAClB,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAUvC,YAAY,CACzB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;KACb,GACL,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAyB1B,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAI7B,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;IAI5B,SAAS,CACtB,CAAC,SAAS,MAAM,qBAAqB,GAAG,MAAM,oBAAoB,EAClE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAUjC,YAAY,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAUrC,cAAc,CAC3B,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IAkED,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IAoG9D;;;;OAIG;IACY,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMpE;;;;OAIG;IACY,KAAK,CAClB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GACnC,OAAO,CAAC,IAAI,CAAC;IAMhB;;OAEG;IACY,IAAI,CACjB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,MAAM,EAAE,KAAK,GACZ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAUpB,SAAS,CACtB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,QAAQ,CACrB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,IAAI,CACjB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,WAAW,CACxB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAC9B,OAAO,CAAC,EAAE;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,GACxB,OAAO,CAAC,IAAI,CAAC;IAOD,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IA4C9C,UAAU,CACvB,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,EACxC,GAAG,SAAS,EAAE,MAAM,EAAE,GACrB,OAAO,CAAC,IAAI,CAAC;IAuDD,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAOnD,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAM1D,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMzD,QAAQ,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAKxD,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAStB,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAK5D,KAAK,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;IAK3D,WAAW,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAiB1C,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAoCpC,UAAU,CACvB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,OAAO,GAAE,iBAAsB,GAC9B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;CAsD5B"}