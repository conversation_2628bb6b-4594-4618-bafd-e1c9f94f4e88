{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/common/ExecutionContext.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAOH,uEAA+D;AAC/D,qDAAsD;AAEtD,+DAAuD;AACvD,6CAAqC;AAErC,yDAAoD;AAEpD,+CAA0C;AAC1C,6CAAqC;AACrC,2DAAmD;AAEnD,uCAKmB;AAEnB;;GAEG;AACU,QAAA,qBAAqB,GAAG,wCAAwC,CAAC;AAC9E,MAAM,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,gBAAgB;IAM3B,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAqB;;QAUvB,8CAAqB,KAAK,EAAC;QAC3B,kDAAkD;QAThD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC;QACpC,IAAI,cAAc,CAAC,IAAI,EAAE;YACvB,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;SACzC;IACH,CAAC;IAID,IAAI,aAAa;QACf,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACpD,IAAI,CAAC,uBAAA,IAAI,2CAAmB,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBACpB,uBAAA,IAAI,2EAAsB,MAA1B,IAAI,EACF,IAAI,oBAAO,CACT,qBAAqB,EACrB,sCAAgB,CAAC,QAA2C,CAC7D,CACF;gBACD,uBAAA,IAAI,2EAAsB,MAA1B,IAAI,EACF,IAAI,oBAAO,CAAC,wBAAwB,EAAE,CAAC,KAAK,EAC1C,OAA4B,EAC5B,QAAgB,EACW,EAAE;oBAC7B,MAAM,OAAO,GAAG,sCAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAC7D,OAAO,OAAO,CAAC,gBAAgB,EAAE,CAAC,cAAc,CAAC,CAAC,GAAG,QAAQ,EAAE,EAAE;wBAC/D,OAAO,QAAQ,CAAC;oBAClB,CAAC,EAAE,GAAG,CAAC,MAAM,wCAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAoC,CAAC,CACvC;aACF,CAAC,CAAC;YACH,uBAAA,IAAI,uCAAsB,IAAI,MAAA,CAAC;SAChC;QACD,kCAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,uBAAA,IAAI,uCAAe,EAAE;gBACvB,uBAAA,IAAI,uCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAChC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,CAAC,CAAC,CAAC;aACJ;YACD,uBAAA,IAAI,mCAAkB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAqC,CAAC;YACzE,CAAC,CAAC,MAAA,CAAC;QACL,CAAC,EAAE,CAAC,uBAAA,IAAI,uCAAe,CAAC,CAAC;QACzB,OAAO,uBAAA,IAAI,uCAAmD,CAAC;IACjE,CAAC;IAeD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,uBAAA,IAAI,+DAAU,MAAd,IAAI,EAAW,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,uBAAA,IAAI,+DAAU,MAAd,IAAI,EAAW,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;CAwIF;AA1TD,4CA0TC;4LAhQC,KAAK,iDAAuB,OAAgB;IAC1C,IAAI;QACF,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5D;KACF;IAAC,MAAM;QACN,0EAA0E;QAC1E,uEAAuE;QACvE,gCAAgC;KACjC;AACH,CAAC,+BA+HD,KAAK,qCAIH,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;IAEf,MAAM,MAAM,GAAG,iBAAiB,6BAAqB,EAAE,CAAC;IAExD,IAAI,IAAA,kBAAQ,EAAC,YAAY,CAAC,EAAE;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,UAAU,GAAG,YAAY,CAAC;QAChC,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;YAC/D,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,GAAG,IAAI,GAAG,MAAM,CAAC;QAE/B,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO;aAChE,IAAI,CAAC,kBAAkB,EAAE;YACxB,UAAU,EAAE,uBAAuB;YACnC,SAAS;YACT,aAAa;YACb,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;SAClB,CAAC;aACD,KAAK,CAAC,YAAY,CAAC,CAAC;QAEvB,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,IAAA,6BAAmB,EAAC,gBAAgB,CAAC,CAC9D,CAAC;SACH;QAED,OAAO,aAAa;YAClB,CAAC,CAAC,IAAA,+BAAqB,EAAC,YAAY,CAAC;YACrC,CAAC,CAAC,IAAA,wBAAc,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACxC;IAED,IAAI,qBAAqB,CAAC;IAC1B,IAAI;QACF,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAClE,mBAAmB,EAAE,GAAG,IAAA,+BAAiB,EAAC,YAAY,CAAC,KAAK,MAAM,IAAI;YACtE,kBAAkB,EAAE,IAAI,CAAC,UAAU;YACnC,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClE,aAAa;YACb,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;KACJ;IAAC,OAAO,KAAK,EAAE;QACd,IACE,KAAK,YAAY,SAAS;YAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE;YACA,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;SACxD;QACD,MAAM,KAAK,CAAC;KACb;IACD,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAC5C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAClD,IAAI,gBAAgB,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,IAAA,6BAAmB,EAAC,gBAAgB,CAAC,CAC9D,CAAC;KACH;IACD,OAAO,aAAa;QAClB,CAAC,CAAC,IAAA,+BAAqB,EAAC,YAAY,CAAC;QACrC,CAAC,CAAC,IAAA,wBAAc,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAEvC,KAAK,UAAU,eAAe,CAE5B,GAAY;QAEZ,IAAI,GAAG,YAAY,oBAAO,EAAE;YAC1B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC3B;QACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,mCAAmC;YACnC,OAAO,EAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAC,CAAC;SACpD;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,EAAC,mBAAmB,EAAE,IAAI,EAAC,CAAC;SACpC;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;YAC5B,OAAO,EAAC,mBAAmB,EAAE,UAAU,EAAC,CAAC;SAC1C;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,EAAC,mBAAmB,EAAE,WAAW,EAAC,CAAC;SAC3C;QACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YACvB,OAAO,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC;SACrC;QACD,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,yBAAW,IAAI,GAAG,YAAY,mCAAgB,CAAC;YACpE,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,YAAY,EAAE;YAChB,IAAI,YAAY,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;gBAC5C,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;aACH;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE;gBACnD,OAAO;oBACL,mBAAmB,EACjB,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB;iBAClD,CAAC;aACH;YACD,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE;gBACzC,OAAO,EAAC,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,KAAK,EAAC,CAAC;aACnD;YACD,OAAO,EAAC,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAC,CAAC;SACzD;QACD,OAAO,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;IACtB,CAAC;AACH,CAAC;AAGH,MAAM,YAAY,GAAG,CAAC,KAAY,EAAqC,EAAE;IACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;QAChE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAAE;QAClE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IAED,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAC9D;QACA,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;KACH;IACD,MAAM,KAAK,CAAC;AACd,CAAC,CAAC"}