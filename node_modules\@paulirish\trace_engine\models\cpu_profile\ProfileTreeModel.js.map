{"version": 3, "file": "ProfileTreeModel.js", "sourceRoot": "", "sources": ["../../../../../../front_end/models/cpu_profile/ProfileTreeModel.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAK7B,MAAM,OAAO,WAAW;IACtB,SAAS,CAA6B;IACtC,OAAO,CAAS;IAChB,IAAI,CAAS;IACb,KAAK,CAAS;IACd,EAAE,CAAS;IACX,MAAM,CAAmB;IACzB,QAAQ,CAAgB;IACxB,YAAY,CAAS;IACrB,KAAK,CAAU;IACf,WAAW,CAAe;IAC1B,YAAY,SAAqC;QAC/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,GAAG,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;QACnH,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAA8B,CAAC;IACtE,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,SAAS,CAAC,GAAsC,CAAC;IAC/D,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED,eAAe,CAAC,IAAiB;QAC/B,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;CACF;AAED,MAAM,OAAO,gBAAgB;IAC3B,IAAI,CAAe;IACnB,KAAK,CAAU;IACf,QAAQ,CAAU;IAClB;IACA,CAAC;IAED,UAAU,CAAC,IAAiB;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAEO,sBAAsB;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,4DAA4D;QAC5D,+BAA+B;QAC/B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAI,eAAe,CAAC,GAAG,EAAkB,CAAC;YACtD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACxB,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC7B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;gBACtB,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,IAAiB;QACvC,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,GAAI,eAAe,CAAC,GAAG,EAAkB,CAAC;YACpD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,GAAI,OAAO,CAAC,GAAG,EAAkB,CAAC;YAC5C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;YAClC,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["// Copyright 2016 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport type * as Protocol from '../../generated/protocol.js';\nimport type * as Platform from '../../core/platform/platform.js';\n\nexport class ProfileNode {\n  callFrame: Protocol.Runtime.CallFrame;\n  callUID: string;\n  self: number;\n  total: number;\n  id: number;\n  parent: ProfileNode|null;\n  children: ProfileNode[];\n  functionName: string;\n  depth!: number;\n  deoptReason!: string|null;\n  constructor(callFrame: Protocol.Runtime.CallFrame) {\n    this.callFrame = callFrame;\n    this.callUID = `${callFrame.functionName}@${callFrame.scriptId}:${callFrame.lineNumber}:${callFrame.columnNumber}`;\n    this.self = 0;\n    this.total = 0;\n    this.id = 0;\n    this.functionName = callFrame.functionName;\n    this.parent = null;\n    this.children = [];\n  }\n\n  get scriptId(): Protocol.Runtime.ScriptId {\n    return String(this.callFrame.scriptId) as Protocol.Runtime.ScriptId;\n  }\n\n  get url(): Platform.DevToolsPath.UrlString {\n    return this.callFrame.url as Platform.DevToolsPath.UrlString;\n  }\n\n  get lineNumber(): number {\n    return this.callFrame.lineNumber;\n  }\n\n  get columnNumber(): number {\n    return this.callFrame.columnNumber;\n  }\n\n  setFunctionName(name: string|null): void {\n    if (name === null) {\n      return;\n    }\n    this.functionName = name;\n  }\n}\n\nexport class ProfileTreeModel {\n  root!: ProfileNode;\n  total!: number;\n  maxDepth!: number;\n  constructor() {\n  }\n\n  initialize(root: ProfileNode): void {\n    this.root = root;\n    this.assignDepthsAndParents();\n    this.total = this.calculateTotals(this.root);\n  }\n\n  private assignDepthsAndParents(): void {\n    const root = this.root;\n    // TODO(crbug.com/1354548): start depth from 0 once profiler\n    // panel dependencies are gone.\n    root.depth = -1;\n    root.parent = null;\n    this.maxDepth = 0;\n    const nodesToTraverse = [root];\n    while (nodesToTraverse.length) {\n      const parent = (nodesToTraverse.pop() as ProfileNode);\n      const depth = parent.depth + 1;\n      if (depth > this.maxDepth) {\n        this.maxDepth = depth;\n      }\n      const children = parent.children;\n      for (const child of children) {\n        child.depth = depth;\n        child.parent = parent;\n        nodesToTraverse.push(child);\n      }\n    }\n  }\n\n  private calculateTotals(root: ProfileNode): number {\n    const nodesToTraverse = [root];\n    const dfsList = [];\n    while (nodesToTraverse.length) {\n      const node = (nodesToTraverse.pop() as ProfileNode);\n      node.total = node.self;\n      dfsList.push(node);\n      nodesToTraverse.push(...node.children);\n    }\n    while (dfsList.length > 1) {\n      const node = (dfsList.pop() as ProfileNode);\n      if (node.parent) {\n        node.parent.total += node.total;\n      }\n    }\n    return root.total;\n  }\n}\n"]}