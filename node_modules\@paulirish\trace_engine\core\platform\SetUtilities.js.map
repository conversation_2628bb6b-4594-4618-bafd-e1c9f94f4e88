{"version": 3, "file": "SetUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/SetUtilities.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,yEAAyE;AACzE,6BAA6B;AAE7B,MAAM,CAAC,MAAM,MAAM,GAAG,UAAY,GAAW,EAAE,QAAqB;IAClE,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC5B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,UAAY,IAAY,EAAE,IAAY;IAC3D,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC", "sourcesContent": ["// Copyright (c) 2020 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const addAll = function<T>(set: Set<T>, iterable: Iterable<T>): void {\n  for (const item of iterable) {\n    set.add(item);\n  }\n};\n\nexport const isEqual = function<T>(setA: Set<T>, setB: Set<T>): boolean {\n  if (setA === setB) {\n    return true;\n  }\n  if (setA.size !== setB.size) {\n    return false;\n  }\n  for (const item of setA) {\n    if (!setB.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n"]}