import faqService, { FAQItem, FAQCategory } from '@/lib/services/faqService'

// Mock de los datos JSON
jest.mock('@/tramites_chia_optimo.json', () => [
  {
    "Nombre": "Impuesto predial unificado",
    "Formulario": "No",
    "Tiempo de respuesta": "1 hora",
    "¿Tiene pago?": "De acuerdo con el avalúo catastral del predio",
    "dependencia": "Secretaría de Hacienda",
    "subdependencia": "Dirección de Rentas"
  }
], { virtual: true })

jest.mock('@/OPA-chia-optimo.json', () => [
  {
    "Nombre": "Información sobre impuesto predial",
    "Formulario": "No",
    "Tiempo de respuesta": "1 hora",
    "¿Tiene pago?": "No",
    "dependencia": "Secretaría de Hacienda"
  }
], { virtual: true })

describe('FAQService', () => {
  beforeEach(() => {
    // Reset del singleton para cada test
    (faqService as any).initialized = false
    ;(faqService as any).faqs.clear()
    ;(faqService as any).categories.clear()
  })

  describe('Inicialización', () => {
    it('debería ser un singleton', () => {
      const instance1 = faqService
      const instance2 = faqService
      expect(instance1).toBe(instance2)
    })

    it('debería inicializar categorías correctamente', async () => {
      const categories = await faqService.getCategories()
      
      expect(categories).toHaveLength(6)
      expect(categories[0]).toHaveProperty('id')
      expect(categories[0]).toHaveProperty('name')
      expect(categories[0]).toHaveProperty('description')
      expect(categories[0]).toHaveProperty('icon')
      expect(categories[0]).toHaveProperty('color')
      expect(categories[0]).toHaveProperty('count')
    })

    it('debería cargar FAQs predefinidas', async () => {
      const popularFAQs = await faqService.getPopularFAQs(5)
      
      expect(popularFAQs.length).toBeGreaterThan(0)
      expect(popularFAQs[0]).toHaveProperty('id')
      expect(popularFAQs[0]).toHaveProperty('question')
      expect(popularFAQs[0]).toHaveProperty('answer')
      expect(popularFAQs[0]).toHaveProperty('category')
      expect(popularFAQs[0]).toHaveProperty('tags')
      expect(popularFAQs[0]).toHaveProperty('popularity')
    })
  })

  describe('Búsqueda de FAQs', () => {
    it('debería buscar FAQs por texto en preguntas', async () => {
      const results = await faqService.searchFAQs('impuesto predial')
      
      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq => 
        faq.question.toLowerCase().includes('predial') ||
        faq.answer.toLowerCase().includes('predial')
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería buscar FAQs por texto en respuestas', async () => {
      const results = await faqService.searchFAQs('avalúo catastral')
      
      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq => 
        faq.answer.toLowerCase().includes('avalúo')
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería buscar FAQs por tags', async () => {
      const results = await faqService.searchFAQs('tributos')
      
      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq => 
        faq.tags.some(tag => tag.toLowerCase().includes('tributos'))
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería retornar array vacío para búsqueda vacía', async () => {
      const results = await faqService.searchFAQs('')
      expect(results).toEqual([])
    })

    it('debería retornar array vacío para búsqueda solo con espacios', async () => {
      const results = await faqService.searchFAQs('   ')
      expect(results).toEqual([])
    })

    it('debería filtrar por categoría', async () => {
      const results = await faqService.searchFAQs('impuesto', { category: 'impuestos' })
      
      expect(results.length).toBeGreaterThan(0)
      results.forEach(faq => {
        expect(faq.category).toBe('impuestos')
      })
    })

    it('debería respetar el límite de resultados', async () => {
      const limit = 3
      const results = await faqService.searchFAQs('certificado', { limit })
      
      expect(results.length).toBeLessThanOrEqual(limit)
    })

    it('debería ordenar resultados por relevancia', async () => {
      const results = await faqService.searchFAQs('impuesto')
      
      if (results.length > 1) {
        // Verificar que están ordenados por relevancia (popularidad decreciente como proxy)
        for (let i = 0; i < results.length - 1; i++) {
          const currentScore = (results[i] as any).popularity || 0
          const nextScore = (results[i + 1] as any).popularity || 0
          // Los resultados deberían estar ordenados por relevancia
          expect(currentScore).toBeGreaterThanOrEqual(0)
          expect(nextScore).toBeGreaterThanOrEqual(0)
        }
      }
    })
  })

  describe('Gestión de categorías', () => {
    it('debería obtener todas las categorías', async () => {
      const categories = await faqService.getCategories()
      
      expect(categories).toHaveLength(6)
      
      const expectedCategories = ['impuestos', 'licencias', 'certificados', 'servicios', 'tramites', 'pagos']
      const categoryIds = categories.map(cat => cat.id)
      
      expectedCategories.forEach(expectedId => {
        expect(categoryIds).toContain(expectedId)
      })
    })

    it('debería obtener FAQs por categoría', async () => {
      const impuestosFAQs = await faqService.getFAQsByCategory('impuestos')
      
      expect(impuestosFAQs.length).toBeGreaterThan(0)
      impuestosFAQs.forEach(faq => {
        expect(faq.category).toBe('impuestos')
      })
    })

    it('debería ordenar FAQs por popularidad dentro de categoría', async () => {
      const categoryFAQs = await faqService.getFAQsByCategory('impuestos')
      
      if (categoryFAQs.length > 1) {
        for (let i = 0; i < categoryFAQs.length - 1; i++) {
          expect(categoryFAQs[i].popularity).toBeGreaterThanOrEqual(categoryFAQs[i + 1].popularity)
        }
      }
    })

    it('debería respetar límite en FAQs por categoría', async () => {
      const limit = 2
      const categoryFAQs = await faqService.getFAQsByCategory('impuestos', limit)
      
      expect(categoryFAQs.length).toBeLessThanOrEqual(limit)
    })

    it('debería retornar array vacío para categoría inexistente', async () => {
      const results = await faqService.getFAQsByCategory('categoria-inexistente')
      expect(results).toEqual([])
    })
  })

  describe('FAQs populares', () => {
    it('debería obtener FAQs más populares', async () => {
      const popularFAQs = await faqService.getPopularFAQs(5)
      
      expect(popularFAQs.length).toBeGreaterThan(0)
      expect(popularFAQs.length).toBeLessThanOrEqual(5)
      
      // Verificar que están ordenadas por popularidad
      if (popularFAQs.length > 1) {
        for (let i = 0; i < popularFAQs.length - 1; i++) {
          expect(popularFAQs[i].popularity).toBeGreaterThanOrEqual(popularFAQs[i + 1].popularity)
        }
      }
    })

    it('debería respetar el límite de FAQs populares', async () => {
      const limit = 3
      const popularFAQs = await faqService.getPopularFAQs(limit)
      
      expect(popularFAQs.length).toBeLessThanOrEqual(limit)
    })
  })

  describe('FAQ individual', () => {
    it('debería obtener FAQ por ID', async () => {
      // Primero obtener una FAQ para tener un ID válido
      const popularFAQs = await faqService.getPopularFAQs(1)
      expect(popularFAQs.length).toBeGreaterThan(0)
      
      const faqId = popularFAQs[0].id
      const faq = await faqService.getFAQById(faqId)
      
      expect(faq).not.toBeNull()
      expect(faq!.id).toBe(faqId)
    })

    it('debería retornar null para ID inexistente', async () => {
      const faq = await faqService.getFAQById('id-inexistente')
      expect(faq).toBeNull()
    })
  })

  describe('Estadísticas', () => {
    it('debería obtener estadísticas del FAQ', async () => {
      const stats = await faqService.getFAQStats()
      
      expect(stats).toHaveProperty('totalFAQs')
      expect(stats).toHaveProperty('totalCategories')
      expect(stats).toHaveProperty('averagePopularity')
      expect(stats).toHaveProperty('mostPopularCategory')
      
      expect(stats.totalFAQs).toBeGreaterThan(0)
      expect(stats.totalCategories).toBe(6)
      expect(stats.averagePopularity).toBeGreaterThan(0)
      expect(typeof stats.mostPopularCategory).toBe('string')
    })

    it('debería calcular correctamente el promedio de popularidad', async () => {
      const stats = await faqService.getFAQStats()
      const allFAQs = await faqService.getPopularFAQs(100) // Obtener todas
      
      const expectedAverage = Math.round(
        allFAQs.reduce((sum, faq) => sum + faq.popularity, 0) / allFAQs.length
      )
      
      expect(stats.averagePopularity).toBe(expectedAverage)
    })
  })

  describe('Cálculo de relevancia', () => {
    it('debería calcular puntuación de relevancia correctamente', async () => {
      // Buscar un término que aparezca en diferentes partes
      const results = await faqService.searchFAQs('impuesto')
      
      expect(results.length).toBeGreaterThan(0)
      
      // Los resultados deberían estar ordenados por relevancia
      if (results.length > 1) {
        // Verificar que el primer resultado es más relevante
        expect(results[0]).toBeDefined()
        expect(results[1]).toBeDefined()
      }
    })
  })

  describe('Manejo de errores', () => {
    it('debería manejar errores en búsqueda graciosamente', async () => {
      // Test con caracteres especiales
      const results = await faqService.searchFAQs('!@#$%^&*()')
      expect(results).toEqual([])
    })

    it('debería manejar búsquedas con texto muy largo', async () => {
      const longText = 'a'.repeat(1000)
      const results = await faqService.searchFAQs(longText)
      expect(Array.isArray(results)).toBe(true)
    })
  })
})
