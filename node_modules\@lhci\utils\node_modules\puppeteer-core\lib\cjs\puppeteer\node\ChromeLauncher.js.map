{"version": 3, "file": "ChromeLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ChromeLauncher.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,0CAAoC;AACpC,gDAAwB;AAExB,kDAI6B;AAE7B,+CAA6C;AAE7C,iDAAyC;AAOzC,6DAAyE;AAEzE,wCAAgC;AAEhC;;GAEG;AACH,MAAa,cAAe,SAAQ,oCAAe;IACjD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEQ,MAAM,CAAC,UAAsC,EAAE;;QACtD,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,QAAQ,mCAAI,IAAI,CAAC;QAC1C,IACE,QAAQ,KAAK,IAAI;YACjB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ;gBACrC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,KAAK,MAAM,CAAC;YACnD,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC,EAC3D;YACA,OAAO,CAAC,IAAI,CACV;gBACE,yBAAyB;gBACzB,4DAA4D;gBAC5D,8EAA8E;gBAC9E,mEAAmE;gBACnE,gFAAgF;gBAChF,iFAAiF;gBACjF,uHAAuH;aACxH,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;SACH;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAsC,EAAE;QAExC,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,KAAK,EACZ,aAAa,EACb,OAAO,EACP,cAAc,GACf,GAAG,OAAO,CAAC;QAEZ,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE;YACtB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;SACpD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YAC3C,eAAe,CAAC,IAAI,CAClB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;SACH;aAAM;YACL,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAC/B;QAED,IACE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/B,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF;YACA,IAAI,IAAI,EAAE;gBACR,IAAA,kBAAM,EACJ,CAAC,aAAa,EACd,2EAA2E,CAC5E,CAAC;gBACF,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;aACjD;iBAAM;gBACL,eAAe,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;aACvE;SACF;QAED,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAE9B,sEAAsE;QACtE,gEAAgE;QAChE,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACrD,OAAO,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,GAAG,CAAC,EAAE;YACxB,iBAAiB,GAAG,IAAI,CAAC;YACzB,eAAe,CAAC,IAAI,CAClB,mBAAmB,MAAM,IAAA,kBAAO,EAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAC1D,CAAC;YACF,gBAAgB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;SAC/C;QAED,MAAM,WAAW,GAAG,eAAe,CAAC,gBAAgB,CAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAA,kBAAM,EAAC,OAAO,WAAW,KAAK,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QAE1E,IAAI,gBAAgB,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE;YACrB,IAAA,kBAAM,EACJ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAC3C,+EAA+E,CAChF,CAAC;YACF,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SACjD;QAED,OAAO;YACL,cAAc,EAAE,gBAAgB;YAChC,IAAI,EAAE,eAAe;YACrB,iBAAiB;YACjB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,IAAY,EACZ,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI;gBACF,MAAM,IAAA,UAAE,EAAC,IAAI,CAAC,CAAC;aAChB;YAAC,OAAO,KAAK,EAAE;gBACd,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;aACb;SACF;IACH,CAAC;IAEQ,WAAW,CAAC,UAAwC,EAAE;QAC7D,+FAA+F;QAC/F,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,iCAAiC;YACjC,uCAAuC;YACvC,0CAA0C;YAC1C,oBAAoB;YACpB,0CAA0C;YAC1C,sDAAsD;YACtD,4BAA4B;YAC5B,wBAAwB;YACxB,yBAAyB;YACzB,sBAAsB;YACtB,uDAAuD;YACvD,2FAA2F;YAC3F,wBAAwB;YACxB,mCAAmC;YACnC,0BAA0B;YAC1B,4BAA4B;YAC5B,kCAAkC;YAClC,gBAAgB;YAChB,qBAAqB;YACrB,mEAAmE;YACnE,yCAAyC;YACzC,uCAAuC;YACvC,4CAA4C;YAC5C,qBAAqB;YACrB,4BAA4B;YAC5B,0BAA0B;YAC1B,gBAAgB;YAChB,wBAAwB;YACxB,qBAAqB;SACtB,CAAC;QACF,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GACZ,GAAG,OAAO,CAAC;QACZ,IAAI,WAAW,EAAE;YACf,eAAe,CAAC,IAAI,CAAC,mBAAmB,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;SACtE;QACD,IAAI,QAAQ,EAAE;YACZ,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;SACvD;QACD,IAAI,QAAQ,EAAE;YACZ,eAAe,CAAC,IAAI,CAClB,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,EACpD,mBAAmB,EACnB,cAAc,CACf,CAAC;SACH;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF;YACA,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACrC;QACD,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAEQ,cAAc,CAAC,OAA8B;QACpD,IAAI,OAAO,EAAE;YACX,OAAO,IAAA,sCAA2B,EAAC;gBACjC,OAAO,EAAE,kBAAiB,CAAC,MAAM;gBACjC,OAAO,EAAE,wCAAwC,CAAC,OAAO,CAAC;aAC3D,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;SACrC;IACH,CAAC;CACF;AAvMD,wCAuMC;AAED,SAAS,wCAAwC,CAC/C,OAA6B;IAE7B,QAAQ,OAAO,EAAE;QACf,KAAK,QAAQ;YACX,OAAO,+BAA4B,CAAC,MAAM,CAAC;QAC7C,KAAK,YAAY;YACf,OAAO,+BAA4B,CAAC,GAAG,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,+BAA4B,CAAC,IAAI,CAAC;QAC3C,KAAK,eAAe;YAClB,OAAO,+BAA4B,CAAC,MAAM,CAAC;KAC9C;AACH,CAAC"}