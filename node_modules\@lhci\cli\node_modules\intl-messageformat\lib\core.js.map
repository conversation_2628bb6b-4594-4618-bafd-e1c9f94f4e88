{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": "AAAA;;;;EAIE;;;;;;;;;;;;;;;;;;;;;;;;;AAEF,yBAAyB;AAEzB,OAAO,QAAQ,EAAE,EAEf,sBAAsB,EAGvB,MAAM,YAAY,CAAC;AAGpB,4EAA4E;AAE5E,SAAS,aAAa,CAAC,OAA0B;IAC/C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;KACrB;IACD,IAAI;QACF,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACnD,+DAA+D;YAC/D,0BAA0B;YAC1B,aAAa,EAAE,UAAU;SAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;KACP;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,iBAAiB,CAAC,aAAa,CAAC;KACxC;AACH,CAAC;AAED,SAAS,cAAc,CACrB,OAAkB,EAClB,MAAqE;IAErE,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAmB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;QAAvB,IAAM,IAAI,gBAAA;QACb,gCAAgC;QAChC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,IAAI,CAAC;YACf,SAAS;SACV;QAEO,IAAA,YAAE,CAAU;QAEpB,+DAA+D;QAC/D,IAAI,CAAC,CAAC,MAAM,IAAI,EAAE,IAAI,MAAM,CAAC,EAAE;YAC7B,MAAM,IAAI,WAAW,CAAC,mCAAiC,EAAI,EAAE,EAAE,CAAC,CAAC;SAClE;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAEzB,sEAAsE;QACtE,iEAAiE;QACjE,yDAAyD;QACzD,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,KAAY,CAAC,EAAE,MAAM,CAAC,CAAC;SAChE;aAAM;YACL,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAY,CAAC,CAAC;SACrC;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,EAA0B,EAAE,EAA2B;IAC1E,IAAI,CAAC,EAAE,EAAE;QACP,OAAO,EAAE,CAAC;KACX;IACD,oBACK,CAAC,EAAE,IAAI,EAAE,CAAC,EACV,CAAC,EAAE,IAAI,EAAE,CAAC,EACV,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,GAA2B,EAAE,CAAC;QACvD,GAAG,CAAC,CAAC,CAAC,gBACD,EAAE,CAAC,CAAC,CAAC,EACL,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CACjB,CAAC;QACF,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,EACN;AACJ,CAAC;AAED,SAAS,YAAY,CACnB,aAAsB,EACtB,OAA0B;IAE1B,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,aAAa,CAAC;KACtB;IAED,OAAQ,MAAM,CAAC,IAAI,CAAC,aAAa,CAA0B,CAAC,MAAM,CAChE,UAAC,GAAY,EAAE,CAAgB;QAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,OAAO,GAAG,CAAC;IACb,CAAC,eACI,aAAa,EACnB,CAAC;AACJ,CAAC;AAED;IAA0B,+BAAK;IAE7B,qBAAY,GAAY,EAAE,UAAmB;QAA7C,YACE,kBAAM,GAAG,CAAC,SAEX;QADC,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;;IAC/B,CAAC;IACH,kBAAC;AAAD,CAAC,AAND,CAA0B,KAAK,GAM9B;AAMD,MAAM,UAAU,uBAAuB;IACrC,OAAO;QACL,eAAe;;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YACrB,YAAW,CAAA,KAAA,IAAI,CAAC,YAAY,CAAA,gCAAI,IAAI,MAAE;QACxC,CAAC;QACD,iBAAiB;;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YACvB,YAAW,CAAA,KAAA,IAAI,CAAC,cAAc,CAAA,gCAAI,IAAI,MAAE;QAC1C,CAAC;QACD,cAAc;;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YACpB,YAAW,CAAA,KAAA,IAAI,CAAC,WAAW,CAAA,gCAAI,IAAI,MAAE;QACvC,CAAC;KACF,CAAC;AACJ,CAAC;AAED;IAKE,2BACE,OAAsC,EACtC,OAA4D,EAC5D,eAAkC,EAClC,IAAc;QAJhB,iBAwCC;QAtCC,wBAAA,EAAA,UAA6B,iBAAiB,CAAC,aAAa;QAwC9D,WAAM,GAAG,UACP,MAAqE;YAErE,IAAI;gBACF,OAAO,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAC7C;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,CAAC,UAAU,EAAE;oBAChB,MAAM,IAAI,KAAK,CACb,uCAAqC,CAAC,CAAC,UAAU,0CAAqC,KAAI,CAAC,OAAO,MAAG,CACtG,CAAC;iBACH;qBAAM;oBACL,MAAM,CAAC,CAAC;iBACT;aACF;QACH,CAAC,CAAC;QAlDA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;gBAC9B,MAAM,IAAI,SAAS,CACjB,6EAA6E,CAC9E,CAAC;aACH;YACD,qCAAqC;YACrC,IAAI,CAAC,GAAG,GAAG,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC/C;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;SACpB;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAsB,CAAC,EAAE;YAC3D,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;SACvE;QAED,4EAA4E;QAC5E,WAAW;QACX,IAAM,OAAO,GAAG,YAAY,CAAC,iBAAiB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAEzE,+DAA+D;QAC/D,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAE3C,IAAI,UAAU,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,uBAAuB,EAAE,CAAC;QAExE,uEAAuE;QACvE,2EAA2E;QAC3E,0DAA0D;QAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE5E,2EAA2E;QAC3E,yBAAyB;IAC3B,CAAC;IAiBD,2CAAe,GAAf;QACE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IACD,kCAAM,GAAN;QACE,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACM,+BAAa,GAAG,IAAI,CAAC;IACrB,yBAAO,GAAuC,SAAS,CAAC;IAC/D,gFAAgF;IAChF,+EAA+E;IAC/E,qCAAqC;IAC9B,yBAAO,GAAG;QACf,MAAM,EAAE;YACN,QAAQ,EAAE;gBACR,KAAK,EAAE,UAAU;aAClB;YAED,OAAO,EAAE;gBACP,KAAK,EAAE,SAAS;aACjB;SACF;QAED,IAAI,EAAE;YACJ,KAAK,EAAE;gBACL,KAAK,EAAE,SAAS;gBAChB,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,SAAS;aAChB;YAED,MAAM,EAAE;gBACN,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,SAAS;aAChB;YAED,IAAI,EAAE;gBACJ,KAAK,EAAE,MAAM;gBACb,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,SAAS;aAChB;YAED,IAAI,EAAE;gBACJ,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,MAAM;gBACb,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,SAAS;aAChB;SACF;QAED,IAAI,EAAE;YACJ,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;aAClB;YAED,MAAM,EAAE;gBACN,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;aAClB;YAED,IAAI,EAAE;gBACJ,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,OAAO;aACtB;YAED,IAAI,EAAE;gBACJ,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,OAAO;aACtB;SACF;KACF,CAAC;IACJ,wBAAC;CAAA,AA1ID,IA0IC;SA1IY,iBAAiB;AA6I9B,eAAe,iBAAiB,CAAC"}