{"version": 3, "file": "cpu_profile.js", "sourceRoot": "", "sources": ["../../../../../../front_end/models/cpu_profile/cpu_profile.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAC;AAChE,OAAO,KAAK,gBAAgB,MAAM,uBAAuB,CAAC;AAE1D,OAAO,EACL,mBAAmB,EACnB,gBAAgB,GACjB,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as CPUProfileDataModel from './CPUProfileDataModel.js';\nimport * as ProfileTreeModel from './ProfileTreeModel.js';\n\nexport {\n  CPUProfileDataModel,\n  ProfileTreeModel,\n};\n"]}