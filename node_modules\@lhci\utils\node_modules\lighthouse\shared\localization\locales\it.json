{"core/audits/accessibility/accesskeys.js | description": {"message": "Le chiavi di accesso consentono agli utenti di impostare rapidamente lo stato attivo su una parte della pagina. Per assicurare una navigazione corretta, ogni chiave di accesso deve essere univoca. [Scopri di più sulle chiavi di accesso](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "I valori `[accesskey]` non sono univoci"}, "core/audits/accessibility/accesskeys.js | title": {"message": "I valori `[accesskey]` sono univoci"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Ogni elemento `role` ARIA supporta un determinato sottoinsieme di attributi `aria-*`. Se non dovessero corrispondere, gli attributi `aria-*` non saranno considerati validi. [<PERSON><PERSON><PERSON> come far corrispondere gli attributi ARIA ai relativi ruoli](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Gli attributi `[aria-*]` non corrispondono ai rispettivi ruoli"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Gli attributi `[aria-*]` corrispondono ai rispettivi ruoli"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Quando un elemento non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri come rendere più accessibili gli elementi di comando](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Gli elementi `button`, `link` e `menuitem` non hanno nomi accessibili"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Gli elementi `button`, `link` e `menuitem` hanno nomi accessibili"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Le tecnologie per la disabilità, come gli screen reader, funzionano in modo incoerente se viene impostato il valore `aria-hidden=\"true\"` nel documento `<body>`. [<PERSON><PERSON><PERSON> in che modo `aria-hidden` influisce sul corpo del documento](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "L'attributo `[aria-hidden=\"true\"]` è presente nel documento `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "L'attributo `[aria-hidden=\"true\"]` non è presente nel documento `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "I discendenti attivabili all'interno di un elemento `[aria-hidden=\"true\"]` impediscono di mettere questi elementi interattivi a disposizione degli utenti che usano tecnologie per la disabilità come gli screen reader. [Sc<PERSON><PERSON> in che modo `aria-hidden` influisce sugli elementi attivabili](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Gli elementi `[aria-hidden=\"true\"]` contengono discendenti per cui è possibile impostare lo stato attivo"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Gli elementi `[aria-hidden=\"true\"]` non contengono discendenti per cui è possibile impostare lo stato attivo"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Quando un campo di immissione non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri di più sulle etichette dei campi di immissione](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "I campi di immissione ARIA non hanno nomi accessibili"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "I campi di immissione ARIA hanno nomi accessibili"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Quando un elemento meter non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scop<PERSON> come assegnare un nome agli elementi `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Gli elementi ARIA `meter` non hanno nomi accessibili"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Gli elementi ARIA `meter` hanno nomi accessibili"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Quando un elemento `progressbar` non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri come etichettare gli elementi `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Gli elementi ARIA `progressbar` non hanno nomi accessibili"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Gli elementi ARIA `progressbar` hanno nomi accessibili"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Alcuni ruoli ARIA hanno attributi obbligatori che descrivono lo stato dell'elemento agli screen reader. [Scopri di più sui ruoli e sugli attributi richiesti](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Gli elementi `[role]` non hanno tutti gli attributi `[aria-*]` obbligatori"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Gli elementi `[role]` hanno tutti gli attributi `[aria-*]` obbligatori"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Alcuni ruoli principali ARIA devono contenere specifici ruoli secondari per poter eseguire le funzionalità per l'accessibilità previste. [Scopri di più sui ruoli e sugli elementi secondari richiesti](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Negli elementi con un ruolo ARIA `[role]` che richiedono che gli elementi secondari contengano un ruolo `[role]` specifico mancano alcuni o tutti questi elementi secondari obbligatori."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Gli elementi con un ruolo ARIA `[role]` che richiedono che gli elementi secondari contengano un ruolo `[role]` specifico hanno tutti gli elementi secondari obbligatori."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Alcuni ruoli secondari ARIA devono essere contenuti in specifici ruoli principali per poter eseguire in modo corretto le funzionalità per l'accessibilità previste. [Scopri di più sui ruoli ARIA e sull'elemento principale richiesto](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Gli elementi `[role]` non sono contenuti nei rispettivi elementi principali obbligatori"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Gli elementi `[role]` sono contenuti nei rispettivi elementi principali obbligatori"}, "core/audits/accessibility/aria-roles.js | description": {"message": "I ruoli ARIA devono contenere valori validi per poter eseguire le funzionalità per l'accessibilità previste. [Scopri di più sui ruoli ARIA validi](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "I valori `[role]` non sono validi"}, "core/audits/accessibility/aria-roles.js | title": {"message": "I valori `[role]` sono validi"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Quando un campo di attivazione/disattivazione non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri di più sui campi di attivazione/disattivazione](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "I campi di attivazione/disattivazione ARIA non hanno nomi accessibili"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "I campi di attivazione/disattivazione ARIA hanno nomi accessibili"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Quando un elemento tooltip non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri come assegnare un nome agli elementi `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Gli elementi ARIA `tooltip` non hanno nomi accessibili"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Gli elementi ARIA `tooltip` hanno nomi accessibili"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Quando un elemento `treeitem` non ha un nome accessibile, gli screen reader lo descrivono con un nome generico, renden<PERSON>lo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri di più sull'etichettatura di elementi `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Gli elementi ARIA `treeitem` non hanno nomi accessibili"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Gli elementi ARIA `treeitem` hanno nomi accessibili"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Le tecnologie per la disabilità, come gli screen reader, non sono in grado di interpretare gli attributi ARIA con valori non validi. [Scopri di più sui valori validi per gli attributi ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Gli attributi `[aria-*]` non hanno valori validi"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Gli attributi `[aria-*]` hanno valori validi"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Le tecnologie per la disabilità, come gli screen reader, non sono in grado di interpretare gli attributi ARIA con nomi non validi. [Scopri di più sugli attributi ARIA validi](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Gli attributi `[aria-*]` non sono validi o contengono errori ortografici"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Gli attributi `[aria-*]` sono validi e non contengono errori ortografici"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementi respinti"}, "core/audits/accessibility/button-name.js | description": {"message": "Quando un pulsante non ha un nome accessibile, gli screen reader lo descrivono semplicemente come \"pulsante\", rendendolo inutilizzabile per gli utenti che si affidano agli screen reader. [Scopri come rendere più accessibili i pulsanti](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "I pulsanti non hanno nomi accessibili"}, "core/audits/accessibility/button-name.js | title": {"message": "I pulsanti hanno un nome accessibile"}, "core/audits/accessibility/bypass.js | description": {"message": "Se aggiungi metodi per bypassare contenuti ripetitivi, la navigazione della pagina diventa più efficiente per chi usa la tastiera. [Scopri di più sui blocchi di bypass](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "La pagina non contiene alcun titolo, skip link o area di riferimento"}, "core/audits/accessibility/bypass.js | title": {"message": "La pagina contiene un titolo, uno skip link o un'area di riferimento"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Il testo a basso contrasto è difficile, se non impossibile, da leggere per molti utenti. [<PERSON><PERSON><PERSON> come fornire un contrasto cromatico sufficiente](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Il rapporto di contrasto tra i colori di sfondo e primo piano non è sufficiente."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Il rapporto di contrasto tra i colori di sfondo e primo piano è sufficiente"}, "core/audits/accessibility/definition-list.js | description": {"message": "Se il markup degli elenchi di definizioni non è stato eseguito in modo corretto, gli screen reader possono generare output ambigui o imprecisi. [<PERSON><PERSON><PERSON> come strutturare correttamente gli elenchi di definizioni](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Gli elementi `<dl>` non contengono solo gruppi `<dt>` e `<dd>` ed elementi `<script>`, `<template>` o `<div>` nell'ordine corretto."}, "core/audits/accessibility/definition-list.js | title": {"message": "Gli elementi `<dl>` contengono solo gruppi `<dt>` e `<dd>` ed elementi `<script>`, `<template>` o `<div>` nell'ordine corretto."}, "core/audits/accessibility/dlitem.js | description": {"message": "Gli elementi dell'elenco di definizioni (`<dt>` e `<dd>`) devono essere aggregati in un elemento `<dl>` principale affinché gli screen reader possano descriverli correttamente. [<PERSON><PERSON><PERSON> come strutturare correttamente gli elenchi di definizioni](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Gli elementi dell'elenco di definizioni non sono aggregati negli elementi `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Gli elementi dell'elenco di definizioni sono aggregati negli elementi `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Il titolo fornisce agli utenti di screen reader una panoramica della pagina, mentre per gli utenti di motori di ricerca è utile per stabilire se una pagina è pertinente alla loro ricerca. [Scopri di più sui titoli dei documenti](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Il documento non ha un elemento `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Il documento ha un elemento `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Tutti gli elementi attivabili devono avere un valore `id` univoco per garantirne la visibilità alle tecnologie per la disabilità. [<PERSON><PERSON><PERSON> come correggere i valori `id` duplicati](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Gli attributi `[id]` in elementi attivi per cui è possibile impostare lo stato attivo non sono univoci"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Gli attributi `[id]` in elementi attivi per cui è possibile impostare lo stato attivo sono univoci"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Il valore di un ID ARIA deve essere univoco per evitare che altre istanze vengano ignorate dalle tecnologie per la disabilità. [Sc<PERSON><PERSON> come correggere gli ID ARIA duplicati](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Gli ID ARIA non sono univoci"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Gli ID ARIA sono univoci"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "I campi dei moduli con più etichette potrebbero essere descritti in modo confuso dalle tecnologie per la disabilità come gli screen reader, che usano la prima etichetta, l'ultima o tutte le etichette. [<PERSON><PERSON><PERSON> come utilizzare le etichette dei moduli](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "I campi del modulo hanno più etichette"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON><PERSON><PERSON> campo del modulo ha più etichette"}, "core/audits/accessibility/frame-title.js | description": {"message": "Gli utenti di screen reader si affidano ai titoli dei frame per la descrizione dei relativi contenuti. [Scopri di più sui titoli dei frame](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Gli elementi `<frame>` o `<iframe>` non hanno un titolo"}, "core/audits/accessibility/frame-title.js | title": {"message": "Gli elementi `<frame>` o `<iframe>` hanno un titolo"}, "core/audits/accessibility/heading-order.js | description": {"message": "Le intestazioni nell'ordine corretto che non saltano livelli descrivono la struttura semantica della pagina, facilitando la navigazione e la comprensione quando vengono usate tecnologie per la disabilità. [Scopri di più sull'ordine delle intestazioni](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Gli elementi di intestazione non sono in ordine decrescente sequenziale"}, "core/audits/accessibility/heading-order.js | title": {"message": "Gli elementi di intestazione vengono visualizzati in ordine decrescente sequenziale"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Se per una pagina non viene specificato un attributo `lang`, lo screen reader presuppone che la lingua della pagina sia quella predefinita scelta dall'utente durante la configurazione dello screen reader. Se la lingua della pagina non è effettivamente quella predefinita, lo screen reader potrebbe non pronunciare correttamente il testo della pagina. [Scopri di più sull'attributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "L'elemento `<html>` non ha un attributo `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "L'elemento `<html>` ha un attributo `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "La specifica di una [lingua BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valida consente agli screen reader di pronunciare correttamente il testo. [Sc<PERSON><PERSON> come utilizzare l'attributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "L'attributo `[lang]` dell'elemento `<html>` non ha un valore valido."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "L'attributo `[lang]` dell'elemento `<html>` ha un valore valido"}, "core/audits/accessibility/image-alt.js | description": {"message": "Gli elementi informativi dovrebbero mostrare testo alternativo breve e descrittivo. Gli elementi decorativi possono essere ignorati con un attributo ALT vuoto. [Scopri di più sull'attributo `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Gli elementi immagine non hanno attributi `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Gli elementi immagine hanno attributi `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Quando viene utilizzata un'immagine come pulsante `<input>`, fornire un testo alternativo può aiutare gli utenti di screen reader a comprendere lo scopo del pulsante. [Scopri di più sul testo alternativo dell'immagine di input](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Gli elementi `<input type=\"image\">` non hanno testo `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Gli elementi `<input type=\"image\">` hanno testo `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Le etichette consentono di assicurarsi che i controlli dei moduli vengano descritti in modo corretto dalle tecnologie per la disabilità, come gli screen reader. [Scopri di più sulle etichette degli elementi del modulo](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Gli elementi del modulo non hanno le corrispondenti etichette"}, "core/audits/accessibility/label.js | title": {"message": "Gli elementi del modulo sono associati a etichette"}, "core/audits/accessibility/link-name.js | description": {"message": "Un testo dei link (incluso il testo alternativo delle immagini, se come link) distinguibile, univoco e attivabile migliora l'esperienza di navigazione per gli utenti di screen reader. [Scopri come rendere accessibili i link](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Il nome dei link non è distinguibile"}, "core/audits/accessibility/link-name.js | title": {"message": "I link hanno un nome distinguibile"}, "core/audits/accessibility/list.js | description": {"message": "Gli screen reader descrivono gli elenchi in un determinato modo. Una struttura dell'elenco corretta agevola l'output dello screen reader. [Scop<PERSON> di più sulla struttura dell'elenco corretta](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Gli elenchi non contengono solo elementi `<li>` ed elementi che supportano script (`<script>` e `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Gli elenchi contengono solo elementi `<li>` ed elementi che supportano gli script (`<script>` e `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Gli screen reader richiedono che gli elementi dell'elenco (`<li>`) siano contenuti in un elemento `<ul>`, `<ol>` o `<menu>` principale per poterli descrivere in modo corretto. [Scop<PERSON> di più sulla struttura dell'elenco corretta](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Gli elementi dell'elenco (`<li>`) non sono contenuti negli elementi principali `<ul>`, `<ol>` o `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Gli elementi dell'elenco (`<li>`) sono contenuti negli elementi principali `<ul>`, `<ol>` o `<menu>`."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "L'aggiornamento automatico della pagina è un evento imprevisto per l'utente e, una volta verificatosi, imposta di nuovo lo stato attivo sulla parte superiore della pagina. Ciò può costituire motivo di frustrazione o confusione per l'utente. [Scopri di più sul meta tag di aggiornamento](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Il documento usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Il documento non usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Disattivare lo zoom è problematico per gli utenti ipovedenti che si affidano all'ingrandimento dello schermo per vedere in modo chiaro i contenuti di una pagina web. [Scopri di più sul meta tag viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` viene usato nell'elemento `<meta name=\"viewport\">` o l'attributo `[maximum-scale]` è inferiore a 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` non viene usato nell'elemento `<meta name=\"viewport\">` e l'attributo `[maximum-scale]` non è inferiore a 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Gli screen reader non possono tradurre contenuti non testuali. Aggiungere testo alternativo agli elementi `<object>` aiuta gli screen reader a comunicare il significato agli utenti. [Scopri di più sul testo alternativo per gli elementi `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Gli elementi `<object>` non hanno testo alternativo"}, "core/audits/accessibility/object-alt.js | title": {"message": "Gli elementi `<object>` hanno testo alternativo"}, "core/audits/accessibility/tabindex.js | description": {"message": "Un valore maggiore di 0 implica un ordine di navigazione esplicito. Sebbene sia tecnicamente valido, spesso genera un'esperienza frustrante per gli utenti che usano tecnologie per la disabilità. [Scopri di più sull'attributo `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Alcuni elementi hanno un valore `[tabindex]` maggiore di 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nessun elemento ha un valore `[tabindex]` maggiore di 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Gli screen reader sono dotati di funzionalità che semplificano la navigazione nelle tabelle. Se ti assicuri che le celle `<td>` che usano l'attributo `[headers]` facciano riferimento esclusivamente ad altre celle nella stessa tabella, puoi migliorare l'esperienza degli utenti di screen reader. [Scopri di più sull'attributo `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Le celle in un elemento `<table>` che utilizzano l'attributo `[headers]` fanno riferimento a un elemento `id` non trovato all'interno della stessa tabella."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Le celle in un elemento `<table>` che utilizzano l'attributo `[headers]` fanno riferimento a celle della stessa tabella."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Gli screen reader sono dotati di funzionalità che semplificano la navigazione nelle tabelle. Assicurare che le intestazioni delle tabelle facciano sempre riferimento a un insieme di celle può migliorare l'esperienza degli utenti di screen reader. [Scopri di più sulle intestazioni delle tabelle](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Gli elementi `<th>` e gli elementi con `[role=\"columnheader\"/\"rowheader\"]` non hanno le celle di dati da essi descritte."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Gli elementi `<th>` e gli elementi con ruolo `[role=\"columnheader\"/\"rowheader\"]` hanno le celle di dati da essi descritte."}, "core/audits/accessibility/valid-lang.js | description": {"message": "La specifica di una [lingua BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valida per gli elementi consente di assicurarsi che il testo sia pronunciato correttamente dallo screen reader. [<PERSON><PERSON><PERSON> come utilizzare l'attributo `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Gli attributi `[lang]` non hanno un valore valido"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Gli attributi `[lang]` hanno un valore valido"}, "core/audits/accessibility/video-caption.js | description": {"message": "Se un video ha i sottotitoli codificati, per le persone sorde o gli utenti con problemi di udito è più facile accedere alle relative informazioni. [Scopri di più sui sottotitoli codificati dei video](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Gli elementi `<video>` non contengono un elemento `<track>` con `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Gli elementi `<video>` contengono un elemento `<track>` con `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON> corrente"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON> suggerito"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` consente agli utenti di inviare più velocemente i moduli. Per facilitare le cose agli utenti, potresti attivare la funzionalità impostando un valore valido per l'attributo `autocomplete`. [Scopri di più su `autocomplete` nei moduli](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Gli elementi `<input>` non hanno attributi `autocomplete` corretti"}, "core/audits/autocomplete.js | manualReview": {"message": "Richiede revisione manuale"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Rivedi l'ordine dei token"}, "core/audits/autocomplete.js | title": {"message": "Gli elementi `<input>` usano correttamente `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Token `autocomplete`: valore \"{token}\" non valido in {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "<PERSON><PERSON><PERSON> l'ordine dei token: \"{tokens}\" in {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Azioni possibili"}, "core/audits/bf-cache.js | description": {"message": "Molte navigazioni vengono eseguite tornando a una pagina precedente o di nuovo avanti. La cache back-forward (bfcache) può velocizzare queste navigazioni di ritorno. [Scopri di più sulla cache back-forward](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo dell'errore}other{# motivi dell'errore}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivo dell'errore"}, "core/audits/bf-cache.js | failureTitle": {"message": "La pagina ha impedito il ripristino della cache back-forward"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON> di errore"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Nessuna azione possibile"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Supporto del browser in sospeso"}, "core/audits/bf-cache.js | title": {"message": "La pagina non ha impedito il ripristino della cache back-forward"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Le estensioni di Chrome incidono negativamente sulle prestazioni di caricamento di questa pagina. Prova a controllare la pagina in modalità di navigazione in incognito o da un profilo Chrome senza estensioni."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Valutazione degli script"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Analisi script"}, "core/audits/bootup-time.js | columnTotal": {"message": "Tempo di CPU totale"}, "core/audits/bootup-time.js | description": {"message": "Potresti ridurre i tempi di analisi, compilazione ed esecuzione di JavaScript. A questo scopo potrebbe essere utile pubblicare payload JavaScript di dimensioni inferiori. [Scopri come ridurre il tempo di esecuzione di JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Riduci il tempo di esecuzione di JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Tempo di esecuzione JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Rimuovi i moduli JavaScript duplicati di grandi dimensioni dai bundle per ridurre i byte superflui consumati dall'attività di rete. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Rimuovi moduli duplicati nei bundle JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "I file GIF di grandi dimensioni non sono efficaci per la pubblicazione di contenuti animati. Anziché il formato GIF potresti usare video MPEG4/WebM per le animazioni e PNG/WebP per le immagini statiche. In questo modo userai meno byte di rete. [Scopri di più sui formati video efficienti](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Usa formati video per i contenuti animati"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill e trasformazioni consentono ai browser precedenti di usare nuove funzionalità JavaScript. Tanti non sono però necessari per i browser moderni. Per il tuo codice JavaScript in bundle, adotta una moderna strategia di implementazione degli script usando il rilevamento di funzionalità module/nomodule per ridurre la quantità di codice inviata ai browser moderni e mantenere il supporto dei browser precedenti. [<PERSON><PERSON><PERSON> come utilizzare il moderno codice JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Evita di pubblicare codice JavaScript precedente in browser moderni"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "I formati delle immagini come WebP e AVIF spesso consentono una compressione migliore rispetto a quella dei formati PNG o JPEG, che comporta download più veloci e un minor consumo di dati. [Scopri di più sui moderni formati delle immagini](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Pubblica immagini in formati più recenti"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Potresti usare il caricamento lento per le immagini fuori schermo e nascoste al termine del caricamento di tutte le risorse fondamentali per ridurre il Tempo all'interattività. [Scopri come rimandare le immagini fuori schermo](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Rimanda immagini fuori schermo"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Alcune risorse bloccano la prima visualizzazione della pagina. Potresti pubblicare le risorse JS/CSS fondamentali incorporate e rimandare tutte le risorse JS/styles non fondamentali. [Scopri come eliminare le risorse che bloccano il rendering](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimina le risorse di blocco della visualizzazione"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "I payload di rete di grandi dimensioni comportano costi reali per gli utenti e sono strettamente correlati a lunghi tempi di caricamento. [Scopri come ridurre le dimensioni dei payload](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Dimensioni totali: {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evita payload di rete enormi"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Vengono evitati payload di rete enormi"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minimizza i file CSS per ridurre le dimensioni dei payload di rete. [Scopri come minimizzare i file CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minimizza CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minimizza i file JavaScript per ridurre le dimensioni dei payload e i tempi di analisi degli script. [Scopri come minimizzare JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minimizza <PERSON>"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Riduci le regole inutilizzate dei fogli di stile e rimanda i contenuti CSS non usati per i contenuti above the fold al fine di ridurre i byte consumati dall'attività di rete. [Scop<PERSON> come ridurre il CSS inutilizzato](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Riduci i contenuti CSS inutilizzati"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Riduci il codice JavaScript inutilizzato e rimanda il caricamento degli script finché non sono necessari al fine di ridurre i byte consumati dall'attività di rete. [Scopri come ridurre il codice JavaScript inutilizzato](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Riduci il codice JavaScript inutilizzato"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "La memorizzazione nella cache per un lungo periodo di tempo può velocizzare le visite abituali alla tua pagina. [Scopri di più sui criteri efficaci relativi alla cache](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 risorsa trovata}other{# risorse trovate}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Pubblica le risorse statiche con criteri della cache efficaci"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Vengono usati criteri della cache efficaci per le risorse statiche"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Le immagini ottimizzate vengono caricate più velocemente e consumano meno rete dati. [Scopri come codificare le immagini in modo efficiente](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifica in modo efficace le immagini"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensioni effettive"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensioni visualizzate"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Le immagini erano più grandi rispetto alle dimensioni visualizzate"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Le immagini erano appropriate per le dimensioni visualizzate"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Pubblica immagini di dimensioni adeguate per consumare meno rete dati e ridurre i tempi di caricamento. [Scopri come ridimensionare le immagini](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Usa immagini di dimensioni adeguate"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Le risorse basate sul testo dovrebbero essere pubblicate con compressione (gzip, deflate o brotli) per minimizzare il numero totale di byte di rete. [Scopri di più sulla compressione del testo](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Attiva la compressione del testo"}, "core/audits/content-width.js | description": {"message": "Se la larghezza dei contenuti dell'app non corrisponde a quella dell'area visibile, l'app potrebbe non essere ottimizzata per gli schermi dei dispositivi mobili. [Scopri come ridimensionare i contenuti per l'area visibile](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Le dimensioni di {innerWidth} px dell'area visibile non corrispondono alle dimensioni di {outerWidth} px della finestra."}, "core/audits/content-width.js | failureTitle": {"message": "Le dimensioni dei contenuti non sono corrette per l'area visibile"}, "core/audits/content-width.js | title": {"message": "Le dimensioni dei contenuti sono corrette per l'area visibile"}, "core/audits/critical-request-chains.js | description": {"message": "Nella sezione Catene di richieste fondamentali indicata di seguito vengono mostrate le risorse caricate con priorità elevata. Potresti ridurre la lunghezza delle catene e le dimensioni del download delle risorse oppure rimandare il download delle risorse non necessarie per velocizzare il caricamento pagina. [Scopri come evitare di concatenare le richieste fondamentali](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 catena trovata}other{# catene trovate}}"}, "core/audits/critical-request-chains.js | title": {"message": "Evita di concatenare le richieste fondamentali"}, "core/audits/csp-xss.js | columnDirective": {"message": "Istruzione"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravità"}, "core/audits/csp-xss.js | description": {"message": "Un Criterio di sicurezza del contenuto (CSP) efficace riduce notevolmente il rischio di attacchi cross-site scripting (XSS). [Scopri come utilizzare un CSP per evitare XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sin<PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "La pagina contiene un criterio CSP definito in un tag <meta>. <PERSON><PERSON>ti spostare il CSP in un'intestazione HTTP o definirne un altro in un'intestazione HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Nessun criterio CSP trovato in modalità di applicazione forzata"}, "core/audits/csp-xss.js | title": {"message": "Assicurati che il criterio CSP sia efficace contro gli attacchi XSS (cross-site scripting)"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Ritiro/avviso"}, "core/audits/deprecations.js | columnLine": {"message": "Riga"}, "core/audits/deprecations.js | description": {"message": "Le API deprecate verranno rimosse dal browser prima o poi. [Scopri di più sulle API deprecate](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 avviso trovato}other{# avvisi trovati}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Usa API obsolete"}, "core/audits/deprecations.js | title": {"message": "Evita le API obsolete"}, "core/audits/dobetterweb/charset.js | description": {"message": "È richiesta una dichiarazione della codifica dei caratteri. Può essere inserita con un tag `<meta>` nei primi 1024 byte del codice HTML oppure nell'intestazione della risposta HTTP Content-Type. [Scopri di più sulla dichiarazione della codifica dei caratteri](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "La dichiarazione del set di caratteri non è presente o è definita troppo tardi nel codice HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Set di caratteri definito correttamente"}, "core/audits/dobetterweb/doctype.js | description": {"message": "La specifica di un doctype impedisce al browser di passare alla modalità non standard. [Scopri di più sulla dichiarazione doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Il nome del doctype deve essere la stringa `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Il documento contiene un elemento `doctype` che attiva `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Il documento deve contenere un doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Il campo publicId dovrebbe essere vuoto"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Il campo systemId dovrebbe essere vuoto"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Il documento contiene un elemento `doctype` che attiva `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Nella pagina manca il doctype HTML e viene quindi attivata la modalità non standard"}, "core/audits/dobetterweb/doctype.js | title": {"message": "La pagina ha il doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistica"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valore"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Un DOM di grandi dimensioni aumenta l'utilizzo di memoria, causa [calcoli di stile](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) più lunghi e genera costosi [adattamenti dinamici del layout](https://developers.google.com/speed/articles/reflow). [Scopri come evitare dimensioni eccessive del DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}other{# elementi}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evita di usare un DOM di dimensioni eccessive"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profondità massima DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Elementi DOM totali"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Elementi secondari massimi"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Viene evitato un DOM di dimensioni eccessive"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Gli utenti sono sospettosi nei confronti dei siti che chiedono la loro posizione senza contesto o sono confusi da questi siti. Potresti associare la richiesta a un'azione dell'utente. [Scopri di più sull'autorizzazione alla geolocalizzazione](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Chiede l'autorizzazione alla geolocalizzazione durante il caricamento della pagina"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita di chiedere l'autorizzazione alla geolocalizzazione durante il caricamento della pagina"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON><PERSON> di errore"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Gli errori registrati nel riquadro `Issues` in Strumenti per sviluppatori di Chrome indicano la presenza di problemi irrisolti. Tali errori potrebbero riguardare richieste di rete non andate a buon fine, controlli di sicurezza insufficienti e altri problemi del browser. Apri il riquadro Errori in Strumenti per sviluppatori di Chrome per ulteriori dettagli su ciascun errore."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Sono stati registrati degli errori nel riquadro `Issues` in Strumenti per sviluppatori di Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloccata dal criterio multiorigine"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Utilizzo intensivo delle risorse da parte degli annunci"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Nessun errore nel riquadro `Issues` in Strumenti per sviluppatori di Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versione"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Tutte le librerie JavaScript front-end rilevate nella pagina. [Sc<PERSON><PERSON> di più su questo controllo diagnostico del rilevamento della libreria JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Librerie JavaScript rilevate"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Per gli utenti con connessioni lente, gli script esterni inseriti in modo dinamico tramite `document.write()` potrebbero ritardare il caricamento pagina di decine di secondi. [Scopri come evitare document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Gli utenti sono sospettosi nei confronti dei siti che chiedono di inviare notifiche senza contesto o sono confusi da questi siti. Potresti associare la richiesta ai gesti dell'utente. [Scopri di più su come ottenere responsabilmente un'autorizzazione per le notifiche](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Chiede l'autorizzazione di accesso alle notifiche durante il caricamento della pagina"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita di chiedere l'autorizzazione di accesso alle notifiche durante il caricamento della pagina"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 offre tanti vantaggi rispetto a HTTP/1.1, tra cui intestazioni binarie e multiplexing. [Scopri di più su HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 richiesta non pubblicata tramite HTTP/2}other{# richieste non pubblicate tramite HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Usa HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Potresti contrassegnare i listener di eventi di tocco e rotellina come `passive` per migliorare le prestazioni di scorrimento della pagina. [Scopri di più sull'utilizzo di listener di eventi passivi](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Non usa listener passivi per migliorare le prestazioni dello scorrimento"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Usa listener passivi per migliorare le prestazioni dello scorrimento"}, "core/audits/errors-in-console.js | description": {"message": "Gli errori registrati nella console indicano la presenza di problemi irrisolti. Questi errori potrebbero riguardare richieste di rete non andate a buon fine e altri problemi del browser. [Scopri di più su questi errori nel controllo diagnostico della console](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Gli errori del browser sono stati registrati nella console"}, "core/audits/errors-in-console.js | title": {"message": "Nessun errore del browser registrato nella console"}, "core/audits/font-display.js | description": {"message": "Utilizza la funzionalità CSS `font-display` per assicurarti che il testo sia visibile all'utente durante il caricamento dei caratteri web. [Scopri di più su `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Assicurati che il testo rimanga visibile durante il caricamento dei caratteri web"}, "core/audits/font-display.js | title": {"message": "Tutto il testo rimane visibile durante il caricamento dei caratteri web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Impossibile controllare automaticamente in Lighthouse il valore `font-display` per l'origine {fontOrigin}.}other{Impossibile controllare automaticamente in Lighthouse i valori `font-display` per l'origine {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Proporzioni (effettive)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Proporzioni (visualizzate)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Le dimensioni di visualizzazione delle immagini dovrebbero corrispondere alle proporzioni naturali. [Scopri di più sulle proporzioni delle immagini](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Visualizza immagini con proporzioni errate"}, "core/audits/image-aspect-ratio.js | title": {"message": "Visualizza immagini con proporzioni corrette"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Dimensioni effettive"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Dimensioni visualizzate"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Dimensioni previste"}, "core/audits/image-size-responsive.js | description": {"message": "Le dimensioni naturali delle immagini dovrebbero essere proporzionali a quelle dello schermo e al rapporto pixel per ottimizzare la nitidezza delle immagini. [<PERSON><PERSON><PERSON> come fornire immagini adattabili](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Vengono pubblicate immagini a bassa risoluzione"}, "core/audits/image-size-responsive.js | title": {"message": "Vengono pubblicate immagini con risoluzione appropriata"}, "core/audits/installable-manifest.js | already-installed": {"message": "L'app è già installata"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Impossibile scaricare un'icona richiesta dal file manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivo dell'errore"}, "core/audits/installable-manifest.js | description": {"message": "Il service worker è la tecnologia che consente alla tua app di usare tante funzionalità delle app web progressive, ad esempio il funzionamento offline, l'aggiunta alla schermata Home e le notifiche push. Grazie a implementazioni adeguate del service worker e del file manifest, i browser possono chiedere proattivamente agli utenti di aggiungere la tua app alla schermata Home. Ciò potrebbe comportare un maggiore coinvolgimento. [Scopri di più sui requisiti di installabilità del file manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo}other{# motivi}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Il file manifest o il service worker dell'app web non soddisfano i requisiti di installabilità"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "L'URL e l'ID dell'app nel Play Store non corrispondono"}, "core/audits/installable-manifest.js | in-incognito": {"message": "La pagina è stata caricata in una finestra di navigazione in incognito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "La proprietà \"display\" del file manifest deve essere \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Il file manifest contiene il campo \"display_override\" e la prima modalità di visualizzazione supportata deve essere \"standalone\", '\"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Non è stato possibile recuperare il file manifest, è vuoto o non è stato possibile analizzarlo"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "L'URL del file manifest è cambiato durante il recupero del file manifest."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Il file manifest non contiene un campo \"name\" o \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Il file manifest non contiene un'icona adatta, deve essere in formato PNG, SVG o WebP e avere una dimensione di almeno {value0} px. L'attributo sizes deve essere impostato e l'attributo purpose, se configurato, deve includere \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nessuna delle icone fornite ha una dimensione minima di {value0} px quadrati in formato PNG, SVG o WebP, con l'attributo purpose non impostato o impostato su \"any\"."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "L'icona scaricata era vuota o danneggiata"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nessun ID Play Store fornito"}, "core/audits/installable-manifest.js | no-manifest": {"message": "La pagina non contiene URL del file manifest <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Nessun service worker corrispond<PERSON> trovato. Potrebbe essere necessario ricaricare la pagina o verificare che l'ambito del service worker per la pagina corrente includa l'ambito e l'URL di avvio dal file manifest."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Impossibile verificare il service worker se il file manifest non contiene un campo \"start_url\""}, "core/audits/installable-manifest.js | noErrorId": {"message": "L'ID errore di installabilità \"{errorId}\" non è riconosciuto"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "La pagina non è pubblicata da un'origine sicura"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "La pagina non è caricata nel frame principale"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "La pagina non funziona offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "La PWA è stata disinstallata e i controlli di installabilità sono in fase di reimpostazione."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "La piattaforma di applicazioni specificata non è supportata su Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Il file manifest specifica prefer_related_applications: vero"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications è supportato solo sui canali beta e stabili di Chrome su Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse non ha potuto determinare se fosse presente un service worker. Prova con una versione più recente di Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Lo schema dell'URL del file manifest ({scheme}) non è supportato su Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "L'URL di avvio del file manifest non è valido"}, "core/audits/installable-manifest.js | title": {"message": "Il file manifest e il service worker dell'app web soddisfano i requisiti di installabilità"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Un URL nel file manifest contiene uno username, una password o una porta"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "La pagina non funziona offline. La pagina non sarà considerata installabile nelle versioni successive a Chrome 93, la cui versione stabile sarà rilasciata ad agosto 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Bloccato"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL non sicuro"}, "core/audits/is-on-https.js | columnResolution": {"message": "Richiedi risoluzione"}, "core/audits/is-on-https.js | description": {"message": "Tutti i siti dovrebbero essere protetti con HTTPS, anche quelli che non trattano dati sensibili. Si dovrebbero quindi evitare i [contenuti misti](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) perché alcune risorse vengono caricate tramite HTTP nonostante la richiesta iniziale venga pubblicata tramite HTTPS. HTTPS impedisce agli intrusi di manomettere o ascoltare passivamente le comunicazioni tra la tua app e i tuoi utenti ed è un prerequisito per HTTP/2 e tante nuove API delle piattaforme web. [Scopri di più su HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 richiesta non sicura trovata}other{# richieste non sicure trovate}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Non usa HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Usa HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Upgrade a HTTPS eseguito automaticamente"}, "core/audits/is-on-https.js | warning": {"message": "Consentito con avviso"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Si tratta dell'elemento identificato come Largest Contentful Paint all'interno dell'area visibile. [Scopri di più sull'elemento Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elemento Largest Contentful Paint"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contributo a CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Questi elementi DOM contribuiscono maggiormente alla metrica CLS della pagina. [Scopri come migliorare il CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Evita significative variazioni di layout"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Le immagini above the fold che vengono caricate tramite caricamento lento vengono visualizzate più tardi nel ciclo di vita della pagina e questo può ritardare la visualizzazione dell'elemento più grande. [Scopri di più sul caricamento lento ottimale](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "L'immagine Largest Contentful Paint è stata caricata tramite caricamento lento"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "L'immagine Largest Contentful Paint non è stata caricata tramite caricamento lento"}, "core/audits/long-tasks.js | description": {"message": "Elenca le attività più lunghe nel thread principale; è utile per identificare gli elementi che contribuiscono maggiormente al ritardo dell'input. [Sc<PERSON><PERSON> come evitare attività lunghe nel thread principale](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# attività lunga trovata}other{# attività lunghe trovate}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON> attivi<PERSON> lunghe nel thread principale"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoria"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Potresti ridurre i tempi di analisi, compilazione ed esecuzione di JavaScript. A questo scopo potrebbe essere utile pubblicare payload JavaScript di dimensioni inferiori. [Scopri come minimizzare il lavoro del thread principale](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Riduci al minimo il lavoro del thread principale"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Il lavoro del thread principale è ridotto al minimo"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Per raggiungere il maggior numero di utenti, i siti dovrebbero funzionare su ogni browser più usato. [Scopri di più sulla compatibilità cross-browser](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Il sito funziona su più browser"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Assicurati che le singole pagine siano collegabili tramite link diretti sotto forma di URL e che gli URL siano univoci per la condivisione sui social media. [Scopri di più sull'offerta di link diretti](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Ogni pagina ha un URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Le transizioni dovrebbero sembrare rapide mentre esegui i tocchi, anche con una rete lenta. Questa esperienza è un fattore chiave che incide sulle prestazioni percepite dall'utente. [Scopri di più sulle transizioni di pagina](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Non sembra che le transizioni di pagina si blocchino sulla rete"}, "core/audits/maskable-icon.js | description": {"message": "Un'icona mascherabile assicura che l'immagine riempia l'intera forma senza effetto letterbox durante l'installazione dell'app su un dispositivo. [Scopri di più sulle icone manifest mascherabili](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Il file manifest non contiene un'icona mascherabile"}, "core/audits/maskable-icon.js | title": {"message": "Il file manifest contiene un'icona mascherabile"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "La metrica Cumulative Layout Shift misura lo spostamento degli elementi visibili all'interno dell'area visibile. [Scopri di più sulla metrica Cumulative Layout Shift](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "L'interazione con Next Paint misura l'adattabilità della pagina, il tempo necessario alla pagina per rispondere in modo visibile all'input utente. [Scopri di più sulla metrica Interaction to Next Paint](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint indica il momento in cui vengono visualizzati il primo testo o la prima immagine. [Scopri di più sulla metrica First Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "La metrica First Meaningful Paint indica quando diventano visibili i contenuti principali di una pagina. [Scopri di più sulla metrica First Meaningful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Tempo all'interattività indica il tempo necessario affinché la pagina diventi completamente interattiva. [Scopri di più sulla metrica Tempo all'interattività](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "La metrica Largest Contentful Paint indica il momento in cui vengono visualizzati il testo o l'immagine più grandi. [Scopri di più sulla metrica Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Il potenziale massimo di First Input Delay che i tuoi utenti potrebbero riscontrare è la durata dell'attività più lunga. [Scopri di più sulla metrica Maximum Potential First Input Delay](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "La metrica Speed Index mostra la velocità con cui diventano visibili i contenuti di una pagina. [Scopri di più sulla metrica Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Somma di tutti i periodi di tempo, espressi in millisecondi, tra FCP e Tempo all'interattività, quando la durata dell'attività ha superato 50 ms. [Scopri di più sulla metrica Total Blocking Time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "I tempi di round trip della rete (RTT) influiscono notevolmente sulle prestazioni. Quando l'RTT verso un'origine è elevato, significa che i server più vicini all'utente potrebbero migliorare le prestazioni. [Scopri di più sul tempo di round trip](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Tempi di round trip della rete"}, "core/audits/network-server-latency.js | description": {"message": "Le latenze dei server possono influire sulle prestazioni del Web. Quando la latenza del server di un'origine è elevata, significa che il server è sovraccarico oppure ha prestazioni di backend scadenti. [Scopri di più sui tempi di risposta del server](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latenze server backend"}, "core/audits/no-unload-listeners.js | description": {"message": "L'evento `unload` non viene attivato in modo affidabile e il relativo ascolto potrebbe impedire ottimizzazioni del browser quali cache back-forward. Utilizza invece gli eventi `pagehide` o `visibilitychange`. [Scopri di più sull'unload dei listener di eventi](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registra un listener `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Evita i listener di eventi `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Le animazioni non composite possono essere scadenti e aumentare il CLS. [Scopri come evitare le animazioni non composte](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# elemento animato trovato}other{# elementi animati trovati}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "La proprietà relativa al filtro potrebbe spostare i pixel"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Il target ha un'altra animazione che non è compatibile"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "L'effetto ha una modalità composita diversa da \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evita animazioni non composite"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "La proprietà relativa alla trasformazione dipende dalle dimensioni della casella"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Proprietà CSS non supportata: {properties}}other{Proprietà CSS non supportate: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "L'effetto ha parametri di timing non supportati"}, "core/audits/performance-budget.js | description": {"message": "Mantieni la quantità e le dimensioni delle richieste di rete al di sotto dei target impostati tramite il budget delle prestazioni fornito. [Scopri di più sui budget delle prestazioni](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 richiesta}other{# richieste}}"}, "core/audits/performance-budget.js | title": {"message": "Budget per le prestazioni"}, "core/audits/preload-fonts.js | description": {"message": "Precarica caratteri `optional` per consentirne l'utilizzo ai visitatori alla prima visita. [Scopri di più sul precaricamento dei caratteri](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "I caratteri con `font-display: optional` non vengono precaricati"}, "core/audits/preload-fonts.js | title": {"message": "I caratteri con `font-display: optional` vengono precaricati"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Se l'elemento LCP viene aggiunto alla pagina in modo dinamico, dovresti precaricare l'immagine per migliorarlo. [Scopri di più sul precaricamento degli elementi LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Precarica l'immagine Largest Contentful Paint"}, "core/audits/redirects.js | description": {"message": "I reindirizzamenti comportano ulteriori ritardi prima del caricamento della pagina. [Scopri come evitare i reindirizzamenti delle pagine](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evita i reindirizzamenti tra più pagine"}, "core/audits/resource-summary.js | description": {"message": "Per impostare budget relativi alla quantità e alle dimensioni delle risorse della pagina, aggiungi un file budget.json. [Scopri di più sui budget delle prestazioni](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 richiesta • {byteCount, number, bytes} KiB}other{# richieste • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Mantieni un numero ridotto di richieste e dimensioni di trasferimento limitate"}, "core/audits/seo/canonical.js | description": {"message": "I link canonici suggeriscono quale URL mostrare nei risultati di ricerca. [Scopri di più sui link canonici](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Diversi URL in conflitto ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL non valido ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Rimanda a un'altra posizione `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Non è un URL assoluto ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Indirizza all'URL principale del dominio (la home page), anzi<PERSON><PERSON> a una pagina di contenuto equivalente"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Il documento non ha un valore `rel=canonical` valido"}, "core/audits/seo/canonical.js | title": {"message": "Il documento ha un elemento `rel=canonical` valido"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Link non sottoponibile a scansione"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "I motori di ricerca potrebbero usare gli attributi `href` dei link per eseguire la scansione dei siti web. Assicurati che l'attributo `href` degli elementi anchor rimandi a una destinazione appropriata per consentire il rilevamento di un numero maggiore di pagine del sito. [Scopri come consentire la scansione dei link](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "I link non possono essere sottoposti a scansione"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "I link possono essere sottoposti a scansione"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Ulteriore testo illeggibile"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Dimensione carattere"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% di testo della pagina"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Se<PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Le dimensioni dei caratteri minori di 12 px sono troppo piccole per essere leggibili e richiederebbero ai visitatori di dispositivi mobili di \"pizzicare per eseguire lo zoom\" e poter leggere la pagina. Cerca di avere più del 60% del testo della pagina con una dimensione uguale o superiore a 12 px. [Scopri di più sulle dimensioni dei caratteri leggibili](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} del testo leggibile"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Il testo è illeggibile perché non esiste un meta tag viewport ottimizzato per gli schermi dei dispositivi mobili."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Il documento non usa dimensioni dei caratteri leggibili"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON> leggi<PERSON>e"}, "core/audits/seo/font-size.js | title": {"message": "Il documento utilizza dimensioni dei caratteri leggibili"}, "core/audits/seo/hreflang.js | description": {"message": "I link hreflang indicano ai motori di ricerca quale versione di una pagina devono elencare nei risultati di ricerca per una determinata lingua o regione. [Scopri di più su `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Il documento non ha un elemento `hreflang` valido"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valore href relativo"}, "core/audits/seo/hreflang.js | title": {"message": "Il documento ha un elemento `hreflang` valido"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Codice lingua imprevisto"}, "core/audits/seo/http-status-code.js | description": {"message": "Le pagine con codici di stato HTTP non validi potrebbero non essere indicizzate correttamente. [Scopri di più sui codici di stato HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "La pagina ha un codice di stato HTTP non valido"}, "core/audits/seo/http-status-code.js | title": {"message": "La pagina ha un codice di stato HTTP valido"}, "core/audits/seo/is-crawlable.js | description": {"message": "I motori di ricerca non sono in grado di includere le pagine nei risultati di ricerca se non dispongono dell'autorizzazione per eseguirne la scansione. [Scopri di più sulle istruzioni dei crawler](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "L'indicizzazione della pagina è bloccata"}, "core/audits/seo/is-crawlable.js | title": {"message": "L'indicizzazione della pagina non è bloccata"}, "core/audits/seo/link-text.js | description": {"message": "Il testo descrittivo dei link aiuta i motori di ricerca a comprendere i tuoi contenuti. [Scopri come rendere più accessibili i link](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link trovato}other{# link trovati}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "I link non contengono testo descrittivo"}, "core/audits/seo/link-text.js | title": {"message": "I link hanno un testo descrittivo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Esegui lo [Strumento di test per i dati strutturati](https://search.google.com/structured-data/testing-tool/) e [Structured Data Linter](http://linter.structured-data.org/) per convalidare i dati strutturati. [Scopri di più sui dati strutturati](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "<PERSON><PERSON> strutt<PERSON> validi"}, "core/audits/seo/meta-description.js | description": {"message": "Le meta descrizioni possono essere incluse nei risultati di ricerca per riassumere brevemente i contenuti della pagina. [Scopri di più sulla meta descrizione](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Il testo della descrizione è vuoto."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Il documento non ha una meta descrizione"}, "core/audits/seo/meta-description.js | title": {"message": "Il documento ha una meta descrizione"}, "core/audits/seo/plugins.js | description": {"message": "I motori di ricerca non possono indicizzare i contenuti dei plug-in e molti dispositivi limitano i plug-in o non li supportano. [Scopri di più su come evitare i plug-in](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Il documento utilizza plug-in"}, "core/audits/seo/plugins.js | title": {"message": "Il documento non fa uso di plug-in"}, "core/audits/seo/robots-txt.js | description": {"message": "Se il file robots.txt non è valido, i crawler potrebbero non essere in grado di capire come vuoi che il tuo sito web venga sottoposto a scansione o indicizzato. [Scopri di più sul file robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La richiesta per robots.txt ha restituito uno stato HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 errore trovato}other{# errori trovati}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse non può completare il download del file robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt non è valido"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt è valido"}, "core/audits/seo/tap-targets.js | description": {"message": "Gli elementi interattivi come pulsanti e link dovrebbero essere abbastanza grandi (48 x 48 px) e avere abbastanza spazio intorno a loro, per essere facili da toccare senza sovrapporsi ad altri elementi. [Scopri di più sui target dei tocchi](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "Il {decimalProportion, number, percent} dei target dei tocchi ha dimensioni appropriate"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "I target dei tocchi sono troppo piccoli perché non esiste un meta tag viewport ottimizzato per gli schermi dei dispositivi mobili"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "I target dei tocchi non sono dimensionati in modo appropriato"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Target sovrapposto"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Target dei tocchi"}, "core/audits/seo/tap-targets.js | title": {"message": "I target dei tocchi sono dimensionati in modo appropriato"}, "core/audits/server-response-time.js | description": {"message": "Fai in modo che il tempo di risposta del server per il documento principale sia breve perché tutte le altre richieste dipendono da questo. [Scopri di più sulla metrica Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Il documento radice ha richiesto {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Riduci il tempo di risposta iniziale del server"}, "core/audits/server-response-time.js | title": {"message": "Il tempo di risposta iniziale del server è stato breve"}, "core/audits/service-worker.js | description": {"message": "Il service worker è la tecnologia che consente alla tua app di usare tante funzionalità delle app web progressive, ad esempio il funzionamento offline, l'aggiunta alla schermata Home e le notifiche push. [Scopri di più sui service worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Questa pagina è controllata tramite un service worker, ma non è stato trovato alcun elemento `start_url` perché non è stato possibile analizzare il file manifest come JSON valido"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Questa pagina è controllata tramite un service worker, ma `start_url` ({startUrl}) non rientra nell'ambito del service worker ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Questa pagina è controllata tramite un service worker, ma non è stato trovato alcun elemento `start_url` perché non è stato recuperato alcun file manifest."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Questa origine contiene uno o più service worker, ma la pagina ({pageUrl}) non rientra nell'ambito."}, "core/audits/service-worker.js | failureTitle": {"message": "Non registra un service worker che controlla la pagina e `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registra un service worker che controlla la pagina e `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Una schermata iniziale a tema assicura un'esperienza di alta qualità quando gli utenti avviano la tua app dalla schermata Home. [Scopri di più sulle schermate iniziali](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Non è configurato con una schermata iniziale personalizzata"}, "core/audits/splash-screen.js | title": {"message": "Configurato con una schermata iniziale personalizzata"}, "core/audits/themed-omnibox.js | description": {"message": "È possibile impostare per la barra degli indirizzi del browser un tema corrispondente a quello del tuo sito. [Scopri di più sulla barra degli indirizzi a tema.](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Non imposta un colore tema per la barra degli indirizzi."}, "core/audits/themed-omnibox.js | title": {"message": "Imposta un colore tema per la barra degli indirizzi."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Supporto ai clienti)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Alcuni incorporamenti di terze parti possono essere caricati tramite caricamento lento. Puoi sostituirli con un facade finché non sono richiesti. [Sc<PERSON><PERSON> come rimandare terze parti con un facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternativa facade disponibile}other{# alternative facade disponibili}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Alcune risorse di terze parti possono essere caricate tramite caricamento lento con un facade"}, "core/audits/third-party-facades.js | title": {"message": "Carica risorse di terze parti tramite caricamento lento con i facade"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Terza parte"}, "core/audits/third-party-summary.js | description": {"message": "Il codice di terze parti può incidere notevolmente sulle prestazioni del caricamento. Limita il numero di provider di terze parti superflui e prova a caricare il codice di terze parti al termine del caricamento della pagina. [Scopri come minimizzare l'impatto di terze parti](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Il codice di terze parti ha bloccato il thread principale per {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Riduci l'impatto del codice di terze parti"}, "core/audits/third-party-summary.js | title": {"message": "Riduci al minimo l'utilizzo di codice di terze parti"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Misurazione"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Metrica"}, "core/audits/timing-budget.js | description": {"message": "Imposta un budget per le tempistiche per tenere sotto controllo le prestazioni del tuo sito. I siti con ottime prestazioni vengono caricati velocemente e rispondono rapidamente agli eventi di input utente. [Scopri di più sui budget delle prestazioni](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Budget per le tempistiche"}, "core/audits/unsized-images.js | description": {"message": "Imposta larghezza e altezza esplicite negli elementi immagine per ridurre le variazioni di layout e migliorare la metrica CLS. [Scopri come impostare le dimensioni delle immagini](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Gli elementi immagine non hanno `width` e `height` esplicite"}, "core/audits/unsized-images.js | title": {"message": "Gli elementi immagine hanno `width` e `height` esplicite"}, "core/audits/user-timings.js | columnType": {"message": "Tipo"}, "core/audits/user-timings.js | description": {"message": "Potresti dotare la tua app dell'API User Timing per misurare le prestazioni reali dell'app durante le esperienze utente chiave. [Scopri di più sugli indicatori User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 tempo utente}other{# tempi utente}}"}, "core/audits/user-timings.js | title": {"message": "Indicatori e misure User Timing"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "È stato trovato un elemento `<link rel=preconnect>` per \"{securityOrigin}\", ma non è stato utilizzato dal browser. Verifica che l'attributo `crossorigin` sia utilizzato in modo corretto."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Potresti aggiungere hint delle risorse `preconnect` o `dns-prefetch` per collegarti anticipatamente a importanti origini di terze parti. [Scopri come precollegarti alle origini richieste](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Precollegati alle origini necessarie"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Sono state trovate più di 2 connessioni `<link rel=preconnect>`. Queste connessioni devono essere usate con moderazione e soltanto per le origini più importanti."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "È stato trovato un elemento `<link rel=preconnect>` per \"{securityOrigin}\", ma non è stato utilizzato dal browser. Usa `preconnect` soltanto per origini importanti che verranno sicuramente richieste dalla pagina."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "È stato trovato un precaricamento `<link>` per \"{preloadURL}\", ma non è stato utilizzato dal browser. Verifica che l'attributo `crossorigin` sia utilizzato in modo corretto."}, "core/audits/uses-rel-preload.js | description": {"message": "Potresti usare `<link rel=preload>` per dare la priorità al recupero delle risorse attualmente richieste in un secondo momento nel caricamento della pagina. [<PERSON><PERSON><PERSON> come precaricare le richieste chiave](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Precarica le richieste fondamentali"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL mappa"}, "core/audits/valid-source-maps.js | description": {"message": "Le mappe di origine convertono il codice minimizzato nel codice sorgente originale. Questo facilita il debug agli sviluppatori in fase di produzione. Inoltre, Lighthouse può fornire maggiori informazioni. Valuta la possibilità di implementare le mappe di origine per usufruire di questi vantaggi. [Scopri di più sulle mappe di origine](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Mancano le mappe di origine per il file JavaScript proprietario di grandi dimensioni"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Nel file JavaScript di grandi dimensioni manca una mappa di origine"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Avviso: manca 1 elemento in `.sourcesContent`}other{Avviso: mancano # elementi in `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "La pagina ha mappe di origine valide"}, "core/audits/viewport.js | description": {"message": "Un tag `<meta name=\"viewport\">` non soltanto ottimizza la tua app per gli schermi di dispositivi mobili di varie dimensioni, ma evita anche [un ritardo di 300 millisecondi per l'input utente](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Scopri di più sull'utilizzo del meta tag viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Nessun tag `<meta name=\"viewport\">` trovato"}, "core/audits/viewport.js | failureTitle": {"message": "Non ha un tag `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Ha un tag `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Si tratta del lavoro di blocco dei thread che si verifica durante la misurazione di Interaction to Next Paint. [Scopri di più sulla metrica Interaction to Next Paint](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms utilizzati per l'evento \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Target evento"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimizza il lavoro durante l'interazione con il tasto"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Input Delay"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON> presentazione"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tempo di elaborazione"}, "core/audits/work-during-interaction.js | title": {"message": "Minimizza il lavoro durante l'interazione con il tasto"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Si tratta di opportunità per facilitare l'uso di ARIA nella tua applicazione e migliorare l'esperienza per gli utenti di tecnologie per la disabilità, come uno screen reader."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Si tratta di opportunità per fornire contenuti alternativi per audio e video. <PERSON><PERSON>ò può migliorare l'esperienza per gli utenti con problemi di udito o di vista."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio e video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Questi elementi evidenziano le best practice di accessibilità comuni."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Best practice"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Questi controlli mettono in evidenza le opportunità per [migliorare l'accessibilità della tua applicazione web](https://developer.chrome.com/docs/lighthouse/accessibility/). È possibile rilevare automaticamente soltanto un sottoinsieme di problemi di accessibilità, pertanto sono consigliati anche i test manuali."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Questi elementi riguardano aree che uno strumento di test automatizzato non può coprire. Leggi ulteriori informazioni nella nostra guida su come [effettuare un esame di accessibilità](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accessibilità"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Si tratta di opportunità per migliorare la leggibilità dei contenuti."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrasto"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Si tratta di opportunità per migliorare l'interpretazione data ai tuoi contenuti da utenti di lingua diversa."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internazionalizzazione e localizzazione"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Si tratta di opportunità per migliorare la semantica dei comandi della tua applicazione. <PERSON><PERSON>ò può migliorare l'esperienza per gli utenti di tecnologie per la disabilità, come uno screen reader."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nomi ed etichette"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Si tratta di opportunità per migliorare la navigazione da tastiera nella tua applicazione."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigazione"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Si tratta di opportunità per migliorare l'esperienza di lettura dei dati nelle tabelle o negli elenchi per gli utenti di tecnologie per la disabilità, come gli screen reader."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> ed elenchi"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilità dei browser"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Best practice"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Generali"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Affidabilità e sicurezza"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Esperienza utente"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "I budget per le prestazioni consentono di stabilire gli standard per le prestazioni del tuo sito."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budget"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Ulteriori informazioni sulle prestazioni della tua applicazione. Questi valori non [incidono direttamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) sul punteggio Prestazioni."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostica"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "L'aspetto più importante delle prestazioni è la velocità di visualizzazione dei pixel sullo schermo. Metriche chiave: First Contentful Paint, First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Miglioramenti della prima visualizzazione"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Questi suggerimenti possono aiutarti a velocizzare il caricamento della pagina. Non [incidono direttamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) sul punteggio Prestazioni."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Opportunità"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Migliora l'esperienza di caricamento generale per fare in modo che la pagina diventi reattiva e pronta all'uso nel più breve tempo possibile. Metriche chiave: Tempo per interattività, Indice velocità"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Miglioramenti generali"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Prestazioni"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Questi controlli consentono di convalidare gli aspetti di un'app web progressiva. [Scopri le caratteristiche di un'app web progressiva di qualità](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Questi controlli sono richiesti in base all'[Elenco di controllo PWA](https://web.dev/pwa-checklist/) di riferimento, ma non vengono eseguiti automaticamente da Lighthouse. Non incidono sul tuo punteggio, ma è importante verificarli manualmente."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installabile"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> per PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Questi controlli assicurano che la pagina stia applicando il seguente consiglio relativo all'ottimizzazione per i motori di ricerca di base. Esistono molti altri fattori che Lighthouse non tratta qui che potrebbero influire sul tuo ranking di ricerca, incluse le prestazioni su [Segnali web essenziali](https://web.dev/learn-core-web-vitals/). [Scopri di più su Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Esegui questi altri strumenti di convalida sul tuo sito per controllare ulteriori best practice per SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatta il tuo codice HTML in modo che i crawler possano comprendere meglio i contenuti della tua app."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Best practice per i contenuti"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Per poter mostrare la tua app nei risultati di ricerca, i crawler devono potervi accedere."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Scansione e indicizzazione"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Assicurati che le tue pagine siano ottimizzate per il mobile in modo che gli utenti non debbano pizzicare o aumentare lo zoom per riuscire a leggere i contenuti delle pagine. [Sc<PERSON><PERSON> come ottimizzare le pagine per il mobile](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Ottimizzata per i dispositivi mobili"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Il dispositivo testato sembra disporre di una CPU più lenta rispetto a quella prevista da Lighthouse. Ciò può influire negativamente sul tuo punteggio relativo alle prestazioni. Scopri di più sulla [calibrazione di un moltiplicatore di rallentamento CPU adeguato](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "La pagina potrebbe non essere caricata come previsto perché l'URL di prova ({requested}) è stato reindirizzato a {final}. Prova a testare direttamente il secondo URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Il caricamento della pagina è stato troppo lento e non è stato completato entro il limite di tempo. I risultati potrebbero essere incompleti."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Timeout della cache del browser scaduto. Prova a controllare di nuovo questa pagina e a segnalare un bug se il problema persiste."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Potrebbero esserci dei dati memorizzati che incidono sulle prestazioni di caricamento in questa posizione: {locations}. Controlla questa pagina in una finestra di navigazione in incognito per evitare che queste risorse incidano sui tuoi punteggi.}other{Potrebbero esserci dei dati memorizzati che incidono sulle prestazioni di caricamento in queste posizioni: {locations}. Controlla questa pagina in una finestra di navigazione in incognito per evitare che queste risorse incidano sui tuoi punteggi.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Timeout dei dati di origine scaduti. Prova a controllare di nuovo questa pagina e a segnalare un bug se il problema persiste."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Solo le pagine caricate tramite una richiesta GET possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Solo le pagine con il codice di stato 2XX possono essere memorizzate nella cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome ha rilevato un tentativo di eseguire JavaScript mentre la pagina si trovava nella cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Al momento le pagine che hanno richiesto un AppBanner non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "La cache back-forward è stata disattivata in chrome://flags. Visita la pagina chrome://flags/#back-forward-cache per attivarla localmente su questo dispositivo."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "La cache back-forward è stata disattivata dalla riga di comando."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "La cache back-forward è stata disattivata a causa dell'insufficienza di memoria."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "La cache back-forward non è supportata dall'incorporamento."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "La cache back-forward è stata disattivata per lo strumento di prerendering."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "La pagina non può essere memorizzata nella cache perché contiene un'istanza BroadcastChannel con listener registrati."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Le pagine che presentano l'intestazione cache-control:no-store non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "La cache è stata eliminata intenzionalmente."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "La pagina è stata eliminata dalla cache per consentire a un'altra pagina di essere memorizzata nella cache."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Al momento le pagine che contengono plug-in non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Le pagine che utilizzano l'API FileChooser non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Le pagine che utilizzano l'API File System Access non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Le pagine che utilizzano un dispositivo multimediale non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "L'utente è uscito dalla pagina mentre un media player era in riproduzione."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Le pagine che utilizzano l'API MediaSession e impostano uno stato di riproduzione non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Le pagine che utilizzano l'API MediaSession e impostano gestori per le azioni non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "La cache back-forward è stata disattivata a causa dello screen reader."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Le pagine che utilizzano SecurityHandler non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Le pagine che utilizzano l'API Serial non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Le pagine che utilizzano l'API WebAuthetication non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Le pagine che utilizzano l'API WebBluetooth non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Le pagine che utilizzano l'API WebUSB non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Al momento le pagine che utilizzano un worker o worklet dedicato non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "L'utente è uscito dal documento prima che venisse completato il caricamento."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Era presente App Banner al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Era presente Gestore delle password di Chrome al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Era in corso la distillazione DOM al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Era presente il visualizzatore DOM Distiller al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "La cache back-forward è stata disattivata a causa di estensioni che usano l'API Messaging."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Le estensioni con connessione di lunga durata devono chiudere la connessione prima di poter essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Le estensioni con connessione di lunga durata hanno cercato di inviare messaggi ai frame nella cache back-forward."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "La cache back-forward è stata disattivata a causa delle estensioni."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Al momento dell'uscita era mostrata una finestra di dialogo modale come un nuovo invio di un modulo o la finestra di dialogo di una password http."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Era mostrata la pagina offline al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Era presente la barra Out-Of-Memory Intervention al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Erano presenti richieste di autorizzazione al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Era presente il blocco popup al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Erano mostrati dettagli di Navigazione sicura al momento dell'uscita."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Navigazione sicura ha considerato illecita questa pagina e ha bloccato il popup."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "È stato attivato un service worker mentre la pagina si trovava nella cache back-forward."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "La cache back-forward è stata disattivata a causa di un errore del documento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Le pagine che usano FencedFrames non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "La pagina è stata eliminata dalla cache per consentire a un'altra pagina di essere memorizzata nella cache."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Al momento le pagine che hanno concesso l'accesso alla riproduzione in streaming di contenuti multimediali non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Al momento le pagine che utilizzano portali non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Al momento le pagine che utilizzano IdleManager non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Al momento le pagine che hanno una connessione IndexedDB aperta non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Sono state usate API non idonee."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Al momento le pagine in cui JavaScript viene inserito dalle estensioni non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Al momento le pagine in cui StyleSheet viene inserito dalle estensioni non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Errore interno."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "La cache back-forward è stata disattivata a causa di una richiesta keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Al momento le pagine che utilizzano il blocco della tastiera non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | loading": {"message": "L'utente è uscito dalla pagina prima che venisse completato il caricamento."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Le pagine la cui risorsa principale presenta l'intestazione cache-control:no-cache non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Le pagine la cui principale risorsa presenta l'intestazione cache-control:no-store non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "La navigazione è stata annullata prima che la pagina potesse essere ripristinata dalla cache back-forward."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "La pagina è stata eliminata dalla cache perché una connessione di rete attiva ha ricevuto troppi dati. Chrome limita la quantità di dati che una pagina può ricevere mentre è memorizzata nella cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Al momento le pagine con una richiesta di rete XHR o fetch() in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "La pagina è stata eliminata dalla cache back-forward perché una richiesta di rete attiva ha coinvolto un reindirizzamento."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "La pagina è stata eliminata dalla cache perché una connessione di rete è stata aperta troppo a lungo. Chrome limita l'intervallo di tempo durante il quale una pagina può ricevere dati mentre è memorizzata nella cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Le pagine che non hanno un'intestazione di risposta valida non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "La navigazione è avvenuta in un frame diverso da quello principale."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Al momento le pagine con transazioni DB indicizzate in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Al momento le pagine con una richiesta di rete in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Al momento le pagine con una richiesta di rete fetch in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Al momento le pagine con una richiesta di rete in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Al momento le pagine con una richiesta di rete XHR in corso non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Al momento le pagine che utilizzano PaymentManager non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Al momento le pagine che utilizzano Picture in picture non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | portal": {"message": "Al momento le pagine che utilizzano portali non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | printing": {"message": "Al momento le pagine che mostrano le UI di stampa non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "La pagina è stata aperta usando \"`window.open()`\" e un'altra scheda contiene un riferimento alla pagina oppure la pagina ha aperto una finestra."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Si è verificato un arresto anomalo nel processo di rendering della pagina nella cache back-forward."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Il processo di rendering della pagina nella cache back-forward è stato interrotto."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Al momento le pagine che hanno richiesto autorizzazioni relative all'acquisizione di audio non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Al momento le pagine che hanno richiesto autorizzazioni relative ai sensori non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Al momento le pagine che hanno richiesto autorizzazioni relative al fetch o alla sincronizzazione in background non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Al momento le pagine che hanno richiesto autorizzazioni per accedere a dispositivi MIDI non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Al momento le pagine che hanno richiesto autorizzazioni relative alle notifiche non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Al momento le pagine che hanno richiesto l'accesso allo spazio di archiviazione non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Al momento le pagine che hanno richiesto autorizzazioni relative all'acquisizione di video non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Solo le pagine il cui schema dell'URL è HTTP o HTTPS possono essere memorizzate nella cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "La pagina è stata rivendicata da un service worker mentre si trovava nella cache back-forward."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker ha tentato di inviare un `MessageEvent` alla pagina nella cache back-forward."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "La registrazione di ServiceWorker è stata annullata mentre una pagina si trovava nella cache back-forward."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "La pagina è stata eliminata dalla cache back-forward a causa dell'attivazione di un service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome è stato riavviato e le voci della cache back-forward sono state eliminate."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Al momento le pagine che utilizzano SharedWorker non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Al momento le pagine che utilizzano SpeechRecognizer non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Al momento le pagine che utilizzano SpeechSynthesis non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Un iframe sulla pagina ha avviato una navigazione che non è stata completata."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Le pagine le cui sottorisorse presentano l'intestazione cache-control:no-cache non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Le pagine le cui sottorisorse presentano l'intestazione cache-control:no-store non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | timeout": {"message": "La pagina ha superato il tempo massimo nella cache back-forward ed <PERSON> scaduta."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Si è verificato il timeout della pagina durante la memorizzazione nella cache back-forward (probabilmente a causa dell'esecuzione prolungata di gestori pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "La pagina ha un gestore dell'unload nel frame principale."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "La pagina ha un gestore dell'unload in un frame secondario."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Il browser ha modificato l'intestazione di override dello user agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Al momento le pagine che hanno concesso l'accesso alla registrazione di video o audio non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Al momento le pagine che utilizzano WebDatabase non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Al momento le pagine che utilizzano WebHID non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Al momento le pagine che utilizzano WebLocks non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Al momento le pagine che utilizzano WebNfc non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Al momento le pagine che utilizzano WebOTPService non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Le pagine con WebRTC non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Al momento le pagine che utilizzano WebShare non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Le pagine con WebSocket non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Le pagine con WebTransport non possono essere memorizzate nella cache back-forward."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Al momento le pagine che utilizzano WebXR non possono essere memorizzate nella cache back-forward."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Potresti aggiungere schemi URL https: e http: (ignorati dai browser che supportano \"strict-dynamic\") per la compatibilità con le versioni precedenti dei browser."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "L'istruzione disown-opener è deprecata da CSP3. Usa invece l'intestazione Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "L'istruzione referrer è deprecata da CSP2. Usa invece l'intestazione Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "L'istruzione reflected-xss è deprecata da CSP2. Usa invece l'intestazione X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "A causa della mancanza di un'istruzione base-uri, i tag <base> inseriti possono impostare l'URL di base di tutti gli URL relativi (ad esempio gli script) per un dominio controllato da un utente malintenzionato. Potresti impostare base-uri su \"none\" o \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "L'assenza di un'istruzione object-src consente l'inserimento di plug-in che eseguono script non sicuri. Se puoi, imposta object-src su \"none\"."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Manca l'istruzione script-src. <PERSON><PERSON><PERSON> essere consentita l'esecuzione di script non sicuri."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Hai dimenticato il punto e virgola? {keyword} sembra essere un'istruzione, non una parola chiave."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Per i valori nonce dovresti usare il set di caratteri Base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "I valori nonce devono essere di almeno 8 caratteri."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Evita di usare schemi URL standard ({keyword}) in questa istruzione. Questi schemi consentono di recuperare script da un dominio non sicuro."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Evita di usare caratteri jolly standard ({keyword}) in questa istruzione. Questi caratteri consentono di recuperare script da un dominio non sicuro."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "La destinazione di reporting è configurata soltanto tramite l'istruzione report-to. Questa istruzione è supportata soltanto nei browser basati su Chromium, pertanto è consigliato usare anche un'istruzione report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nessun criterio CSP configura una destinazione di reporting. In questo modo è difficile mantenere il criterio CSP nel tempo e monitorare eventuali interruzioni."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Spesso le liste consentite di host possono essere aggirate. Potresti usare hash o nonce CSP, insieme a \"strict-dynamic\" se necessario."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Istruzione CSP sconosciuta."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} sembra essere una parola chiave non valida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "L'istruzione \"unsafe-inline\" consente l'esecuzione di gestori di eventi e script in-page non sicuri. Potresti usare hash o nonce CSP per consentire singoli script."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Potresti aggiungere l'istruzione \"unsafe-inline\" (ignorata dai browser che supportano nonce/hash) per la compatibilità con le versioni precedenti dei browser."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "L'autorizzazione non verrà coperta dal carattere jolly (*) durante la gestione di `Access-Control-Allow-Headers` da parte del meccanismo CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Le richieste di risorse i cui URL contenevano sia spazi vuoti rimossi `(n|r|t)` sia simboli di minore (`<`) sono bloccati. Per caricare queste risorse, rimuovi i ritorni a capo e codifica i simboli di minore da posizioni come valori degli attributi di elementi."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "L'elemento `chrome.loadTimes()` è deprecato, utilizza invece l'API standardizzata: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "L'elemento `chrome.loadTimes()` è deprecato, utilizza invece l'API standardizzata: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "L'elemento `chrome.loadTimes()` è deprecato, utilizza invece l'API standardizzata: `nextHopProtocol` in Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "I cookie che contengono un carattere `(0|r|n)` verranno rifiutati e non troncati."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Il rilascio del criterio della stessa origine impostando `document.domain` è deprecato e verrà disattivato per impostazione predefinita. Questo avviso di ritiro riguarda un accesso multiorigine che è stato attivato impostando `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "L'attivazione dell'elemento {PH1} da iframe multiorigine è stata deprecata e verrà rimossa in futuro."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Per disattivare l'integrazione predefinita di Cast, utilizza l'attributo `disableRemotePlayback` invece del selettore `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "L'API {PH1} è deprecata. Utilizza invece {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Questo è un esempio di messaggio tradotto relativo a un problema di ritiro."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Il rilascio del criterio della stessa origine impostando `document.domain` è deprecato e verrà disattivato per impostazione predefinita. Per continuare a utilizzare questa funzionalità, disattiva i cluster di agenti in base all'origine inviando un'intestazione `Origin-Agent-Cluster: ?0` insieme alla risposta HTTP per documento e frame. Vedi https://developer.chrome.com/blog/immutable-document-domain/ per maggiori dettagli."}, "core/lib/deprecations-strings.js | eventPath": {"message": "L'API `Event.path` è deprecata e verrà rimossa. Utilizza invece `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "L'intestazione `Expect-CT` è deprecata e verrà rimossa. Chrome richiede Certificate Transparency per tutti i certificati attendibili pubblicamente emessi dopo il 30 aprile 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Per maggiori de<PERSON>, controlla la pagina dello stato della funzionalità."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` e `watchPosition()` non funzionano più su origini non sicure. Per utilizzare questa funzionalità, considera di passare la tua applicazione a un'origine sicura, come HTTPS. Vedi https://goo.gle/chrome-insecure-origins per maggiori dettagli."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "Gli elementi `getCurrentPosition()` e `watchPosition()` su origini non sicure sono deprecati. Per utilizzare questa funzionalità, considera di passare la tua applicazione a un'origine sicura, come HTTPS. Vedi https://goo.gle/chrome-insecure-origins per maggiori dettagli."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` non funziona più su origini non sicure. Per utilizzare questa funzionalità, considera di passare la tua applicazione a un'origine sicura, come HTTPS. Vedi https://goo.gle/chrome-insecure-origins per maggiori dettagli."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "L'elemento `RTCPeerConnectionIceErrorEvent.hostCandidate` è deprecato. Utilizza invece `RTCPeerConnectionIceErrorEvent.address` o `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "L'origine del commerciante e i dati arbitrari dell'evento del service worker di `canmakepayment` sono deprecati e verranno rimossi: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Il sito web ha richiesto una sottorisorsa a una rete a cui può accedere solo a causa della posizione di rete privilegiata dei relativi utenti. Queste richieste espongono dispositivi e servizi non pubblici a Internet, aumentando il rischio di un attacco Cross-Site Request Forgery (CSRF) e/o una fuga di informazioni. Per limitare questi rischi, Chrome ritira le richieste a sottorisorse non pubbliche quando iniziate da contesti non sicuri e le blocca."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "Non è possibile caricare il CSS dagli URL di `file:` a meno che questi non terminino in un'estensione del file `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "L'utilizzo dell'elemento `SourceBuffer.abort()` per interrompere la rimozione dell'intervallo asincrono di `remove()` è deprecato a causa di una modifica di specifica. Il relativo supporto verrà rimosso in futuro. Dovrai invece ascoltare l'evento `updateend`. L'elemento `abort()` è destinato solo a interrompere un allegato multimediale asincrono o a reimpostare lo stato dell'analizzatore sintattico."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "L'impostazione di `MediaSource.duration` al di sotto del timestamp di presentazione più alto di qualsiasi frame codificato con buffer è deprecata a causa di una modifica di specifica. Il supporto della rimozione implicita di contenuti multimediali con buffer troncati verrà rimosso in futuro. Dovresti invece eseguire l'elemento esplicito `remove(newDuration, oldDuration)` su tutti gli elementi `sourceBuffers`, in cui si trova`newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Questa modifica entrerà in vigore per il traguardo {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI richiederà l'autorizzazione per l'utilizzo anche se il valore sysex non è specificato nell'elemento `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "L'uso dell'API Notification da origini non sicure potrebbe non essere più supportato. Dovresti considerare di passare la tua applicazione a un’origine sicura, come HTTPS. Vedi https://goo.gle/chrome-insecure-origins per maggiori dettagli."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "L'autorizzazione per l'API Notification potrebbe non essere più richiesta da un iframe multiorigine. Considera di richiedere l'autorizzazione da un frame di primo livello o aprire invece una nuova finestra."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Il tuo partner sta negoziando una versione (D)TLS obsoleta. Rivolgiti al tuo partner per correggere questo problema."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL in contesti non sicuri è deprecato e verrà rimosso a breve. Utilizza Web Storage o Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Se specifichi `overflow: visible` nei tag img, video e canvas, gli spettatori potrebbero produrre contenuti visivi al di fuori dei limiti dell'elemento. Vedi https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "L'API `paymentManager.instruments` è deprecata. Utilizza l'installazione just-in-time per i gestori dei pagamenti."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "La tua chiamata `PaymentRequest` ha ignorato l'istruzione `connect-src` dei Criteri di sicurezza del contenuto (CSP). L'esclusione è stata ritirata. Aggiungi all'istruzione CSP `connect-src` l'identificatore del metodo di pagamento dall'API `PaymentRequest` (nel campo `supportedMethods`)."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "L'elemento `StorageType.persistent` è deprecato. Utilizza invece l'elemento `navigator.storage` standardizzato."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "L'elemento `<source src>` con un elemento principale `<picture>` non è valido e pertanto viene ignorato. Utilizza invece `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "L'elemento `window.webkitStorageInfo` è deprecato. Utilizza invece l'elemento `navigator.storage` standardizzato."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Le richieste di sottorisorse i cui URL contengono credenziali incorporate (ad es. `**********************/`) sono bloccate."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Il vincolo `DtlsSrtpKeyAgreement` è stato rimosso. Hai specificato un valore `false` per questo vincolo, il che viene interpretato come un tentativo di utilizzo del metodo `SDES key negotiation` rimosso. Questa funzionalità è stata rimossa; utilizza invece un servizio che supporti `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Il vincolo `DtlsSrtpKeyAgreement` è stato rimosso. Hai specificato un valore `true` per questo vincolo, il che non ha avuto alcun effetto, ma puoi rimuovere il vincolo per fare ordine."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Elemento `Complex Plan B SDP` rilevato. Il dialetto dell'elemento `Session Description Protocol` non è più supportato. Utilizza invece `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "La versione `Plan B SDP semantics`, che viene utilizzata quando si crea un elemento `RTCPeerConnection` con `{sdpSemantics:plan-b}`, è una versione precedente non standard del protocollo `Session Description Protocol` che è stata eliminata definitivamente dalla piattaforma web. È comunque disponibile durante la creazione con l'elemento `IS_FUCHSIA`, ma lo rimuoveremo il prima possibile. Non utilizzarlo più. Vedi https://crbug.com/1302249 per controllare lo stato."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "L'opzione `rtcpMuxPolicy` è deprecata e verrà rimossa."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` richiederà l'isolamento multiorigine. Vedi https://developer.chrome.com/blog/enabling-shared-array-buffer/ per maggiori de<PERSON>gli."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "L'API `speechSynthesis.speak()` senza attivazione utente è deprecata e verrà rimossa."}, "core/lib/deprecations-strings.js | title": {"message": "È stata usata una funzionalità deprecata"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Le estensioni dovrebbero attivare l'isolamento multiorigine per continuare a utilizzare `SharedArrayBuffer`. Vedi https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "L'elemento {PH1} è specifico del fornitore. Utilizza invece l'elemento {PH2} standard."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "L'elemento UTF-16 non è supportato dal file json di risposta in `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "L'elemento `XMLHttpRequest` sincrono nel thread principale è deprecato a causa dei suoi effetti negativi sull'esperienza utente finale. Vedi https://xhr.spec.whatwg.org/ per maggiore assistenza."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "L'elemento `supportsSession()` è deprecato. Al suo posto utilizza `isSessionSupported()` e controlla il valore booleano risolto."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Durata blocco thread principale"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL cache"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Descrizione"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementi respinti"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Posizione"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nome"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Oltre il budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Dimensioni risorsa"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo di risorsa"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Dimensioni"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Origine"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tempo trascorso"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Dimensioni trasferimento"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenziali riduzioni"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenziali riduzioni"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenziali riduzioni di {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elemento trovato}other{# elementi trovati}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenziali riduzioni di {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alta"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Bass<PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Media"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "First Input Delay potenziale max"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Contenuti multimediali"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Altro"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Altre risorse"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON>oglio di stile"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Terze parti"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Totale"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Si è verificato un problema con la registrazione della traccia durante il caricamento della pagina. Esegui di nuovo Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Timeout durante l'attesa della connessione iniziale al protocollo del debugger."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome non ha raccolto nessuno screenshot durante il caricamento pagina. Assicurati che nella pagina siano presenti contenuti visibili, quindi riprova a eseguire Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "I server DNS non sono stati in grado di risolvere il dominio fornito."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Il gatherer {artifactName} richiesto ha riscontrato un errore: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Errore interno di Chrome. Riavvia Chrome e prova a eseguire di nuovo Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Il gatherer {artifactName} richiesto non è stato eseguito."}, "core/lib/lh-error.js | noFcp": {"message": "La pagina non ha visualizzato alcun contenuto. Assicurati che la finestra del browser rimanga in primo piano durante il caricamento e riprova. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "La pagina non mostrava contenuti che si qualificano come Largest Contentful Paint (LCP). Assicurati che la pagina abbia un elemento LCP valido, quindi riprova. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "La pagina specificata non è HTML (pubblicata come tipo MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Questa versione di Chrome è troppo vecchia e non supporta \"{featureName}\". Usa una versione più recente per vedere i risultati completi."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse non può completare il caricamento della pagina richiesta. Assicurati che l'URL verificato sia quello corretto e che il server stia rispondendo opportunamente a tutte le richieste."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse non può completare il caricamento dell'URL richiesto perché la pagina non risponde più."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "L'URL fornito non ha un certificato di sicurezza valido. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ha impedito il caricamento della pagina con un interstitial. Assicurati che l'URL verificato sia quello corretto e che il server stia rispondendo opportunamente a tutte le richieste."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Impossibile caricare la pagina richiesta in modo affidabile in Lighthouse. Assicurati che l'URL verificato sia quello corretto e che il server stia rispondendo opportunamente a tutte le richieste. Informazioni dettagliate: {errorDetails}"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Impossibile caricare la pagina richiesta in modo affidabile in Lighthouse. Assicurati che l'URL verificato sia quello corretto e che il server stia rispondendo opportunamente a tutte le richieste. (Codice di stato: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Il caricamento della tua pagina ha richiesto troppo tempo. Segui le opportunità fornite nel rapporto per ridurre il tempo di caricamento della pagina e prova a eseguire di nuovo Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Il tempo di attesa allocato per ricevere una risposta dal protocollo DevTools è scaduto. (Metodo: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Il tempo allocato per il recupero dei contenuti della risorsa è scaduto"}, "core/lib/lh-error.js | urlInvalid": {"message": "L'URL fornito non è valido."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Il tipo MIME della pagina è XHTML: Lighthouse non supporta esplicitamente questo tipo di documento"}, "core/user-flow.js | defaultFlowName": {"message": "Flusso utente ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Report relativo alla navigazione ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Report relativo a un momento specifico ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Report relativo al periodo di tempo ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Tutti i report"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorie"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accessibilità"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Best practice"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Prestazioni"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "App web progressiva"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Desktop"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Informazioni sul report flusso di Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Informazioni sui flussi"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Usa i report relativi alla navigazione per…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Usa i report relativi a un momento specifico per…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Usa i report relativi al periodo di tempo per…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Ottenere un punteggio Lighthouse relativo alle prestazioni."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mi<PERSON>rare le metriche relative alle prestazioni di caricamento pagina quali Largest Contentful Paint e Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Valutare le funzionalità delle app web progressive."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Trovare problemi di accessibilità nelle applicazioni a pagina singola o in moduli complessi."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Valutare best practice di menu ed elementi UI nascosti dietro l'interazione."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Misurare le variazioni di layout e il tempo di esecuzione di JavaScript per una serie di interazioni."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Scoprire opportunità legate alle prestazioni per migliorare l'esperienza relativa alle pagine di lunga durata e alle applicazioni a pagina singola."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON> impatto"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} controllo informativo}other{{numInformative} controlli informativi}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Dispositivi mobili"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Caricamento della pagina"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "I report relativi alla navigazione consentono di analizzare il caricamento di una singola pagina, esattamente come i report Lighthouse originali."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Report relativo alla navigazione"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} report relativo alla navigazione}other{{numNavigation} report relativi alla navigazione}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} controllo superabile}other{{numPassableAudits} controlli superabili}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} controllo superato}other{{numPassed} controlli superati}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Nella media"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Errore"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Sc<PERSON>nte"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Stato acquisito della pagina"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "I report relativi a un momento specifico consentono di analizzare la pagina in uno stato specifico, generalmente dopo le interazioni degli utenti."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Report relativo a un momento specifico"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} report relativo a un momento specifico}other{{numSnapshot} report relativi a un momento specifico}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Riepilogo"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interazioni dell'utente"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "I report relativi al periodo di tempo consentono di analizzare un periodo di tempo arbitrario, generalmente durante il quale ci sono state interazioni degli utenti."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Report relativo al periodo di tempo"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} report relativo al periodo di tempo}other{{numTimespan} report relativi al periodo di tempo}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Report Lighthouse sulla procedura"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Per i contenuti animati, usa [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) per ridurre al minimo l'utilizzo di CPU mentre i contenuti sono fuori dallo schermo."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Potresti visualizzare tutti i componenti [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) nei formati WebP e specificare un'immagine di riserva appropriata per altri browser. [Ulteriori informazioni](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Assicurati di usare un attributo [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) per attivare il caricamento lento automatico delle immagini. [Ulteriori informazioni](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Usa strumenti quali lo [strumento di ottimizzazione AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) per [eseguire il rendering lato server dei layout AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulta la [documentazione relativa ad AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) per verificare che tutti i tuoi stili siano supportati."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Il componente [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) supporta l'attributo [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), che consente di specificare quali asset immagine usare in base alle dimensioni dello schermo. [Ulteriori informazioni](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Potresti usare lo scorrimento virtuale con Component Dev Kit (CDK) se viene eseguito il rendering di elenchi di dimensioni molto grandi. [Ulteriori informazioni](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Applica la [suddivisione del codice a livello di route](https://web.dev/route-level-code-splitting-in-angular/) per ridurre al minimo le dimensioni dei bundle JavaScript. Potresti anche memorizzare anticipatamente nella cache gli asset usando il [service worker Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Se usi l'interfaccia a riga di comando Angular, assicurati che le build vengano generate in modalità di produzione. [Ulteriori informazioni](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Se usi l'interfaccia a riga di comando Angular, includi mappe di origine nella build di produzione per controllare i tuoi bundle. [Ulteriori informazioni](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Precarica in anticipo le route per velocizzare la navigazione. [Ulteriori informazioni](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Potresti usare l'utility `BreakpointObserver` di Component Dev Kit (CDK) per gestire i punti di interruzione delle immagini. [Ulteriori informazioni](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Potresti caricare la tua GIF su un servizio che la renda disponibile per l'incorporamento come video HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Specifica `@font-display` durante la definizione dei caratteri personalizzati per il tuo tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Sul tuo sito potresti configurare [formati delle immagini WebP con uno stile per le immagini Converti](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installa [un modulo Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) che consenta il caricamento lento delle immagini. Questi moduli offrono la possibilità di rimandare qualsiasi immagine fuori schermo per migliorare le prestazioni."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Potresti usare un modulo per incorporare codice JavaScript e CSS fondamentale o potenzialmente caricare asset in modo asincrono tramite JavaScript, come il modulo [CSS avanzato/Aggregazione JS](https://www.drupal.org/project/advagg). Fai attenzione perché le ottimizzazioni offerte da questo modulo potrebbero interrompere la funzionalità del tuo sito, pertanto potresti dover apportare modifiche al codice."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Le specifiche del server, i moduli e i temi contribuiscono al tempo di risposta del server. Potresti usare un tema più ottimizzato, selezionando con cura un modulo per l'ottimizzazione e/o eseguendo l'upgrade del server. I server hosting dovrebbero usare la memorizzazione nella cache opcode PHP, la memorizzazione nella cache per ridurre i tempi di query del database come Redis o memcache e una logica dell'applicazione ottimizzata per preparare più rapidamente le pagine."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Potresti usare gli [stili di immagini adattabili](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) per ridurre le dimensioni delle immagini caricate nella tua pagina. Se usi Views per mostrare diversi contenuti su una pagina, potresti implementare l'impaginazione per limitare il numero di contenuti mostrati su una data pagina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Assicurati di aver attivato l'opzione \"Aggregate CSS files\" (Aggrega file CSS) nella pagina \"Administration » Configuration » Development\" (Amministrazione » Configurazione » Sviluppo). Inoltre, puoi configurare altre opzioni di aggregazione avanzate tramite i [moduli aggiuntivi](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) per velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi stili CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Assicurati di aver attivato l'opzione \"Aggregate JavaScript files\" (Aggrega file JavaScript) nella pagina \"Administration » Configuration » Development\" (Amministrazione » Configurazione » Sviluppo). Inoltre, puoi configurare altre opzioni di aggregazione avanzate tramite i [moduli aggiuntivi](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) per velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi asset JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Potresti rimuovere le regole CSS inutilizzate e collegare solo le librerie Drupal necessarie alle pagine o ai componenti in una pagina pertinenti. Per i dettagli, apri il [link alla documentazione relativa a Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Per individuare le librerie collegate che aggiungono CSS estraneo, prova a eseguire la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. Puoi individuare il tema/modulo responsabile nell'URL del foglio di stile quando l'aggregazione CSS è disattivata sul tuo sito Drupal. Cerca i temi/moduli che nell'elenco hanno diversi fogli di stile con molto rosso nella copertura del codice. Un tema/modulo dovrebbe aggiungere in coda un foglio di stile solo se viene effettivamente utilizzato nella pagina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Potresti rimuovere gli asset JavaScript inutilizzati e collegare solo le librerie Drupal necessarie alle pagine o ai componenti in una pagina pertinenti. Per informazioni dettagliate, apri il [link che rimanda alla documentazione relativa a Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Per individuare le librerie collegate che aggiungono JavaScript estraneo, prova a eseguire la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Strumenti per sviluppatori di Chrome. Puoi individuare il tema/modulo responsabile nell'URL dello script quando l'aggregazione JavaScript è disattivata sul tuo sito Drupal. Cerca i temi/moduli che nell'elenco hanno diversi script con molto rosso nella copertura del codice. Un tema/modulo dovrebbe aggiungere in coda uno script solo se viene effettivamente utilizzato nella pagina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Imposta l'opzione \"Browser and proxy cache maximum age\" (Durata massima cache browser e proxy) nella pagina \"Administration » Configuration » Development\" (Amministrazione » Configurazione » Sviluppo). Leggi informazioni su [cache e ottimizzazione di Drupal per le prestazioni](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Potresti usare [un modulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) che ottimizza e riduce automaticamente le dimensioni delle immagini caricate tramite il sito preservandone la qualità. Inoltre, assicurati di stare utilizzando gli [stili di immagini adattabili](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) nativi forniti da Drupal (disponibili in Drupal 8 e versioni successive) per tutte le immagini visualizzate sul sito."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Puoi aggiungere hint di precollegamento o prelettura DNS delle risorse installando e configurando [un modulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) che offre servizi per gli hint delle risorse user agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Assicurati di stare utilizzando gli [stili di immagini adattabili](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) nativi forniti da Drupal (disponibili in Drupal 8 e versioni successive). Usa gli stili di immagini adattabili per il rendering dei campi immagine con modalità di visualizzazione, visualizzazioni o immagini caricate tramite l'editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Optimize Fonts` per usare automaticamente la funzionalità CSS `font-display` in modo da garantire che il testo sia visibile all'utente mentre è in corso il caricamento dei caratteri web."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Next-Gen Formats` per convertire le immagini in WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Lazy Load Images` per rimandare il caricamento delle immagini nell'area non visibile finché non sono necessarie."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Critical CSS` e `Script Delay` per rimandare le risorse JS/CSS non fondamentali."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Usa [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) per memorizzare i tuoi contenuti nella cache della nostra rete globale e ridurre il tempi per il primo byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Minify CSS` per minimizzare automaticamente il CSS in modo da ridurre le dimensioni dei payload di rete."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Minify Javascript` per minimizzare automaticamente il JS in modo da ridurre le dimensioni dei payload di rete."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Remove Unused CSS` per aiutarti a risolvere il problema. In questo modo verranno identificate le classi CSS attualmente in uso in ogni pagina del tuo sito e le altre verranno rimosse per contenere le dimensioni del file."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Efficient Static Cache Policy` per impostare i valori consigliati nell'intestazione per la memorizzazione nella cache degli asset statici."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Next-Gen Formats` per convertire le immagini in WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Pre-Connect Origins` per aggiungere automaticamente gli hint delle risorse `preconnect` per collegarti anticipatamente a importanti origini di terze parti."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Preload Fonts` e `Preload Background Images` per aggiungere link di `preload` per dare la priorità al recupero delle risorse attualmente richieste in un secondo momento nel caricamento della pagina."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) e attiva `Resize Images` per ridimensionare le immagini in modo appropriato per il dispositivo e contenere le dimensioni dei payload di rete."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Potresti caricare la tua GIF su un servizio che la renda disponibile per l'incorporamento come video HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Potresti usare un [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) o un servizio che converta automaticamente le immagini caricate nei formati ottimali."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installa un [plug-in Joomla per il caricamento lento](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) che consenta di rimandare qualsiasi immagine fuori schermo oppure passa a un modello che offra questa funzionalità. A partire da Joomla 4.0, tutte le nuove immagini riceveranno [automaticamente](https://github.com/joomla/joomla-cms/pull/30748) l'attributo `loading` dal core."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Esistono diversi plug-in Joomla che possono esserti utili per [incorporare asset di importanza critica](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) o [rimandare le risorse meno importanti](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Fai attenzione perché le ottimizzazioni offerte da questi plug-in potrebbero interrompere le funzionalità dei tuoi modelli o plug-in, pertanto potresti dover effettuare un test accurato."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Le specifiche del server, i modelli e le estensioni contribuiscono al tempo di risposta del server. Potresti usare un modello più ottimizzato, selezionando con cura un'estensione per l'ottimizzazione e/o eseguendo l'upgrade del server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Potresti mostrare degli estratti nelle categorie dei tuoi articoli (ad esempio utilizzando il link \"Ulteriori informazioni\"), riducendo il numero di articoli che vengono mostrati su una determinata pagina, dividendo i post più lunghi in più pagine o utilizzando un plug-in per il caricamento lento dei commenti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Esistono diverse [estensioni di Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) in grado di velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi stili CSS. Ci sono anche dei modelli che offrono questa funzionalità."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Esistono diverse [estensioni di Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) in grado di velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi script. Ci sono anche dei modelli che offrono questa funzionalità."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Potresti ridurre, o cambiare, il numero di [estensioni di Joomla](https://extensions.joomla.org/) che caricano file CSS inutilizzati nella tua pagina. Per individuare le estensioni che aggiungono CSS estraneo, prova a eseguire la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. Puoi individuare il tema/plug-in responsabile nell'URL del foglio di stile. Cerca i plug-in che nell'elenco hanno diversi fogli di stile con molto rosso nella copertura del codice. Un plug-in dovrebbe aggiungere in coda un foglio di stile solo se la pagina lo utilizza davvero."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Po<PERSON>ti ridurre, o cambiare, il numero di [estensioni di Joomla](https://extensions.joomla.org/) che caricano file JavaScript inutilizzati nella tua pagina. Per individuare i plug-in che aggiungono JavaScript estraneo, prova a eseguire la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Chrome DevTools. Puoi individuare l'estensione responsabile nell'URL dello script. Cerca le estensioni che nell'elenco hanno diversi script con molto rosso nella copertura del codice. Un'estensione dovrebbe aggiungere in coda uno script solo se viene effettivamente utilizzato nella pagina."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Leggi informazioni sulla [memorizzazione nella cache del browser in Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Potresti usare un [plug-in per l'ottimizzazione delle immagini](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) in grado di comprimere le tue immagini preservandone la qualità."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Potresti usare un [plug-in di immagini adattabili](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) per usare questo tipo di immagini nei tuoi contenuti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Puoi attivare la compressione del testo abilitando la compressione Gzip della pagina in Joomla: System (Sistema) > Global configuration (Configurazione globale) > Server."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Se non crei bundle per gli asset JavaScript, potresti usare [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Disattiva le funzionalità di [minimizzazione e creazione di bundle JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) incorporate in Magento; al loro posto potresti usare [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Specifica `@font-display` quando [definisci caratteri personalizzati](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Potresti cercare sul [Marketplace Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) una serie di estensioni di terze parti per utilizzare i formati di immagine più recenti."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Potresti modificare i modelli del prodotto e del catalogo in modo da usare la funzionalità di [caricamento lento](https://web.dev/native-lazy-loading) della piattaforma web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Usa l'[integrazione Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) di Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Attiva l'opzione per minimizzare i file CSS nelle impostazioni sviluppatore del tuo store. [Ulteriori informazioni](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Usa [Terser](https://www.npmjs.com/package/terser) per minimizzare tutti gli asset JavaScript del deployment dei contenuti statici e disattiva la funzionalità di minimizzazione incorporata."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Disattiva la [funzionalità di creazione di bundle JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) incorporata in Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Potresti cercare sul [Marketplace Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) una serie di estensioni di terze parti per ottimizzare le immagini."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Puoi aggiungere hint di precollegamento o prelettura DNS delle risorse [modificando il layout di un tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Puoi aggiungere i tag `<link rel=preload>` [modificando il layout di un tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Usa il componente `next/image` anziché `<img>` per ottimizzare automaticamente il formato delle immagini. [Scopri di più](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Usa il componente `next/image` anziché `<img>` per attivare il caricamento lento automatico delle immagini. [Scopri di più](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Usa il componente `next/image` e imposta la \"priorità\" su vero per precaricare l'immagine LCP. [Scopri di più](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Utilizza il componente `next/script` per rimandare il caricamento di script di terze parti non critici. [Scopri di più](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Usa il componente `next/image` per assicurarti che le immagini abbiano sempre le dimensioni appropriate. [Scopri di più](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON> impostare `PurgeCSS` nella configurazione `Next.js` per rimuovere le regole non utilizzate dai fogli di stile. [Scopri di più](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Usa `Webpack Bundle Analyzer` per rilevare codice JavaScript non utilizzato. [Scopri di più](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Po<PERSON>ti usare `Next.js Analytics` per misurare le prestazioni reali della tua app. [Scopri di più](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configura la memorizzazione nella cache per asset e pagine `Server-side Rendered` (SSR) non modificabili. [Scopri di più](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Usa il componente `next/image` anziché `<img>` per regolare la qualità delle immagini. [Scopri di più](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Utilizza il componente `next/image` per impostare il valore `sizes` appropriato. [Scopri di più](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Attiva la compressione sul tuo server Next.js. [<PERSON><PERSON><PERSON> di più](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Usa il componente `nuxt/image` e imposta `format=\"webp\"`. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Usa il componente `nuxt/image` e imposta `loading=\"lazy\"` per le immagini non mostrate sullo schermo. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Usa il componente `nuxt/image` e specifica `preload` per l'immagine LCP. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Usa il componente `nuxt/image` e specifica i valori espliciti `width` e `height`. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Usa il componente `nuxt/image` e imposta il valore `quality` appropriato. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Usa il componente `nuxt/image` e imposta il valore `sizes` appropriato. [Scopri di più](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Sostituisci le GIF animate con i video](https://web.dev/replace-gifs-with-videos/) per caricamenti della pagina web più rapidi e valuta l'utilizzo di formati file moderni come [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) o [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) per migliorare l'efficienza della compressione di oltre il 30% rispetto al codec video attualmente più avanzato, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Valuta l'utilizzo di un [plug-in](https://octobercms.com/plugins?search=image) o di un servizio che converta automaticamente le immagini caricate nei formati ottimali. Le [immagini WebP senza perdita](https://developers.google.com/speed/webp) sono il 26% più piccole rispetto a quelle in formato PNG e il 25-34% più piccole rispetto alle immagini JPEG in base all'indice di qualità SSIM equivalente. Un altro formato dell'immagine innovativo da valutare è [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Valuta l'installazione di un [plugin per il caricamento lento delle immagini](https://octobercms.com/plugins?search=lazy) che consenta di rimandare qualsiasi immagine fuori schermo oppure passa a un tema che offra questa funzionalità. Potresti anche valutare l'utilizzo del [plug-in AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Esistono diversi plug-in che possono essere utili per [incorporare le risorse di importanza critica](https://octobercms.com/plugins?search=css). Questi plug-in potrebbero interrompere altri plug-in, pertanto dovresti effettuare un test accurato."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Le specifiche del server, i plug-in e i temi contribuiscono al tempo di risposta del server. Valuta l'utilizzo di un tema più ottimizzato, selezionando con cura un plug-in di ottimizzazione e/o eseguendo l'upgrade del server. Il CMS October consente inoltre agli sviluppatori di utilizzare [`Queues`](https://octobercms.com/docs/services/queues) per rimandare l'elaborazione di un'attività lunga, ad esempio l'invio di un'email. In questo modo le richieste web si velocizzano drasticamente."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Potresti mostrare degli estratti negli elenchi dei tuoi post (ad es. utilizzando un pulsante `show more`), ridurre il numero di post che vengono mostrati su una determinata pagina web, dividere i post più lunghi su più pagine o utilizzare un plug-in per il caricamento lento dei commenti."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Esistono diversi [plugin](https://octobercms.com/plugins?search=css) in grado di velocizzare un sito web tramite la concatenazione, la minimizzazione e la compressione degli stili. L'utilizzo di un processo di compilazione per eseguire la minimizzazione in anticipo può velocizzare lo sviluppo."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Esistono diversi [plug-in](https://octobercms.com/plugins?search=javascript) in grado di velocizzare un sito web tramite la concatenazione, la minimizzazione e la compressione degli script. L'utilizzo di un processo di compilazione per eseguire la minimizzazione in anticipo può velocizzare lo sviluppo."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Valuta la revisione dei [plug-in](https://octobercms.com/plugins) che caricano CSS inutilizzato sul sito web. Per identificare i plug-in che aggiungono CSS non necessario, esegui la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Strumenti per sviluppatori di Chrome. Individua il tema/plugin responsabile dall'URL del foglio di stile. Cerca i plug-in che hanno diversi fogli di stile con molto rosso nella copertura del codice. Un plug-in dovrebbe aggiungere un foglio di stile solo se viene effettivamente utilizzato nella pagina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Valuta la revisione dei [plug-in](https://octobercms.com/plugins?search=javascript) che caricano JavaScript inutilizzati nella pagina web. Per identificare i plug-in che aggiungono JavaScript non necessari, esegui la [copertura del codice](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in Strumenti per sviluppatori di Chrome. Individua il tema/plug-in responsabile nell'URL dello script. Cerca i plug-in che hanno diversi script con molto rosso nella copertura del codice. Un plug-in dovrebbe aggiungere uno script solo se viene effettivamente utilizzato nella pagina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Scopri come [impedire le richieste di rete non necessarie con la cache HTTP](https://web.dev/http-cache/#caching-checklist). Esistono numerosi [plug-in](https://octobercms.com/plugins?search=Caching) che si possono utilizzare per velocizzare la memorizzazione nella cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Valuta l'utilizzo di un [plug-in per l'ottimizzazione delle immagini](https://octobercms.com/plugins?search=image) per comprimere le tue immagini preservandone la qualità."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Carica le immagini direttamente sul gestore contenuti multimediali per assicurarti che le dimensioni delle immagini richieste siano disponibili. Valuta l'utilizzo del [filtro di ridimensionamento](https://octobercms.com/docs/markup/filter-resize) o di un [plug-in per il ridimensionamento delle immagini](https://octobercms.com/plugins?search=image) per assicurarti che vengano utilizzate le dimensioni delle immagini ottimali."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Attiva la compressione del testo nella configurazione del server web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Valuta l'utilizzo di una libreria di \"windowing\" come `react-window` per ridurre al minimo il numero di nodi DOM creati se esegui il rendering di tanti elementi ripetuti nella pagina. [Scopri di più](https://web.dev/virtualize-long-lists-react-window/) Riduci al minimo anche le ripetizioni del rendering superflue usando [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) o [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) e [salta gli effetti](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) soltanto fino alla modifica di determinate dipendenze se usi l'hook `Effect` per migliorare le prestazioni di runtime."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Se usi React Router, riduci al minimo l'utilizzo del componente `<Redirect>` per la [navigazione tra route](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Se usi il rendering lato server per i componenti React, potresti usare `renderToPipeableStream()` o `renderToStaticNodeStream()` per consentire al client di ricevere e compilare parti diverse del markup anziché tutto il markup contemporaneamente. [Ulteriori informazioni](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Se il tuo sistema di compilazione minimizza automaticamente i file CSS, assicurati di eseguire il deployment della build di produzione della tua applicazione. Puoi verificare usando l'estensione React Developer Tools. [Ulteriori informazioni](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Se il tuo sistema di compilazione minimizza automaticamente i file JS, assicurati di eseguire il deployment della build di produzione della tua applicazione. Puoi verificare usando l'estensione React Developer Tools. [Ulteriori informazioni](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Se non usi il rendering lato server, [suddividi i tuoi bundle JavaScript](https://web.dev/code-splitting-suspense/) con `React.lazy()`. In alternativa, suddividi il codice usando una libreria di terze parti come [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Usa lo strumento React DevTools Profiler, che usa l'API Profiler, per misurare le prestazioni di rendering dei tuoi componenti. [Ulteriori informazioni](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Potresti caricare la tua GIF su un servizio che la renda disponibile per l'incorporamento come video HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Considera l'utilizzo del plug-in [Performance Lab](https://wordpress.org/plugins/performance-lab/) per convertire automaticamente le immagini JPEG caricate in formato WebP, ovunque sia supportato."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installa un [plug-in di WordPress per il caricamento lento](https://wordpress.org/plugins/search/lazy+load/) che consenta di rimandare qualsiasi immagine fuori schermo oppure passa a un tema che offra questa funzionalità. Potresti anche usare [il plug-in AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Esistono diversi plug-in di WordPress che possono esserti utili per [incorporare le risorse di importanza critica](https://wordpress.org/plugins/search/critical+css/) o [rimandare le risorse meno importanti](https://wordpress.org/plugins/search/defer+css+javascript/). Fai attenzione perché le ottimizzazioni offerte da questi plug-in potrebbero interrompere le funzionalità del tuo tema o dei tuoi plug-in, pertanto potresti dover modificare il codice."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Le specifiche del server, i plug-in e i temi contribuiscono al tempo di risposta del server. Prendi in considerazione l'idea di utilizzare un tema più ottimizzato, selezionando con cura un plug-in per l'ottimizzazione e/o eseguendo l'upgrade del server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Prendi in considerazione la possibilità di mostrare degli estratti nell'elenco dei tuoi post (ad esempio utilizzando il tag more), riducendo il numero di post che vengono mostrati su una determinata pagina, spezzando i post più lunghi su più pagine o utilizzando un plug-in per il caricamento differito dei commenti."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Esistono diversi [plug-in di WordPress](https://wordpress.org/plugins/search/minify+css/) in grado di velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi stili. Se possibile, puoi anche utilizzare un processo di compilazione per eseguire la minimizzazione in anticipo."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Esistono diversi [plug-in di WordPress](https://wordpress.org/plugins/search/minify+javascript/) in grado di velocizzare il tuo sito concatenando, minimizzando e comprimendo i tuoi script. Se possibile, puoi anche utilizzare un processo di compilazione per eseguire la minimizzazione in anticipo."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Potresti ridurre, o cambiare, il numero di [plug-in di WordPress](https://wordpress.org/plugins/) che caricano file CSS inutilizzati nella tua pagina. Per individuare i plug-in che aggiungono CSS estraneo, prova a eseguire la [copertura del codice](https://developer.chrome.com/docs/devtools/coverage/) in Chrome DevTools. Puoi individuare il tema o il plug-in responsabile nell'URL del foglio di stile. Cerca i plug-in che nell'elenco hanno diversi fogli di stile con molto rosso nella copertura del codice. Un plug-in dovrebbe aggiungere in coda un foglio di stile solo se viene effettivamente utilizzato nella pagina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON> ridurre, o cambiare, il numero di [plug-in di WordPress](https://wordpress.org/plugins/) che caricano file JavaScript inutilizzati nella tua pagina. Per individuare i plug-in che aggiungono JavaScript estraneo, prova a eseguire la [copertura del codice](https://developer.chrome.com/docs/devtools/coverage/) in Chrome DevTools. Puoi individuare il tema o il plug-in responsabile nell'URL dello script. Cerca i plug-in che nell'elenco hanno diversi script con molto rosso nella copertura del codice. Un plug-in dovrebbe aggiungere in coda uno script solo se viene effettivamente utilizzato nella pagina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Leggi informazioni sulla [memorizzazione nella cache del browser in WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Potresti usare un [plug-in di WordPress per l'ottimizzazione delle immagini](https://wordpress.org/plugins/search/optimize+images/) in grado di comprimere le tue immagini preservandone la qualità."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Carica le immagini direttamente tramite la [libreria multimediale](https://wordpress.org/support/article/media-library-screen/) per assicurarti che siano disponibili le dimensioni delle immagini richieste, quindi inseriscile dalla libreria multimediale o utilizza il widget per immagini per assicurarti che vengano utilizzate le dimensioni ottimali (incluse quelle per i punti di interruzione adattabili). Evita di utilizzare immagini `Full Size`, a meno che le dimensioni siano adatte all'utilizzo previsto. [Ulteriori informazioni](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Puoi attivare la compressione del testo nella configurazione del server web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Attiva \"Imagify\" dalla scheda Ottimizzazione immagine in \"WP Rocket\" per convertire le tue immagini in WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Attiva [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) in WP Rocket per correggere questo consiglio. Questa funzionalità ritarda il caricamento delle immagini finché il visitatore non scorre la pagina verso il basso e non ha bisogno di visualizzarle."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Attiva [R<PERSON><PERSON><PERSON> CSS inutilizzato](https://docs.wp-rocket.me/article/1529-remove-unused-css) e [Carica JavaScript differito](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) in \"WP Rocket\" per risolvere questo suggerimento. Queste funzionalità ottimizzano rispettivamente i file CSS e JavaScript in modo che non blocchino il rendering della pagina."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Attiva [Minimizza file CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) in \"WP Rocket\" per risolvere questo problema. Gli spazi e i commenti presenti nei file CSS del tuo sito verranno rimossi per ridurre le dimensioni del file e velocizzarne il download."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Attiva [Minimizza file JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) in \"WP Rocket\" per risolvere questo problema. Gli spazi e i commenti vuoti verranno rimossi dai file JavaScript per ridurre le dimensioni e velocizzare il download."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Attiva [R<PERSON><PERSON><PERSON> CSS inutilizzato](https://docs.wp-rocket.me/article/1529-remove-unused-css) in \"WP Rocket\" per risolvere il problema. Riduce le dimensioni della pagina rimuovendo tutti i CSS e i fogli di stile che non vengono utilizzati, mantenendo al tempo stesso solo il CSS utilizzato per ogni pagina."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Attiva [Ritarda l'esecuzione di JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) in \"WP Rocket\" per risolvere questo problema. Migliorerà il caricamento della pagina ritardando l'esecuzione degli script fino all'interazione dell'utente. Se il tuo sito contiene iframe, puoi utilizzare anche [LazyLoad per iframe e video](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) di WP Rocket e [Sostituisci l'iframe di YouTube con un'immagine di anteprima](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Attiva \"Imagify\" dalla scheda Ottimizzazione immagine in \"WP Rocket\" ed esegui l'ottimizzazione collettiva per comprimere le immagini."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Usa le [Richieste DNS di precaricamento](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) in \"WP Rocket\" per aggiungere \"dns-prefetch\" e velocizzare la connessione con domini esterni. Inoltre, \"WP Rocket\" aggiunge automaticamente il comando \"preconnect\" al [dominio di Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) e a qualsiasi CNAME aggiunto tramite la funzionalità [Abilita CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Per risolvere il problema relativo ai caratteri, attiva [Rimuovi CSS inutilizzato](https://docs.wp-rocket.me/article/1529-remove-unused-css) in \"WP Rocket\". I caratteri critici del tuo sito verranno precaricati con priorità."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Vai al calcolatore."}, "report/renderer/report-utils.js | collapseView": {"message": "Comprimi visualizzazione"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navigazione iniziale"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latenza massima del percorso critico:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copia JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Attiva/disattiva Tema scuro"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Stampa espansa"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Stampa riepilogo"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON> come Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Salva come HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "<PERSON><PERSON> come JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Apri nell'app Viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "Errore"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Errore segnalato: nessuna informazione sul controllo"}, "report/renderer/report-utils.js | expandView": {"message": "Espandi visualizzazione"}, "report/renderer/report-utils.js | footerIssue": {"message": "Se<PERSON>la un problema"}, "report/renderer/report-utils.js | hide": {"message": "Nascondi"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Dati di prova controllati"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) della pagina corrente su una rete mobile emulata. I valori sono delle stime e potrebbero variare."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Ulteriori elementi da controllare manualmente"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Non applicabile"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Opportunità"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> stimati"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Controlli superati"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Caricamento pagina iniziale"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitazione personalizzata"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Desktop emulato"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Nessuna emulazione"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versione Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potenza CPU/memoria non limitata"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitazione CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositivo"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitazione della larghezza di banda della rete"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulazione schermo"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User-agent (rete)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Caricamento singola pagina"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Questi dati riguardano il caricamento di una singola pagina, al contrario dei dati del campo che riepilogano tante sessioni."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitazione a 4G lenta"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Mostra"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostra controlli relativi a:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Comp<PERSON>i snippet"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON> snippet"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostra risorse di terze parti"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Fornita dall'ambiente"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Si sono verificati dei problemi che incidono su questa esecuzione di Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "I valori sono delle stime e potrebbero variare. Il [punteggio relativo alle prestazioni viene calcolato](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) direttamente in base a queste metriche."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Visualizza traccia originale"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Visualizza traccia"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Visualizza Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Controlli superati ma con avvisi"}, "report/renderer/report-utils.js | warningHeader": {"message": "Avvisi: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Tutti gli script"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Byte risorsa"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nome"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Mostra/nascondi tabella"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Byte non utilizzati"}}