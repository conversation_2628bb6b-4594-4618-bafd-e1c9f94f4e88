{"version": 3, "file": "ArrayUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/ArrayUtilities.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,yEAAyE;AACzE,6BAA6B;AAE7B,MAAM,CAAC,MAAM,aAAa,GAAG,CAAI,KAAU,EAAE,OAAU,EAAE,SAAmB,EAAW,EAAE;IACvF,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACnC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,SAAS,EAAE,CAAC;QACd,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QACrD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;YACzB,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAIF,SAAS,IAAI,CAAC,KAAe,EAAE,EAAU,EAAE,EAAU;IACnD,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;IACvB,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;IACtB,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACnB,CAAC;AAED,SAAS,SAAS,CACd,KAAe,EAAE,UAA4B,EAAE,IAAY,EAAE,KAAa,EAAE,UAAkB;IAChG,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;IACrC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAC/B,IAAI,UAAU,GAAG,IAAI,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;QAClC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;YAC3B,EAAE,UAAU,CAAC;QACf,CAAC;IACH,CAAC;IACD,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAC/B,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,cAAc,CACnB,KAAe,EAAE,UAA4B,EAAE,IAAY,EAAE,KAAa,EAAE,cAAsB,EAClG,eAAuB;IACzB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IACD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;IACrE,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAC5E,IAAI,cAAc,GAAG,aAAa,EAAE,CAAC;QACnC,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;IAC9F,CAAC;IACD,IAAI,aAAa,GAAG,eAAe,EAAE,CAAC;QACpC,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE,aAAa,GAAG,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;IAC/F,CAAC;AACH,CAAC;AAED,MAAM,UAAU,SAAS,CACrB,KAAe,EAAE,UAA4B,EAAE,SAAiB,EAAE,UAAkB,EAAE,cAAsB,EAC5G,eAAuB;IACzB,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,KAAK,CAAC,IAAI,eAAe,IAAI,UAAU,EAAE,CAAC;QAClH,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;IAC5F,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AACD,MAAM,CAAC,MAAM,aAAa,GAAG,CAAO,KAAU,EAAE,KAAQ,EAAE,UAAkC,EAAU,EAAE;IACtG,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACnD,OAAO,KAAK,GAAG,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC;AAEF,SAAS,gBAAgB,CACrB,MAAW,EAAE,MAAW,EAAE,UAAkC,EAAE,iBAA0B;IAC1F,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAC9C,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI,iBAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACtB,CAAC,EAAE,CAAC;QACN,CAAC;QACD,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACtB,CAAC,EAAE,CAAC;QACN,CAAC;IACH,CAAC;IACD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAI,MAAW,EAAE,MAAW,EAAE,UAAkC,EAAO,EAAE;IACvG,OAAO,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAI,MAAW,EAAE,MAAW,EAAE,UAAkC,EAAO,EAAE;IACnG,OAAO,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,CAAgB,EAAE,CAAgB,EAAU,EAAE;IAC/E,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC;AAyBF,MAAM,UAAU,UAAU,CACtB,KAAQ,EAAE,MAAS,EAAE,UAAuC,EAAE,IAAa,EAAE,KAAc;IAC7F,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAClB,IAAI,CAAC,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACb,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;QACR,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAsBD,MAAM,UAAU,UAAU,CACtB,KAAQ,EAAE,MAAS,EAAE,UAAuC,EAAE,IAAa,EAAE,KAAc;IAC7F,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAClB,IAAI,CAAC,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACb,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;QACR,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAMD;;;;;;;;;;;GAWG;AACH,SAAS,YAAY,CACjB,GAAiB,EAAE,SAAoC,EAAE,WAA+B;IAC1F,MAAM,aAAa,GAAG,WAAW,uCAA2B,CAAC;IAC7D,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,GAAG,CAAC;QACF,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/D,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACzC,cAAc,GAAG,gBAAgB,KAAK,aAAa,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC,QAAQ,KAAK,KAAK,IAAI,EAAE;IAEzB,kEAAkE;IAClE,iEAAiE;IACjE,aAAa;IACb,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,yBAAyB,CAAI,GAAQ,EAAE,SAAoC;IACzF,OAAO,YAAY,CAAC,GAAG,EAAE,SAAS,iDAA+B,CAAC;AACpE,CAAC;AAED;;;;;;;;GAQG;AAEH,MAAM,UAAU,mBAAmB,CAAI,GAAiB,EAAE,SAAoC;IAC5F,OAAO,YAAY,CAAC,GAAG,EAAE,SAAS,qCAAyB,CAAC;AAC9D,CAAC;AAED,wEAAwE;AACxE,MAAM,UAAU,kCAAkC,CAAI,GAAyB;IAC7E,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACzD,CAAC", "sourcesContent": ["// Copyright (c) 2020 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const removeElement = <T>(array: T[], element: T, firstOnly?: boolean): boolean => {\n  let index = array.indexOf(element);\n  if (index === -1) {\n    return false;\n  }\n  if (firstOnly) {\n    array.splice(index, 1);\n    return true;\n  }\n  for (let i = index + 1, n = array.length; i < n; ++i) {\n    if (array[i] !== element) {\n      array[index++] = array[i];\n    }\n  }\n  array.length = index;\n  return true;\n};\n\ntype NumberComparator = (a: number, b: number) => number;\n\nfunction swap(array: number[], i1: number, i2: number): void {\n  const temp = array[i1];\n  array[i1] = array[i2];\n  array[i2] = temp;\n}\n\nfunction partition(\n    array: number[], comparator: NumberComparator, left: number, right: number, pivotIndex: number): number {\n  const pivotValue = array[pivotIndex];\n  swap(array, right, pivotIndex);\n  let storeIndex = left;\n  for (let i = left; i < right; ++i) {\n    if (comparator(array[i], pivotValue) < 0) {\n      swap(array, storeIndex, i);\n      ++storeIndex;\n    }\n  }\n  swap(array, right, storeIndex);\n  return storeIndex;\n}\n\nfunction quickSortRange(\n    array: number[], comparator: NumberComparator, left: number, right: number, sortWindowLeft: number,\n    sortWindowRight: number): void {\n  if (right <= left) {\n    return;\n  }\n  const pivotIndex = Math.floor(Math.random() * (right - left)) + left;\n  const pivotNewIndex = partition(array, comparator, left, right, pivotIndex);\n  if (sortWindowLeft < pivotNewIndex) {\n    quickSortRange(array, comparator, left, pivotNewIndex - 1, sortWindowLeft, sortWindowRight);\n  }\n  if (pivotNewIndex < sortWindowRight) {\n    quickSortRange(array, comparator, pivotNewIndex + 1, right, sortWindowLeft, sortWindowRight);\n  }\n}\n\nexport function sortRange(\n    array: number[], comparator: NumberComparator, leftBound: number, rightBound: number, sortWindowLeft: number,\n    sortWindowRight: number): number[] {\n  if (leftBound === 0 && rightBound === (array.length - 1) && sortWindowLeft === 0 && sortWindowRight >= rightBound) {\n    array.sort(comparator);\n  } else {\n    quickSortRange(array, comparator, leftBound, rightBound, sortWindowLeft, sortWindowRight);\n  }\n  return array;\n}\nexport const binaryIndexOf = <T, S>(array: T[], value: S, comparator: (a: S, b: T) => number): number => {\n  const index = lowerBound(array, value, comparator);\n  return index < array.length && comparator(value, array[index]) === 0 ? index : -1;\n};\n\nfunction mergeOrIntersect<T>(\n    array1: T[], array2: T[], comparator: (a: T, b: T) => number, mergeNotIntersect: boolean): T[] {\n  const result = [];\n  let i = 0;\n  let j = 0;\n  while (i < array1.length && j < array2.length) {\n    const compareValue = comparator(array1[i], array2[j]);\n    if (mergeNotIntersect || !compareValue) {\n      result.push(compareValue <= 0 ? array1[i] : array2[j]);\n    }\n    if (compareValue <= 0) {\n      i++;\n    }\n    if (compareValue >= 0) {\n      j++;\n    }\n  }\n  if (mergeNotIntersect) {\n    while (i < array1.length) {\n      result.push(array1[i++]);\n    }\n    while (j < array2.length) {\n      result.push(array2[j++]);\n    }\n  }\n  return result;\n}\n\nexport const intersectOrdered = <T>(array1: T[], array2: T[], comparator: (a: T, b: T) => number): T[] => {\n  return mergeOrIntersect(array1, array2, comparator, false);\n};\n\nexport const mergeOrdered = <T>(array1: T[], array2: T[], comparator: (a: T, b: T) => number): T[] => {\n  return mergeOrIntersect(array1, array2, comparator, true);\n};\n\nexport const DEFAULT_COMPARATOR = (a: string|number, b: string|number): -1|0|1 => {\n  return a < b ? -1 : (a > b ? 1 : 0);\n};\n\n/**\n * Returns the index of the element closest to the needle that is equal to or\n * greater than it. Assumes that the provided array is sorted.\n *\n * If no element is found, the right bound is returned.\n *\n * Uses the provided comparator function to determine if two items are equal or\n * if one is greater than the other. If you are working with strings or\n * numbers, you can use ArrayUtilities.DEFAULT_COMPARATOR. Otherwise, you\n * should define one that takes the needle element and an element from the\n * array and returns a positive or negative number to indicate which is greater\n * than the other.\n *\n * When specified, |left| (inclusive) and |right| (exclusive) indices\n * define the search window.\n */\nexport function lowerBound<T>(\n    array: Uint32Array|Int32Array, needle: T, comparator: (needle: T, b: number) => number, left?: number,\n    right?: number): number;\nexport function lowerBound<S, T>(\n    array: S[], needle: T, comparator: (needle: T, b: S) => number, left?: number, right?: number): number;\nexport function lowerBound<S, T>(\n    array: readonly S[], needle: T, comparator: (needle: T, b: S) => number, left?: number, right?: number): number;\nexport function lowerBound<S, T, A extends S[]>(\n    array: A, needle: T, comparator: (needle: T, b: S) => number, left?: number, right?: number): number {\n  let l = left || 0;\n  let r = right !== undefined ? right : array.length;\n  while (l < r) {\n    const m = (l + r) >> 1;\n    if (comparator(needle, array[m]) > 0) {\n      l = m + 1;\n    } else {\n      r = m;\n    }\n  }\n  return r;\n}\n\n/**\n * Returns the index of the element closest to the needle that is greater than\n * it. Assumes that the provided array is sorted.\n *\n * If no element is found, the right bound is returned.\n *\n * Uses the provided comparator function to determine if two items are equal or\n * if one is greater than the other. If you are working with strings or\n * numbers, you can use ArrayUtilities.DEFAULT_COMPARATOR. Otherwise, you\n * should define one that takes the needle element and an element from the\n * array and returns a positive or negative number to indicate which is greater\n * than the other.\n *\n * When specified, |left| (inclusive) and |right| (exclusive) indices\n * define the search window.\n */\nexport function upperBound<T>(\n    array: Uint32Array, needle: T, comparator: (needle: T, b: number) => number, left?: number, right?: number): number;\nexport function upperBound<S, T>(\n    array: S[], needle: T, comparator: (needle: T, b: S) => number, left?: number, right?: number): number;\nexport function upperBound<S, T, A extends S[]>(\n    array: A, needle: T, comparator: (needle: T, b: S) => number, left?: number, right?: number): number {\n  let l = left || 0;\n  let r = right !== undefined ? right : array.length;\n  while (l < r) {\n    const m = (l + r) >> 1;\n    if (comparator(needle, array[m]) >= 0) {\n      l = m + 1;\n    } else {\n      r = m;\n    }\n  }\n  return r;\n}\n\nconst enum NearestSearchStart {\n  BEGINNING = 'BEGINNING',\n  END = 'END',\n}\n/**\n * Obtains the first or last item in the array that satisfies the predicate function.\n * So, for example, if the array were arr = [2, 4, 6, 8, 10], and you are looking for\n * the last item arr[i] such that arr[i] < 5  you would be returned 1, because\n * array[1] is 4, the last item in the array that satisfies the\n * predicate function.\n *\n * If instead you were looking for the first item in the same array that satisfies\n * arr[i] > 5 you would be returned 2 because array[2] = 6.\n *\n * Please note: this presupposes that the array is already ordered.\n */\nfunction nearestIndex<T>(\n    arr: readonly T[], predicate: (arrayItem: T) => boolean, searchStart: NearestSearchStart): number|null {\n  const searchFromEnd = searchStart === NearestSearchStart.END;\n  if (arr.length === 0) {\n    return null;\n  }\n\n  let left = 0;\n  let right = arr.length - 1;\n  let pivot = 0;\n  let matchesPredicate = false;\n  let moveToTheRight = false;\n  let middle = 0;\n  do {\n    middle = left + (right - left) / 2;\n    pivot = searchFromEnd ? Math.ceil(middle) : Math.floor(middle);\n    matchesPredicate = predicate(arr[pivot]);\n    moveToTheRight = matchesPredicate === searchFromEnd;\n    if (moveToTheRight) {\n      left = Math.min(right, pivot + (left === pivot ? 1 : 0));\n    } else {\n      right = Math.max(left, pivot + (right === pivot ? -1 : 0));\n    }\n  } while (right !== left);\n\n  // Special-case: the indexed item doesn't pass the predicate. This\n  // occurs when none of the items in the array are a match for the\n  // predicate.\n  if (!predicate(arr[left])) {\n    return null;\n  }\n  return left;\n}\n\n/**\n * Obtains the first item in the array that satisfies the predicate function.\n * So, for example, if the array was arr = [2, 4, 6, 8, 10], and you are looking for\n * the first item arr[i] such that arr[i] > 5 you would be returned 2, because\n * array[2] is 6, the first item in the array that satisfies the\n * predicate function.\n *\n * Please note: this presupposes that the array is already ordered.\n */\nexport function nearestIndexFromBeginning<T>(arr: T[], predicate: (arrayItem: T) => boolean): number|null {\n  return nearestIndex(arr, predicate, NearestSearchStart.BEGINNING);\n}\n\n/**\n * Obtains the last item in the array that satisfies the predicate function.\n * So, for example, if the array was arr = [2, 4, 6, 8, 10], and you are looking for\n * the last item arr[i] such that arr[i] < 5 you would be returned 1, because\n * arr[1] is 4, the last item in the array that satisfies the\n * predicate function.\n *\n * Please note: this presupposes that the array is already ordered.\n */\n\nexport function nearestIndexFromEnd<T>(arr: readonly T[], predicate: (arrayItem: T) => boolean): number|null {\n  return nearestIndex(arr, predicate, NearestSearchStart.END);\n}\n\n// Type guard for ensuring that `arr` does not contain null or undefined\nexport function arrayDoesNotContainNullOrUndefined<T>(arr: (T|null|undefined)[]): arr is T[] {\n  return !arr.includes(null) && !arr.includes(undefined);\n}\n"]}