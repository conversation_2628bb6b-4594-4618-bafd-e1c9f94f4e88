{"version": 3, "file": "MimeType.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/MimeType.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAe7B;;GAEG;AACH,MAAM,0BAA0B,GAAG,IAAI,GAAG,CAAC;IACzC,wBAAwB;IACxB,wBAAwB;IACxB,kBAAkB;IAClB,2BAA2B;IAC3B,sBAAsB;IACtB,iBAAiB;IACjB,oBAAoB;IACpB,0BAA0B;IAC1B,mBAAmB;IACnB,yBAAyB;CAC1B,CAAC,CAAC;AAEH;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,QAAgB;IACzC,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC1F,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,WAAmB;IAClD,IAAI,WAAW,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,EAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACzC,CAAC;IAED,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAC,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;IACpE,OAAO,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAC;AAC7B,CAAC;AAED,SAAS,aAAa,CAAC,WAAmB;IACxC,iGAAiG;IACjG,kEAAkE;IAClE,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IAEjC,0EAA0E;IAC1E,IAAI,WAAW,GAAG,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACzD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACpB,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;IACnC,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;QAC3C,OAAO,EAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,GAAG,EAAE,EAAC,CAAC;IAC7C,CAAC;IACD,MAAM,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;IAErE,sFAAsF;IACtF,kCAAkC;IAClC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;IACzC,IAAI,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IACnD,OAAO,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;QAClD,0BAA0B;QAC1B,EAAE,MAAM,CAAC;QAET,sBAAsB;QACtB,MAAM,GAAG,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,SAAS;QACX,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,CAAC;QAE9B,oEAAoE;QACpE,MAAM,GAAG,gBAAgB,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACrD,IAAI,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9C,sEAAsE;YACtE,SAAS;QACX,CAAC;QAED,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAE9E,qBAAqB;QACrB,EAAE,MAAM,CAAC;QAET,uBAAuB;QACvB,MAAM,GAAG,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEzD,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9C,0EAA0E;YAC1E,SAAS;QACX,CAAC;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;YACvC,8CAA8C;YAC9C,MAAM,UAAU,GAAG,MAAM,CAAC;YAC1B,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;YAE3D,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,oFAAoF;YACpF,4CAA4C;YAE5C,iCAAiC;YACjC,EAAE,MAAM,CAAC;YAET,OAAO,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;gBAClE,yEAAyE;gBACzE,gFAAgF;gBAChF,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;oBACpE,EAAE,MAAM,CAAC;gBACX,CAAC;gBACD,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;gBAClC,EAAE,MAAM,CAAC;YACX,CAAC;YAED,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,sBAAsB;YACtB,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,OAAO,EAAC,QAAQ,EAAE,MAAM,EAAC,CAAC;AAC5B,CAAC;AAED;;;GAGG;AACH,SAAS,gBAAgB,CAAC,YAAoB,EAAE,UAAkB,EAAE,MAAc,CAAC;IACjF,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB,CAAC,YAAoB,EAAE,UAAkB,EAAE,MAAc,CAAC;IACpF,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const enum MimeType {\n  HTML = 'text/html',\n  XML = 'text/xml',\n  PLAIN = 'text/plain',\n  XHTML = 'application/xhtml+xml',\n  SVG = 'image/svg+xml',\n  CSS = 'text/css',\n  XSL = 'text/xsl',\n  VTT = 'text/vtt',\n  PDF = 'application/pdf',\n  EVENTSTREAM = 'text/event-stream',\n}\n\n/**\n * MIME types other than the ones with the \"text\" type that have text content.\n */\nconst ADDITIONAL_TEXT_MIME_TYPES = new Set([\n  'application/ecmascript',\n  'application/javascript',\n  'application/json',\n  'application/json+protobuf',\n  'application/vnd.dart',\n  'application/xml',\n  'application/x-aspx',\n  'application/x-javascript',\n  'application/x-jsp',\n  'application/x-httpd-php',\n]);\n\n/**\n * @returns true iff `mimeType` has textual content. Concretely we return true if:\n *   - `mimeType` starts with \"text/\"\n *   - `mimeType` ends with \"+json\" or \"+xml\"\n *   - if `mimeType` is one of a predefined list textual mime types.\n */\nexport function isTextType(mimeType: string): boolean {\n  return mimeType.startsWith('text/') || mimeType.endsWith('+json') || mimeType.endsWith('+xml') ||\n      ADDITIONAL_TEXT_MIME_TYPES.has(mimeType);\n}\n\n/**\n * Port of net::HttpUtils::ParseContentType to extract mimeType and charset from\n * the 'Content-Type' header.\n */\nexport function parseContentType(contentType: string): {mimeType: string|null, charset: string|null} {\n  if (contentType === '*/*') {\n    return {mimeType: null, charset: null};\n  }\n\n  const {mimeType, params} = parseMimeType(contentType);\n  const charset = params.get('charset')?.toLowerCase().trim() ?? null;\n  return {mimeType, charset};\n}\n\nfunction parseMimeType(contentType: string): {mimeType: string|null, params: Map<string, string>} {\n  // Remove any leading and trailing whitespace. Note that String.prototype.trim removes a lot more\n  // than what the spec considers whitespace. We are fine with that.\n  contentType = contentType.trim();\n\n  // The mimetype is basically everything until the first ';' (but trimmed).\n  let mimeTypeEnd = findFirstIndexOf(contentType, ' \\t;(');\n  if (mimeTypeEnd < 0) {\n    mimeTypeEnd = contentType.length;\n  }\n\n  const slashPos = contentType.indexOf('/');\n  if (slashPos < 0 || slashPos > mimeTypeEnd) {\n    return {mimeType: null, params: new Map()};\n  }\n  const mimeType = contentType.substring(0, mimeTypeEnd).toLowerCase();\n\n  // Iterate over parameters. We can't split the string around semicolons because quoted\n  // strings may include semicolons.\n  const params = new Map<string, string>();\n  let offset = contentType.indexOf(';', mimeTypeEnd);\n  while (offset >= 0 && offset < contentType.length) {\n    // Trim off the semicolon.\n    ++offset;\n\n    // Trim off whitespace\n    offset = findFirstIndexNotOf(contentType, ' \\t', offset);\n    if (offset < 0) {\n      continue;\n    }\n    const paramNameStart = offset;\n\n    // Extend parameter name until we run into semicolon or equals sign.\n    offset = findFirstIndexOf(contentType, ';=', offset);\n    if (offset < 0 || contentType[offset] === ';') {\n      // Nothing more to do if no more input or there is no parameter value.\n      continue;\n    }\n\n    const paramName = contentType.substring(paramNameStart, offset).toLowerCase();\n\n    //  Trim off the '='.\n    ++offset;\n\n    // Trim off whitespace.\n    offset = findFirstIndexNotOf(contentType, ' \\t', offset);\n\n    let paramValue = '';\n    if (offset < 0 || contentType[offset] === ';') {\n      // Nothing to do here: the value is an unquoted string of only whitespace.\n      continue;\n    } else if (contentType[offset] !== '\"') {\n      // Not a quote so we can copy the value as-is.\n      const valueStart = offset;\n      offset = contentType.indexOf(';', offset);\n      const valueEnd = offset >= 0 ? offset : contentType.length;\n\n      paramValue = contentType.substring(valueStart, valueEnd).trimEnd();\n    } else {\n      // Otherwise append data with special handling for backslashes, until a close quote.\n      // Don't trim whitespace for quoted strings.\n\n      // Trim off the opening quote '\"'\n      ++offset;\n\n      while (offset < contentType.length && contentType[offset] !== '\"') {\n        // Skip over backslash and append the next character, when not at the end\n        // of the string. Otherwise, copy the next character (which may be a backslash).\n        if (contentType[offset] === '\\\\' && offset + 1 < contentType.length) {\n          ++offset;\n        }\n        paramValue += contentType[offset];\n        ++offset;\n      }\n\n      offset = contentType.indexOf(';', offset);\n    }\n    if (!params.has(paramName)) {\n      // The first one wins!\n      params.set(paramName, paramValue);\n    }\n  }\n\n  return {mimeType, params};\n}\n\n/**\n * @returns the smallest index of any character in 'characters' or -1 if none of\n * the characters occur in 'searchString'\n */\nfunction findFirstIndexOf(searchString: string, characters: string, pos: number = 0): number {\n  for (let i = pos; i < searchString.length; i++) {\n    if (characters.includes(searchString[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/**\n * @returns the smallest index of any character not in 'characters' or -1 if only\n * 'characters' occur in 'searchString'\n */\nfunction findFirstIndexNotOf(searchString: string, characters: string, pos: number = 0): number {\n  for (let i = pos; i < searchString.length; i++) {\n    if (!characters.includes(searchString[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n"]}