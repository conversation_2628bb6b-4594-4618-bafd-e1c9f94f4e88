{"version": 3, "file": "TypedArrayUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/TypedArrayUtilities.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAiB7B;;;;GAIG;AACH,MAAM,UAAU,8BAA8B;IAC5C,OAAO,IAAI,4BAA4B,EAAE,CAAC;AAC5C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,yBAAyB,CAAC,MAAc,EAAE,mBAA4B;IACpF,IAAI,CAAC;QACH,IAAI,mBAAmB,KAAK,SAAS,IAAI,MAAM,GAAG,mBAAmB,EAAE,CAAC;YACtE,+BAA+B;YAC/B,MAAM,IAAI,UAAU,EAAE,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAAC,MAAM,CAAC;QACP,iDAAiD;QACjD,OAAO,IAAI,uBAAuB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED,MAAM,uBAAwB,SAAQ,WAAW;IAC/C,QAAQ,CAAC,KAAa;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IACD,QAAQ,CAAC,KAAa,EAAE,KAAa;QACnC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACtB,CAAC;IACD,mBAAmB;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,aAAa;QACX,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;CACF;AAED,MAAM,uBAAuB;IAC3B,KAAK,CAAgB;IACrB,WAAW,CAAS;IACpB,MAAM,CAAS;IAEf,YAAY,MAAc,EAAE,mBAA4B;QACtD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,IAAI,EAAE,CAAC;YACZ,SAAS,IAAI,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC;gBACH,IAAI,mBAAmB,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,GAAG,mBAAmB,EAAE,CAAC;oBAChF,+BAA+B;oBAC/B,MAAM,IAAI,UAAU,EAAE,CAAC;gBACzB,CAAC;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;oBACnC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpD,CAAC;gBACD,OAAO;YACT,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;oBAC3B,sEAAsE;oBACtE,0BAA0B;oBAC1B,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,KAAa;QACpB,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;YACpC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC;QACxE,CAAC;QACD,0EAA0E;QAC1E,sDAAsD;QACtD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,KAAa,EAAE,KAAa;QACnC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC;QACzE,CAAC;QACD,0EAA0E;IAC5E,CAAC;IAED,mBAAmB;QACjB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IACD,aAAa;QACX,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;CACF;AAED,MAAM,4BAA6B,SAAQ,KAAa;IACtD,QAAQ,CAAC,KAAa;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IACD,QAAQ,CAAC,KAAa,EAAE,KAAa;QACnC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACtB,CAAC;IACD,mBAAmB;QACjB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IACD,aAAa;QACX,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["// Copyright 2024 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * An object which provides functionality similar to Uint32Array. It may be\n * implemented as:\n * 1. A Uint32Array,\n * 2. An array of Uint32Arrays, to support more data than Uint32Array, or\n * 3. A plain array, in which case the length may change by setting values.\n */\nexport interface BigUint32Array {\n  get length(): number;\n  getValue(index: number): number;\n  setValue(index: number, value: number): void;\n  asUint32ArrayOrFail(): Uint32Array;\n  asArrayOrFail(): number[];\n}\n\n/**\n * @returns A BigUint32Array implementation which is based on Array.\n * This means that its length automatically expands to include the highest index\n * used, and asArrayOrFail will succeed.\n */\nexport function createExpandableBigUint32Array(): BigUint32Array {\n  return new ExpandableBigUint32ArrayImpl();\n}\n\n/**\n * @returns A BigUint32Array implementation which is based on Uint32Array.\n * If the length is small enough to fit in a single Uint32Array, then\n * asUint32ArrayOrFail will succeed. Otherwise, it will throw an exception.\n */\nexport function createFixedBigUint32Array(length: number, maxLengthForTesting?: number): BigUint32Array {\n  try {\n    if (maxLengthForTesting !== undefined && length > maxLengthForTesting) {\n      // Simulate allocation failure.\n      throw new RangeError();\n    }\n    return new BasicBigUint32ArrayImpl(length);\n  } catch {\n    // We couldn't allocate a big enough ArrayBuffer.\n    return new SplitBigUint32ArrayImpl(length, maxLengthForTesting);\n  }\n}\n\nclass BasicBigUint32ArrayImpl extends Uint32Array implements BigUint32Array {\n  getValue(index: number): number {\n    return this[index];\n  }\n  setValue(index: number, value: number): void {\n    this[index] = value;\n  }\n  asUint32ArrayOrFail(): Uint32Array {\n    return this;\n  }\n  asArrayOrFail(): number[] {\n    throw new Error('Not an array');\n  }\n}\n\nclass SplitBigUint32ArrayImpl implements BigUint32Array {\n  #data: Uint32Array[];\n  #partLength: number;\n  length: number;\n\n  constructor(length: number, maxLengthForTesting?: number) {\n    this.#data = [];\n    this.length = length;\n    let partCount = 1;\n    while (true) {\n      partCount *= 2;\n      this.#partLength = Math.ceil(length / partCount);\n      try {\n        if (maxLengthForTesting !== undefined && this.#partLength > maxLengthForTesting) {\n          // Simulate allocation failure.\n          throw new RangeError();\n        }\n        for (let i = 0; i < partCount; ++i) {\n          this.#data[i] = new Uint32Array(this.#partLength);\n        }\n        return;\n      } catch (e) {\n        if (this.#partLength < 1e6) {\n          // The length per part is already small, so continuing to subdivide it\n          // will probably not help.\n          throw e;\n        }\n      }\n    }\n  }\n\n  getValue(index: number): number {\n    if (index >= 0 && index < this.length) {\n      const partLength = this.#partLength;\n      return this.#data[Math.floor(index / partLength)][index % partLength];\n    }\n    // On out-of-bounds accesses, match the behavior of Uint32Array: return an\n    // undefined value that's incorrectly typed as number.\n    return this.#data[0][-1];\n  }\n\n  setValue(index: number, value: number): void {\n    if (index >= 0 && index < this.length) {\n      const partLength = this.#partLength;\n      this.#data[Math.floor(index / partLength)][index % partLength] = value;\n    }\n    // Attempting to set a value out of bounds does nothing, like Uint32Array.\n  }\n\n  asUint32ArrayOrFail(): Uint32Array {\n    throw new Error('Not a Uint32Array');\n  }\n  asArrayOrFail(): number[] {\n    throw new Error('Not an array');\n  }\n}\n\nclass ExpandableBigUint32ArrayImpl extends Array<number> implements BigUint32Array {\n  getValue(index: number): number {\n    return this[index];\n  }\n  setValue(index: number, value: number): void {\n    this[index] = value;\n  }\n  asUint32ArrayOrFail(): Uint32Array {\n    throw new Error('Not a Uint32Array');\n  }\n  asArrayOrFail(): number[] {\n    return this;\n  }\n}\n"]}