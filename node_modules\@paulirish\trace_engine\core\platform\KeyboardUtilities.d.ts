export declare const enum ArrowKey {
    UP = "ArrowUp",
    DOWN = "ArrowDown",
    LEFT = "ArrowLeft",
    RIGHT = "ArrowRight"
}
export declare const enum PageKey {
    UP = "PageUp",
    DOWN = "PageDown"
}
export declare const ENTER_KEY = "Enter";
export declare const ESCAPE_KEY = "Escape";
export declare const TAB_KEY = "Tab";
export declare const ARROW_KEYS: Set<ArrowKey>;
export declare function keyIsArrowKey(key: string): key is ArrowK<PERSON>;
export declare function isEscKey(event: KeyboardEvent): boolean;
export declare function isEnterOrSpaceKey(event: KeyboardEvent): boolean;
