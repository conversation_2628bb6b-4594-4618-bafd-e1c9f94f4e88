{"version": 3, "file": "CustomQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/CustomQueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;AAGH,iDAAyC;AACzC,qDAA2E;AAE3E,uDAAgF;AAChF,2DAAmD;AAgBnD;;;;;;;;;;;GAWG;AACH,MAAa,0BAA0B;IAAvC;QACE,+CAAY,IAAI,GAAG,EAGhB,EAAC;IA6HN,CAAC;IA3HC;;OAEG;IACH,GAAG,CAAC,IAAY;QACd,MAAM,OAAO,GAAG,uBAAA,IAAI,4CAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,QAAQ,CAAC,IAAY,EAAE,OAA2B;;QAChD,IAAI,uBAAA,IAAI,4CAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;SACnE;QACD,IAAA,kBAAM,EACJ,CAAC,uBAAA,IAAI,4CAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EACzB,0CAA0C,IAAI,EAAE,CACjD,CAAC;QACF,IAAA,kBAAM,EACJ,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,sDAAsD,CACvD,CAAC;QACF,IAAA,kBAAM,EACJ,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EACpC,gDAAgD,CACjD,CAAC;QAEF,MAAM,OAAO,SAAG,KAAM,SAAQ,8BAAY;aAiBzC;YAhBiB,mBAAgB,GAAqB,IAAA,iCAAmB,EACtE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC5B;YACc,gBAAa,GAAkB,IAAA,iCAAmB,EAChE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACnC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC5B;eACH,CAAC;QACF,MAAM,cAAc,GAAG,IAAA,iCAAmB,EACxC,CAAC,aAA4B,EAAE,EAAE;YAC/B,aAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBAC/D,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;gBACjC,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,EACD;YACE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,IAAA,+BAAiB,EAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;YACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,IAAA,+BAAiB,EAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;SACtB,CACF,CAAC,QAAQ,EAAE,CAAC;QAEb,uBAAA,IAAI,4CAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QACpD,kCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;OAOG;IACH,UAAU,CAAC,IAAY;QACrB,MAAM,OAAO,GAAG,uBAAA,IAAI,4CAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;SAC/D;QACD,kCAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,uBAAA,IAAI,4CAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,OAAO,CAAC,GAAG,uBAAA,IAAI,4CAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,uBAAA,IAAI,4CAAU,EAAE;YAC7C,kCAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;SACpC;QACD,uBAAA,IAAI,4CAAU,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACF;AAjID,gEAiIC;;AAED;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAI,0BAA0B,EAAE,CAAC;AAEpE;;;;;GAKG;AACH,SAAgB,0BAA0B,CACxC,IAAY,EACZ,OAA2B;IAE3B,2BAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AALD,gEAKC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAAC,IAAY;IACvD,2BAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;AAFD,oEAEC;AAED;;;;;GAKG;AACH,SAAgB,uBAAuB;IACrC,OAAO,2BAAmB,CAAC,KAAK,EAAE,CAAC;AACrC,CAAC;AAFD,0DAEC;AAED;;;;;GAKG;AACH,SAAgB,wBAAwB;IACtC,2BAAmB,CAAC,KAAK,EAAE,CAAC;AAC9B,CAAC;AAFD,4DAEC"}