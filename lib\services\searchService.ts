import { createClient } from '@/lib/supabase/client'
import tramitesData from '@/tramites_chia_optimo.json'
import opasData from '@/OPA-chia-optimo.json'

// Tipos para los resultados de búsqueda
export interface SearchResult {
  id: string
  name: string
  type: 'TRAMITE' | 'OPA'
  description?: string
  dependency: string
  subdependency?: string
  cost?: string
  responseTime?: string
  popularity?: number
  matchScore?: number
  highlightedName?: string
  suitUrl?: string
  govUrl?: string
  formRequired?: boolean
}

export interface SearchFilters {
  dependency?: string
  type?: 'TRAMITE' | 'OPA' | 'ALL'
  hasCost?: boolean
  maxResponseTime?: number
}

export interface SearchOptions {
  limit?: number
  offset?: number
  includeHighlight?: boolean
  sortBy?: 'relevance' | 'name' | 'popularity' | 'responseTime'
  sortOrder?: 'asc' | 'desc'
}

/**
 * Servicio de búsqueda inteligente para trámites y OPAs
 */
export class SearchService {
  private supabase = createClient()
  private tramites: any[] = []
  private opas: any[] = []
  private initialized = false

  constructor() {
    this.initializeData()
  }

  /**
   * Inicializar datos de trámites y OPAs
   */
  private async initializeData() {
    if (this.initialized) return

    try {
      // Cargar trámites desde JSON
      this.tramites = tramitesData.map((tramite, index) => ({
        id: `tramite-${index}`,
        name: tramite.Nombre,
        type: 'TRAMITE' as const,
        description: `Trámite de ${tramite.Nombre}`,
        dependency: tramite.dependencia,
        subdependency: tramite.subdependencia,
        cost: tramite['¿Tiene pago?'] === 'No' ? 'Gratuito' : tramite['¿Tiene pago?'],
        responseTime: tramite['Tiempo de respuesta'],
        suitUrl: tramite['Visualización trámite en el SUIT'],
        govUrl: tramite['Visualización trámite en GOV.CO'],
        formRequired: tramite.Formulario === 'Sí',
        popularity: Math.floor(Math.random() * 100) + 1 // Simulado por ahora
      }))

      // Cargar OPAs desde JSON
      this.opas = this.flattenOPAs(opasData)

      this.initialized = true
    } catch (error) {
      console.error('Error inicializando datos de búsqueda:', error)
    }
  }

  /**
   * Aplanar estructura jerárquica de OPAs
   */
  private flattenOPAs(opasData: any): any[] {
    const flattened: any[] = []
    let opaIndex = 0

    Object.entries(opasData.dependencias).forEach(([depCode, dep]: [string, any]) => {
      Object.entries(dep.subdependencias).forEach(([subDepCode, subDep]: [string, any]) => {
        subDep.OPA?.forEach((opa: any) => {
          flattened.push({
            id: `opa-${opaIndex++}`,
            name: opa.OPA.length > 100 ? opa.OPA.substring(0, 100) + '...' : opa.OPA,
            type: 'OPA' as const,
            description: opa.OPA,
            dependency: dep.nombre,
            subdependency: subDep.nombre,
            cost: 'Gratuito',
            responseTime: '1 día hábil', // Valor por defecto para OPAs
            popularity: Math.floor(Math.random() * 50) + 1 // Simulado
          })
        })
      })
    })

    return flattened
  }

  /**
   * Realizar búsqueda inteligente
   */
  async search(
    query: string,
    filters: SearchFilters = {},
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    await this.initializeData()

    const {
      limit = 10,
      offset = 0,
      includeHighlight = true,
      sortBy = 'relevance',
      sortOrder = 'desc'
    } = options

    if (!query.trim()) {
      return []
    }

    const searchTerm = query.toLowerCase().trim()
    let allProcedures = [...this.tramites, ...this.opas]

    // Aplicar filtros
    if (filters.type && filters.type !== 'ALL') {
      allProcedures = allProcedures.filter(proc => proc.type === filters.type)
    }

    if (filters.dependency) {
      allProcedures = allProcedures.filter(proc =>
        proc.dependency.toLowerCase().includes(filters.dependency!.toLowerCase())
      )
    }

    if (filters.hasCost !== undefined) {
      allProcedures = allProcedures.filter(proc => {
        const isFree = proc.cost === 'Gratuito' || proc.cost === 'No'
        return filters.hasCost ? !isFree : isFree
      })
    }

    // Realizar búsqueda con scoring
    const results = allProcedures
      .map(proc => {
        const matchScore = this.calculateMatchScore(proc, searchTerm)
        if (matchScore === 0) return null

        return {
          ...proc,
          matchScore,
          highlightedName: includeHighlight ? this.highlightMatch(proc.name, searchTerm) : proc.name
        }
      })
      .filter(Boolean) as SearchResult[]

    // Ordenar resultados
    results.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return sortOrder === 'asc'
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name)
        case 'popularity':
          return sortOrder === 'asc'
            ? (a.popularity || 0) - (b.popularity || 0)
            : (b.popularity || 0) - (a.popularity || 0)
        case 'relevance':
        default:
          return (b.matchScore || 0) - (a.matchScore || 0)
      }
    })

    return results.slice(offset, offset + limit)
  }

  /**
   * Calcular score de relevancia para un procedimiento
   */
  private calculateMatchScore(procedure: any, searchTerm: string): number {
    let score = 0
    const name = procedure.name.toLowerCase()
    const description = (procedure.description || '').toLowerCase()
    const dependency = procedure.dependency.toLowerCase()

    // Coincidencia exacta en el nombre (peso alto)
    if (name === searchTerm) {
      score += 100
    }
    // Coincidencia al inicio del nombre
    else if (name.startsWith(searchTerm)) {
      score += 80
    }
    // Coincidencia en el nombre
    else if (name.includes(searchTerm)) {
      score += 60
    }

    // Coincidencia en la descripción
    if (description.includes(searchTerm)) {
      score += 30
    }

    // Coincidencia en la dependencia
    if (dependency.includes(searchTerm)) {
      score += 20
    }

    // Bonus por popularidad
    if (procedure.popularity) {
      score += Math.floor(procedure.popularity / 10)
    }

    // Bonus por palabras múltiples
    const searchWords = searchTerm.split(' ').filter(word => word.length > 2)
    if (searchWords.length > 1) {
      const wordMatches = searchWords.filter(word =>
        name.includes(word) || description.includes(word)
      ).length
      score += (wordMatches / searchWords.length) * 20
    }

    return score
  }

  /**
   * Resaltar coincidencias en el texto
   */
  private highlightMatch(text: string, searchTerm: string): string {
    if (!searchTerm.trim()) return text

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }

  /**
   * Obtener sugerencias populares
   */
  async getPopularSearches(limit: number = 10): Promise<string[]> {
    await this.initializeData()

    // En producción, esto vendría de analytics/métricas reales
    const popularTerms = [
      'licencia construcción',
      'certificado residencia',
      'impuesto predial',
      'licencia comercial',
      'certificado libertad',
      'permiso eventos',
      'paz y salvo',
      'certificado estratificación',
      'licencia funcionamiento',
      'registro mercantil'
    ]

    return popularTerms.slice(0, limit)
  }

  /**
   * Obtener dependencias disponibles para filtros
   */
  async getDependencies(): Promise<string[]> {
    await this.initializeData()

    const dependencies = new Set<string>()

    this.tramites.forEach(tramite => {
      if (tramite.dependency) {
        dependencies.add(tramite.dependency)
      }
    })

    this.opas.forEach(opa => {
      if (opa.dependency) {
        dependencies.add(opa.dependency)
      }
    })

    return Array.from(dependencies).sort()
  }

  /**
   * Obtener estadísticas de búsqueda
   */
  async getSearchStats(): Promise<{
    totalTramites: number
    totalOPAs: number
    totalDependencies: number
  }> {
    await this.initializeData()

    const dependencies = await this.getDependencies()

    return {
      totalTramites: this.tramites.length,
      totalOPAs: this.opas.length,
      totalDependencies: dependencies.length
    }
  }
}

// Instancia singleton del servicio de búsqueda
export const searchService = new SearchService()