{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../src/install.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,oDAA4B;AAC5B,2BAA8B;AAC9B,0CAA0C;AAC1C,4CAAoB;AACpB,gDAAwB;AAExB,oEAIwC;AACxC,yCAAmD;AACnD,yCAAiC;AACjC,2DAA0D;AAC1D,+CAA4C;AAC5C,+CAA4D;AAE5D,MAAM,YAAY,GAAG,IAAA,gBAAK,EAAC,4BAA4B,CAAC,CAAC;AAEzD,MAAM,KAAK,GAAG,IAAI,GAAG,EAA4B,CAAC;AAClD,SAAS,SAAS,CAAC,KAAa;IAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,qCAAqC;IAC1G,YAAY,CAAC,gBAAgB,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC;AACvD,CAAC;AAkDD;;GAEG;AACI,KAAK,UAAU,OAAO,CAC3B,OAAuB;;IAEvB,MAAA,OAAO,CAAC,QAAQ,oCAAhB,OAAO,CAAC,QAAQ,GAAK,IAAA,yCAAqB,GAAE,EAAC;IAC7C,MAAA,OAAO,CAAC,MAAM,oCAAd,OAAO,CAAC,MAAM,GAAK,IAAI,EAAC;IACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACrB,MAAM,IAAI,KAAK,CACb,uDAAuD,YAAE,CAAC,QAAQ,EAAE,KAAK,YAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;KACH;IACD,MAAM,GAAG,GAAG,cAAc,CACxB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,IAAA,gBAAM,EAAC,QAAQ,EAAE,uCAAuC,GAAG,GAAG,CAAC,CAAC;IAChE,MAAM,SAAS,GAAG,IAAI,gBAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3D,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IACrD,IAAI,CAAC,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE;QAC5B,MAAM,IAAA,gBAAK,EAAC,WAAW,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;KAC7C;IAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACnB,IAAI,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE;YAC3B,OAAO;gBACL,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC;SACH;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,CAAC;QACtB,MAAM,IAAA,0BAAY,EAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACvE,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;KACH;IAED,MAAM,UAAU,GAAG,SAAS,CAAC,eAAe,CAC1C,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,IAAI,IAAA,eAAU,EAAC,UAAU,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;KACH;IACD,IAAI;QACF,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,IAAI;YACF,SAAS,CAAC,UAAU,CAAC,CAAC;YACtB,MAAM,IAAA,0BAAY,EAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;SACxE;gBAAS;YACR,YAAY,CAAC,UAAU,CAAC,CAAC;SAC1B;QAED,YAAY,CAAC,cAAc,WAAW,OAAO,UAAU,EAAE,CAAC,CAAC;QAC3D,IAAI;YACF,SAAS,CAAC,SAAS,CAAC,CAAC;YACrB,MAAM,IAAA,2BAAa,EAAC,WAAW,EAAE,UAAU,CAAC,CAAC;SAC9C;gBAAS;YACR,YAAY,CAAC,SAAS,CAAC,CAAC;SACzB;KACF;YAAS;QACR,IAAI,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE;YAC3B,MAAM,IAAA,iBAAM,EAAC,WAAW,CAAC,CAAC;SAC3B;KACF;IACD,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;KACzB,CAAC;AACJ,CAAC;AAtFD,0BAsFC;AAED;;GAEG;AACI,KAAK,UAAU,WAAW,CAAC,OAAuB;;IACvD,MAAA,OAAO,CAAC,QAAQ,oCAAhB,OAAO,CAAC,QAAQ,GAAK,IAAA,yCAAqB,GAAE,EAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACrB,MAAM,IAAI,KAAK,CACb,uDAAuD,YAAE,CAAC,QAAQ,EAAE,KAAK,YAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;KACH;IACD,OAAO,MAAM,IAAA,6BAAe,EAC1B,cAAc,CACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CACF,CAAC;AACJ,CAAC;AAfD,kCAeC;AAED,SAAS,cAAc,CACrB,OAAgB,EAChB,QAAyB,EACzB,OAAe,EACf,OAAgB;IAEhB,OAAO,IAAI,GAAG,CAAC,8BAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,CAAC"}