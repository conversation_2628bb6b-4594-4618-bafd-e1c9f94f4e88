{"compilerOptions": {"allowJs": true, "checkJs": true, "composite": true, "declaration": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "inlineSources": true, "lib": ["esnext", "dom", "dom.iterable"], "module": "esnext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "outDir": ".", "rootDir": "../../../../../../front_end/core/platform", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "esnext", "tsBuildInfoFile": "platform-tsconfig.json.tsbuildinfo", "typeRoots": [], "useUnknownInCatchVariables": false}, "files": ["../../../../../../front_end/core/platform/ArrayUtilities.ts", "../../../../../../front_end/core/platform/Brand.ts", "../../../../../../front_end/core/platform/DOMUtilities.ts", "../../../../../../front_end/core/platform/DateUtilities.ts", "../../../../../../front_end/core/platform/DevToolsPath.ts", "../../../../../../front_end/core/platform/KeyboardUtilities.ts", "../../../../../../front_end/core/platform/MapUtilities.ts", "../../../../../../front_end/core/platform/MimeType.ts", "../../../../../../front_end/core/platform/NumberUtilities.ts", "../../../../../../front_end/core/platform/PromiseUtilities.ts", "../../../../../../front_end/core/platform/SetUtilities.ts", "../../../../../../front_end/core/platform/StringUtilities.ts", "../../../../../../front_end/core/platform/Timing.ts", "../../../../../../front_end/core/platform/TypedArrayUtilities.ts", "../../../../../../front_end/core/platform/TypescriptUtilities.ts", "../../../../../../front_end/core/platform/UIString.ts", "../../../../../../front_end/core/platform/UserVisibleError.ts", "../../../../../../front_end/legacy/legacy-defs.d.ts", "../../../../../../front_end/global_typings/global_defs.d.ts", "../../../../../../front_end/global_typings/request_idle_callback.d.ts", "../../../../../../node_modules/@types/filesystem/index.d.ts"]}