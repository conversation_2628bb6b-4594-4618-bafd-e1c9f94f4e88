{"core/audits/accessibility/accesskeys.js | description": {"message": "As chaves de acesso permitem ao usuário focar rapidamente em uma determinada parte da página. Para haver uma navegação adequada, cada chave de acesso precisa ser única. [Saiba mais sobre as chaves de acesso](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Os valores de `[accesskey]` não são únicos"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Valores de `[accesskey]` são exclusivos"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Cada `role` ARIA é compatível com um subconjunto específico de atributos `aria-*`. A falta de correspondência entre eles invalida os atributos `aria-*`. [Aprenda a associar atributos ARIA às próprias funções](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não correspondem às próprias funções"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Os atributos `[aria-*]` correspondem às próprias funções"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Quando um elemento não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que os usuários que dependem desses leitores não possam usá-lo. [Aprenda a tornar os elementos de comando mais acessíveis](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Os elementos `button`, `link` e `menuitem` não têm nomes acessíveis."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Os elementos `button`, `link` e `menuitem` têm nomes acessíveis"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "As tecnologias adaptativas, como leitores de tela, funcionam de maneira inconsistente quando o `aria-hidden=\"true\"` está configurado no documento `<body>`. [Aprenda como o `aria-hidden` afeta o corpo do documento](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "O `[aria-hidden=\"true\"]` está presente no documento `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "O `[aria-hidden=\"true\"]` não está presente no documento `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Os descendentes focalizáveis dentro de um elemento `[aria-hidden=\"true\"]` impedem que esses elementos interativos sejam disponibilizados para usuários de tecnologias adaptativas, por exemplo, leitores de tela. [Aprenda como o elemento `aria-hidden` afeta elementos focalizáveis](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Os elementos `[aria-hidden=\"true\"]` contêm descendentes <PERSON>is"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Os elementos `[aria-hidden=\"true\"]` não contêm descendentes focalizáveis"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Quando um campo de entrada não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que os usuários com leitores de tela não possam usá-lo. [Saiba mais sobre os rótulos de campos de entrada](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Os campos de entrada ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Os campos de entrada ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Quando um elemento meter não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que ele não possa ser usado pelos usuários que dependem desses leitores. [Aprenda a nomear elementos `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Os elementos ARIA `meter` não têm nomes acessíveis."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Os elementos ARIA `meter` têm nomes acessíveis"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Quando um elemento `progressbar` não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que os usuários que dependem desses leitores não possam usá-lo. [Aprenda a marcar elementos `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Os elementos ARIA `progressbar` não têm nomes acessíveis."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Os elementos ARIA `progressbar` têm nomes acessíveis"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Algumas funções ARIA têm atributos obrigatórios que descrevem o estado do elemento para leitores de tela. [Saiba mais sobre funções e atributos obrigatórios](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`s não têm todos os atributos `[aria-*]` obrigat<PERSON><PERSON>s"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`s têm todos os atributos `[aria-*]` obri<PERSON><PERSON><PERSON>s"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Algumas funções ARIA mães precisam ter funções filhas específicas para cumprir as tarefas de acessibilidade pretendidas. [Saiba mais sobre funções e os elementos filhos obrigatórios](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementos com uma `[role]` ARIA que exigem que os filhos contenham uma `[role]` específica não têm alguns ou nenhum dos filhos obrigatórios."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementos com uma `[role]` ARIA que exigem que os filhos contenham uma `[role]` específica têm todos os filhos obrigatórios."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Algumas funções ARIA filhas precisam fazer parte das funções mães específicas para cumprir as tarefas de acessibilidade pretendidas. [Saiba mais sobre as funções ARIA e o elemento pai obrigatório](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`s não fazem parte do elemento pai obrigatório"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`s fazem parte do elemento pai obrigatório"}, "core/audits/accessibility/aria-roles.js | description": {"message": "As funções ARIA precisam ter valores válidos para realizar as tare<PERSON>s de acessibilidade pretendidas. [<PERSON><PERSON> mais sobre as funções ARIA válidas](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Os valores de `[role]` não são válidos"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Os valores de `[role]` s<PERSON> válidos"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Quando um campo de alternância não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que os usuários com leitores de tela não possam usá-lo. [Saiba mais sobre campos de alternância](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Os campos de alternância ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Os campos de alternância ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Quando um elemento tooltip não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que ele não possa ser usado pelos usuários que dependem desses leitores. [Aprenda a nomear elementos `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Os elementos ARIA `tooltip` não têm nomes acessíveis."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Os elementos ARIA `tooltip` têm nomes acessíveis"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Quando um elemento `treeitem` não tem um nome acessível, os leitores de tela o anunciam com um nome genérico, fazendo com que os usuários que dependem desses leitores não possam usá-lo. [Saiba mais sobre como marcar elementos `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Os elementos ARIA `treeitem` não têm nomes acessíveis."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Os elementos ARIA `treeitem` têm nomes acessíveis"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Tecnologias adaptativas, como leitores de tela, não conseguem interpretar atributos ARIA com valores inválidos. [Saiba mais sobre os valores válidos para atributos ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Os atributos `[aria-*]` não têm valores válidos"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Os atributos `[aria-*]` têm valores válidos"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "As tecnologias adaptativas, como leitores de tela, não conseguem interpretar atributos ARIA com nomes inválidos. [Saiba mais sobre os atributos ARIA válidos](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não são válidos nem contêm erros de ortografia"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Os atributos `[aria-*]` são válidos e não contêm erros de ortografia"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos com falha"}, "core/audits/accessibility/button-name.js | description": {"message": "Quando um botão não tem um nome acessível, os leitores de tela o enunciam como \"botão\", o que o inutiliza para usuários que dependem desses leitores. [Aprenda a tornar os botões mais acessíveis](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Os botões não têm um nome acessível"}, "core/audits/accessibility/button-name.js | title": {"message": "Os botões têm um nome acessível"}, "core/audits/accessibility/bypass.js | description": {"message": "A adição de maneiras de ignorar conteúdo repetido permite que usuários de teclado naveguem pela página com mais eficiência. [Saiba mais sobre ignorar blocos](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "A página não contém um cabeçalho, link de salto ou região de ponto de referência"}, "core/audits/accessibility/bypass.js | title": {"message": "A página contém um título, um link de salto ou uma região de ponto de referência"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Para muitos usuários, é difícil ou impossível ler textos com baixo contraste. [Aprenda a fornecer contraste de cor suficiente](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "As cores de primeiro e segundo plano não têm uma taxa de contraste suficiente."}, "core/audits/accessibility/color-contrast.js | title": {"message": "As cores de primeiro e segundo plano têm uma taxa de contraste suficiente"}, "core/audits/accessibility/definition-list.js | description": {"message": "Quando listas de definição não são marcadas corretamente, os leitores de tela podem produzir resultados confusos ou imprecisos. [Aprenda a estruturar listas de definição da forma correta](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`s não contêm apenas grupos `<dt>` e `<dd>`, elementos `<script>`, `<template>` ou `<div>` devidamente organizados."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`s contêm apenas os grupos `<dt>` e `<dd>`, elementos `<script>`, `<template>` ou `<div>` devidamente organizados."}, "core/audits/accessibility/dlitem.js | description": {"message": "Os itens da lista de definição (`<dt>` e `<dd>`) precisam ficar unidos em um elemento `<dl>` pai para garantir que os leitores de tela consigam enunciá-los corretamente. [Aprenda a estruturar listas de definição da forma correta](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Os itens da lista de definição não estão unidos em elementos `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Os itens da lista de definição estão unidos em elementos `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "O título oferece ao usuário do leitor de tela uma visão geral da página, além de ser extremamente útil para que os usuários de mecanismos de pesquisa determinem se uma página é relevante à pesquisa deles. [Saiba mais sobre títulos de documentos](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "O documento não tem um elemento `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "O documento tem um elemento `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Todos os elementos focalizáveis precisam ter um `id` único para garantir que estejam visíveis para tecnologias adaptativas. [Aprenda a corrigir `id`s duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Os atributos `[id]` em elementos focalizáveis ativos não são únicos"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Os atributos `[id]` em elementos focalizáveis ativos são únicos"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "É necessário que o valor de um código ARIA seja único para impedir que outras instâncias sejam ignoradas por tecnologias adaptativas. [Aprenda a corrigir códigos ARIA duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Os códigos ARIA não são únicos"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Os códigos ARIA são únicos"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Os campos de formulários com vários rótulos podem ser anunciados de maneira confusa por tecnologias adaptativas como leitores de tela, que usam o primeiro, o último ou todos os rótulos. [Aprenda a usar os rótulos de formulários](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Os campos de formulários têm vários ró<PERSON>"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nenhum campo de formulário tem vários rótulos"}, "core/audits/accessibility/frame-title.js | description": {"message": "Os usuários de leitores de tela utilizam títulos para descrever o conteúdo de frames. [Saiba mais sobre os títulos de frames](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Os elementos `<frame>` ou `<iframe>` não têm um título"}, "core/audits/accessibility/frame-title.js | title": {"message": "Os elementos `<frame>` ou `<iframe>` têm um título"}, "core/audits/accessibility/heading-order.js | description": {"message": "Títulos propriamente ordenados que não pulam níveis comunicam a estrutura semântica da página, facilitando a navegação e compreensão ao usar tecnologias adaptativas. [Saiba mais sobre ordem de títulos](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Os elementos de título não aparecem em uma ordem sequencial descendente"}, "core/audits/accessibility/heading-order.js | title": {"message": "Os elementos de título aparecem em uma ordem sequencial descendente"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Se uma página não especifica um atributo `lang`, o leitor de tela presume que a página está no idioma padrão escolhido pelo usuário na configuração. Se a página não está no idioma padrão, o leitor de tela pode ler o texto dela incorretamente. [Saiba mais sobre o atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "O elemento `<html>` não tem um atributo `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "O elemento `<html>` tem um atributo `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "A especificação de um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido ajuda os leitores de tela a enunciar o texto corretamente. [Aprenda a usar o atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "O elemento `<html>` não tem um valor válido para o atributo `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "O elemento `<html>` tem um valor válido para o atributo `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "O texto de elementos informativos precisa ser alternativo, breve e descritivo. Elementos decorativos podem ser ignorados com um atributo alternativo vazio. [Saiba mais sobre o atributo `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Os elementos de imagem não têm atributos `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Os elementos de imagem têm atributos `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Quando uma imagem é usada como um botão `<input>`, a oferta de texto alternativo ajuda o usuário do leitor de tela a entender a finalidade do botão. [Saiba mais sobre texto alternativo para imagens de entrada](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Os elementos `<input type=\"image\">` não têm texto `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Os elementos `<input type=\"image\">` têm texto `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Os marcadores garantem que os controles de formulário sejam enunciados corretamente por tecnologias adaptativas, como, por exemplo, leitores de tela. [Saiba mais sobre marcadores de elementos de formulários](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Os elementos de formulário não têm etiquetas associadas"}, "core/audits/accessibility/label.js | title": {"message": "Os elementos de formulário têm etiquetas associadas"}, "core/audits/accessibility/link-name.js | description": {"message": "Textos de link (e textos alternativos de imagens, quando utilizados como link) compreensíveis, únicos e focalizáveis melhoram a experiência de navegação para usuários de leitores de tela. [Aprenda a deixar os links mais acessíveis](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON> links não têm um nome compreensível"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON> links têm um nome compreensível"}, "core/audits/accessibility/list.js | description": {"message": "Os leitores de tela têm uma maneira específica de enunciar listas. Uma estrutura de lista adequada melhora os resultados do leitor de tela. [Saiba mais sobre estruturas de lista adequadas](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "As listas não contêm apenas elementos `<li>` e elementos compatíveis com script (`<script>` e `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "As listas contêm somente elementos `<li>` e elementos compatíveis com script (`<script>` e `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Os leitores de tela exigem que os itens de lista (`<li>`) estejam contidos em um elemento pai `<ul>`, `<ol>` ou `<menu>` para serem enunciados da forma correta. [Saiba mais sobre estruturas de lista adequadas](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Itens de lista (`<li>`) não estão contidos nos elementos pai `<ul>`, `<ol>` ou `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Itens de lista (`<li>`) estão contidos nos elementos pai `<ul>`, `<ol>` ou `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "O usuário não espera a atualização automática da página, o que move o foco novamente para a parte superior dela. Isso pode causar uma experiência confusa ou frustrante. [Saiba mais sobre a metatag de atualização](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "O documento usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "O documento não usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "A desativação do zoom gera problemas para usuários com baixa visão que utilizam a ampliação de tela para enxergar corretamente o conteúdo de uma página da Web. [Saiba mais sobre a metatag viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` é usado no elemento `<meta name=\"viewport\">` ou o atributo `[maximum-scale]` é menor que 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` não é usado no elemento `<meta name=\"viewport\">`, e o atributo `[maximum-scale]` não é menor que 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Os leitores de tela não traduzem conteúdo não textual. A adição de texto alternativo a elementos `<object>` ajuda os leitores de tela a transmitir o significado para os usuários. [Saiba mais sobre texto alternativo para elementos `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Os elementos `<object>` não têm texto alternativo"}, "core/audits/accessibility/object-alt.js | title": {"message": "Os elementos `<object>` têm texto alternativo"}, "core/audits/accessibility/tabindex.js | description": {"message": "Um valor maior que 0 indica uma ordem explícita de navegação. Embora tecnicamente válido, isso costuma gerar experiências frustrantes para os usuários que utilizam tecnologias adaptativas. [Saiba mais sobre o atributo `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Alguns elementos têm um valor de `[tabindex]` maior que 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nenhum elemento tem um valor de `[tabindex]` maior que 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Os leitores de tela têm recursos para facilitar a navegação em tabelas. Para melhorar a experiência dos usuários de leitores de tela, as c<PERSON><PERSON><PERSON> `<td>` que usam o atributo `[headers]` devem referenciar apenas outras células na mesma tabela. [Saiba mais sobre o atributo `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Células em um elemento `<table>` que usam o atributo `[headers]` referem-se a um elemento `id` não encontrado na mesma tabela."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Células em um elemento `<table>` que usam o atributo `[headers]` referem-se às células na mesma tabela."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Os leitores de tela têm recursos para facilitar a navegação em tabelas. Garantir que os cabeçalhos das tabelas se refiram sempre a alguns conjuntos de células pode melhorar a experiência dos usuários de leitores de tela. [Saiba mais sobre os cabeçalhos da tabela](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` não têm as c<PERSON><PERSON><PERSON> de dados descritas."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` têm as c<PERSON><PERSON><PERSON> de dados descritas."}, "core/audits/accessibility/valid-lang.js | description": {"message": "A especificação de um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido nos elementos ajuda a garantir que o texto seja pronunciado corretamente pelo leitor de tela. [Aprenda a usar o atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Os atributos `[lang]` não têm um valor válido"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Os atributos `[lang]` têm um valor válido"}, "core/audits/accessibility/video-caption.js | description": {"message": "Quando um vídeo é acompanhado de legendas, pessoas surdas e deficientes auditivas têm mais facilidade para acessar as informações dele. [Saiba mais sobre legendas de vídeo](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Os elementos `<video>` não contêm um elemento `<track>` com `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Os elementos `<video>` contêm um elemento `<track>` com `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON>or atual"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Token sugerido"}, "core/audits/autocomplete.js | description": {"message": "O `autocomplete` ajuda os usuários a enviar formulários mais rapidamente. Para reduzir o esforço dos usuários, considere definir o atributo `autocomplete` como um valor válido para ativá-lo. [<PERSON>ba mais sobre `autocomplete` nos formulários](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "Os elementos `<input>` não têm os atributos `autocomplete` corretos"}, "core/audits/autocomplete.js | manualReview": {"message": "Revisão manual necessária"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Revisar pedido de tokens"}, "core/audits/autocomplete.js | title": {"message": "Elementos `<input>` usam `autocomplete` corretamente"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Token(s) `autocomplete`: \"{token}\" é inválido em {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Revisão de pedido de tokens: \"{tokens}\" em {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Acionável"}, "core/audits/bf-cache.js | description": {"message": "Muitas navegações voltam para uma página anterior ou avançam de novo. O cache de avanço e retorno (bfcache) pode acelerar essas navegações de retorno. [Saiba mais sobre o bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo para a falha}one{# motivo para a falha}other{# motivos para a falha}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivo da falha"}, "core/audits/bf-cache.js | failureTitle": {"message": "A página impede a restauração do cache de avanço e retorno"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON> de falha"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Não há ações possíveis"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Suporte pendente do navegador"}, "core/audits/bf-cache.js | title": {"message": "A página não impediu a restauração do cache de avanço e retorno"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "As extensões do Chrome afetaram negativamente o desempenho de carregamento desta página. Tente fazer a auditoria da página no modo de navegação anônima ou em um perfil do Chrome sem extensões."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Avaliação de script"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Tempo total de CPU"}, "core/audits/bootup-time.js | description": {"message": "Diminua o tempo gasto com análise, compilação e execução de JS. Você vai perceber que a entrega de payloads JS menores ajuda a fazer isso. [Aprenda a reduzir o tempo de execução do JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduza o tempo de execução de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Tempo de execução de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Remover módulos JavaScript grandes e duplicados de pacotes para reduzir bytes desnecessários consumidos por atividades da rede. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Remova módulos duplicados em pacotes JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIFs grandes não são eficientes para exibir conteúdo animado. Para economizar bytes de rede, use vídeos MPEG4/WebM para animações e PNG/WebP em vez de GIF. [Saiba mais sobre formatos de vídeo eficientes](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)."}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Use formatos de vídeo para conteúdo animado"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfills e transformações permitem que navegadores legados usem novos recursos do JavaScript. No entanto, muitos não são necessários para navegadores modernos. Para seu JavaScript empacotado, adote uma estratégia moderna de implantação de scripts, usando detecção de recursos por módulo/sem módulo para reduzir o total de códigos enviados a navegadores modernos e, ao mesmo tempo, manter a compatibilidade com navegadores legados. [Aprenda a usar o JavaScript moderno](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Evitar a exibição de JavaScript legado em navegadores modernos"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formatos de imagem como WebP e AVIF costumam ter uma compressão melhor que PNG e JPEG, o que gera downloads mais rápidos e menos consumo de dados. [Saiba mais sobre formatos de imagem modernos](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Disponibilize imagens em formatos de última geração"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "A fim de reduzir o tempo para interação da página, faça o carregamento lento de imagens fora da tela e ocultas quando todos os recursos críticos já estiverem carregados. [Aprenda a adiar o carregamento de imagens fora da tela](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON> imagens fora da tela"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Recursos estão bloqueando a primeira exibição da sua página. Inclua JS/CSS crítico inline e adie todos os JS/estilos não críticos. [Aprenda a eliminar recursos de bloqueio de renderização](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimine recursos que impedem a renderização"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Grandes payloads de rede geram custos para o usuário e estão diretamente relacionados a tempos de carregamento maiores. [Aprenda a reduzir o tamanho dos payloads](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "O tamanho total foi de {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evite payloads de rede muito grandes"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita payloads de rede muito grandes"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "A redução de arquivos CSS pode diminuir o tamanho do payload de rede. [Aprenda a reduzir os arquivos CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Reduza o CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "A redução de arquivos JavaScript pode diminuir o tamanho de payloads e o tempo de análise de scripts. [Aprenda a diminuir o JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Reduza o JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Para diminuir o consumo de bytes da atividade da rede, reduza as regras não usadas nas folhas de estilo e adie o CSS não usado para conteúdo acima da dobra. [Aprenda a reduzir o CSS não usado](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduza o CSS não usado"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Para diminuir o consumo de bytes da atividade da rede, reduza o JavaScript não usado e adie o carregamento de scripts até que eles sejam necessários. [Aprenda a reduzir o JavaScript não usado](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduza o JavaScript não usado"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Um cache com ciclo de vida longo pode acelerar visitas repetidas à sua página. [Saiba mais sobre as políticas de cache eficientes](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 recurso encontrado}one{# recurso encontrado}other{# recursos encontrados}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Disponibilize recursos estáticos com uma política de cache eficiente"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Usa uma política de cache eficiente em recursos estáticos"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Imagens otimizadas são carregadas mais rapidamente e consomem menos dados da rede celular. [Aprenda a codificar imagens de forma eficiente](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifique as imagens com eficiência"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensõ<PERSON> reais"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensões exibidas"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "As imagens são maiores que o tamanho exibido"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "As imagens são adequadas para o tamanho exibido"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Veicule imagens que tenham o tamanho adequado para economizar dados da rede celular e melhorar o tempo de carregamento. [Aprenda a redimensionar imagens](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Defina um tamanho adequado para as imagens"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Recursos baseados em texto precisam ser veiculados com compactação (gzip, deflate ou brotli) para minimizar o total de bytes da rede. [Saiba mais sobre compactação de texto](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Ative a compactação de texto"}, "core/audits/content-width.js | description": {"message": "Se a largura do conteúdo do seu app não corresponder à largura da janela de visualização, não será possível otimizar o app para telas de dispositivos móveis. [Aprenda a redimensionar o conteúdo da janela de visualização](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "O tamanho da janela de visualização de {innerWidth} px não corresponde ao tamanho da janela {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "O conteúdo não está no tamanho correto para a janela de visualização"}, "core/audits/content-width.js | title": {"message": "O conteúdo está no tamanho correto para a janela de visualização"}, "core/audits/critical-request-chains.js | description": {"message": "As cadeias de solicitação críticas abaixo mostram quais recursos são carregados com prioridade alta. Diminua o tamanho das cadeias, reduza o tamanho do download de recursos ou adie o download de recursos desnecessários para melhorar o carregamento de página. [Aprenda a evitar encadeamento de solicitações críticas](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 rede encontrada}one{# rede encontrada}other{# redes encontradas}}"}, "core/audits/critical-request-chains.js | title": {"message": "Evitar encadeamento de solicitações críticas"}, "core/audits/csp-xss.js | columnDirective": {"message": "Diretiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravidade"}, "core/audits/csp-xss.js | description": {"message": "Uma Política de Segurança de Conteúdo (CSP) avançada reduz significativamente o risco de ataques de scripting em vários locais (XSS). [Aprenda a usar uma CSP para evitar ataques de XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxe"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "A página contém uma CSP definida em uma tag <meta>. Considere mover a CSP para um cabeçalho HTTP ou definir outra CSP restrita em um cabeçalho HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Nenhuma CSP encontrada no modo restrito"}, "core/audits/csp-xss.js | title": {"message": "Conferir se a CSP é eficaz contra ataques de XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Suspensão de uso/aviso"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "As APIs obsoletas acabarão sendo removidas do navegador. [Saiba mais sobre APIs descontinuadas](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 aviso encontrado}one{# aviso encontrado}other{# avisos encontrados}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Utiliza APIs obsoletas"}, "core/audits/deprecations.js | title": {"message": "Evita APIs obsoletas"}, "core/audits/dobetterweb/charset.js | description": {"message": "Uma declaração de codificação de caracteres é obrigatória. Ela pode ser feita com uma tag `<meta>` nos primeiros 1.024 bytes do HTML ou no cabeçalho de resposta HTTP de tipo de conteúdo. [Saiba mais sobre como declarar a codificação de caracteres](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "A declaração de charset está ausente ou ocorre tardiamente no HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Define corretamente o charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "A especificação de um doctype evita que o navegador alterne para o modo quirks. [Saiba mais sobre a declaração do tipo de documento](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "O nome do tipo de documento precisa ser o `html` da string"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "O documento contém um `doctype` que aciona o `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "O documento precisa conter um doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "O ID público deveria ser uma string vazia"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "O ID do sistema deveria ser uma string vazia"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "O documento contém um `doctype` que aciona o `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "A página não tem o doctype HTML e, assim, aciona o modo quirks"}, "core/audits/dobetterweb/doctype.js | title": {"message": "A página tem o doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estatística"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Um DOM grande aumenta o uso da memória, causa [cálculos de estilo](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) mais longos e produz [reflows de layout](https://developers.google.com/speed/articles/reflow) dispendiosos. [Aprenda a evitar um DOM com um tamanho grande demais](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}one{# elemento}other{# elementos}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evite DOM de tamanho excessivo"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidade máxima de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Máximo de elementos filhos"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evita DOM de tamanho excessivo"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Os usuários não confiam ou ficam confusos com sites que solicitam localização sem contexto. Vincule a solicitação a uma ação do usuário. [Saiba mais sobre a permissão de geolocalização](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita a permissão de geolocalização no carregamento de página"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita o pedido da permissão de geolocalização no carregamento de página"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipo de problema"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Os problemas registrados no painel `Issues` do Chrome Devtools indicam problemas não resolvidos. Eles podem ser causados por falhas de solicitações da rede, controles de segurança insuficientes e outros problemas do navegador. Abra o painel \"Issues\" do Chrome DevTools para ver mais detalhes sobre cada problema."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Os problemas foram registrados no painel `Issues` do Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloqueado pela política de origem cruzada"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Uso intenso de recursos por anúncios"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Nenhum problema no painel `Issues` do Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Vers<PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Todas as bibliotecas JavaScript de front-end detectadas na página. [Saiba mais sobre essa auditoria de diagnóstico de detecção da biblioteca JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Bibliotecas JavaScript detectadas"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Para usuários em conexões lentas, os scripts externos injetados dinamicamente via `document.write()` podem atrasar o carregamento de página em dezenas de segundos. [Aprenda a evitar document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evitar `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Os usuários não confiam ou ficam confusos com sites que solicitam o envio de notificações sem contexto. Vincule a solicitação a gestos do usuário. [Saiba mais sobre como receber permissões para notificações de forma responsável](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita a permissão de notificação no carregamento de página"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita o pedido da permissão de notificação no carregamento de página"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "O HTTP/2 oferece muitos benefícios com relação ao HTTP/1.1, incluindo cabeçalhos binários e multiplexação. [Saiba mais sobre o HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitação não veiculada via HTTP/2}one{# solicitação não veiculada via HTTP/2}other{# solicitações não veiculadas via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Use o HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Marque os listeners de eventos de toque e rolagem como `passive` para melhorar a performance de rolagem da página. [Saiba mais sobre a adoção de listeners de eventos passivos](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Não utiliza listeners passivos para melhorar o desempenho de rolagem"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Utiliza listeners passivos para melhorar o desempenho de rolagem"}, "core/audits/errors-in-console.js | description": {"message": "Erros registrados no console indicam problemas não resolvidos. Eles podem ocorrer devido a falhas de solicitação de rede e outras questões relacionadas ao navegador. [Saiba mais sobre esses erros na auditoria de diagnóstico do console](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "Erros do navegador foram registrados no console"}, "core/audits/errors-in-console.js | title": {"message": "Nenhum erro do navegador registrado no console"}, "core/audits/font-display.js | description": {"message": "Use o recurso CSS `font-display` para garantir que o texto possa ser visto pelo usuário enquanto as webfonts s<PERSON> carregadas. [Saiba mais sobre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Garanta que o texto continue visível durante o carregamento da webfont"}, "core/audits/font-display.js | title": {"message": "Todo o texto continua visível durante o carregamento da webfont"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{O Lighthouse não conseguiu verificar automaticamente o valor `font-display` para o URL {fontOrigin} de origem.}one{O Lighthouse não conseguiu verificar automaticamente o valor `font-display` para o URL {fontOrigin} de origem.}other{O Lighthouse não conseguiu verificar automaticamente os valores `font-display` para o URL {fontOrigin} de origem.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Proporção (real)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Proporção (exibida)"}, "core/audits/image-aspect-ratio.js | description": {"message": "As dimensões de exibição da imagem devem corresponder à proporção. [Saiba mais sobre proporções de imagens](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Exibe imagens com a proporção incorreta"}, "core/audits/image-aspect-ratio.js | title": {"message": "Exibe imagens com a proporção correta"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Tamanho real"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> exibido"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON> esperado"}, "core/audits/image-size-responsive.js | description": {"message": "As dimensões naturais da imagem precisam ser proporcionais ao tamanho da tela e à proporção de pixels para maximizar a nitidez. [Aprenda a fornecer imagens responsivas](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Exibe imagens em baixa resolução"}, "core/audits/image-size-responsive.js | title": {"message": "Exibe imagens em resolução adequada"}, "core/audits/installable-manifest.js | already-installed": {"message": "O app já está instalado"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Não foi possível fazer o download de um ícone necessário do manifesto"}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivo da falha"}, "core/audits/installable-manifest.js | description": {"message": "O service worker é a tecnologia que permite que seu app use muitos recursos do Progressive Web App, como disponibilidade off-line, adição à tela inicial e notificações push. Com implementações de manifesto e service worker adequadas, os navegadores podem solicitar proativamente que os usuários adicionem seu app à tela inicial deles, o que pode aumentar o engajamento. [Saiba mais sobre os requisitos de instalação do manifesto](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo}one{# motivo}other{# motivos}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "O manifesto do app da Web ou o service worker não atendem aos requisitos para instalação"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "O URL do aplicativo não corresponde ao ID da Play Store"}, "core/audits/installable-manifest.js | in-incognito": {"message": "A página foi carregada em uma janela anônima"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "A propriedade \"display\" do manifesto precisa ser \"standalone\", \"fullscreen\" ou \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "O manifesto contém o campo \"display_override\", e o primeiro modo de exibição compatível precisa ser \"standalone\", \"fullscreen\" ou \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Não foi possível buscar o manifesto, ele está vazio ou não foi possível analisá-lo"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "O URL do manifesto mudou enquanto a busca do manifesto era feita."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "O manifesto não contém um campo \"name\" ou \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "O manifesto não contém um ícone adequado. É necessário um formato PNG, SVG ou WebP de pelo menos {value0} px. O atributo \"sizes\" precisa estar definido e o atributo \"purpose\", se definido, precisa incluir \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nenhum dos ícones fornecidos é um quadrado de pelo menos {value0} px no formato PNG, SVG ou WebP com o atributo \"purpose\" não definido ou definido como \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "O ícone de download está vazio ou corrompido"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nenhum ID da Play Store foi fornecido"}, "core/audits/installable-manifest.js | no-manifest": {"message": "A página não tem um URL de manifesto <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Nenhum service worker correspondente foi detectado. Pode ser necessário atualizar a página ou conferir se o escopo do service worker da página atual inclui o escopo e o URL de início do manifesto."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Não foi possível verificar o service worker sem um campo \"start_url\" no manifesto"}, "core/audits/installable-manifest.js | noErrorId": {"message": "O ID do erro de instabilidade \"{errorId}\" não foi reconhecido"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "A página não foi exibida por uma origem segura"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "A página não está carregada no frame principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "A página não funciona off-line"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "O PWA foi desinstalado e as verificações de estabilidade estão sendo redefinidas."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "A plataforma de aplicativo especificada não é compatível com o Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "O manifesto especifica prefer_related_applications: verdadeiro"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications só é compatível no Android com Canais Beta e Stable do Chrome."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "O Lighthouse não conseguiu determinar se havia um service worker. Tente de novo com uma versão mais recente do Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "O Android não tem suporte ao esquema de URL do manifesto ({scheme})."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "O URL de início do manifesto não é válido"}, "core/audits/installable-manifest.js | title": {"message": "O manifesto do app da Web e o service worker atendem aos requisitos para instalação"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Um URL no manifesto contém um nome de usuário, uma senha ou uma porta"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "A página não funciona off-line. Ela não será mais considerada instalável após a versão estável do Chrome 93, em agosto de 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Permitido"}, "core/audits/is-on-https.js | blocked": {"message": "Bloqueado"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL não seguro"}, "core/audits/is-on-https.js | columnResolution": {"message": "Resolução da solicitação"}, "core/audits/is-on-https.js | description": {"message": "Todos os sites precisam ser protegidos com HTTPS, mesmo aqueles que não lidam com dados confidenciais. Por isso, evite [conteúdo misto](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), em que alguns recursos são carregados por HTTP mesmo quando o pedido inicial é exibido por HTTPS. O HTTPS evita que invasores falsifiquem ou escutem passivamente a comunicação entre o app e o usuário, além de ser um pré-requisito para HTTP/2 e várias novas APIs para plataforma da Web. [Saiba mais sobre o HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitação não segura encontrada}one{# solicitação não segura encontrada}other{# solicitações não seguras encontradas}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Não utiliza HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Utiliza HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Upgrade automático para HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Permitido com um alerta"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Este é o maior elemento com conteúdo na janela de visualização. [Saiba mais sobre o elemento Maior exibição de conteúdo](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elemento de Maior exibição de conteúdo"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribuição da CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Estes elementos DOM contribuem principalmente para o CLS da página. [Aprenda a melhorar o CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Evite grandes mudanças no layout"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "As imagens acima da dobra com carregamento lento são renderizadas mais tarde no ciclo de vida da página, podendo atrasar a métrica \"Maior exibição de conteúdo.\" [Saiba mais sobre o carregamento lento ideal](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "A imagem da Maior exibição de conteúdo foi carregada lentamente"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "A imagem da Maior exibição de conteúdo não foi carregada lentamente"}, "core/audits/long-tasks.js | description": {"message": "Lista as tarefas mais longas na linha de execução principal. Útil para identificar os piores contribuidores para a latência de entrada. [Aprenda a evitar tarefas mais longas da linha de execução principal](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tarefa longa encontrada}one{# tarefa longa encontrada}other{# tarefas longas encontradas}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON> longas da linha de execução principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoria"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Considere diminuir o tempo gasto na análise, compilação e execução de JS. Você vai perceber que a entrega de payloads JS menores ajuda a fazer isso. [Aprenda a minimizar o trabalho da linha de execução principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimize o trabalho da thread principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiza o trabalho da thread principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Os sites precisam funcionar em todos os principais navegadores para que sejam acessíveis ao maior número de usuários. [Saiba mais sobre a compatibilidade com vários navegadores](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "O site funciona em diferentes navegadores"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "É necessário que as páginas individuais permitam links diretos via URL e que os URLs sejam exclusivos para o propósito de compartilhamento em mídias sociais. [Saiba mais sobre o fornecimento de links diretos](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tem um URL."}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "As transições precisam ser dinâmicas durante a navegação por toque, mesmo em uma conexão de rede lenta. Essa experiência é essencial para a percepção de performance. [Saiba mais sobre transições de página](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "As transições de página não devem parecer bloqueadas pelo carregamento da rede."}, "core/audits/maskable-icon.js | description": {"message": "Um ícone mascarável garante que a imagem preencha toda a forma sem um efeito letterbox durante a instalação do app em um dispositivo. [Saiba mais sobre os ícones de manifesto mascaráveis](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "O manifesto não tem um ícone mascarável"}, "core/audits/maskable-icon.js | title": {"message": "O manifesto tem um ícone mascarável"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "\"Mudança de layout cumulativa\" mede o movimento de elementos visíveis na janela de visualização. [Saiba mais sobre a métrica \"Mudança de layout cumulativa\"](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "A métrica \"Interação com a próxima exibição\" mede a capacidade de resposta da página, ou seja, quanto tempo a página leva para responder de maneira visível à entrada do usuário. [Saiba mais sobre a métrica \"Interação com a próxima exibição\"](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "\"Primeira exibição de conteúdo\" marca o momento em que o primeiro texto ou imagem é disponibilizado. [Saiba mais sobre a métrica \"Primeira exibição de conteúdo\"](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "\"Primeira exibição significativa\" marca o momento em que o conteúdo principal de uma página se torna visível. [Saiba mais sobre a métrica \"Primeira exibição significativa\"](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "\"Tempo para interação da página\" é o período necessário para que uma página fique totalmente interativa. [Saiba mais sobre a métrica \"Tempo para interação da página\"](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "\"Maior exibição de conteúdo\" marca o momento em que o maior texto ou imagem é exibido. [Saiba mais sobre a métrica \"Maior exibição de conteúdo\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "A possível latência máxima na primeira entrada que seus usuários notariam seria a duração da tarefa mais longa. [Saiba mais sobre a métrica \"Possível latência máxima na primeira entrada\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "\"Índice de velocidade\" mostra a rapidez com que o conteúdo de uma página é preenchido visivelmente. [Saiba mais sobre a métrica \"Índice de velocidade\"](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Soma de todos os períodos entre \"FCP\" e \"Tempo para interação da página\", quando a duração da tarefa ultrapassa 50 ms, expressa em milésimos de segundo. [Saiba mais sobre a métrica \"Tempo total de bloqueio\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "O tempo de retorno (RTT, na sigla em inglês) da rede tem um grande impacto na performance. Se o RTT para uma origem é alto, significa que os servidores mais próximos do usuário podem melhorar a performance. [Saiba mais sobre o tempo de retorno](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Tempos de retorno da rede"}, "core/audits/network-server-latency.js | description": {"message": "As latências de servidor podem afetar a performance na Web. Se a latência do servidor de uma origem é alta, isso indica que ele está sobrecarregado ou que o back-end apresenta uma performance ruim. [Saiba mais sobre tempo de resposta do servidor](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latências do back-end do servidor"}, "core/audits/no-unload-listeners.js | description": {"message": "O evento `unload` não é acionado de maneira confiável, e a detecção dele pode evitar otimizações do navegador, como o Back-Forward Cache. Use `pagehide` ou `visibilitychange`. [Saiba mais sobre como descarregar listeners de eventos](https://web.dev/bfcache/#never-use-the-unload-event)."}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registra um listener `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Evita listeners de eventos `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animações que não são compostas podem ficar instáveis e aumentar a CLS. [Aprenda a evitar animações que não são compostas](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# elemento animado encontrado}one{# elemento animado encontrado}other{# elementos animados encontrados}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "A propriedade \"filter\" pode mover pixels"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Presença de outra animação incompatível"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "O efeito tem um modo composto diferente de \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evitar animações não compostas"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "A propriedade \"transform\" depende do tamanho da caixa"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Propriedade CSS incompatível: {properties}}one{Propriedade CSS incompatível: {properties}}other{Propriedades CSS incompatíveis: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "O efeito tem parâmetros de tempo incompatíveis"}, "core/audits/performance-budget.js | description": {"message": "Mantenha a quantidade e o tamanho de solicitações de rede de acordo com os destinos definidos pelo montante de desempenho informado. [Saiba mais sobre montantes de desempenho](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 solicitação}one{# solicitação}other{# solicitações}}"}, "core/audits/performance-budget.js | title": {"message": "Orçamento de desempenho"}, "core/audits/preload-fonts.js | description": {"message": "Pré-carregue fontes `optional` para que visitantes novos possam usá-las. [Saiba mais sobre o pré-carregamento de fontes](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "As fontes com `font-display: optional` não são pré-carregadas"}, "core/audits/preload-fonts.js | title": {"message": "As fontes com `font-display: optional` são pré-carregadas"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Se o elemento LCP for adicionado à página de maneira dinâmica, será necessário pré-carregar a imagem para melhorar a métrica LCP. [Saiba mais sobre o pré-carregamento de elementos LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Pré-carregar imagem de Maior exibição de conteúdo"}, "core/audits/redirects.js | description": {"message": "Os redirecionamentos causam mais atrasos antes do carregamento da página. [Aprenda a evitar o redirecionamentos de página](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evite redirecionamentos múltiplos de página"}, "core/audits/resource-summary.js | description": {"message": "Para definir orçamentos para a quantidade e o tamanho dos recursos da página, adicione um arquivo budget.json. [Saiba mais sobre montantes de desempenho](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 solicitação • {byteCount, number, bytes} KiB}one{# solicitação • {byteCount, number, bytes} Ki<PERSON>}other{# solicitações • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Man<PERSON>ha as contagens de solicitações baixas e os tamanhos de transferência pequenos"}, "core/audits/seo/canonical.js | description": {"message": "Os links canônicos sugerem o URL a ser exibido nos resultados da pesquisa. [Saiba mais sobre links canônicos](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Vários URLs com conflito ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL inválido ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Aponta para outro local de `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Não é um URL absoluto ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Aponta para o URL raiz do domínio (a página inicial), em vez de uma página de conteúdo equivalente"}, "core/audits/seo/canonical.js | failureTitle": {"message": "O documento não tem um `rel=canonical` válido"}, "core/audits/seo/canonical.js | title": {"message": "O documento tem um `rel=canonical` válido"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Link não rastreável"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Os mecanismos de pesquisa podem usar atributos `href` em links para rastrear sites. Garanta que o atributo `href` de elementos de âncora esteja vinculado a um destino adequado para que mais páginas do site possam ser descobertas. [Aprenda a tornar os links rastreáveis](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Os links não são rastreáveis"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Os links são rastreáveis"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Mais texto il<PERSON>í<PERSON>"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% de texto da página"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Fontes menores que 12 px não são legíveis e exigem que os visitantes que utilizam dispositivos móveis façam um gesto de pinça para aumentar o zoom e conseguir ler. Faça o possível para ter mais de 60% do texto da página com pelo menos 12 px. [Saiba mais sobre tamanhos de fonte legíveis](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} do texto legível"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "O texto é ilegível porque não há nenhuma metatag de janela de visualização otimizada para telas de dispositivos móveis."}, "core/audits/seo/font-size.js | failureTitle": {"message": "O documento não usa tamanhos de fonte legíveis"}, "core/audits/seo/font-size.js | legibleText": {"message": "Texto legível"}, "core/audits/seo/font-size.js | title": {"message": "O documento usa tamanhos de fonte legíveis"}, "core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON> links hreflang informam aos mecanismos de pesquisa qual versão de uma página deve ser listada nos resultados de pesquisa para um determinado idioma ou região. [Saiba mais sobre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "O documento não tem um `hreflang` válido"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valor de href relativo"}, "core/audits/seo/hreflang.js | title": {"message": "O documento tem um `hreflang` v<PERSON>lido"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Código de idioma inesperado"}, "core/audits/seo/http-status-code.js | description": {"message": "As páginas com falha no código de status HTTP talvez não sejam indexadas corretamente. [Saiba mais sobre códigos de status HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "A página tem uma falha no código de status HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "A página tem um código de status HTTP bem-sucedido"}, "core/audits/seo/is-crawlable.js | description": {"message": "Os mecanismos de pesquisa não vão incluir suas páginas nos resultados se não tiverem permissão para rastreá-las. [Saiba mais sobre as diretivas do rastreador](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "A página está bloqueada para indexação"}, "core/audits/seo/is-crawlable.js | title": {"message": "A página não está bloqueada para indexação"}, "core/audits/seo/link-text.js | description": {"message": "Um texto com link descritivo ajuda os mecanismos de pesquisa a entender o conteúdo. [Aprenda a deixar os links mais acessíveis](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link encontrado}one{# link encontrado}other{# links encontrados}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON> links não têm texto descritivo"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON>s links têm texto descritivo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Execute a [Ferramenta de teste de dados estruturados](https://search.google.com/structured-data/testing-tool/) e o [Linter de dados estruturados](http://linter.structured-data.org/) para validar os dados estruturados. [Saiba mais sobre dados estruturados](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Os dados estruturados são válidos"}, "core/audits/seo/meta-description.js | description": {"message": "Metadescrições podem ser incluídas nos resultados da pesquisa para resumir concisamente o conteúdo da página. [Saiba mais sobre metadescrições](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "O campo de texto da descrição está vazio."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "O documento não tem uma metadescrição"}, "core/audits/seo/meta-description.js | title": {"message": "O documento tem uma metadescrição"}, "core/audits/seo/plugins.js | description": {"message": "Mecanismos de pesquisa não podem indexar conteúdo de plug-in, e muitos dispositivos restringem plug-ins ou não são compatíveis com eles. [Saiba mais sobre como evitar plug-ins](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "O documento usa plug-ins"}, "core/audits/seo/plugins.js | title": {"message": "O documento evita plug-ins"}, "core/audits/seo/robots-txt.js | description": {"message": "Se o arquivo robots.txt for inválido, talvez não seja possível aos rastreadores entender como você quer que seu site seja rastreado ou indexado. [Saiba mais sobre o arquivo robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "A solicitação para robots.txt retornou o status HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 erro encontrado}one{# erro encontrado}other{# erros encontrados}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "O Lighthouse não fez o download de um arquivo robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt não é válido"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt é válido"}, "core/audits/seo/tap-targets.js | description": {"message": "Elementos interativos, como botões e links, precisam ser grandes o bastante (48x48 px) e ter espaço suficiente ao redor para poderem ser tocados sem sobreposição com outros elementos. [Saiba mais sobre áreas de toque](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} de áreas de toque dimensionadas corretamente"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "As áreas de toque são muito pequenas porque não há nenhuma metatag de janela de visualização otimizada para telas de dispositivos móveis"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "As áreas de toque não estão dimensionadas corretamente"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Área com sobreposição"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "As áreas de toque estão dimensionadas corretamente"}, "core/audits/server-response-time.js | description": {"message": "Mantenha curto o tempo de resposta do servidor para o documento principal, já que todos os outros pedidos dependem dele. [Saiba mais sobre a métrica \"Tempo até o primeiro byte\"](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "O documento raiz levou {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reduza o tempo de resposta inicial do servidor"}, "core/audits/server-response-time.js | title": {"message": "O tempo de resposta inicial do servidor foi curto"}, "core/audits/service-worker.js | description": {"message": "O service worker é a tecnologia que permite que seu app use muitos recursos do Progressive Web App, por exemplo, disponibilidade off-line, adicionar à tela inicial e notificações de push. [Saiba mais sobre os service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Esta página é controlada por um service worker, no entanto, nenhum `start_url` foi encontrado porque o manifesto não foi analisado como um JSON válido."}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Esta página é controlada por um service worker, no entanto, a `start_url` ({startUrl}) não está no escopo dele ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Esta página é controlada por um service worker, no entanto, nenhuma `start_url` foi encontrada porque nenhum manifesto foi recuperado."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Esta origem tem um ou mais service workers, no entanto, a página ({pageUrl}) não está em escopo."}, "core/audits/service-worker.js | failureTitle": {"message": "Não há registro de um service worker que controle a página e `start_url`"}, "core/audits/service-worker.js | title": {"message": "Há registro de um service worker que controla a página e `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Uma tela de apresentação personalizada garante uma experiência de alta qualidade quando os usuários abrem o aplicativo na tela inicial. [Saiba mais sobre telas de apresentação](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Não foi configurado para uma tela de apresentação personalizada"}, "core/audits/splash-screen.js | title": {"message": "Configurado para uma tela de apresentação personalizada"}, "core/audits/themed-omnibox.js | description": {"message": "É possível atribuir um tema relacionado ao seu site à barra de endereço do navegador. [Saiba mais sobre como adicionar temas à barra de endereço](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Não foi definida uma cor de tema para a barra de endereços."}, "core/audits/themed-omnibox.js | title": {"message": "Foi definida uma cor de tema para a barra de endereços."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (sucesso do cliente)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (redes sociais)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vídeo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produ<PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Algumas incorporações de terceiros podem ser carregadas lentamente. Considere substituí-las por uma fachada até que sejam necessárias. [Aprenda a adiar o carregamento de terceiros com uma fachada](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternativa de fachada disponível}one{# alternativa de fachada disponível}other{# alternativas de fachada disponíveis}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Alguns recursos de terceiros podem ser carregados lentamente com uma fachada"}, "core/audits/third-party-facades.js | title": {"message": "Carregamento lento de recursos de terceiros com fachadas"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Te<PERSON><PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "Código de terceiros pode afetar significativamente a performance de carregamento. Limite o número de provedores terceirizados redundantes e carregue o código de terceiros depois que a página tiver sido carregada. [Aprenda a minimizar o impacto de terceiros](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "A linha de execução principal foi bloqueada por {timeInMs, number, milliseconds} ms pelo código de terceiros"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduza o impacto de códigos de terceiros"}, "core/audits/third-party-summary.js | title": {"message": "Reduzir o uso de terceiros"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Medição"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "M<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Defina um orçamento de tempo para ajudar a monitorar a performance do seu site. Sites com bom desempenho são carregados rapidamente e respondem a eventos de entrada do usuário mais depressa. [Saiba mais sobre montantes de desempenho](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Orçamento de tempo"}, "core/audits/unsized-images.js | description": {"message": "Defina uma largura e altura explícitas em elementos de imagem para reduzir mudanças de layout e melhorar a CLS. [Aprenda a definir as dimensões de uma imagem](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Os elementos de imagem não têm `width` e `height` explícitas"}, "core/audits/unsized-images.js | title": {"message": "Os elementos de imagem têm `width` e `height` explícitas"}, "core/audits/user-timings.js | columnType": {"message": "Tipo"}, "core/audits/user-timings.js | description": {"message": "Instrumente seu app com a API User Timing para avaliar a performance real do app durante as principais experiências do usuário. [Saiba mais sobre marcações de velocidade do usuário](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 velocidade do usuário}one{# velocidade do usuário}other{# velocidades do usuário}}"}, "core/audits/user-timings.js | title": {"message": "Marcações e medições de User Timing"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Um `<link rel=preconnect>` foi encontrado para \"{securityO<PERSON>in}\", mas não foi usado pelo navegador. Verifique se você está usando o atributo `crossorigin` corretamente."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Adicione dicas de recursos de `preconnect` ou `dns-prefetch` para estabelecer conexões antecipadas a origens importantes de terceiros. [Aprenda a se pré-conectar às origens necessárias](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Pré-conecte às origens necessárias"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Foram encontradas mais de duas conexões `<link rel=preconnect>`. Elas precisam ser usadas com moderação e somente para as origens mais importantes."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Um `<link rel=preconnect>` foi encontrado para \"{securityOrigin}\", mas não foi usado pelo navegador. Use `preconnect` apenas para origens importantes que certamente serão solicitadas pela página."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Um `<link>` de pré-carregamento foi encontrado para \"{preloadURL}\", mas não foi usado pelo navegador. Verifique se você está usando o atributo `crossorigin` corretamente."}, "core/audits/uses-rel-preload.js | description": {"message": "Use `<link rel=preload>` para priorizar a busca de recursos que, no momento, são solicitados posteriormente no carregamento de página. [Aprenda a pré-carregar solicitações importantes](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as principais solicitaç<PERSON>es"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL do mapa"}, "core/audits/valid-source-maps.js | description": {"message": "Os mapas de origem traduzem códigos minificados para o código-fonte original. Isso ajuda os desenvolvedores na depuração durante a produção. Além disso, o Lighthouse pode fornecer mais insights. Implante mapas de origem para usar esses benefícios. [Saiba mais sobre os mapas de origem](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Mapas de origem ausentes no JavaScript principal grande"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Mapa de origem ausente do arquivo JavaScript grande"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Alerta: 1 item ausente em `.sourcesContent`}one{Alerta: # item ausente em `.sourcesContent`}other{Alerta: # itens ausentes em `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "A página tem mapas de origem válidos"}, "core/audits/viewport.js | description": {"message": "Uma metatag `<meta name=\"viewport\">` não só otimiza seu app para tamanhos de tela de dispositivos móveis, como também evita [um atraso de 300 milissegundos na entrada do usuário](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Saiba mais sobre como usar a metatag viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Nenhuma tag `<meta name=\"viewport\">` foi encontrada."}, "core/audits/viewport.js | failureTitle": {"message": "Não há uma tag `<meta name=\"viewport\">` com `width` ou `initial-scale` definida"}, "core/audits/viewport.js | title": {"message": "Há uma tag `<meta name=\"viewport\">` com `width` ou `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Esse é o trabalho de bloqueio de linhas de execução que ocorre durante a medição da interação com a próxima exibição. [Saiba mais sobre a métrica \"Interação com a próxima exibição\"](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms gastos no evento \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Destino do evento"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimizar o trabalho durante interações importantes"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Latência na entrada"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Atraso na apresentação"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tempo de processamento"}, "core/audits/work-during-interaction.js | title": {"message": "Minimizar o trabalho durante interações importantes"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Veja aqui oportunidades de melhorar o uso de ARIA no seu aplicativo, o que pode aprimorar a experiência dos usuários de tecnologias assistivas, como leitores de tela."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Veja aqui oportunidades de oferecer conteúdo alternativo para áudio e vídeo. <PERSON><PERSON> pode melhorar a experiência de usuários com deficiências auditivas e visuais."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Áudio e vídeo"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Esses itens destacam as práticas recomendadas comuns para acessibilidade."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Práticas recomendadas"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Essas verificações destacam oportunidades para [melhorar a acessibilidade do seu app da Web](https://developer.chrome.com/docs/lighthouse/accessibility/). Somente um subconjunto de problemas de acessibilidade pode ser detectado automaticamente, portanto, testes manuais também devem ser realizados."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Esses itens se referem a áreas que uma ferramenta de teste automatizada não pode cobrir. Saiba mais no nosso guia sobre [como realizar uma avaliação de acessibilidade](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Acessibilidade"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Veja aqui oportunidades de melhorar a legibilidade do seu conteúdo."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Veja aqui oportunidades de melhorar a interpretação que usuários de diferentes localidades fazem do seu conteúdo."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalização e localização"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Veja aqui oportunidades de melhorar a semântica dos controles do seu aplicativo. <PERSON><PERSON> pode melhorar a experiência de usuários de tecnologias assistivas, como leitores de tela."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nomes e etiquetas"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Veja aqui oportunidades de melhorar a navegação por teclado no seu aplicativo."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegação"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Veja aqui oportunidades para melhorar a experiência de leitura de dados em tabelas ou listas usando tecnologia adaptativa, como um leitor de tela."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabelas e listas"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilidade de navegadores"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Práticas recomendadas"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "G<PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Garantia e segurança"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiência do usuário"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Os orçamentos de desempenho definem padrões para o desempenho do seu site."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Orçamentos"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mais informações sobre o desempenho do seu aplicativo. Esses números não [afetam diretamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) o índice de desempenho."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "O aspecto de desempenho mais importante é a rapidez com que os pixels são renderizados na tela. Principais métricas: Primeiro aparecimento com conteúdo, Primeira exibição importante"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Melhorias do primeiro aparecimento"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Essas sugestões podem ajudar a acelerar o carregamento de página. Elas não [afetam diretamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) o índice de desempenho."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Aprimore a experiência geral de carregamento, para que a página seja responsiva e esteja pronta para ser usada assim que possível. Principais métricas: Tempo até fica interativa, Índice de velocidade"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> gera<PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Essas verificações validam os aspectos de um Progressive Web App. [Aprenda a criar um bom Progressive Web App](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Essas verificações são solicitadas pela [Lista de verificação de PWA](https://web.dev/pwa-checklist/) de referência, mas não são automaticamente realizadas pelo Lighthouse. Elas não afetam sua pontuação, mas é importante verificá-las manualmente."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Pode ser instalado"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Otimizado para PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Essas verificações garantem que sua página siga orientações básicas para otimização de mecanismos de pesquisa. Há muitos outros fatores não avaliados pelo Lighthouse que ainda podem afetar sua classificação na pesquisa, como a performance nas [Principais métricas da Web](https://web.dev/learn-core-web-vitals/). [Saiba mais sobre os Fundamentos da Pesquisa Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Execute estes validadores adicionais no seu site para verificar mais práticas recomendadas de SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formate seu HTML de maneira que permita que os rastreadores entendam melhor o conteúdo do seu app."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Práticas recomendadas para conteúdo"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para que seu app seja exibido nos resultados da pesquisa, é necessário que os rastreadores tenham acesso a ele."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreamento e indexação"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Suas páginas precisam ser compatíveis com dispositivos móveis, para que o usuário não tenha que usar gestos de pinça ou aumentar o zoom para ler o conteúdo. [Aprenda a criar páginas compatíveis com dispositivos móveis](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Otimizada para dispositivos móveis"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "O dispositivo testado parece ter uma CPU mais lenta do que o esperado pelo Lighthouse. Isso pode afetar negativamente seu índice de desempenho. Saiba mais sobre [como calibrar um multiplicador de lentidão da CPU adequado](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "A página pode não ser carregada conforme esperado porque seu URL de teste ({requested}) foi redirecionado para {final}. Teste o segundo URL diretamente."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Carregamento da página lento demais para ser concluído dentro do limite de tempo. Os resultados podem estar incompletos."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "A limpeza do cache do navegador expirou. Tente auditar esta página mais uma vez e registre um bug se o problema persistir."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Pode haver dados armazenados afetando o desempenho de carregamento neste local: {locations}. Examine esta página em uma janela anônima para evitar que esses recursos afetem suas pontuações.}one{Pode haver dados armazenados afetando o desempenho de carregamento neste local: {locations}. Examine esta página em uma janela anônima para evitar que esses recursos afetem suas pontuações.}other{Pode haver dados armazenados afetando o desempenho de carregamento nestes locais: {locations}. Examine esta página em uma janela anônima para evitar que esses recursos afetem suas pontuações.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "A limpeza dos dados de origem expirou. Tente auditar esta página mais uma vez e registre um bug se o problema persistir."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Apenas páginas carregadas por uma solicitação GET são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Apenas páginas com um código de status de 2XX podem ser adicionadas ao cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "O Chrome detectou uma tentativa de executar o JavaScript enquanto a página estava no cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "No momento, as páginas que solicitaram um AppBanner não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "O cache de avanço e retorno foi desativado por flags. Acesse chrome://flags/#back-forward-cache para ativá-lo localmente no dispositivo."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "O cache de avanço e retorno foi desativado pela linha de comando."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "O cache de avanço e retorno foi desativado devido à memória insuficiente."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "O delegado não oferece suporte ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "O cache de avanço e retorno foi desativado pelo pré-renderizador."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Não é possível adicionar a página ao cache porque ela tem uma instância BroadcastChannel com listeners registrados."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Não é possível adicionar páginas com um cabeçalho cache-control:no-store ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "O cache foi apagado intencionalmente."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "A página foi removida do cache para permitir que outra fosse adicionada."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "No momento, as páginas que contêm plug-ins não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "As páginas que usam a API FileChooser não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "As páginas que usam a API File System Access não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "No momento, as páginas que usam o Media Device Dispatcher não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Um player de mídia estava ativo quando o usuário sa<PERSON> da página."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "As páginas que usam a API MediaSession e definiram um estado de reprodução não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "As páginas que usam a API MediaSession e definiram gerenciadores de ação não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "O cache de avanço e retorno foi desativado devido ao leitor de tela."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "As páginas que usam a classe SecurityHandler não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "As páginas que usam a API Serial não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "As páginas que usam a API WebAuthetication não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "As páginas que usam a API WebBluetooth não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "As páginas que usam a API WebUSB não são qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "No momento, as páginas que usam um worker ou worklet dedicados não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "O documento não concluiu o carregamento antes de o usuário sair dele."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "O banner de apps estava presente quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "O Gerenciador de senhas do Chrome estava presente quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A destilação do DOM estava em andamento quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "O leitor do destilador do DOM estava presente quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "O cache de avanço e retorno foi desativado porque as extensões usam uma API de mensagens."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Extensões com conexão de longa duração precisam encerrar a conexão antes de serem adicionadas ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Extensões com conexão de longa duração tentaram enviar mensagens a frames no cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "O cache de avanço e retorno foi desativado devido a extensões."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Uma caixa de diálogo modal (por exemplo, uma de senha HTTP ou reenvio de formulário) foi mostrada quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "A página off-line foi mostrada quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "A barra de intervenção de falta de memória estava presente quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Havia solicitações de permissão quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "O bloqueador de pop-ups estava presente quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Os detalhes do recurso Navegação segura foram mostrados quando o usuário saiu da página."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "O recurso Navegação segura considerou esta página como abusiva e bloqueou pop-ups."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Um service worker foi ativado enquanto a página estava no cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "O cache de avanço e retorno foi desativado devido a um erro no documento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Páginas usando FencedFrames não podem ser armazenadas em bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "A página foi removida do cache para permitir que outra fosse adicionada."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "No momento, as páginas que concederam acesso ao streaming de mídia não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "No momento, as páginas que usam portais não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "No momento, as páginas que usam a classe IdleManager não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "No momento, as páginas com uma conexão IndexedDB aberta não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "APIs não qualificadas foram usadas."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "No momento, as páginas que tiveram o JavaScript injetado por extensões não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "No momento, as páginas que tiveram folhas de estilo injetadas por extensões não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Ocorreu um erro interno."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "O cache de avanço e retorno foi desativado devido a uma solicitação de sinal de atividade."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "No momento, as páginas que usam o bloqueio de teclado não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | loading": {"message": "A página não concluiu o carregamento antes de o usuário sair dela."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-cache ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-store ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "A navegação foi cancelada antes que a página pudesse ser restaurada usando o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "A página foi removida do cache porque uma conexão de rede ativa recebeu dados demais. O Chrome limita a quantidade de dados que uma página pode receber em cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "No momento, as páginas com fetch() ou XHR em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "A página foi removida do cache de avanço e retorno porque uma solicitação de rede ativa envolveu um redirecionamento."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "A página foi removida do cache porque uma conexão de rede ficou aberta por tempo demais. O Chrome limita a quantidade de tempo pelo qual uma página pode receber dados em cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Não é possível adicionar páginas que não têm um cabeçalho de resposta válido ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "A navegação aconteceu em um frame diferente do principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "No momento, as páginas com transações de banco de dados indexado em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "No momento, as páginas com uma solicitação de rede em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "No momento, as páginas com uma solicitação de busca de rede em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "No momento, as páginas com uma solicitação de rede em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "No momento, as páginas com uma solicitação XHR de rede em andamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "No momento, as páginas que usam o PaymentManager não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "No momento, as páginas que usam o picture-in-picture não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | portal": {"message": "No momento, as páginas que usam portais não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | printing": {"message": "No momento, as páginas que usam a interface de impressão não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "A página foi aberta usando \"`window.open()`\" e é referenciada por outra guia, ou a página abriu uma janela."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "O processo do renderizador da página no cache de avanço e retorno falhou."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "O processo do renderizador da página no cache de avanço e retorno foi encerrado."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "No momento, as páginas que solicitaram permissões de captura de áudio não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "No momento, as páginas que solicitaram permissões de sensor não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "No momento, as páginas que solicitaram a sincronização em segundo plano ou permissões de busca não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "No momento, as páginas que solicitaram permissões de MIDI não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "No momento, as páginas que solicitaram permissões de notificação não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "No momento, as páginas que solicitaram acesso ao armazenamento não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "No momento, as páginas que solicitaram permissões de captura de vídeo não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Apenas páginas com um esquema de URL HTTP/HTTPS podem ser adicionadas ao cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "A página foi reivindicada por um service worker enquanto estava no cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Um service worker tentou enviar um `MessageEvent` à página no cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "A inscrição do ServiceWorker foi cancelada enquanto a página estava no cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "A página foi removida do cache de avanço e retorno devido à ativação de um service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "O Chrome foi reiniciado e apagou as entradas do cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "No momento, as páginas que usam a interface SharedWorker não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "No momento, as páginas que usam a classe SpeechRecognizer não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "No momento, as páginas que usam a SpeechSynthesis não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Um iframe na página iniciou uma navegação que não foi concluída."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos secundários com cache-control:no-cache ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos secundários com cache-control:no-store ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | timeout": {"message": "A página excedeu o tempo máximo no cache de avanço e retorno e expirou."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "A página expirou ao ser adicionada ao cache de avanço e retorno, provavelmente devido aos gerenciadores pagehide abertos há muito tempo."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "A página tem um gerenciador de descarregamento no frame principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "A página tem um gerenciador de descarregamento em um frame secundário."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "O navegador mudou o cabeçalho de substituição do user agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "No momento, as páginas que concederam acesso à gravação de vídeo ou áudio não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "No momento, as páginas que usam WebDatabase não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webHID": {"message": "No momento, as páginas que usam WebHID não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "No momento, as páginas que usam WebLocks não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "No momento, as páginas que usam WebNfc não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "No momento, as páginas que usam a classe WebOTPService não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Não é possível adicionar páginas com WebRTC ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webShare": {"message": "No momento, as páginas que usam WebShare não estão qualificadas para o cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Não é possível adicionar páginas com WebSocket ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Não é possível adicionar páginas com WebTransport ao cache de avanço e retorno."}, "core/lib/bf-cache-strings.js | webXR": {"message": "No momento, as páginas que usam o WebXR não estão qualificadas para o cache de avanço e retorno."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Adicione os esquemas de URL https: e http:, ignorados por navegadores compatíveis com \"strict-dynamic\", para oferecer compatibilidade com versões anteriores dos navegadores."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "O uso de disown-opener está suspenso desde a CSP3. Use o cabeçalho Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "O uso de referrer está suspenso desde a CSP2. Use o cabeçalho Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "O uso de reflected-xss está suspenso desde a CSP2. Use o cabeçalho X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "A ausência de base-uri permite a injeção de tags <base> para definir o URL base de todos os URLs relativos (como scripts) em um domínio controlado por invasores. Recomendamos definir base-uri como \"none\" ou \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "A ausência do object-src permite a injeção de plug-ins que executam scripts não seguros. Se possível, defina o object-src como \"none\"."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "A diretiva script-src está ausente. Isso pode permitir a execução de scripts não seguros."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Você se esqueceu de usar o ponto e vírgula? {keyword} parece ser uma diretiva, não uma palavra-chave."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces precisam usar o conjunto de caracteres base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Os nonces precisam ter pelo menos oito caracteres."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Evite usar esquemas de URL simples ({keyword}) nesta diretiva. Esse tipo de esquema permite o uso de scripts de um domínio não seguro."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Evite usar caracteres curinga simples ({keyword}) nesta diretiva. Esse tipo de caractere permite o uso de scripts de domínios não seguros."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "O destino do relatório só é configurado pela diretiva report-to, que é compatível apenas com navegadores baseados no Chromium. Por isso, é recomendado usar também uma diretiva report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nenhuma CSP configura um destino de relatório. Isso dificulta a manutenção da CSP ao longo do tempo e o monitoramento de falhas."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "As listas de permissões de host podem ser frequentemente ignoradas. Em vez disso, se necess<PERSON>rio, use \"strict-dynamic\" junto a nonces ou hashes da CSP."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Diretiva desconhecida da CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON> que {keyword} é uma palavra-chave inválida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inline\" permite a execução de scripts não seguros na página, além de manipuladores de eventos. Use os nonces ou hashes de CSP para permitir scripts individualmente."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Adicione \"unsafe-inline\", ignorado por navegadores compatíveis com nonces/hashes, para oferecer compatibilidade com versões anteriores dos navegadores."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "A autorização não será coberta pelo caractere curinga (*) no gerenciamento do CORS de `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Solicitações de recursos com URLs que contêm caracteres `(n|r|t)` de espaço em branco removido e caracteres \"menor que\" (`<`) estão bloqueadas. Remova novas linhas e codifique caracteres \"menor que\" em lugares como valores de atributos de elementos para carregar esses recursos."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "O uso de `chrome.loadTimes()` foi descontinuado. Use a API padronizada Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "O uso de `chrome.loadTimes()` foi descontinuado. Use a API padronizada Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "O uso de `chrome.loadTimes()` foi descontinuado. Use a API padronizada `nextHopProtocol` na Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Cookies contendo um caractere `(0|r|n)` serão rejeitados em vez de truncados."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "O relaxamento de políticas da mesma origem com a definição de `document.domain` foi descontinuado e será desativado por padrão. Este alerta de descontinuação é para um acesso de origem cruzada que foi ativado ao configurar `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "O acionamento da função {PH1} de iframes de origem cruzada foi descontinuado e será removido."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Use o atributo `disableRemotePlayback` para desativar a integração padrão do Google Cast em vez de usar o seletor `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "O uso de {PH1} foi descontinuado. Use {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Este é um exemplo de mensagem traduzida sobre um problema de descontinuação."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "O relaxamento de políticas da mesma origem com a definição de `document.domain` foi descontinuado e será desativado por padrão. Para continuar usando esse recurso, desative os clusters de agente com origin-key, enviando um cabeçalho `Origin-Agent-Cluster: ?0` junto à resposta HTTP para o documento e os frames. Confira mais detalhes em https://developer.chrome.com/blog/immutable-document-domain/ (link em inglês)."}, "core/lib/deprecations-strings.js | eventPath": {"message": "A API `Event.path` foi descontinuada e será removida. Use `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "O cabeçalho `Expect-CT` foi descontinuado e vai ser removido. O Chrome exige Transparência dos certificados para todos os certificados considerados confiáveis emitidos depois de 30 de abril de 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Verifique a página de status do recurso para saber mais detalhes."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` e `watchPosition()` não funcionam mais em origens não seguras. Para usar esse recurso, troque a origem do seu aplicativo por outra que seja segura, por exemplo, HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "O uso de `getCurrentPosition()` e `watchPosition()` foi descontinuado em origens não seguras. Para usar esse recurso, troque a origem do seu aplicativo por outra que seja segura, por exemplo, HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` não funciona mais em origens não seguras. Para usar esse recurso, troque a origem do seu aplicativo por outra que seja segura, por exemplo, HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "O uso de `RTCPeerConnectionIceErrorEvent.hostCandidate` foi descontinuado. Use `RTCPeerConnectionIceErrorEvent.address` ou `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "A origem do comerciante e os dados arbitrários do evento do service worker `canmakepayment` foram descontinuados e vão ser removidos: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "O site solicitou um recurso secundário de uma rede que ele só conseguiu acessar devido à posição privilegiada na rede que os usuários dele têm. Essas solicitações expõem para a Internet dispositivos e servidores particulares, aumentando o risco de ataques de falsificação de solicitações entre sites (CSRF, na sigla em inglês) e/ou de vazamento de informações. Para reduzir os riscos, o Chrome descontinua solicitações para recursos secundários particulares quando iniciadas em contextos não seguros e vai começar a fazer o bloqueio delas."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "O CSS não pode ser carregado usando URLs `file:`, a menos que eles terminem com uma extensão de arquivo `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "O uso de `SourceBuffer.abort()` para cancelar a remoção de intervalo assíncrono de `remove()` foi descontinuado devido a uma mudança na especificação. O suporte será removido futuramente. Como alternativa, você precisa detectar o evento `updateend`. O uso de `abort()` tem como objetivo cancelar um anexo de mídia assíncrono ou redefinir o estado do analisador."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "A definição de `MediaSource.duration` abaixo do carimbo de data/hora mais alto da apresentação de frames codificados em buffer foi descontinuada por causa de uma mudança na especificação. O suporte para remoção implícita de mídia truncada em buffer será removido no futuro. Faça uma `remove(newDuration, oldDuration)` explícita em todos os `sourceBuffers`, em que `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Esta mudança vai entrar em vigor com a versão principal {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "A Web MIDI pede permissão de uso mesmo que o sysex não esteja especificado em `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "A API Notification não pode mais ser usada em origens não seguras. Troque a origem do seu aplicativo para uma que seja segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "A permissão para a API Notification não pode mais ser solicitada em um iframe de origem cruzada. Solicite a permissão em um frame de nível superior ou abra uma nova janela."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Seu parceiro está negociando uma versão (D)TLS obsoleta. Fale com ele para que isso seja corrigido."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "O WebSQL em contextos não seguros foi descontinuado e vai ser removido em breve. Use Web Storage ou Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Especificar `overflow: visible` em tags de img, video e canvas pode fazer com que elas produzam conteúdo visual fora dos limites do elemento. Consulte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md (em inglês) para saber mais."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "O uso de `paymentManager.instruments` foi descontinuado. Use uma instalação just-in-time para gerenciadores de pagamento."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Sua chamada `PaymentRequest` ignorou a diretiva `connect-src` da Política de Segurança de Conteúdo (CSP). Essa ação foi descontinuada. Adicione o identificador da forma de pagamento da API `PaymentRequest` (no campo `supportedMethods`) à diretiva `connect-src` da CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "O uso de `StorageType.persistent` foi descontinuado. Use o `navigator.storage` padronizado."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "O elemento `<source src>` com um pai `<picture>` é inválido e será ignorado. Use `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "O uso de `window.webkitStorageInfo` foi descontinuado. Use o `navigator.storage` padronizado."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "As solicitações de recursos secundários com URLs que contêm credenciais incorporadas (como `**********************/`) estão bloqueadas."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "A restrição `DtlsSrtpKeyAgreement` foi removida. Você especificou um valor `false` para essa restrição. Essa ação foi interpretada como uma tentativa de usar o método `SDES key negotiation` removido. Essa funcionalidade foi removida. Use um serviço que tenha suporte à `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "A restrição `DtlsSrtpKeyAgreement` foi removida. Você especificou um valor `true` para essa restrição. Essa ação não teve efeito, mas você pode remover a restrição para manter a organização."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` detectado. Não há mais suporte para esse dialeto do `Session Description Protocol`. Use `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "O `Plan B SDP semantics`, que é usado ao criar uma `RTCPeerConnection` com `{sdpSemantics:plan-b}`, é uma versão legada não padrão do `Session Description Protocol`, que foi excluído permanentemente da plataforma Web. Ele ainda está disponível ao criar com `IS_FUCHSIA`, mas pretendemos fazer a exclusão assim que possível. Evite depender dele. Acesse https://crbug.com/1302249 (link em inglês) para ver o status."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "A opção `rtcpMuxPolicy` foi descontinuada e vai ser removida."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` requer isolamento de origem cruzada. Consulte https://developer.chrome.com/blog/enabling-shared-array-buffer/ (link em inglês) para ver mais detalhes."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "A API `speechSynthesis.speak()` sem ativação do usuário foi descontinuada e será removida."}, "core/lib/deprecations-strings.js | title": {"message": "Um recurso descontinuado foi usado"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "As extensões precisam ativar o isolamento de origem cruzada para continuar usando o recurso `SharedArrayBuffer`. Consulte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ (link em inglês)."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "A função {PH1} é específica para fornecedores. Use a função padrão {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "Não há suporte da resposta JSON para UTF-16 no `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "O `XMLHttpRequest` síncrono na linha de execução principal foi descontinuado por causa dos efeitos prejudiciais à experiência do usuário final. Para receber mais ajuda, acesse https://xhr.spec.whatwg.org/ (link em inglês)."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "O uso de `supportsSession()` foi descontinuado. Use `isSessionSupported()` e verifique o valor booleano resolvido."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tempo de bloqueio da linha de execução principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Descrição"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Duração"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementos com falha"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Localização"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nome"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Acima do orçamento"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Solicitações"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Tamanho do recurso"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Fonte"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> iní<PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tempo gasto"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Possível economia"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Possível economia"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Possível economia de {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elemento encontrado}one{# elemento encontrado}other{# elementos encontrados}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Possível economia de {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Primeira exibição importante"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Fonte"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagem"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interação com Próxima Exibição"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alto"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Baixo"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Médio"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Possível latência máxima na primeira entrada"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Mí<PERSON>"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Outro"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Outros recursos"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Folha de estilo"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Te<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Ocorreu um erro com a gravação dos rastros no carregamento de página. Execute o Lighthouse novamente. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Tempo limite atingido ao aguardar a conexão inicial do protocolo do depurador."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "O Chrome não coletou nenhuma captura de tela durante o carregamento de página. Verifique se há conteúdo visível na página e execute o Lighthouse novamente. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Os servidores DNS não resolveram o domínio fornecido."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "O coletor {artifactName} obrigatório encontrou um erro: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Ocorreu um erro interno do Chrome. Reinicie o Chrome e tente executar o Lighthouse novamente."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "O coletor {artifactName} obrigatório não foi executado."}, "core/lib/lh-error.js | noFcp": {"message": "A página não desenhou nenhum conteúdo. Mantenha a janela do navegador em primeiro plano durante o carregamento e tente novamente. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "A página não mostrava conteúdo qualificado como a Maior exibição de conteúdo (LCP). Verifique se a página tem um elemento LCP válido e tente de novo. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "A página fornecida não é HTML (exibida como Tipo MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Esta versão do Chrome é antiga demais para ser compatível com \"{featureName}\". Use uma versão mais recente para ver resultados completos."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "O Lighthouse não carregou de maneira confiável o URL solicitado, porque a página parou de responder."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "O URL informado não tem um certificado de segurança válido. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "O Chrome impediu o carregamento da página com um intersticial. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações. (Detalhes: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações. (Código de status: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "A página demorou muito para ser carregada. Siga as oportunidades no relatório para diminuir o tempo de carregamento da página, depois execute o Lighthouse novamente. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "A espera pela resposta do protocolo DevTools excedeu o tempo limite. (Método: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "A busca de conteúdo de recursos excedeu o tempo limite"}, "core/lib/lh-error.js | urlInvalid": {"message": "O URL informado parece ser inválido."}, "core/lib/navigation-error.js | warningXhtml": {"message": "O tipo MIME da página é XHTML: o Lighthouse não oferece suporte explícito a esse tipo de documento."}, "core/user-flow.js | defaultFlowName": {"message": "Fluxo de usuários ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Relatório de navegação ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "<PERSON><PERSON><PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Relatório de período ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Todos os relatórios"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorias"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Acessibilidade"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Práticas recomendadas"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Computador"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Como o relatório de fluxos do Lighthouse funciona"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Como os fluxos funcionam"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Use os relatórios de navegação para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Use os relatórios instantâneos para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Use os relatórios de período para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obter uma pontuação de desempenho do Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Medir o desempenho de carregamento de página, como, por exemplo, Maior exibição de conteúdo e Índice de velocidade."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Avaliar os recursos do Progressive Web App."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Localizar problemas de acessibilidade em aplicativos de página única ou formulários complexos."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Avaliar práticas recomendadas de menus e elementos da IU ocultos nas interações."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Medir as mudanças de layout e o tempo de execução em JavaScript em uma série de interações."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descobrir oportunidades de desempenho para melhorar a experiência de páginas de longa duração e aplicativos de página única."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} auditoria informativa}one{{numInformative} auditoria informativa}other{{numInformative} auditorias informativas}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Dispositivo móvel"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Carregamento de página"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Os relatórios de navegação analisam o carregamento de uma única página, exatamente como os relatórios originais do Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Relatório de navegação"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} relatório de navegação}one{{numNavigation} relatório de navegação}other{{numNavigation} relatórios de navegação}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} auditoria com possibilidade de aprovação}one{{numPassableAudits} auditoria com possibilidade de aprovação}other{{numPassableAudits} auditorias com possibilidade de aprovação}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} auditoria aprovada}one{{numPassed} auditoria aprovada}other{{numPassed} auditorias aprovadas}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Média"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Erro"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Bo<PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Estado capturado da página"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Os relatórios instantâneos analisam a página em um estado específico, normalmente após interações do usuário."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Relat<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} relatório de snapshot}one{{numSnapshot} relatório de snapshot}other{{numSnapshot} relatórios de snapshot}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Resumo"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interações do usuário"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Os relatórios de período analisam um período arbitrário de tempo, que normalmente contém interações do usuário."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Relatório de período"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} relatório de período}one{{numTimespan} relatório de período}other{{numTimespan} relatórios de período}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Relatório de fluxo de usuários do Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Para conteúdo animado, use [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) para reduzir o uso da CPU quando o conteúdo estiver fora da tela."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Considere exibir todos os componentes [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) em formatos WebP ao especificar um substituto apropriado para outros navegadores. [<PERSON><PERSON> ma<PERSON>](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Verifique se você está usando [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) para imagens que são carregadas lentamente. [Saiba mais](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Use ferramentas como o [Otimizador de AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) para [renderizar layouts de AMP no servidor](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulte a [documentação de AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) para garantir que todos os estilos sejam compatíveis."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "O componente [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) é compatível com o atributo [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) para especificar quais recursos de imagem serão usados com base no tamanho da tela. [Sai<PERSON> mais](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Considere a rolagem virtual com o Component Dev Kit (CDK) se listas muito grandes estiverem sendo renderizadas. [<PERSON><PERSON> mais](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Aplique a [divisão de código no nível da rota](https://web.dev/route-level-code-splitting-in-angular/) para reduzir o tamanho dos pacotes JavaScript. Além disso, considere armazenar os ativos em cache com antecedência com o [service worker Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Se você estiver usando a CLI Angular, garanta que as versões sejam geradas no modo de produção. [<PERSON><PERSON> ma<PERSON>](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Se você estiver usando a CLI Angular, inclua mapas de origem no build de produção para inspecionar seus pacotes. [Saiba ma<PERSON>](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Pré-carregue rotas com antecedência para agilizar a navegação. [Sai<PERSON> ma<PERSON>](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Considere usar o utilitário `BreakpointObserver` no Component Dev Kit (CDK) para gerenciar pontos de interrupção da imagem. [Sai<PERSON> mais](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Faça upload do seu GIF para um serviço que o disponibilizará para incorporação como um vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Especifique `@font-display` ao definir fontes personalizadas no seu tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Recomendamos que configure [formatos de imagem WebP com um estilo de imagem Convert](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) no seu site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instale [um módulo Dr<PERSON>](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) que possa carregar imagens lentamente. Esses módulos oferecem a capacidade de adiar qualquer imagem fora da tela para melhorar o desempenho."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Use um módulo para CSS e JavaScript in-line essenciais ou carregue recursos de forma assíncrona via JavaScript, como o módulo [Agregação CSS/JS avançada](https://www.drupal.org/project/advagg). As otimizações oferecidas por esse módulo podem corromper seu site, então é provável que você precise fazer modificações no código."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON> as especificações de servidor, temas e módulos contribuem para o tempo de resposta do servidor. Recomendamos que você use um tema mais otimizado, selecionando cuidadosamente um módulo de otimização e/ou fazendo upgrade do seu servidor. Seu servidor de hospedagem precisa usar armazenamento em cache com código de operação PHP, memória cache para reduzir o tempo de consulta de banco de dados, como no Redis ou Memcached, além de otimizar a aplicação lógica para preparar as páginas mais rapidamente."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Use [Estilo de Imagem Responsiva](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) para reduzir o tamanho das imagens carregadas na sua página. Se estiver usando \"Visualização\" para mostrar vários conteúdos em uma página, implemente a paginação para limitar o número de itens mostrados em uma determinada página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Não esqueça de ativar o \"Agregar documentos CSS\" na página \"Administração » Configurações » Desenvolvimento\". É possível também configurar opções de agregação mais avançadas pelos [módulos adicionais](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) para acelerar seu site por concatenação, minificação, e compactação dos estilos CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Não esqueça de ativar \"Agregar documentos JavaScript\" na página \"Administração » Configurações » Desenvolvimento\". É possível também configurar opções de agregação mais avançadas pelos [módulos adicionais](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) para acelerar seu site por concatenação, minificação, e compactação dos recursos JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Remova as regras não utilizadas do CSS e anexe apenas as bibliotecas Drupal necessárias para a página ou para o componente da página. Veja o [link do documento Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para mais detalhes. Para identificar bibliotecas anexas que estão adicionando CSS externo, execute a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. É possível identificar o tema/módulo responsável pelo URL da folha de estilo quando a agregação do CSS está desativada no seu site Drupal. Procure temas/módulos que tenham muitas folhas de estilo na lista, apresentando um nível alto de vermelho na cobertura do código. Um tema/módulo só deverá colocar uma folha de estilo na fila se ela for realmente usada na página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Remova os recursos não utilizados do JavaScript e anexe apenas as bibliotecas Drupal necessárias para a página ou para o componente da página. Consulte o [link do documento Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para ver mais detalhes. Para identificar bibliotecas anexas que estão adicionando JavaScript externo, execute a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. É possível identificar o tema/módulo responsável pelo URL do script quando a agregação do JavaScript está desativada no seu site Drupal. Procure temas/módulos que tenham muitos scripts na lista, apresentando um nível alto de vermelho na cobertura do código. Um tema/módulo só deverá colocar um script na fila se ele realmente for usado na página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Configure o \"Idade máxima para cache de navegador e proxy\" na página \"Administração » Configurações » Desenvolvimento\". Leia sobre [cache Drupal e otimização de performance](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Use [um módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) que automaticamente otimiza e reduz o tamanho das imagens enviadas pelo site sem afetar a qualidade. Não esqueça de usar o [Estilo de Imagens Responsivas](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) original fornecido pelo Drupal (disponível no Drupal 8 e mais recentes) para todas imagens renderizadas no site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Dicas de recursos pré-conexão ou pré-busca em DNS podem ser adicionadas instalando e configurando [um módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) que fornece facilidade para as dicas de recursos do user agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Não esqueça de usar o [Estilo de Imagens Responsivas](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) original fornecido pelo Drupal (disponível no Drupal 8 e mais recentes). Use o Estilo de Imagens Responsivas quando renderizar campos de imagem pelo modo de visualização, ou imagens enviadas pelo editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Optimize Fonts` para aproveitar automaticamente o recurso CSS `font-display` e garantir que o texto possa ser visto pelo usuário enquanto as webfonts carregam."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Next-Gen Formats` para converter imagens para WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Lazy <PERSON>ad Images` para adiar o carregamento de imagens fora da tela até que elas sejam necessárias."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative as configurações `Critical CSS` e `Script Delay` para adiar JS/CSS que não sejam essenciais."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Use o [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) para armazenar seu conteúdo em cache em nossa rede mundial, melhorando o tempo até o primeiro byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Minify CSS` para minificar automaticamente seu CSS e diminuir o tamanho dos payloads de rede."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Minify Javascript` para minificar automaticamente seu JS e diminuir o tamanho dos payloads de rede."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Remove Unused CSS` para ajudar com esse problema. Ela vai identificar classes CSS que são usadas em cada página do seu site e remover as que não são e manter o arquivo pequeno."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Efficient Static Cache Policy` para definir valores recomendados no cabeçalho do armazenamento em cache para recursos estáticos."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Next-Gen Formats` para converter imagens para WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Pre-Connect Origins` para adicionar automaticamente as dicas do recurso `preconnect`. Faça isso para estabelecer conexões prévias com origens de terceiros importantes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative as configurações `Preload Fonts` e `Preload Background Images` para adicionar links `preload` e priorizar a busca de recursos que são solicitados posteriormente no carregamento de página."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Use o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a configuração `Resize Images` para redimensionar imagens a um tamanho adequado para dispositivos, reduzindo o tamanho do payload de rede."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Faça upload do seu GIF para um serviço que o disponibilizará para incorporação como um vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Use um [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ou serviço que converta automaticamente as imagens enviadas nos formatos ideais."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instale um [plug-in de carregamento lento do Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), que permite adiar imagens fora da tela, ou alterne para um modelo que ofereça essa funcionalidade. A partir do Joomla 4.0, todas as novas imagens receberão o atributo `loading` [automaticamente](https://github.com/joomla/joomla-cms/pull/30748) do núcleo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Existem vários plug-ins do Joomla que podem ajudar você a [aplicar inline a recursos essenciais](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ou [adiar recursos menos importantes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). As otimizações oferecidas por esses plug-ins podem corromper os recursos dos seus modelos ou dos plug-ins, então será necessário testá-los cuidadosamente."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Todas as especificações de servidor, módulos e extensões contribuem para o tempo de resposta do servidor. Recomendamos que você use um módulo mais otimizado, selecionando cuidadosamente uma extensão de otimização e/ou fazendo upgrade do seu servidor."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Recomendamos que você mostre trechos nas suas categorias de artigo (por exemplo, pelo link \"saiba mais\"), reduza o número de artigos exibidos em uma determinada página, divida postagens longas em várias páginas ou use um plug-in para aplicar carregamento lento nos comentários."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "V<PERSON><PERSON><PERSON> [extens<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) podem acelerar seu site concatenando, reduzindo e compactando seus estilos css. Há também modelos que fornecem essa função."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "V<PERSON><PERSON><PERSON> [extens<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) podem acelerar seu site concatenando, reduzindo e compactando seus scripts. Há também modelos que fornecem essa função."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Troque ou reduza o número de [extensões Joomla](https://extensions.joomla.org/) que carregam CSS não utilizado na sua página. Para identificar extensões que estão adicionando CSS externo, execute a [cobertura do código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. Você pode identificar o tema/plug-in responsável no URL da folha de estilo. Procure plug-ins que tenham muitas folhas de estilo na lista, apresentando um nível alto de vermelho na cobertura do código. Um plug-in só deverá colocar uma folha de estilo na fila se ela for realmente utilizada na página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Troque ou reduza o número de [extensões Joomla](https://extensions.joomla.org/) que carregam JavaScript não utilizado na sua página. Para identificar plug-ins que estão adicionando JS externo, execute a [cobertura do código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. É possível identificar a extensão responsável no URL do script. Procure extensões que tenham muitos scripts na lista, apresentando um nível alto de vermelho na cobertura do código. Uma extensão só deverá colocar um script na fila se ele realmente for utilizado na página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> sobre o [Processo de cache do navegador no Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Use um [plug-in para otimização de imagens](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), que as compacta sem afetar a qualidade."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Use o [plug-in de imagens responsivas](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) para utilizá-las no seu conteúdo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "É possível ativar a compactação de texto ao ativar Compactação de Página Gzip em Joomla (Sistema > Configuração Global > Servidor)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Se você não estiver agrupando os ativos JavaScript, considere usar [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Desative recursos de [compilação JavaScript e minificação](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrados da Magento e considere usar [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Especifique `@font-display` ao [definir fontes personalizadas](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Considere procurar no [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) por uma variedade de extensões de terceiros para aproveitar formatos de imagem mais recentes."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Considere modificar seus modelos de produto e catálogo para usar o recurso de [carregamento lento](https://web.dev/native-lazy-loading) da plataforma da Web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Use a [integração de Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) da Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Ative a opção \"Reduzir arquivos CSS\" nas configurações de desenvolvedor da loja. [<PERSON><PERSON> ma<PERSON>](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Use [Terser](https://www.npmjs.com/package/terser) para reduzir todos os recursos JavaScript da implantação de conteúdo estático e desative o recurso de minificação integrada."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Desative a [compilação JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrada da Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Considere procurar no [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) por uma variedade de extensões de terceiros para otimizar imagens."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Dicas de recursos pré-conexão ou pré-busca em DNS podem ser adicionadas [modificando o layout de um tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "As tags `<link rel=preload>` podem ser adicionadas [modificando o layout de um tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Use o componente `next/image` em vez de `<img>` para otimizar automaticamente o formato das imagens. [Saiba mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Use o componente `next/image` em vez de `<img>` para carregar imagens de maneira automática e lenta. [<PERSON><PERSON> mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Use o componente `next/image` e defina o campo \"prioridade\" como verdadeiro para pré-carregar imagens LCP. [<PERSON><PERSON> ma<PERSON>](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Use o componente `next/script` para adiar o carregamento de scripts terceirizados não essenciais. [Sai<PERSON> mais](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Use o componente `next/image` para garantir que as imagens tenham sempre o tamanho adequado. [<PERSON><PERSON> mais](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Configure o plug-in `PurgeCSS` com `Next.js` para remover regras não usadas das folhas de estilo. [<PERSON><PERSON> ma<PERSON>](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Use o `Webpack Bundle Analyzer` para detectar códigos JavaScript não usados. [<PERSON><PERSON> mais](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Use o `Next.js Analytics` para medir o desempenho do seu app no mundo real. [<PERSON><PERSON> mais](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configure o armazenamento em cache para recursos e páginas `Server-side Rendered` (SSR) imutáveis. [<PERSON><PERSON> mais](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Use o componente `next/image` em vez de `<img>` para ajustar a qualidade das imagens. [Sai<PERSON> mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Use o componente `next/image` para configurar o valor de `sizes` adequado. [Sai<PERSON> mais](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Ative a compressão no seu servidor Next.js. [<PERSON><PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Use o componente `nuxt/image` e o defina como `format=\"webp\"`. [<PERSON><PERSON> <PERSON>](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Use o componente `nuxt/image` e o defina como `loading=\"lazy\"` para imagens fora da tela. [<PERSON><PERSON> ma<PERSON>](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Use o componente `nuxt/image` e especifique o `preload` para imagens LCP. [Sai<PERSON> ma<PERSON>](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Use o componente `nuxt/image` e especifique os elementos `width` e `height` de forma explícita. [<PERSON><PERSON> ma<PERSON>](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Use o componente `nuxt/image` e defina o elemento `quality` adequado. [<PERSON><PERSON> ma<PERSON>](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Use o componente `nuxt/image` e defina o elemento `sizes` adequado. [<PERSON><PERSON> ma<PERSON>](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Substitua GIFs animados por vídeos](https://web.dev/replace-gifs-with-videos/) para acelerar o carregamento de páginas da Web e considere usar formatos de arquivo modernos, como [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ou [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), para melhorar a eficiência de compactação em mais de 30% em relação ao codec de vídeo mais avançado atualmente, o VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Use um [plug-in](https://octobercms.com/plugins?search=image) ou serviço que converterá automaticamente as imagens enviadas para os formatos ideais. [Imagens WebP sem perdas](https://developers.google.com/speed/webp) são 26% menores que as imagens PNG e 25% a 34% menores que as JPEG, com índices de qualidade SSIM equivalentes. Outro possível formato de imagem da última geração é [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Instale um [plug-in de carregamento lento de imagens](https://octobercms.com/plugins?search=lazy) que permite adiar imagens fora da tela, ou alterne para um tema que ofereça essa funcionalidade. Também recomendamos o uso do [plug-in de AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Existem vários plug-ins que ajudam [recursos essenciais in-line](https://octobercms.com/plugins?search=css) a funcionar melhor. Esses plug-ins podem corromper outros, então teste-os cuidadosamente."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON>das as especificações de servidor, temas e plug-ins contribuem para o tempo de resposta do servidor. Recomendamos usar um tema mais otimizado, selecionando cuidadosamente um plug-in de otimização e/ou fazendo o upgrade do servidor. O CMS de outubro também permite que os desenvolvedores usem [`Queues`](https://octobercms.com/docs/services/queues) para adiar o processamento de tarefas que levam muito tempo, como o envio de um e-mail. Isso acelera drasticamente as solicitações da Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Recomendamos que você mostre trechos nas listas de postagem (por exemplo, com o botão `show more`), reduza o número de postagens exibidas em uma determinada página da Web, divida postagens longas em várias páginas ou use um plug-in para aplicar o carregamento lento nos comentários."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Há vários [plug-ins](https://octobercms.com/plugins?search=css) que podem acelerar um site por concatenação, minificação e compactação de estilos. É possível acelerar o desenvolvimento usando um processo de compilação para fazer essa minificação antecipadamente."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Há vários [plug-ins](https://octobercms.com/plugins?search=javascript) que podem acelerar um site por concatenação, minificação e compactação de scripts. É possível acelerar o desenvolvimento usando um processo de compilação para fazer essa minificação antecipadamente."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Revise os [plug-ins](https://octobercms.com/plugins) que carregam CSS não utilizado no site. Para identificar os plug-ins que adicionam CSS desnecessário, execute a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. Identifique o tema/plug-in responsável no URL da folha de estilo. Procure plug-ins com muitas folhas de estilo que tenham várias marcações em vermelho na cobertura de código. Um plug-in só precisa adicionar uma folha de estilo se ela for usada na página da Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Revise os [plug-ins](https://octobercms.com/plugins?search=javascript) que carregam JavaScript não utilizado na página da Web. Para identificar os plug-ins que adicionam JavaScript desnecessário, execute a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. Identifique o tema/plug-in responsável no URL do script. Procure plug-ins com muitos scripts que tenham várias marcações em vermelho na cobertura de código. Um plug-in só precisa adicionar um script se ele for usado na página da Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Leia sobre [como impedir solicitações de rede desnecessárias com o cache HTTP](https://web.dev/http-cache/#caching-checklist). Há vários [plug-ins](https://octobercms.com/plugins?search=Caching) que podem ser usados para acelerar o armazenamento em cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Use um [plug-in para otimização de imagens](https://octobercms.com/plugins?search=image) para compactá-las sem afetar a qualidade."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Faça upload de imagens diretamente no gerenciador de mídia para garantir que os tamanhos de imagem necessários estejam disponíveis. Use o [filtro de redimensionamento](https://octobercms.com/docs/markup/filter-resize) ou um [plug-in de redimensionamento de imagem](https://octobercms.com/plugins?search=image) para garantir que os tamanhos de imagem ideais sejam usados."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Ativar a compactação de texto na configuração do servidor da Web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Use uma biblioteca \"em janelas\", como `react-window`, para minimizar o número de nós DOM criados se você estiver renderizando muitos elementos repetidos na página. [<PERSON><PERSON> mais](https://web.dev/virtualize-long-lists-react-window/). Alé<PERSON> disso, reduza novas renderizações desnecessárias usando [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ou [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) e [efeitos para pular](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) somente até que algumas dependências tenham sido mudadas se você estiver usando o hook `Effect` para melhorar o desempenho durante a execução."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Se você estiver usando React Router, reduza o uso do componente `<Redirect>` para [navegações de rota](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Se você estiver renderizando componentes do React no servidor, considere usar `renderToPipeableStream()` ou `renderToStaticNodeStream()` para permitir que o cliente receba e hidrate diferentes partes da marcação em vez de todas juntas. [<PERSON><PERSON> mais](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Caso seu sistema de compilação reduza os arquivos CSS automaticamente, verifique se você está implantando o build de produção do seu app. Você pode verificar isso com a extensão de Ferramentas para Desenvolvedores do React. [<PERSON><PERSON> mais](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Caso seu sistema de compilação reduza os arquivos JS automaticamente, verifique se você está implantando o build de produção do seu app. Você pode verificar isso com a extensão de Ferramentas para Desenvolvedores do React. [<PERSON><PERSON> mais](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Se você não estiver renderizando no servidor, [divida os pacotes JavaScript](https://web.dev/code-splitting-suspense/) com `React.lazy()`. <PERSON><PERSON><PERSON> contrá<PERSON>, divida o código usando uma biblioteca de terceiros como [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Use o React DevTools Profiler, que usa a API Profiler, para medir o desempenho de renderização dos componentes. [Saiba mais.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Faça upload do seu GIF para um serviço que o disponibilizará para incorporação como um vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Use o plug-in do [laboratório de performance](https://wordpress.org/plugins/performance-lab/) para converter de forma automática as imagens JPEG enviadas em WebP sempre que possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instale um [plug-in de carregamento lento do WordPress](https://wordpress.org/plugins/search/lazy+load/), que permite adiar imagens fora da tela, ou alterne para um tema que ofereça essa funcionalidade. Também recomendamos o uso do [plug-in de AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Existem vários plug-ins do WordPress que podem ajudar você a [aplicar inline a recursos essenciais](https://wordpress.org/plugins/search/critical+css/) ou [adiar recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). As otimizações oferecidas por esses plug-ins podem corromper os recursos do tema ou dos seus plug-ins, então é provável que você precise fazer alterações no código."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Todas as especificações de servidor, temas e plug-ins contribuem para o tempo de resposta do servidor. Recomendamos que você use um tema mais otimizado, selecionando cuidadosamente um plug-in de otimização e/ou fazendo upgrade do seu servidor."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Recomendamos que você mostre trechos nas suas listas de postagem (por exemplo, por meio da tag \"mais\"), reduza o número de postagens exibidas em uma determinada página, divida suas postagens longas em várias páginas ou use um plug-in para aplicar lazy-load nos comentários."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Vários [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+css/) podem acelerar seu site concatenando, reduzindo e compactando seus estilos. Você também pode usar um processo de compilação para fazer essa redução antecipadamente, se possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Vários [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+javascript/) podem acelerar seu site concatenando, reduzindo e compactando seus scripts. Você também pode usar um processo de compilação para fazer essa redução antecipadamente, se possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Reduza ou troque o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam CSS não utilizado na sua página. Para identificar plug-ins que estão adicionando CSS externo, execute a [cobertura do código](https://developer.chrome.com/docs/devtools/coverage/) no Chrome DevTools. Você pode identificar o tema/plug-in responsável a partir do URL da folha de estilo. Procure plug-ins que tenham muitas folhas de estilo na lista, apresentando um nível alto de vermelho na cobertura do código. Um plug-in só deverá colocar uma folha de estilo na fila se ela for realmente utilizada na página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Reduza ou troque o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam JavaScript não utilizado na sua página. Para identificar plug-ins que estão adicionando JS externo, execute a [cobertura do código](https://developer.chrome.com/docs/devtools/coverage/) no Chrome DevTools. Você pode identificar o tema/plug-in responsável a partir do URL do script. Procure plug-ins que tenham muitos scripts na lista, apresentando um nível alto de vermelho na cobertura do código. Um plug-in só deverá colocar um script na fila se ele for realmente usado na página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> sobre o [Processo de cache do navegador no WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Use um [plug-in do WordPress para otimização de imagens](https://wordpress.org/plugins/search/optimize+images/), que as compacta sem afetar a qualidade."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Faça upload de imagens diretamente por meio da [biblioteca de mídia](https://wordpress.org/support/article/media-library-screen/) para garantir que os tamanhos de imagem necessários estejam disponíveis. Depois, insira-os na biblioteca de mídia ou use o widget de imagem para garantir que os tamanhos ideais sejam usados (incluindo aqueles para os pontos de interrupção responsivos). Evite usar imagens `Full Size`, a não ser que as dimensões sejam adequadas para uso. [Sai<PERSON> mais](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Você pode ativar a compactação de texto na configuração do servidor da Web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Ative o recurso \"Imagify\" na guia \"Otimização de imagens\" do \"WP Rocket\" para converter as imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Ative [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) no WP Rocket para corrigir essa recomendação. Esse recurso atrasa o carregamento das imagens até que o visitante role para baixo na página e precise visualizá-las."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Para processar essa recomendação, ative as opções [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) e [Carregar JavaScript adiado](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) no \"WP Rocket\". Esses recursos otimizam os arquivos CSS e JavaScript para que não bloqueiem a renderização da página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Ative a opção [<PERSON><PERSON><PERSON> arquivos CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) no \"WP Rocket\" para corrigir esse problema. Todos os espaços e comentários nos arquivos CSS do seu site serão removidos para que o tamanho do arquivo seja menor e o download seja mais rápido."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Ative a opção [Mini<PERSON>zar arquivos JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) no \"WP Rocket\" para corrigir esse problema. Espaços e comentários vazios são removidos dos arquivos JavaScript para diminuir e agilizar o download deles."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Ative a opção [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) no \"WP Rocket\" para corrigir o problema. Fazer isso reduz o tamanho da página removendo todos os CSS e folhas de estilo que não são usados, mantendo apenas o CSS usado para cada página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Ative a opção [Atrasar a execução do JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) no \"WP Rocket\" para corrigir esse problema. Isso melhora o carregamento da sua página atrasando a execução dos scripts até a interação do usuário. Caso seu site tenha iframes, você pode usar o [LazyLoad do WP Rocket para iframes e vídeos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) e também [Substituir o iframe do YouTube por uma imagem de visualização](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Ative o recurso \"Imagify\" na guia \"Otimização de imagens\" do \"WP Rocket\" e execute a otimização em massa para compactar suas imagens."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Use a [Pré-busca de solicitações DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) no \"WP Rocket\" para adicionar \"dns-prefetch\" e acelerar a conexão com domínios externos. Além disso, o \"WP Rocket\" adiciona automaticamente \"preconnect\" ao [domínio do Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) e a todos os CNAMEs adicionados com o recurso [Ativar CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Para corrigir esse problema em fontes, ative a opção [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) no \"WP Rocket\". As fontes essenciais do seu site serão pré-carregadas com prioridade."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Ver calculadora."}, "report/renderer/report-utils.js | collapseView": {"message": "Fechar visualiza<PERSON>"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navegação inicial"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latência máxima do caminho crítico:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copiar JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Alternar tema escuro"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Caixa de diálogo de impressão expandida"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Imprimir resumo"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON> como Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Salvar como HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Salvar como JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Abrir no visualizador"}, "report/renderer/report-utils.js | errorLabel": {"message": "Erro!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Informar erro: nenhuma informação de auditoria"}, "report/renderer/report-utils.js | expandView": {"message": "Abrir visualização"}, "report/renderer/report-utils.js | footerIssue": {"message": "A<PERSON><PERSON><PERSON> um problema"}, "report/renderer/report-utils.js | hide": {"message": "Ocultar"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Dados de laboratório"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "An<PERSON><PERSON><PERSON> do [Lighthouse](https://developers.google.com/web/tools/lighthouse/) da página atual em uma rede móvel emulada. Os valores são estimados e podem variar."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Outros itens para verificação manual"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Não aplicável"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunidade"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Economia estimada"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Auditorias aprovadas"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Carregamento inicial da página"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitação personalizada"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON> de trabalho emulada"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Sem emulação"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versão <PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potência não limitada da CPU/memória"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitação de CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositivo"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitação de rede"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulação de tela"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User agent (rede)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Carregamento de uma única página"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Esses dados são recebidos do carregamento de uma única página. Eles não são dados de campo resumindo várias sessões."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitação lenta de 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Desconhecida"}, "report/renderer/report-utils.js | show": {"message": "Mostrar"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostrar auditorias relevantes para:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> snippet"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Expandir snippet"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos de terceiros"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Fornecido pelo ambiente"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Alguns problemas afetaram esta execução do Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Os valores são estimados e podem variar. O [índice de desempenho é calculado](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) diretamente por essas métricas."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Ver rastro original"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON>er rastro"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Ver Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Passou nas auditorias, mas como avisos"}, "report/renderer/report-utils.js | warningHeader": {"message": "Avisos: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Todos os scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Cobertura"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes de recursos"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nome"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Alternar tabela"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Bytes não usados"}}