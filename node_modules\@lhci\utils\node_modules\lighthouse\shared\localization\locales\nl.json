{"core/audits/accessibility/accesskeys.js | description": {"message": "Met toegangstoetsen kunnen gebruikers snel de focus op een gedeelte van de pagina plaatsen. Voor correcte navigatie moet elke toegangstoets uniek zijn. [Meer informatie over toegangstoetsen](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-wa<PERSON>en zijn niet uniek"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-wa<PERSON>en zijn uniek"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON>-`role` ondersteunt een specifieke subset van `aria-*`-kenmerken. Als deze verkeerd worden gekoppeld, worden de `aria-*`-kenmerken ongeldig. [Meer informatie over hoe je ARIA-kenmerken aan de bijbehorende rollen koppelt](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-kenmerken komen niet overeen met hun rollen"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-kenmerken komen overeen met hun rollen"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Als een element geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over hoe je opdrachtelementen toegankelijker maakt](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`-, `link`- en `menuitem`-elementen hebben geen toegankelijke namen."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`- `link`- en `menuitem`-elementen hebben toegan<PERSON>e namen"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Ondersteunende technologieën, zoa<PERSON> s<PERSON>, werken inconsistent als `aria-hidden=\"true\"` is ingesteld in het document `<body>`. [Meer informatie over hoe `aria-hidden` invloed heeft op de hoofdtekst van het document](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` is aan<PERSON><PERSON> in het document `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` is niet a<PERSON><PERSON><PERSON> in het document `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Focusbare onderliggende elementen in een `[aria-hidden=\"true\"]`-element voorkomen dat die interactieve elementen beschikbaar zijn voor gebruikers van ondersteunende technologieën, zoals scherm<PERSON>zers. [Meer informatie over hoe `aria-hidden` van invloed is op focusbare elementen](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`-elementen bevatten focusbare onderliggende elementen"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]`-elementen bevatten geen focusbare onderliggende elementen"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Als een invoerveld geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over labels voor invoervelden](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA-invoervelden hebben geen toegankelijke namen"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA-invoervelden hebben toe<PERSON>i<PERSON>e namen"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Als een meter-element geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over hoe je `meter`-elementen een naam geeft](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter`-elementen hebben geen toegankelijke namen."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter`-elementen hebben toe<PERSON>e namen"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Als een `progressbar`-element geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over hoe het labelen van `progressbar`-elementen](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar`-elementen hebben geen toegankelijke namen."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar`-elementen hebben toe<PERSON>i<PERSON>e namen"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Sommige ARIA-rollen hebben vereiste kenmerken die de status van het element beschrijven voor schermlezers. [Meer informatie over rollen en vereiste kenmerken](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-elementen bevatten niet alle vereiste `[aria-*]`-kenmerken"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-elementen bevatten alle vereiste `[aria-*]`-kenmerken"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Sommige bovenliggende ARIA-rollen moeten specifieke onderliggende rollen bevatten om de beoogde toegankelijkheidsfuncties uit te voeren. [Meer informatie over rollen en vereiste onderliggende elementen](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "In elementen met een <PERSON> `[role]` die vereisen dat onderliggende elementen een specifieke `[role]` be<PERSON><PERSON>, ontbreken enkele (of alle) van die vereiste onderliggende elementen."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementen met een <PERSON> `[role]` die vereisen dat onderliggende elementen een specifieke `[role]` bevatten, bevatten alle vereiste onderliggende elementen."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Sommige onderliggende ARIA-rollen moeten opgenomen zijn in specifieke bovenliggende rollen om de beoogde toegankelijkheidsfuncties op de juiste manier uit te voeren. [Meer informatie over ARIA-rollen en het verplichte bovenliggende element](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-elementen zijn niet opgenomen in het vereiste bovenliggende element"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-elementen zijn opgenomen in het vereiste bovenliggende element"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA-rollen moeten geldige waarden hebben om hun beoogde toegankelijkheidsfuncties uit te voeren. [Meer informatie over geldige ARIA-rollen](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-wa<PERSON><PERSON> zijn niet geldig"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-wa<PERSON><PERSON> zijn geldig"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Als een schakelveld geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over het aan-/uitz<PERSON><PERSON> van velden](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA-schakelvelden hebben geen toegankelijke namen"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA-schakelvelden hebben toegankelijke namen"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Als een tooltip-element geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over hoe je `tooltip`-elementen een naam geeft](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip`-elementen hebben geen toegankelijke namen."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip`-elementen hebben toegankelijke namen"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Als een `treeitem`-element geen toegankelijke naam heeft, kondigen schermlezers dit aan met een generieke naam, waardoor het veld onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over het labelen van `treeitem`-elementen](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem`-elementen hebben geen toegankelijke namen."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem`-elementen hebben toegan<PERSON>ijke namen"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "ARIA-ken<PERSON><PERSON> met ongeldige waarden kunnen niet worden geïnterpreteerd door hulptechnologieën, zoals schermlezers. [Meer informatie over geldige waarden voor ARIA-kenmerken](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-kenmerken hebben geen geldige waarden"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-kenmerken bevatten geldige waarden"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "ARIA-kenmerken met ongeldige namen kunnen niet worden geïnterpreteerd door hulptechnologieën, zoals scher<PERSON>. [Meer informatie over geldige ARIA-kenmerken](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-ken<PERSON><PERSON> zijn niet geldig of zijn verkeerd gespeld"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-kenmerken zijn geldig en niet verkeerd gespeld"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Mislukte elementen"}, "core/audits/accessibility/button-name.js | description": {"message": "Als een knop geen toegankelijke naam he<PERSON>, kondigen schermlezers deze aan als 'knop', waardoor de knop onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie over hoe je knoppen toegankelijker maakt](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> hebben geen toegankeli<PERSON>e naam"}, "core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> hebben een toegankelijke naam"}, "core/audits/accessibility/bypass.js | description": {"message": "Als je manieren toevoegt om herhaalde content te omzeilen, kunnen toetsenbordgebruikers efficiënter navigeren op de pagina. [Meer informatie over blokken overslaan](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "De pagina bevat geen kop, link voor overslaan of herkenningspuntregio"}, "core/audits/accessibility/bypass.js | title": {"message": "De pagina bevat een kop, link voor overslaan of herkenningspuntregio"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Voor veel gebruikers is tekst met weinig contrast moeilijk of onmogelijk te lezen. [Meer informatie over hoe je voor voldoende kleurcontrast zorgt](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "De contrastverhouding van achtergrond- en voorgrondkleuren is niet voldo<PERSON>e"}, "core/audits/accessibility/color-contrast.js | title": {"message": "De contrastverhouding van achtergrond- en voorgrondkleuren is voldoende"}, "core/audits/accessibility/definition-list.js | description": {"message": "Wan<PERSON> definitielijsten niet juist zijn <PERSON>, kunnen schermlezers verwarrende of onjuiste uitvoer produceren. [Meer informatie over het correct structureren van definitielijsten](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-elementen bevatten niet alleen juist geordende `<dt>`- en `<dd>`-g<PERSON><PERSON>en, `<script>`-, `<template>`- of `<div>`-elementen."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-elementen bevatten alleen juist geordende `<dt>`- en `<dd>`-g<PERSON><PERSON>en, `<script>`-, `<template>`- of `<div>`-elementen."}, "core/audits/accessibility/dlitem.js | description": {"message": "Definitielijstitems (`<dt>` en `<dd>`) moeten zijn verpakt in een bovenliggend `<dl>`-element om ervoor te zorgen dat schermlezers ze juist kunnen aankondigen. [Meer informatie over het correct structureren van definitielijsten](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definitielijstitems zijn niet verpakt in `<dl>`-elementen"}, "core/audits/accessibility/dlitem.js | title": {"message": "Definitielijstitems zijn verpakt in `<dl>`-elementen"}, "core/audits/accessibility/document-title.js | description": {"message": "De titel geeft gebruikers van een schermlezer een overzicht van de pagina. Gebruikers van een zoekmachine vertrouwen in hoge mate hierop om te bepalen of een pagina relevant is voor hun zoekopdracht. [Meer informatie over documenttitels](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Document bevat geen `<title>`-element"}, "core/audits/accessibility/document-title.js | title": {"message": "Document bevat een `<title>`-element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Alle focusbare elementen moeten een unieke `id` hebben om ervoor te zorgen dat ze zichtbaar zijn voor ondersteunende technologieën. [Meer informatie over het oplossen van problemen met dubbele `id`'s](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]`-kenmerken voor actieve, focusbare elementen zijn niet uniek"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]`-kenmerken voor actieve, focusbare elementen zijn uniek"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "De waarde van een ARIA-ID moet uniek zijn om te voorkomen dat andere instanties over het hoofd worden gezien door ondersteunende technologieën. [Meer informatie over hoe je dubbele ARIA-ID's oplost](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA-ID's zijn niet uniek"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA-ID's zijn uniek"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Formuliervelden met meerdere labels kunnen verwarrend worden aangekondigd door ondersteunende technologieën, zoals schermlezers, die het eerste label, het laatste label of alle labels gebruiken. [Meer informatie over hoe je formulierlabels gebruikt](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met meerd<PERSON> labels"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON>r zijn geen formuliervelden met meerdere labels"}, "core/audits/accessibility/frame-title.js | description": {"message": "Gebruikers van een schermlezer zijn af<PERSON><PERSON><PERSON><PERSON> van frametitels die de content van de frames beschrijven. [Meer informatie over frametitels](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- of `<iframe>`-elementen hebben geen titel"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- of `<iframe>`-elementen hebben een titel"}, "core/audits/accessibility/heading-order.js | description": {"message": "Goed geordende koppen die geen niveaus oversla<PERSON>, laten de semantische structuur van de pagina zien. Dit maakt navigeren op de pagina makkelijker en de pagina kan beter worden geïnterpreteerd door ondersteunende technologieën. [Meer informatie over de volgorde van koppen](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Kopelementen worden niet weergegeven in aflopende volgorde"}, "core/audits/accessibility/heading-order.js | title": {"message": "Kopelementen worden weergegeven in aflopende volgorde"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Als de pagina geen `lang`-ken<PERSON><PERSON> specific<PERSON>t, neemt een schermlezer aan dat de pagina is ges<PERSON><PERSON>ven in de standaardtaal die de gebruiker heeft gekozen toen de schermlezer werd ingesteld. Als de pagina niet in de standaardtaal is ges<PERSON><PERSON>ven, kan de schermlezer de tekst van de pagina mogelijk niet juist aankondigen. [Meer informatie over het kenmerk `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-element bevat geen `[lang]`-kenmerk"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-element bevat een `[lang]`-kenmerk"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Als je een geldige [BCP 47-taal](https://www.w3.org/International/questions/qa-choosing-language-tags#question) opgeeft, kunnen schermlezers de tekst juist aankondigen. [Meer informatie over hoe je het kenmerk `lang` gebruikt](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-element bevat geen geldige waarde voor het `[lang]`-kenmerk."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-element bevat een geldige waarde voor het `[lang]`-kenmerk"}, "core/audits/accessibility/image-alt.js | description": {"message": "Voor informatieve elementen moet een korte, beschrijvende alternatieve tekst worden gebruikt. Decoratieve elementen kunnen worden genegeerd met een leeg alt-kenmerk. [Meer informatie over het kenmerk `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Afbeeldingselementen hebben geen `[alt]`-kenmerken"}, "core/audits/accessibility/image-alt.js | title": {"message": "Afbeeldingselementen bevatten `[alt]`-kenmerken"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Als je een afbeelding gebruikt als `<input>`-knop en hiervoor alternatieve tekst opgeeft, kunnen gebruikers van een schermlezer beter begrijpen wat het doel van de knop is. [Meer informatie over de invoer van alt-text voor afbeeldingen](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-elementen bevatten geen `[alt]`-tekst"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-elementen bevatten `[alt]`-tekst"}, "core/audits/accessibility/label.js | description": {"message": "Labels zorgen ervoor dat formulieropties juist worden aangekondigd door hulptechnologieën, zoals schermlezers. [Meer informatie over formulierelementlabels](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Formulierelementen hebben geen bijbehorende labels"}, "core/audits/accessibility/label.js | title": {"message": "Formulierelementen hebben bijbehorende labels"}, "core/audits/accessibility/link-name.js | description": {"message": "Met linktekst (en alternatieve tekst voor afbeeldingen, indien gebruikt als links) die herkenbaar, uniek en focusbaar is, verbeter je de navigatiefunctionaliteit voor gebruikers van een schermlezer. [Meer informatie over hoe je links toegankelijk maakt](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON>s hebben geen herkenbare naam"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON>s hebben een herkenbare naam"}, "core/audits/accessibility/list.js | description": {"message": "Schermlezers hebben een specifieke manier om lijsten aan te kondigen. Met een juiste lijststructuur verbetert de uitvoer van schermlezers. [Meer informatie over de juiste lijststructuur](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bevatten niet alleen `<li>`-elementen en elementen voor scriptondersteuning (`<script>` en `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "<PERSON><PERSON><PERSON> bevatten alleen `<li>`-elementen en elementen voor scriptondersteuning (`<script>` en `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Voor schermlezers moeten lijstitems (`<li>`) binnen een bovenliggende `<ul>`, `<ol>` of `<menu>` worden geplaatst om juist te worden aangekondigd. [Meer informatie over de juiste lijststructuur](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Lijstitems (`<li>`) zijn niet opgenomen in bovenliggende `<ul>`-, `<ol>`- of `<menu>`-elementen."}, "core/audits/accessibility/listitem.js | title": {"message": "Lijstitems (`<li>`) zijn geplaatst tussen bovenliggende `<ul>`-, `<ol>`- of `<menu>`-elementen"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Gebruikers verwachten niet dat een pagina automatisch wordt vernieuwd. Als dit wel geb<PERSON>t, gaat de focus terug naar de bovenkant van de pagina. Dit kan vervelend of verwarrend zijn voor gebruikers. [Meer informatie over de metatag voor vernieuwen](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Het document gebruikt `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Het document gebruikt `<meta http-equiv=\"refresh\">` niet"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Uitschakeling van de zoomfunctie is problematisch voor slechtzienden die afhankelijk zijn van schermvergroting om de content van een webpagina te zien. [Meer informatie over de viewport-metatag](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` wordt gebruikt in het `<meta name=\"viewport\">`-element of het `[maximum-scale]`-kenmerk is minder dan 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` wordt niet gebruikt in het `<meta name=\"viewport\">`-element en het `[maximum-scale]`-kenmerk is niet minder dan 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Schermlezers kunnen geen andere content dan tekst vertalen. Als je alternatieve tekst aan `<object>`-elementen toevoegt, kunnen schermlezers de betekenis overbrengen aan gebruikers. [Meer informatie over alt-tekst voor `object`-elementen](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-elementen bevatten geen alternatieve tekst"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-elementen bevatten alternatieve tekst"}, "core/audits/accessibility/tabindex.js | description": {"message": "Een waarde groter dan 0 impliceert een expliciete navigatievolgorde. Hoewel dit technisch geldig is, is dit vaak vervelend voor gebruikers die afhankelijk zijn van hulptechnologieën. [Meer informatie over het kenmerk `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Sommige elementen hebben een `[tabindex]`-waarde die groter is dan 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Geen element dat een `[tabindex]`-waarde heeft die groter is dan 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Schermlezers hebben functies die navigeren in tabellen makkelijker maken. Als je zorgt dat `<td>`-cellen die het `[headers]`-kenmerk gebruiken, alleen verwijzen naar andere cellen in dezelfde tabel, kun je de functionaliteit verbeteren voor gebruikers van een schermlezer. [Meer informatie over het kenmerk `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Cellen in een `<table>`-element die het `[headers]`-ken<PERSON>k gebruiken, verwijzen naar een element `id` dat niet in dezelfde tabel wordt gevonden."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Cellen in een `<table>`-element dat het `[headers]`-ken<PERSON>k gebruik<PERSON>, verwijzen naar tabelcellen in dezelfde tabel."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Schermlezers hebben functies die navigeren in tabellen makkelijker maken. Als je zorgt dat tabelheaders altijd verwijzen naar een bepaalde reeks cellen, kun je de functionaliteit verbeteren voor gebruikers van een schermlezer. [Meer informatie over tabelkoppen](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>`-elementen en elementen met `[role=\"columnheader\"/\"rowheader\"]` bevatten niet de gegevenscellen die ze beschrijven."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>`-elementen en elementen met `[role=\"columnheader\"/\"rowheader\"]` bevatten de gegevenscellen die ze beschrijven."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Als je een geldige [BCP 47-taal](https://www.w3.org/International/questions/qa-choosing-language-tags#question) voor elementen opgeeft, kan de tekst juist wordt uitgesproken door een schermlezer. [Meer informatie over hoe je het kenmerk `lang` gebruikt](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-kenmerken hebben geen geldige waarde"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-kenmerken bevatten een geldige waarde"}, "core/audits/accessibility/video-caption.js | description": {"message": "Video's met ondertiteling bieden doven en slechthorenden betere toegang tot de bijbehorende informatie. [Meer informatie over ondertiteling voor video's](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-elementen bevatten geen `<track>`-element met `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-elementen bevatten een `<track>`-element met `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON><PERSON> waarde"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Voorgestelde token"}, "core/audits/autocomplete.js | description": {"message": "Met `autocomplete` kunnen gebruikers sneller formulieren indienen. Overweeg deze functie in te schakelen door het kenmerk `autocomplete` in te stellen op een geldige waarde, zodat gebruikers minder hoeven te typen. [Meer informatie over `autocomplete` in formulieren](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>`-elementen hebben niet de juiste `autocomplete`-ken<PERSON>ken"}, "core/audits/autocomplete.js | manualReview": {"message": "Handmatige beoordeling vereist"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Check de volgorde van tokens"}, "core/audits/autocomplete.js | title": {"message": "`<input>`-<PERSON><PERSON> g<PERSON><PERSON>iken `autocomplete` op de juiste manier"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete`-token(s): {token} is ongeldig in {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Check de volgorde van tokens: '{tokens}' in {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | description": {"message": "Veel navigaties worden uitgevoerd door terug te gaan naar een vorige pagina of een pagina vooruit te gaan. Back-Forward Cache (bfcache) kan dit type navigatie versnellen. [Meer informatie over bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 oor<PERSON><PERSON> van fout}other{# oor<PERSON>en van fout}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON><PERSON><PERSON> van fout"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> he<PERSON>t hers<PERSON> van Back-Forward <PERSON><PERSON> v<PERSON>"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Fouttype"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Kan niet worden opgelost"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Browserondersteuning in behandeling"}, "core/audits/bf-cache.js | title": {"message": "<PERSON><PERSON><PERSON> he<PERSON>t hers<PERSON> van Back-Forward <PERSON><PERSON> niet voor<PERSON>"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-extensies hadden een negatieve invloed op de laadprestaties van deze pagina. Controleer de pagina in de incognitomodus of via een Chrome-profiel zonder extensies."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Scriptevaluatie"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON> parseren"}, "core/audits/bootup-time.js | columnTotal": {"message": "Totale CPU-tijd"}, "core/audits/bootup-time.js | description": {"message": "Overweeg de tijd te verminderen die aan parseren, compileren en uitvoeren van JS wordt besteed. Het leveren van kleinere JS-payloads kan hierbij helpen. [Meer informatie over hoe je JavaScript-uitvoeringstijd verkort](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Verkort de JavaScript-uitvoeringstijd"}, "core/audits/bootup-time.js | title": {"message": "JavaScript-uitvoeringstijd"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> grote, dubbele JavaScript-modules uit bundels, zodat er minder onnodige bytes worden verbruikt door netwerkactiviteit. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Dubbele modules verwijderen uit JavaScript-bundels"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Grote gif's zijn niet efficiënt om content met animaties te leveren. Overweeg het gebruik van mpeg4-/WebM-video's voor animaties en png/WebP voor statische afbeeldingen in plaats van gif's om netwerkbytes te besparen. [Meer informatie over efficiënte video-indelingen](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Gebruik video-indelingen voor content met animaties"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Met polyfills en transformaties kunnen verouderde browsers nieuwe JavaScript-functies gebruiken. Voor moderne browsers is dit meestal niet nodig. Volg voor je gebundelde JavaScript een implementatiestrategie voor moderne scripts op basis van module/nomodule-detectie. Dan beperk je namelijk de hoeveelheid code die naar moderne browsers wordt gestuurd, terwijl oudere browsers ondersteund blijven. [Meer informatie over hoe je modern JavaScript gebruikt](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Voorkomen dat verouderde JavaScript wordt geleverd aan moderne browsers"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Bestandsindelingen zoals webP en avif bieden vaak betere compressie dan png of jpeg. Dit zorgt voor snellere downloads en minder dataverbruik. [Meer informatie over moderne bestandsindelingen](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Lever afbeeldingen in moderne indelingen"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Overweeg lazy loading voor verborgen afbeeldingen en afbeeldingen die uit het beeld zijn nadat alle kritieke bronnen zijn geladen om de tijd tot interactief te verminderen. [Meer informatie over hoe je laden uitstelt van afbeeldingen die uit het beeld zijn](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON>ad afbeeldingen die niet in beeld zijn nog niet"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Bronnen blokkeren de eerste weergave (FP) voor je pagina. Overweeg kritieke JS/css inline te leveren en alle niet-kritieke JS/stijlen uit te stellen. [Meer informatie over hoe je bronnen verwijdert die de weergave blokkeren](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Verwijder bronnen die de weergave blokkeren"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Grote netwerkpayloads kosten gebruikers effectief geld en hebben vaak lange laadtijden. [Meer informatie over hoe je de omvang van payloads beperkt](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Totale grootte was {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Vermijd enorme netwerkpayloads"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Vermijdt enorme netwerkpayloads"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Als je css-best<PERSON><PERSON> verkleint, kun je de omvang van netwerkpayloads verkleinen. [Meer informatie over hoe je CSS verkleint](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Verklein de css"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Als je JavaScript-bestand<PERSON> verklein<PERSON>, kunnen de omvang van de payload en de parseringstijd van het script worden verkleind. [Meer informatie over hoe je JavaScript verkleint](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Verklein JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Beperk niet-gebruikte regels uit stylesheets en stel css uit die niet wordt gebruikt voor content boven de vouw zodat er minder bytes worden verbruikt door netwerkactiviteit. [Meer informatie over hoe je ongebruikte CSS vermindert](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Beperk niet-gebruikte css"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Beperk niet-gebruikt JavaScript en stel het laden van scripts uit totdat ze nodig zijn zodat er minder bytes worden verbruikt door netwerkactiviteit. [Meer informatie over hoe je ongebruikt JavaScript vermindert](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Beperk niet-gebruikt JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "<PERSON>en lange levensduur voor het cachegeheugen kan herhaalde bezoeken aan je pagina versnellen. [Meer informatie over efficiënt cachebeleid](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 bron gevonden}other{# bronnen gevonden}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Lever statische items met een efficiënt cachebeleid"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Gebruikt een efficiënt cachebeleid voor statische items"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Geoptimaliseerde afbeeldingen worden sneller geladen en verbruiken minder mobiele data. [Meer informatie over hoe je afbeeldingen efficiënt codeert](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON> afbeeldingen op een efficiënte manier"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Werkelijke afmetingen"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Getoonde afmetingen"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "De afbeeldingen waren groter dan het weergegeven formaat"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "De afbeeldingen waren geschikt voor het weergegeven formaat"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> met het juiste formaat om mobiele data te besparen en de laadtijd te verbeteren. [Meer informatie over hoe je de grootte van afbeeldingen aanpast](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON> a<PERSON> het juiste formaat"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstgebaseerde bronnen moeten worden geleverd met compressie (gzip, deflate of brotli) om het totale aantal netwerkbytes te minimaliseren. [Meer informatie over tekstcompressie](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Zet tekstcompressie aan"}, "core/audits/content-width.js | description": {"message": "Als de breedte van de content van je app niet overeenkomt met de breedte van de viewport, is je app mogelijk niet geoptimaliseerd voor mobiele schermen. [Meer informatie over hoe je het formaat instelt van content voor de viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Het kijkvensterformaat van {innerWidth}px komt niet overeen met het vensterformaat van {outerWidth}px."}, "core/audits/content-width.js | failureTitle": {"message": "De <PERSON> heeft niet het juiste formaat voor het kijkvenster"}, "core/audits/content-width.js | title": {"message": "De <PERSON> heeft het juiste formaat voor het kijkvenster"}, "core/audits/critical-request-chains.js | description": {"message": "De onderstaande ketens met kritieke verzoeken laten zien welke bronnen met een hoge prioriteit worden geladen. Overweeg de lengte van ketens te verkleinen, de downloadgrootte van bronnen te beperken of het downloaden van onnodige bronnen uit te stellen om de laadtijd van de pagina te verbeteren. [Meer informatie over hoe je ketens van kritieke verzoeken voorkomt](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 keten gevonden}other{# ketens gevonden}}"}, "core/audits/critical-request-chains.js | title": {"message": "Doorlinken van kritieke verzoeken voorkomen"}, "core/audits/csp-xss.js | columnDirective": {"message": "Instructie"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON>"}, "core/audits/csp-xss.js | description": {"message": "<PERSON>en sterke Content Security Policy (CSP) verlaagt het risico op aanvallen met cross-site scripting (XSS) aanzienlijk. [Meer informatie over hoe je een CSP gebruikt om XSS te voorkomen](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntaxis"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "De pagina bevat een CSP dat is gedefinieerd in een <meta>-tag. Overweeg de CSP te verplaatsen naar een HTTP-header, of een andere strikte CSP in een HTTP-header te definiëren."}, "core/audits/csp-xss.js | noCsp": {"message": "Geen CSP gevonden in de handhavingsmodus"}, "core/audits/csp-xss.js | title": {"message": "Zorgen dat het CSP effectief is tegen XSS-aanvallen"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Beëindiging / Waarschuwing"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Beëindigde API's worden uiteindelijk verwijderd uit de browser. [Meer informatie over beëindigde API's](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 waarschuwing gevonden}other{# waarschuwingen gevonden}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Gebruikt beëindigde API's"}, "core/audits/deprecations.js | title": {"message": "Vermijdt beëindigde API's"}, "core/audits/dobetterweb/charset.js | description": {"message": "Een declaratie voor tekencodering is vereist. Dit kan worden gedaan met een `<meta>`-tag in de eerste 1024 bytes van de html of in de HTTP-reactiekop voor het contenttype. [Meer informatie over het definiëren van de tekencodering](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Tekensetdeclaratie ontbreekt of komt te laat in de HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> correct"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Door een doctype op te geven, voorkom je dat de browser overschakelt naar de quirks-modus. [Meer informatie over de declaratie van het doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "De naam van het doctype moet de tekenreeks `html` zijn."}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Het document bevat een `doctype` die `limited-quirks-mode` activeert"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Document moet een doctype bevatten"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Verwachtte een lege tekenreeks voor publicId"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Verwachtte een lege tekenreeks voor systemId"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Het document bevat een `doctype` die `quirks-mode` activeert"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Pagina heeft geen HTML-doctype en activeert dus de quirks-modus"}, "core/audits/dobetterweb/doctype.js | title": {"message": "<PERSON><PERSON>a bevat HTML-doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistiek"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "<PERSON><PERSON> grote DOM vergroot het geheugengebruik, en veroorzaakt langere [stijlberekeningen](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) en dure [dynamische aanpassingen in de vormgeving](https://developers.google.com/speed/articles/reflow). [Meer informatie over hoe je een overmatige DOM-grootte voorkomt](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}other{# elementen}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Vermijd een overmatig grote DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximum DOM-diepte"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Totaal aantal DOM-elementen"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximum aantal onderliggende elementen"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Vermijdt een overmatige grote DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Gebruikers want<PERSON>wen of raken in de war van sites die vragen om hun locatie zonder context. Overweeg het verzoek in plaats daarvan te koppelen aan gebruikershandelingen. [Meer informatie over het recht voor geolocatie](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Verzoekt om de geolocatierechten bij laden van pagina"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Vermijdt verzoeken om de geolocatierechten bij laden van pagina"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Type probleem"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problemen die in het deelvenster `Issues` in Chrome DevTools worden geregistreerd, geven aan dat er onopgeloste problemen zijn. Ze kunnen afkomstig zijn van niet-uitgevoerde netwerkverzoeken, onvoldoende beveiligingsbeheer en andere problemen met de browser. Open het deelvenster met problemen in Chrome DevTools voor meer informatie over elk probleem."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "<PERSON>en zijn geregistreerd in het deelvenster `Issues` Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Geblokkeerd door cross-origin-beleid"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Hoog bronverbruik door advertenties"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "<PERSON><PERSON> problemen in het deelvenster `Issues` in Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle frontend JavaScript-bibliotheken op de pagina gedetecteerd. [Meer informatie over deze diagnostische controle van JavaScript-bibliotheekdetectie](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Gedetecteerde JavaScript-bibliotheken"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Externe scripts die dynamisch worden geïnjecteerd via `document.write()` kunnen bij gebruike<PERSON> met een langzame verbinding het laden van de pagina met tie<PERSON><PERSON> seconden vertragen. [Meer informatie over hoe je document.write() vermijdt](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Vermijd `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Vermijdt `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Gebruikers want<PERSON>wen of raken in de war van sites die vragen om meldingen zonder context te versturen. Overweeg het verzoek in plaats daarvan te koppelen aan gebruikersgebaren. [Meer informatie over het op een verantwoordelijke manier verkrijgen van toestemming voor meldingen](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Verzoekt om de meldingsrechten bij laden van pagina"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Vermijdt verzoeken om de meldingsrechten bij laden van pagina"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 beschikt over veel voordelen ten opzichte van HTTP/1.1, waaronder binaire headers en multiplexing. [Meer informatie over HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 verzoek niet getoond via HTTP/2}other{# verzoeken niet getoond via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2 gebruiken"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Overweeg je touch- en event-listeners te markeren als `passive` om de scrollprestaties van je pagina te verbeteren. [Meer informatie over hoe je passieve event-listeners gebruikt](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Gebruikt geen passieve listeners om scrollprestaties te verbeteren"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Gebruikt passieve listeners voor de verbetering van de scrollprestaties"}, "core/audits/errors-in-console.js | description": {"message": "<PERSON>outen die in de console worden geregistreerd, geven aan dat er onopgeloste problemen zijn. Ze kunnen afkomstig zijn van niet-uitgevoerde netwerkverzoeken en andere problemen met de browser. [Meer informatie over fouten in de console voor diagnostische controle](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> zijn <PERSON>n gelogd op de console"}, "core/audits/errors-in-console.js | title": {"message": "<PERSON>r zijn geen browserfouten gelogd op de console"}, "core/audits/font-display.js | description": {"message": "Gebruik de css-functie `font-display` om ervoor te zorgen dat tekst zichtbaar is voor gebruikers terwijl weblettertypen worden geladen. [Meer informatie over `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "Zorg ervoor dat tekst zichtbaar blijft tijdens het laden van weblettertypen"}, "core/audits/font-display.js | title": {"message": "Alle tekst blijft zichtbaar tijdens het laden van weblettertypen"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountF<PERSON><PERSON><PERSON><PERSON>,plural, =1{Lighthouse kan de `font-display`-waarde voor de herkomst {fontOrigin} niet automatisch checken.}other{Lighthouse kan de `font-display`-waarden voor de herkomst {fontOrigin} niet automatisch checken.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (origineel)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Beeldverhouding (weergegeven)"}, "core/audits/image-aspect-ratio.js | description": {"message": "De weergaveafmetingen van afbeeldingen moeten overeenkomen met de natuurlijke beeldverhouding. [Meer informatie over beeldverhoudingen](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON> af<PERSON><PERSON><PERSON> weer met een on<PERSON><PERSON>e beeld<PERSON>houding"}, "core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON> af<PERSON><PERSON><PERSON> weer met een juiste beeldverhouding"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Werkelijk formaat"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Weergegeven formaat"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Verwacht formaat"}, "core/audits/image-size-responsive.js | description": {"message": "De natuurlijke afmetingen van de afbeelding moeten in verhouding zijn tot het weergaveformaat en de pixelratio om de helderheid van de afbeelding te maximaliseren. [Meer informatie over het leveren van responsieve afbeeldingen](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "<PERSON><PERSON> met lage resolutie"}, "core/audits/image-size-responsive.js | title": {"message": "<PERSON><PERSON> met de juiste resolutie"}, "core/audits/installable-manifest.js | already-installed": {"message": "De app is al geïnstalleerd"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Kan een vereist icoon niet downloaden uit het manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON> van fout"}, "core/audits/installable-manifest.js | description": {"message": "De service worker is de technologie waarmee je app veel functies van progressive web-apps kan gebruiken, zoals offline functionaliteit, toevoegen aan het startscherm en pushmeldingen. Met goede implementaties van de service worker en het manifest kunnen browsers gebruikers proactief vragen je app aan hun startscherm toe te voegen. Dit kan leiden tot grotere betrokkenheid. [Meer informatie over de vereisten voor installeerbaarheid van manifesten](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 reden}other{# redenen}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Het manifest van de web-app of de service worker vol<PERSON><PERSON> niet aan de vereisten voor installeerbaarheid"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "De app-URL en de ID van de Play Store komen niet overeen"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Je laadt de pagina in een incognitovenster"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "De eigenschap 'display' van het manifest moet 'standalone', 'fullscreen' of 'minimal-ui' zijn"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Het manifest bevat het veld 'display_override'. De eerste ondersteunde weergavemodus moet 'standalone', 'fullscreen' of 'minimal-ui' zijn."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Het manifest kan niet worden opgehaald, is leeg of kan niet worden geparseerd"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "De manifest-URL is gewi<PERSON><PERSON><PERSON> tijdens het ophalen van het manifest."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Het manifest bevat geen veld 'name' of 'short_name'"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Het manifest bevat geen geschikt icoon. Een png-, svg- of webp-indeling van ten minste {value0} pixels is vereist, het formaatkenmerk moet zijn ingesteld en het doelkenmerk (indien ingesteld) moet 'any' bevatten."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "<PERSON><PERSON> enkel geleverd icoon is een vierkant van minstens {value0} pixels in png-, svg- of webp-indeling met het doelkenmerk niet ingesteld of ingesteld op 'any'."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Het gedownloade icoon is leeg of beschadigd"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Geen Play Store-ID opgegeven"}, "core/audits/installable-manifest.js | no-manifest": {"message": "De pagina heeft geen manifest-URL <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "<PERSON><PERSON> overeenkomende service worker gevonden. <PERSON>ad de pagina opnieuw of check of het bereik van de service worker voor de huidige pagina het bereik en de start-URL van het manifest omvat."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Kan de service worker niet checken zonder een veld 'start_url' in het manifest"}, "core/audits/installable-manifest.js | noErrorId": {"message": "<PERSON> <PERSON> de installeerbaarheidsfout '{errorId}' wordt niet herkend"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "De pagina wordt niet weergegeven via een beveiligde bron"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "De pagina wordt niet in het hoofdframe geladen"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "De pagina werkt niet offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA is verwijderd en de installeerbaarheidschecks worden gereset."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Het opgegeven app-platform wordt niet ondersteund op Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Het manifest specificeert prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications wordt alleen ondersteund op bèta- en stabiele kanalen van Chrome op Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse kan niet bepalen of er een service worker is. <PERSON><PERSON><PERSON> het met een nieuwere vers<PERSON> van Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Het URL-schema van het manifest ({scheme}) wordt niet ondersteund op Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "De start-URL voor het manifest is ongeldig"}, "core/audits/installable-manifest.js | title": {"message": "Het manifest van de web-app en de service worker voldo<PERSON> aan de vereisten voor installeerbaarheid"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "<PERSON>en U<PERSON> in het manifest bevat een gebruike<PERSON>am, wachtwoord of poort"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "De pagina werkt niet offline. Vanaf de stabiele release van Chrome 93 in augustus 2021 wordt deze pagina niet meer als installeerbaar beschouwd."}, "core/audits/is-on-https.js | allowed": {"message": "Toegestaan"}, "core/audits/is-on-https.js | blocked": {"message": "Geblokkeerd"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Niet-beveiligde URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Oplossing voor verzoek"}, "core/audits/is-on-https.js | description": {"message": "Alle sites moeten worden beschermd met HTTPS, zelfs sites die geen gevoelige gegevens verwerken. Hieronder valt het vermijden van [gemengde content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), waarbij sommige bronnen worden geladen via HTTP ondanks dat het eerste verzoek wordt geleverd via HTTPS. HTTPS voorkomt dat indringers de communicatie tussen je app en je gebruikers manipuleren of hier passief naar luisteren en is een vereiste voor HTTP/2 en veel nieuwe webplatform-API's. [Meer informatie over HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 niet-beveiligd verzoek gevonden}other{# niet-beveiligde verzoeken gevonden}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Gebruikt geen HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Gebruikt HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatisch geüpgraded naar HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Dit is het grootste element met content dat weergegeven wordt in de viewport. [Meer informatie over het element Grootste weergave met content (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "LCP-element (grootste weergave met content)"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-bijdrage"}, "core/audits/layout-shift-elements.js | description": {"message": "Deze DOM-elementen dragen het meeste bij aan de CLS van de pagina. [Meer informatie over hoe je CLS kunt verbeteren](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Grote indelingsverschuivingen vermijden"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Afbeeldingen boven de vouw die worden geladen met lazy loading, worden later tijden<PERSON> de levenscyclus van de pagina weergegeven. Hierdoor kan de Grootste weergave met content (LCP) worden vertraagd. [Meer informatie over optimale lazy loading](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Afbeelding voor Grootste weergave met content (LCP) is geladen met lazy loading"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Afbeelding voor Grootste weergave met content (LCP) is niet geladen met lazy loading"}, "core/audits/long-tasks.js | description": {"message": "Vermeldt de langste taken in de primaire thread (dit is handig om de factoren te identificeren die tot de meeste invoervertraging leiden). [Meer informatie over hoe je lange taken voor de hoofdthread voorkomt](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# lange taak gevonden}other{# lange taken gevonden}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON> taken in de primaire thread vermijden"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categorie"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Overweeg de tijd te verminderen die aan het parseren, compileren en uitvoeren van JS wordt besteed. Het leveren van kleinere JS-payloads kan hierbij helpen. [Meer informatie over hoe je de primaire thread minimaliseert](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Primaire threadbewerkingen minimaliseren"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Primaire threadbewerkingen minimaliseren"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Sites moeten in alle grote browsers werken om zoveel mogelijk gebruikers te bereiken. [Meer informatie over compatibiliteit met verschillende browsers](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "De site werkt in verschillende browsers"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Zorg ervoor dat deep links voor afzonderlijke pagina's via een URL kunnen worden opgenomen en dat URL's uniek zijn zodat ze op social media kunnen worden gedeeld. [Meer informatie over het opgeven van deep links](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Elke pagina heeft een URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Overgangen moeten snel aanvoelen als je op een pagina tikt, zelfs bij gebruik van een langzaam netwerk. Dit is essentieel voor hoe de prestaties bij de gebruiker overkomen. [Meer informatie over paginaovergangen](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Paginaovergangen voelen niet alsof ze vastlopen op het netwerk"}, "core/audits/maskable-icon.js | description": {"message": "<PERSON>en maskeerbaar icoon zorgt ervoor dat de afbeelding de hele vorm zonder horizontale zwarte balken uitvult als een app wordt geïnstalleerd op het apparaat. [Meer informatie over maskeerbare manifesticonen](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest heeft geen maskeerbaar icoon"}, "core/audits/maskable-icon.js | title": {"message": "Manifest heeft een maskeerbaar icoon"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulatieve indelingsverschuiving (CLS) meet de beweging van zichtbare elementen binnen de viewport. [Meer informatie over de statistiek Cumulatieve indelingsverschuiving (CLS)](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interactie tot Volgende weergave meet de responsiviteit van de pagina, dat wil zeggen hoelang het duurt voordat de pagina zichtbaar reageert op gebruikersinvoer. [Meer informatie over de statistiek Interactie tot Volgende weergave](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Eerste weergave met content (FCP) geeft de tijd aan waarbinnen de eerste tekst of afbeelding wordt weergegeven. [Meer informatie over de statistiek Eerste weergave met content (LCP)](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Eerste nuttige weergave (FMP) meet wanneer de primaire content van een pagina zichtba<PERSON> is. [Meer informatie over de statistiek Eerste nuttige weergave (FMP)](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "Tijd tot interactief is de hoeveelheid tijd die nodig is voordat een pagina volledig interactief is. [Meer informatie over de statistiek Tijd tot interactief](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Grootste weergave met content (LCP) geeft het tijdstip aan waarop de grootste tekst of afbeelding is weergegeven. [Meer informatie over de statistiek Grootste weergave met content (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "De Maximale potentiële eerste invoervertraging (MPFID) die gebruikers kunnen ervaren, is de duur van de langste taak. [Meer informatie over de statistiek Maximale potentiële eerste invoervertraging (MPFID)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "Snelheidsindex laat zien hoe snel de content van een pagina zicht<PERSON> is. [Meer informatie over de statistiek Snelheidsindex](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Som van alle perioden tussen Eerste tekenbewerking met content (FCP) en Tijd tot interactief, als de taaklengte langer duurt dan 50 ms, aang<PERSON>ven in milliseconden. [Meer informatie over de statistiek Totale blokkeertijd](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Retourtijden (RTT) van netwerken hebben een grote invloed op de prestaties. Een hoge RTT naar een beginpunt geeft aan dat een server dichter bij de gebruiker de prestaties kan verbeteren. [Meer informatie over retourtijd](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> van netwerk"}, "core/audits/network-server-latency.js | description": {"message": "Serververtragingen kunnen invloed hebben op webprestaties. Als de serververtraging bij een oorsprong hoog is, is dit een indicatie dat de server overbelast is of slechte backend-prestaties levert. [Meer informatie over de reactietijd van de server](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> van server-backend"}, "core/audits/no-unload-listeners.js | description": {"message": "De gebeurtenis `unload` wordt niet op een betrouwbare manier geactiveerd en hierop wachten kan ertoe leiden dat browseroptimalisaties zoals de Back-Forward Cache niet worden uitgevoerd. Gebruik in plaats daarvan de gebeurtenis `pagehide` of `visibilitychange`. [Meer informatie over het ongedaan maken van laden van event-listeners](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registreert een `unload`-listener"}, "core/audits/no-unload-listeners.js | title": {"message": "Ver<PERSON><PERSON>dt `unload`-event-listeners"}, "core/audits/non-composited-animations.js | description": {"message": "Animaties die niet samengesteld zijn, kunnen langzaam zijn en ertoe leiden dat de CLS toeneemt. [Meer informatie over hoe je niet-samengestelde animaties vermijdt](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animatie-element gevonden}other{# animatie-elementen gevonden}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "<PERSON><PERSON> aan filters gerelateerde eigenschap kan pixels verplaatsen"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Het doel heeft een andere animatie die incompatibel is"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effect heeft een andere composite-modus dan 'replace'"}, "core/audits/non-composited-animations.js | title": {"message": "Niet-samengestelde animaties vermijden"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "<PERSON>en aan transformatie gerelateerde eigenschap is a<PERSON><PERSON><PERSON><PERSON><PERSON> van de g<PERSON> van het vak"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Niet-ondersteunde css-eigenschap: {properties}}other{Niet-ondersteunde css-eigenschappen: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Effect heeft niet-ondersteunde tijdparameters"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON> de hoeveelheid en grootte van netwerkverzoeken onder de targets die in het prestatiebudget zijn ingesteld. [Meer informatie over prestatiebudgetten](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 verzoek}other{# verzoeken}}"}, "core/audits/performance-budget.js | title": {"message": "Prestatiebudget"}, "core/audits/preload-fonts.js | description": {"message": "Laad lettertypen `optional` vooraf zodat nieuwe bezoekers deze kunnen gebruiken. [Meer informatie over het vooraf laden van lettertypen](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Lettertypen met `font-display: optional` zijn niet vooraf geladen"}, "core/audits/preload-fonts.js | title": {"message": "Lettertypen met `font-display: optional` zijn vooraf geladen"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Als het LCP-element dynamisch wordt toegevoegd aan de pagina, moet je de afbeelding vooraf laden om LCP te optimaliseren. [Meer informatie over het vooraf laden van LCP-elementen](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Afbeelding voor Grootste weergave met content (LCP) vooraf laden"}, "core/audits/redirects.js | description": {"message": "Omleidingen zorgen voor extra vertraging voordat de pagina kan worden geladen. [Meer informatie over hoe je pagina-omleidingen vermijdt](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Vermijd meerdere pagina-omleidingen"}, "core/audits/resource-summary.js | description": {"message": "Voeg een budget.json-bestand toe om budgetten in te stellen voor de hoeveelheid en grootte van paginabronnen. [Meer informatie over prestatiebudgetten](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 verzoek • {byteCount, number, bytes} Ki<PERSON>}other{# verzoeken • {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "Houd het aantal verzoeken laag en de overdrachtsgrootte klein"}, "core/audits/seo/canonical.js | description": {"message": "Canonieke links geven een suggestie voor welke URL moet worden getoond in de zoekresultaten. [Meer informatie over canonieke links](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Meerdere conflicterende URL's ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Ongeldige URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Wijst naar een andere `hreflang`-locatie ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Is geen absolute URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Verwijst naar de root-URL van het domein (de homepage), in plaats van een equivalente pagina van de content"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Document bevat geen geldig `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Document bevat een geldige `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON> die niet kan worden gecrawld"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Zoekmachines kunnen `href`-kenmerken in links gebruiken om websites te crawlen. Zorg ervoor dat het `href`-kenmerk van ankerelementen linkt naar een geschikte bestemming, zodat meer pagina's van de site kunnen worden gevonden. [Meer informatie over hoe je links crawlbaar maakt](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON>s kunnen niet worden gecrawld"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON>s kunnen worden gecrawld"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Aanvullende onleesbare tekst"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% van paginatekst"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Lettergrootten kleiner dan 12 pixels zijn te klein om leesbaar te zijn en leiden ertoe dat mobiele bezoekers met hun vingers moeten zoomen voordat ze de tekst kunnen lezen. Probeer minstens 12 pixels te gebruiken voor meer dan 60% van de paginatekst. [Meer informatie over leesbare lettergrootten](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} leesbare tekst"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst is onleesbaar omdat er geen kijkvenstermetatag is geoptimaliseerd voor mobiele schermen."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Document gebruikt geen leesbare lettergrootten"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON><PERSON><PERSON> tekst"}, "core/audits/seo/font-size.js | title": {"message": "Document gebruikt leesbare lettergrootten"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang-links laten zoekmachines weten welke versie van een pagina ze moeten vermelden in zoekresultaten voor een bepaalde taal of regio. [Meer informatie over `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Document bevat geen geldige `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relatieve href-waarde"}, "core/audits/seo/hreflang.js | title": {"message": "Document bevat een geldige `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Onverwachte taalcode"}, "core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON>'s met ongeldige HTTP-statuscodes worden mogelijk niet juist geïndexeerd. [Meer informatie over HTTP-statuscodes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Pagina bevat ongeldige HTTP-statuscode"}, "core/audits/seo/http-status-code.js | title": {"message": "<PERSON>gina bevat geldige HTTP-statuscode"}, "core/audits/seo/is-crawlable.js | description": {"message": "Zoekmachines kunnen je pagina's niet opnemen in zoekresultaten als de zoekmachines geen rechten hebben om ze te crawlen. [Meer informatie over crawler-instructies](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Pagina is geblokkeerd tegen indexeren"}, "core/audits/seo/is-crawlable.js | title": {"message": "Pagina is niet geblo<PERSON>erd tegen indexeren"}, "core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON> de <PERSON> van beschrijvende linktekst kunnen zoekmachines je content begrijpen. [Meer informatie over het toegankelijker maken van links](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link gevonden}other{# links gevonden}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Links bevatten geen beschrijvende tekst"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON>s bevatten beschrijvende tekst"}, "core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON> de [Tool voor het testen van gestructureerde gegevens](https://search.google.com/structured-data/testing-tool/) en de [Linter voor gestructureerde gegevens](http://linter.structured-data.org/) uit om gestructureerde gegevens te valideren. [Meer informatie over gestructureerde gegevens](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "De gestructureerde gegevens zijn geldig"}, "core/audits/seo/meta-description.js | description": {"message": "Er kunnen metabeschrijvingen worden opgenomen in zoekresultaten voor een korte samenvatting van paginacontent. [Meer informatie over de metabeschrijving](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Beschrijvingstekst is leeg."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Document bevat geen metabeschrijving"}, "core/audits/seo/meta-description.js | title": {"message": "Document bevat een metabeschrijving"}, "core/audits/seo/plugins.js | description": {"message": "Zoekmachines kunnen content van plug-ins niet indexeren en veel apparaten beperken plug-ins of ondersteunen deze niet. [Meer informatie over het vermijden van plug-ins](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Document gebruikt plug-ins"}, "core/audits/seo/plugins.js | title": {"message": "Document vermijdt plug-ins"}, "core/audits/seo/robots-txt.js | description": {"message": "Als je robots.txt-bestand niet juist is op<PERSON><PERSON><PERSON><PERSON>, begrijpen crawlers mogelijk niet hoe je wilt dat je website wordt gecrawld of geïndexeerd. [Meer informatie over robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Verzoek voor robots.txt heeft volgende HTTP-status geretourneerd: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 fout gevonden}other{# fouten gevonden}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse kan geen robots.txt-bestand downloaden"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt is niet geldig"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt is geldig"}, "core/audits/seo/tap-targets.js | description": {"message": "Interactieve elementen, zoa<PERSON> knoppen en links, moeten groot genoeg zijn (48 x 48 pixels) en moeten voldoende ruimte eromheen hebben, zodat er makkelijk op getikt kan worden zonder dat andere elementen worden aangeraakt. [Meer informatie over tikdoelen](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} van de tik<PERSON>elen heeft een geschikt formaat"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Tikdoelen zijn te klein, omdat er geen kijkvenstermetatag is geoptimaliseerd voor mobiele schermen"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hebben niet het juiste formaat"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Overlappend doel"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hebben het juiste formaat"}, "core/audits/server-response-time.js | description": {"message": "<PERSON>ud de reactietijd van de server voor het hoofddocument kort, omdat alle andere verzoeken daarvan afhankeli<PERSON> zijn. [Meer informatie over de statistiek Tijd tot eerste byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Hoofddocument duurde {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Eerste reactietijd van server verkorten"}, "core/audits/server-response-time.js | title": {"message": "Eerste reactietijd van server was kort"}, "core/audits/service-worker.js | description": {"message": "De service worker is de technologie waarmee je app veel functies van progressive web-apps kan gebruiken, zoals offline functionaliteit, toevoegen aan het startscherm en pushmeldingen. [Meer informatie over service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Deze pagina wordt beheerd door een service worker, maar er is geen `start_url` gevonden omdat het manifest niet kan worden geparseerd als geldig json-bestand"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Deze pagina wordt beheerd door een service worker, maar de `start_url` ({startUrl}) valt niet binnen het bereik van de service worker ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Deze pagina wordt beheerd door een service worker, maar er is geen `start_url` gevonden omdat er geen manifest is opgeha<PERSON>."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Deze herkomst heeft een of meer service workers, maar de pagina ({pageUrl}) valt niet binnen het bereik."}, "core/audits/service-worker.js | failureTitle": {"message": "Registreert geen service worker die de pagina en `start_url` beheert"}, "core/audits/service-worker.js | title": {"message": "Registreert een service worker die de pagina en `start_url` beheert"}, "core/audits/splash-screen.js | description": {"message": "<PERSON><PERSON> met een thema zorgt voor een gebruikerservaring van hoge kwaliteit als gebruikers je app starten vanaf hun startscherm. [Meer informatie over startschermen](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "Is niet geconfigureerd voor een aangepast startscherm"}, "core/audits/splash-screen.js | title": {"message": "Is geconfigureerd voor een aangepast startscherm"}, "core/audits/themed-omnibox.js | description": {"message": "Het thema van de adresbalk van de browser kan worden aangepast aan je site. [Meer informatie over thema's voor de adresbalk](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Hi<PERSON><PERSON> wordt geen themakleur voor de adresbalk ingesteld."}, "core/audits/themed-omnibox.js | title": {"message": "Hi<PERSON><PERSON> wordt een themakleur voor de adresbalk ingesteld."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (succes<PERSON><PERSON>n)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (sociaal)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Product"}, "core/audits/third-party-facades.js | description": {"message": "Bepaalde insluitingen van derden kunnen via lazy loading worden geladen. Overweeg deze te vervangen door een façade totdat ze vereist zijn. [Meer informatie over hoe je derden uitstelt met een façade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternatief beschikbaar voor façade}other{# alternatieven beschikbaar voor façade}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Bepaalde resources van derden kunnen via lazy loading worden geladen met een façade"}, "core/audits/third-party-facades.js | title": {"message": "Resources van derden laden via lazy loading met façades"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Derden"}, "core/audits/third-party-summary.js | description": {"message": "Code van derden kan van grote invloed zijn op de laadprestaties. Beperk het aantal overbodige externe providers en probeer code van derden te laden nadat je primaire pagina is geladen. [Meer informatie over hoe je de impact van derden beperkt](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "Code van derden heeft de primaire thread gedurende {timeInMs, number, milliseconds} ms geblokkeerd"}, "core/audits/third-party-summary.js | failureTitle": {"message": "De impact van code van derden beperken"}, "core/audits/third-party-summary.js | title": {"message": "Gebruik door derden minimaliseren"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Meting"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Statistiek"}, "core/audits/timing-budget.js | description": {"message": "Stel een timingbudget in om de prestaties van je site in de gaten te houden. Sites die goed presteren, worden snel geladen en reageren snel op gebruikersinvoer. [Meer informatie over prestatiebudgetten](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "<PERSON>ingbudget"}, "core/audits/unsized-images.js | description": {"message": "Stel een expliciete breedte en hoogte in voor afbeeldingselementen om opmaakverschuivingen te verminderen en CLS te verbeteren. [Meer informatie over hoe je afbeeldingsafmetingen instelt](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Afbeeldingselementen hebben geen expliciete `width` en `height`"}, "core/audits/unsized-images.js | title": {"message": "Afbeeldingselementen hebben expliciete `width` en `height`"}, "core/audits/user-timings.js | columnType": {"message": "Type"}, "core/audits/user-timings.js | description": {"message": "Overweeg je app te voorzien van de API voor gebruikerstiming om de daadwerkelijke prestaties van je app tijdens belangrijke gebruikerservaringen te meten. [Meer informatie over markeringen voor gebruikerstiming](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 gebruikerstiming}other{# gebruikerstimings}}"}, "core/audits/user-timings.js | title": {"message": "Markeringen en metingen voor gebruikerstiming"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Er is een `<link rel=preconnect>` gevonden voor {securityOrigin} maar deze is niet gebruikt door de browser. Check of je het kenmerk `crossorigin` juist gebruikt."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Overweeg hints voor `preconnect` of `dns-prefetch` van bron<PERSON> toe te voegen om vroege verbindingen met belangrijke externe oorsprongen tot stand te brengen. [Meer informatie over hoe je vooraf verbinding maakt met vereiste oorsprongen](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON> v<PERSON><PERSON> verb<PERSON>ing met ve<PERSON><PERSON> her<PERSON>"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "<PERSON>r zijn meer dan 2 `<link rel=preconnect>`-verbindingen gevonden. Deze moeten niet te veel worden gebruikt en alleen voor de belangrijkste herkomsten."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Er is een `<link rel=preconnect>` gevonden voor {securityOrigin} maar deze is niet gebruikt door de browser. Gebruik `preconnect` alleen voor belangrijke herkomsten die de pagina zeker aanvraagt."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Er is een `<link>` voor vooraf laden gevonden voor {preloadURL} maar deze is niet gebruikt door de browser. Check of je het kenmerk `crossorigin` juist gebruikt."}, "core/audits/uses-rel-preload.js | description": {"message": "Overweeg `<link rel=preload>` te gebruiken om prioriteit te geven aan het ophalen van bronnen die momenteel later tijdens het laden van de pagina worden opgehaald. [Meer informatie over het vooraf laden van sleutelverzoeken](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Laad belangrijke verzoeken vooraf"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "<PERSON><PERSON>-URL"}, "core/audits/valid-source-maps.js | description": {"message": "Bronkaarten zetten verkleinde code om in de oorspronkelijke broncode. Aan de hand hiervan kunnen ontwikkelaars foutopsporing uitvoeren in productie. Daarnaast kan Lighthouse meer inzichten bieden. Overweeg bronkaarten te implementeren om gebruik te maken van deze voordelen. [Meer informatie over bronkaarten](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "De bronkaarten voor een groot first-party JavaScript-bestand ontbreken"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "<PERSON> voor een groot JavaScript-bestand ontbreekt"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Waarschuwing: 1 item ontbreekt in `.sourcesContent`}other{Waarschuwing: # items ontbreken in `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "De pagina heeft geldige bronkaarten"}, "core/audits/viewport.js | description": {"message": "Met een `<meta name=\"viewport\">` wordt je app niet alleen geoptimaliseerd voor formaten van mobiele schermen, maar wordt ook [een vertraging van 300 milliseconden voor gebruikersinvoer](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) voorkomen. [Meer informatie over het gebruik van de viewport-metatag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Geen `<meta name=\"viewport\">`-tag gevonden"}, "core/audits/viewport.js | failureTitle": {"message": "Bevat geen `<meta name=\"viewport\">`-tag met `width` of `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Bevat een `<meta name=\"viewport\">`-tag met `width` of `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Dit is het werk dat threads blokkeert tijdens het meten van de Interactie tot de Volgende weergave. [Meer informatie over de statistiek Interactie tot Volgende weergave](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms besteed aan g<PERSON> '{interactionType}'"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimaliseer het werk tijdens belangrijke interacties"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Invoervertraging"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Presentatievertraging"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Verwerkingstijd"}, "core/audits/work-during-interaction.js | title": {"message": "Minimaliseert het werk tijdens belangrijke interacties"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Dit zijn suggesties om het gebruik van ARIA in je app te verbeteren, wat kan leiden tot betere functionaliteit voor gebruikers van hulptechnologie, zoa<PERSON> een scher<PERSON>."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Dit zijn mogelijkheden om alternatieve content voor audio en video te bieden. Dit verbetert mogelijk de functionaliteit voor gebruikers met een visuele of gehoorbeperking."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio en video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Deze items geven praktische tips voor algemene toegankelijkheid aan."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Praktische tips"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Deze controles geven mogelijkheden aan om [de toegang tot je web-app te verbeteren](https://developer.chrome.com/docs/lighthouse/accessibility/). Alleen een subset van toegankelijkheidsproblemen kan automatisch worden gedetecteerd. Daarom wordt handmatig testen ook aangeraden."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Deze items gaan over gebieden die niet kunnen worden getest met een automatische testtool. Bekijk meer informatie in onze gids over [het uitvoeren van een toegankelijkheidscontrole](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Toegankelijkheid"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Dit zijn suggesties om de le<PERSON>ba<PERSON>he<PERSON> van je content te verbeteren."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Dit zijn suggesties om de interpretatie van je content door gebruikers in verschillende landen te verbeteren."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisering en lokalisatie"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Dit zijn suggesties om de semantiek van de opties in je app te verbeteren. Zo kun je de functionaliteit verbeteren voor gebruikers van hulptechnologie, zoa<PERSON> een scher<PERSON>."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Namen en labels"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Dit zijn de mogelijkheden om toetsenbordnavigatie in je app te verbeteren."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigatie"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Dit zijn mogelijkheden om de functionaliteit voor het lezen van gegevens in tabellen of lijsten te verbeteren met ondersteunende technologie, zoals een scher<PERSON>."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabellen en lijsten"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Browsercompatibiliteit"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Praktische tips"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> en veiligheid"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Gebruikerservaring"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Prestatiebudgetten stellen normen in voor de prestaties van je site."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Meer informatie over de prestaties van je app. Deze cijfers hebben geen [directe invloed](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) op de prestatiescore."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostische gegevens"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Het meest essentiële aspect van de prestaties is hoe snel pixels worden weergegeven op het scherm. Belangrijkste statistieken: eerste weergave met content (FCP), eerste nuttige weergave (FMP)"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Verbeteringen voor eerste weergave"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Deze suggesties kunnen helpen je pagina sneller te laden. Ze hebben geen [directe invloed](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) op de prestatiescore."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Aanbevelingen"}, "core/config/default-config.js | metricGroupTitle": {"message": "Statistieken"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Verbeter de algemene laadfunctionaliteit, zodat de pagina zo snel mogelijk reageert en gebruiksklaar is. Belangrijkste statistieken: Tijd tot interactief, Snelheidsindex"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Algemene verbeteringen"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Prestaties"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON> checks valideren de aspecten van een progressive web-app. [Meer informatie over wat een goede progressive web-app is](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Deze controles worden vereist door de baseline [PWA-checklist](https://web.dev/pwa-checklist/) maar worden niet automatisch gecontroleerd door Lighthouse. Ze zijn niet van invloed op je score, maar het is wel belangrijk dat je ze handmatig verifieert."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installeerbaar"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Geoptimaliseerd voor PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> checks zorgen ervoor dat je pagina het algemene advies voor zoekmachineoptimalisatie volgt. Er zijn veel aanvullende factoren waarvoor Lighthouse hier geen score geeft, maar die wel van invloed kunnen zijn op je zoekpositie, waaronder de prestaties voor [Site-vitaliteit](https://web.dev/learn-core-web-vitals/). [Meer informatie over Google Search Essentials](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> deze extra validators op je site uit om aanvullende praktische tips voor SEO te controleren."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Maak je HTML zo op dat crawlers de content van je app beter begrijpen."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Praktische tips voor content"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Crawlers hebben toegang nodig tot je app om je website in zoekresultaten weer te geven."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawlen en indexeren"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON>org dat je pagina's geschikt zijn voor mobiele apparaten zodat gebruikers niet hoeven te knijpen of in te zoomen om de contentpagina's te lezen. [Meer informatie over hoe je pagina's geschikt maakt voor mobiele apparaten](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Geschikt voor mobiele apparaten"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Zo te zien heeft het geteste apparaat een langzamere CPU dan Lighthouse verwachtte. Dit kan je prestatiescore negatief beïnvloeden. Meer informatie over hoe je [de juiste vertragingsvermenigvuldiger voor de CPU kalibreert](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "De pagina wordt mogelijk niet geladen zoals verwacht omdat je test-URL ({requested}) is omgeleid naar {final}. Probeer de tweede URL rechtstreeks te testen."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "De pagina is te langzaam geladen om binnen de tijdslimiet te voltooien. De resultaten kunnen onvolledig zijn."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Er is een time-out opgetreden bij het wissen van het cachege<PERSON><PERSON> van <PERSON>. Check deze pagina opnieuw en dien een bug in als het probleem aanhoudt."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Er kunnen op de volgende locatie gegevens zijn opgeslagen die van invloed zijn op de laadprestaties: {locations}. Controleer deze pagina in een incognitovenster om te voorkomen dat die bronnen van invloed zijn op je scores.}other{Er kunnen op de volgende locaties gegevens zijn opgeslagen die van invloed zijn op de laadprestaties: {locations}. Controleer deze pagina in een incognitovenster om te voorkomen dat die bronnen van invloed zijn op je scores.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Er is een time-out opgetreden bij het wissen van de oorspronggegevens. Check deze pagina opnieuw en dien een bug in als het probleem aanhoudt."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON>een pagina's die worden geladen via een GET-verzoek, komen in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Alleen pagina's met een statuscode 2XX kunnen worden gecachet."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome heeft een poging waargenomen om JavaScript uit te voeren in het cachegeheugen."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON>gin<PERSON>'s die een App<PERSON><PERSON>r hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Back-Forward Cache is uitgezet door flags. Ga naar chrome://flags/#back-forward-cache om het lokaal aan te zetten op dit apparaat."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Back-Forward <PERSON><PERSON> is uitgezet door de opdrachtregel."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Back-Forward <PERSON><PERSON> is uit<PERSON><PERSON>t vanwege onvoldoende geheugen."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Back-Forward <PERSON><PERSON> wordt niet ondersteund door de gemachtigde."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Back-Forward <PERSON><PERSON> is uitgezet voor prerenderer."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "De pagina kan niet worden gecachet omdat deze een BroadcastChannel-instantie met geregistreerde listeners bevat."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Pagina's met de header cache-control:no-store kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON> <PERSON><PERSON> is opzettelijk gewist."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "De pagina is verwijderd uit het cachegeheugen zodat een andere pagina kon worden gecachet."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON><PERSON><PERSON>'s met plug-ins komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Pagina's die de FileChooser API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Pagina's die de File System Access API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Pagina's die Media Device Dispatcher gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON>en mediaspeler speelde tijdens het sluiten."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Pagina's die de MediaSession API gebruiken en een afspeelstatus instellen, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Pagina's die de MediaSession API gebruiken en action-handlers instellen, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Back-Forward <PERSON><PERSON> is uit<PERSON><PERSON><PERSON> van<PERSON><PERSON> s<PERSON>."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Pagina's die SecurityHandler gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Pagina's die de Serial API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Pagina's die de WebAuthetication API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Pagina's die de WebBluetooth API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Pagina's die de WebUSB API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON>'s die een specifieke worker of worklet geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Het document was niet helemaal geladen voordat het werd gesloten."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "App-banner was geopend tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome W<PERSON>twoordmanager was geopend tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-distillatie was bezig tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer was geopend tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Back-Forward <PERSON><PERSON> is uitgezet vanwege extensies die de API voor berichten gebruiken."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Extensies met een langdurige verbinding moeten de verbinding sluiten voordat Back-Forward <PERSON><PERSON> wordt geopend."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Extensies met een langdurige verbinding hebben gep<PERSON><PERSON> berichten te sturen naar frames in de Back-Forward Cache."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Back-Forward <PERSON><PERSON> is uitgezet vanwege extensies."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON> dialoogven<PERSON>, zoals dialoogvenster voor formulier opnieuw indienen of HTTP-wachtwoord, is getoond voor de pagina tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "De offline pagina werd getoond tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Interventiebalk voor geheugen vol was geopend tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Er waren rechtenverzoeken tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Pop-up blocker was geopend tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing-g<PERSON><PERSON><PERSON> zijn getoond tijdens het sluiten."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing beschouwt deze pagina als misleidend en heeft de pop-up geblokkeerd."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Er is een service worker gea<PERSON><PERSON> te<PERSON><PERSON><PERSON> de pagina zich in Back-Forward <PERSON><PERSON> bevond."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Back-Forward <PERSON><PERSON> is uitge<PERSON>t vanwege een documentfout."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Pagina's die FencedFrames gebruiken, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "De pagina is verwijderd uit het cachegeheugen zodat een andere pagina kon worden gecachet."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Pagin<PERSON>'s die toegang tot mediastreams hebben gegeven, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON><PERSON>'s die portals geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON><PERSON>'s die IdleManager geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON>'s met een open IndexedDB-verbinding komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "<PERSON><PERSON> zijn niet in aanmerking komende API's gebruikt."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Pagina's waarin <PERSON> is geïnjecteerd door extensies, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON>gin<PERSON>'s waarin <PERSON>Sheet is geïnjecteerd door extensies, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Interne fout."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Back-Forward <PERSON><PERSON> is uitge<PERSON>t vanwege een keepalive-verz<PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON><PERSON>'s die toetsenbordvergrendeling gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | loading": {"message": "De pagina was niet helemaal geladen voordat die werd gesloten."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON>gin<PERSON>'s wa<PERSON><PERSON> de hoofdbro<PERSON> cache-control:no-cache bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Pagina's wa<PERSON><PERSON> de hoofdbron cache-control:no-store bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigatie is gean<PERSON><PERSON><PERSON> voordat de pagina kon worden hersteld vanuit Back-Forward <PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "De pagina is verwijderd uit het cachegeheugen omdat een actieve netwerkverbinding te veel gegevens heeft gekregen. Chrome beperkt de hoeveelheid gegevens die een pagina kan ontvangen als deze in het cachegeheugen is opgeslagen."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON>'s met inflight fetch() of XHR komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "De pagina is verwi<PERSON><PERSON><PERSON> uit Back-Forward <PERSON><PERSON> omdat bij een actief netwerkverzoek een omleiding betrokken was."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "De pagina is verwijderd uit het cachegeheugen omdat een netwerkverbinding te lang open was. Chrome beperkt hoelang een pagina gegevens kan ontvangen als deze in het cachegeheugen is opgeslagen."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Pagina's die geen geldige reactiekop hebben, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigatie vond plaats in een ander frame dan het hoofdframe."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON><PERSON>'s met actieve geïndexeerde DB-transacties komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzo<PERSON> in behandeling komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzoek voor ophalen in behandeling komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzo<PERSON> in behandeling komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON><PERSON>'s met een X<PERSON>R-netwerkverzoek in behandeling komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON><PERSON>'s die Payment<PERSON><PERSON><PERSON> g<PERSON>n, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON><PERSON>'s die Picture-in-Picture geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward C<PERSON>."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON><PERSON>'s die portals geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | printing": {"message": "Pagina's die UI voor afdrukken tonen, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "De pagina is geopend met `window.open()` en een ander tabblad bevat een verwijzing er<PERSON>, of de pagina heeft een venster geopend."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Het proces van het weergaveprogramma voor de pagina in Back-Forward <PERSON><PERSON> is g<PERSON><PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Het proces van het weergaveprogramma voor de pagina in Back-Forward <PERSON><PERSON> is beë<PERSON>igd."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor audio-opnamen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON>gin<PERSON>'s die sensorrechten hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Pagina's die rechten voor synchronisatie of op<PERSON>n op de achtergrond hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON>'s die MIDI-rechten hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor meldingen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON>'s die opslagtoegang hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor video-opnamen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Alleen pagina's met HTTP/HTTPS als URL-schema kunnen worden gecachet."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "De pagina is geclaimd door een service worker te<PERSON><PERSON><PERSON><PERSON> de pagina zich in Back-Forward <PERSON><PERSON> bevindt."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Een service worker he<PERSON><PERSON> g<PERSON><PERSON><PERSON> de pagina in Back-Forward <PERSON><PERSON> een `MessageEvent` te sturen."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "De registratie van ServiceWorker is ongedaan gemaakt terwijl een pagina zich in Back-Forward <PERSON><PERSON> bevond."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON> pagina is verwij<PERSON>d uit Back-Forward <PERSON><PERSON> van<PERSON> de <PERSON>ring van een service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome is opnieuw opgestart en heeft de Back-Forward Cache-items gewist."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON>gin<PERSON>'s die SharedWorker gebruiken, komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON><PERSON>'s die SpeechRecognizer gebruiken, komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON><PERSON>'s die SpeechSynthesis gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Een iframe op de pagina heeft navigatie gestart die niet is afgerond."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON>gin<PERSON>'s wa<PERSON><PERSON> de subbron cache-control:no-cache bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Pagina's wa<PERSON><PERSON> de subbron cache-control:no-store bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | timeout": {"message": "De pagina heeft de maximale tijd in Back-Forward <PERSON><PERSON> overs<PERSON>en en is verlopen."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Er is een time-out voor de pagina opgetreden tijdens het op<PERSON>an in Back-Forward Cache (waarschijnlijk vanwege langdurige pagehide-handlers)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "De pagina heeft een unload-handler in het hoofdframe."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "De pagina heeft een unload-handler in een subframe."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Browser heeft de override-header voor user-agents gewijzigd."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON>gin<PERSON>'s die toegang hebben gegeven om video of audio op te nemen, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Pagina's die WebDatabase gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Pagina's die WebHID gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON>gin<PERSON>'s die WebLocks geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Pagina's die WebNfc gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Pagina's die WebOTPService gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Pagina's met WebRTC kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Pagina's die WebShare geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "<PERSON><PERSON><PERSON>'s met WebSocket kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Pagina's met WebTransport kunnen niet worden opgeslagen in Back-Forward Cache."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Pagina's die WebXR geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Voor compatibiliteit met oudere browsers kun je overwegen https: en http: URL-schema's toe te voegen (deze worden genegeerd door browsers die 'strict-dynamic' ondersteunen)."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener is beëindigd sinds CSP3. <PERSON><PERSON><PERSON><PERSON> in plaats daarvan de header Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer is beëindigd sinds CSP2. Gebruik in plaats daarvan de header Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss is beëindigd sinds CSP2. <PERSON><PERSON><PERSON><PERSON> in plaats daarvan de header X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Zonder base-uri kunnen ing<PERSON> <base>-tags de basis-URL instellen voor alle relatieve URL's (bijv. scripts) naar een door de aanvaller beheerd domein. Overweeg base-uri in te stellen op 'none' of 'self'."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Als object-src ontbreekt, kunnen plug-ins worden geïnjecteerd die onveilige scripts uitvoeren. Overweeg object-src in te stellen op 'none' als dat kan."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "De instructie script-src ontbreekt. Dit kan tot gevolg hebben dat onveilige scripts kunnen worden uitgevoerd."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Ben je de puntkomma vergeten? Zo te zien is {keyword} een instructie en geen zoekwoord."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces moeten de base64-tekenset gebruiken."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces moeten ten minste 8 tekens lang zijn."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Vermijd het gebruik van plattetekst-URL-schema's ({keyword}) in deze instructie. Bij plattetekst-URL-schema's kunnen scripts afkomstig zijn van een onveilig domein."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Vermijd het gebruik van plattetekst-jokertekens ({keyword}) in deze instructie. Bij plattetekst-jokertekens kunnen scripts afkomstig zijn van een onveilig domein."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "De rapportagebestemming is alleen ingesteld via de instructie report-to. Deze instructie wordt alleen ondersteund in op Chromium gebaseerde browsers, dus het wordt aanbevolen ook een instructie report-uri te gebruiken."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "<PERSON><PERSON> is geen CSP waarmee een rapportagebestemming wordt ingesteld. <PERSON><PERSON><PERSON> is het moeilijk om het CSP na verloop van tijd te onderhouden en te monitoren op kwetsbaarheden."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "To<PERSON>tingslij<PERSON> van hosts kunnen vaak worden omzeild. Overweeg CSP-nonces of -hashes te geb<PERSON>iken, zo nodig samen met 'strict-dynamic'."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Onbekende CSP-instructie."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON>o te zien is {keyword} een ongeldig z<PERSON>."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Met 'unsafe-inline' kunnen onveilige scripts en gebeurtenishandlers op de pagina worden uitgevoerd. Overweeg CSP-nonces of -hashes te gebruiken om scripts afzonderlijk toe te staan."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Voor compatibiliteit met oudere browsers kun je overwegen 'unsafe-inline' toe te voegen (dit wordt genegeerd door browsers die nonces/hashes ondersteunen)."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Autorisatie wordt niet gedekt door het jokerteken (*) voor het gebruik van `Access-Control-Allow-Headers` in CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Bronverzoeken waarvan de URL's verwijderde `(n|r|t)`-tekens voor witruimten en kleiner-dan-tekens (`<`) bevatten, worden geblokkeerd. Verwijder nieuwe-regelitems en codeer kleiner-dan-tekens, bij<PERSON>orbeeld in kenmerkwaarden voor elementen, om deze bronnen te laden."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` is be<PERSON><PERSON><PERSON><PERSON>, gebruik in plaats daarvan de gestandaardiseerde API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` is be<PERSON><PERSON><PERSON><PERSON>, gebruik in plaats daarvan de gestandaardiseerde API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` is be<PERSON><PERSON><PERSON><PERSON>, gebru<PERSON> in plaats daarvan de gestandaardiseerde API: `nextHopProtocol` in Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Cookies die een `(0|r|n)`-te<PERSON> bevatten, worden afgewezen in plaats van afgekapt."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Versoe<PERSON>ing van het same-origin-beleid door `document.domain` in te stellen is beëindigd en wordt standaard uitgezet. Deze beëindigingswaarschuwing is voor een cross-origin-toegang die is aangezet door `document.domain` in te stellen."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "De activering van {PH1} via cross-origin iframes is beëindigd en wordt in de toekomst verwijderd."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "In plaats van de kiezer `-internal-media-controls-overlay-cast-button` moet het kenmerk `disableRemotePlayback` worden gebruikt om de standaardintegratie van Cast uit te zetten."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} is beëindigd. Gebruik in plaats daarvan {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "<PERSON><PERSON> is een voorbeeld van een vertaalde foutmelding over een beëindigde functie."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Versoe<PERSON><PERSON> van het same-origin-beleid door `document.domain` in te stellen is beëindigd en wordt standaard uitgezet. Als je deze functie wilt blijven geb<PERSON>ike<PERSON>, meld je je af voor origin-keyed agentclusters door een `Origin-Agent-Cluster: ?0`-header met de HTTP-reactie voor het document en de frames mee te sturen. Zie https://developer.chrome.com/blog/immutable-document-domain/ voor meer informatie."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` is beëindigd en wordt verwijderd. Gebruik in plaats daarvan `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "De header `Expect-CT` is beëindigd en wordt verwijderd. Chrome vereist certificaattransparantie voor alle openbaar vertrouwde certificaten die na 30 april 2018 zijn uitgegeven."}, "core/lib/deprecations-strings.js | feature": {"message": "Check de statuspagina van de functie voor meer informatie."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` en `watchPosition()` werken niet meer op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` en `watchPosition()` zijn beëindigd op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` werkt niet meer op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` is beëindigd. Gebruik in plaats daarvan `RTCPeerConnectionIceErrorEvent.address` of `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "De verkoperoorsprong en willekeurige gege<PERSON>s van de `canmakepayment`-service worker-geb<PERSON><PERSON><PERSON> zijn beëindigd en worden verwijderd: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "De website heeft een subbron bij een netwerk aangevraagd die alleen toegankelijk is vanwege de bevoorrechte netwerkpositie van de gebruikers. Deze verzoeken stellen niet-openbare apparaten en servers bloot aan internet, waardoor het risico op een CSRF-aanval (cross-site request forgery) en/of informatielekken toeneemt. Ter beperking van deze risico's beëindigt Chrome verzoeken naar niet-openbare subbronnen als deze worden gestart vanuit onbeveiligde omgevingen en gaat Chrome dergelijke verzoeken blokkeren."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "Css kan niet worden geladen via `file:`-URL's, tenzij ze eindigen op een `.css`-bestandsextensie."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Het gebruik van `SourceBuffer.abort()` om de verwijdering van het asynchrone bereik van `remove()` te annuleren, is verwijderd vanwege wijzigingen in de specificaties. Ondersteuning wordt in de toekomst verwijderd. Gebruik in plaats daarvan een listener voor de gebeurtenis `updateend`. `abort()` is alleen bedoeld om een asynchrone mediatoevoeging te annuleren of om de status van de parser te resetten."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Het instellen van `MediaSource.duration` onder het hoogste presentatietijdstempel van gebufferde gecodeerde frames is beëindigd vanwege wijzigingen in de specificaties. Ondersteuning voor impliciete verwijdering van afgebroken gebufferde media wordt in de toekomst verwijderd. In plaats daarvan moet je `remove(newDuration, oldDuration)` expliciet uitvoeren voor alle `sourceBuffers`, waarbij `newDuration < oldDuration` is."}, "core/lib/deprecations-strings.js | milestone": {"message": "Deze wijziging wordt van kracht vanaf mijlpaal {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI vraagt toestemming voor het gebruik van SysEx, zelfs als de SysEx niet is opgegeven in de `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "De Notification API mag niet meer worden gebruikt vanaf niet-beveiligde oorsprongen. Je kunt overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Rechten voor de Notification API kunnen niet meer worden aangevraagd bij een cross-origin iframe. Je kunt rechten aanvragen bij een frame op het hoofdniveau of in plaats daarvan een nieuw venster openen."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Je partner g<PERSON><PERSON><PERSON>t een verouderde (D)TLS-versie. <PERSON><PERSON>m contact op met je partner om dit op te lossen."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL in niet-beveiligde contexten is beëindigd en wordt binnenkort verwijderd. Gebruik Web Storage of Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Als je `overflow: visible` opgeeft voor img-, video- en canvas-tags, kunnen deze visuele content produceren buiten de elementgrenzen. Zie https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` is beëindigd. Gebruik in plaats daarvan just-in-time-installaties voor betalingshandlers."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "<PERSON> aan<PERSON>ep van `PaymentRequest` heeft de instructie Content-Security-Policy (CSP) `connect-src` genegeerd. Deze negeeractie is beëindigd. Voeg de betaalmethode-ID van de `PaymentRequest`-API (in het veld `supportedMethods`) toe aan je CSP-instructie `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` is beëindigd. Gebruik in plaats daarvan de gestandaardiseerde `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` met een boven<PERSON> `<picture>` is ongeldig en wordt daarom genegeerd. Gebruik in plaats daarvan `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` is beëindigd. Gebruik in plaats daarvan de gestandaardiseerde `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Subbronverzoeken waarvan de URL's ingesloten inloggegevens bevatten (bijv. `**********************/`), worden geblokkeerd."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "De beperking `DtlsSrtpKeyAgreement` is verwijderd. Je hebt een `false`-waarde voor deze beperking opgegeven. Deze wordt geïnterpreteerd als een poging om de verwijderde `SDES key negotiation`-methode te gebruiken. Deze functionaliteit is verwijderd. Gebruik een service die `DTLS key negotiation` wel ondersteunt."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "De beperking `DtlsSrtpKeyAgreement` is verwijderd. Je hebt een `true`-waarde voor deze beperking opgegeven. Deze heeft geen effect en kan worden verwijderd om meer orde te scheppen."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` waargenomen. Dit dialect van `Session Description Protocol` wordt niet meer ondersteund. Gebruik in plaats daarvan `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, dat wordt gebruikt om een `RTCPeerConnection` samen te stellen met `{sdpSemantics:plan-b}`, is een verouderde niet-standaard versie van `Session Description Protocol` dat definitief van het Web Platform is verwijderd. Het protocol is nog wel beschikbaar als je met `IS_FUCHSIA` ontwikkelt, maar we willen het protocol zo snel mogelijk verwijderen. Vertrouw er niet meer op. Zie https://crbug.com/1302249 voor de status."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "De optie `rtcpMuxPolicy` is beëindigd en wordt verwijderd."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` vereist cross-origin-isolatie. Zie https://developer.chrome.com/blog/enabling-shared-array-buffer/ voor meer informatie."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` zonder gebruikersactivering is beëindigd en wordt verwijderd."}, "core/lib/deprecations-strings.js | title": {"message": "Beëindigde functie gebruikt"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensies moeten toestemming geven voor cross-origin-isolatie om `SharedArrayBuffer` te blijven gebruiken. Zie https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} is leverancierspecifiek. Gebruik in plaats daarvan de standaard {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 wordt niet ondersteund door de reactie-json in `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchrone `XMLHttpRequest` op de primaire thread is be<PERSON><PERSON><PERSON>d vanwege het nadelige effect op de gebruikerservaring van de eindgebruiker. Check https://xhr.spec.whatwg.org/ voor meer hulp."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` is beëindigd. G<PERSON><PERSON>ik `isSessionSupported()` en check in plaats daarvan de omgezette booleaanse waarde."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tijd dat primaire thread is geb<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache-TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Beschrijving"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Mislukte elementen"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Locatie"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Over het budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Verzoeken"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Brongrootte"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Brontype"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Form<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Begintijd"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON> tijd"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Overdrachtsgrootte"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potentiële besparingen"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potentiële besparingen"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potentiële besparing van {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element gevonden}other{# elementen gevonden}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potentiële besparing van {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Eerste nuttige weergave (FMP)"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Lettertype"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Afbeelding"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interactie tot Volgende weergave"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Laag"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Normaal"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Max<PERSON> potenti<PERSON> eerste invoervertraging"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON> bronnen"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Derden"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Totaal"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Er is een fout opgetreden bij het opnemen van het netwerkspoor voor het laden van je pagina. Voer Lighthouse opnieuw uit. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Time-out tij<PERSON><PERSON> wachten op eerste verbinding met Debugger-protocol."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome heeft geen screenshots verzameld tijdens het laden van de pagina. Zorg dat er content zichtba<PERSON> is op de pagina en voer Lighthouse daarna opnieuw uit. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-servers kunnen het opgegeven domein niet omzetten."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Er is een fout opgetreden in de vereiste {artifactName}-verzamelaar: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Er is een interne Chrome-fout opgetreden. Start Chrome opnieuw op en probeer Lighthouse nogmaals uit te voeren."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Vereiste {artifactName}-verzamelaar is niet uitgevoerd."}, "core/lib/lh-error.js | noFcp": {"message": "De pagina geeft geen content weer. Zorg dat het browservenster tijdens het laden op de voorgrond blijft en probeer het opnieuw. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "De pagina heeft geen content getoond die in aanmerking komt als Grootste weergave met content (LCP). Zorg dat de pagina een geldig LCP-element bevat en probeer het opnieuw. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "De opgegeven pagina is geen HTML-pagina (weergegeven als MIME-type {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Deze versie van Chrome is te oud om '{featureName}' te ondersteunen. Gebruik een nieuwere versie om volledige resultaten te bekijken."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse kan de door jou aangevraagde pagina niet goed laden. Zorg ervoor dat je de juiste URL test en dat de server correct reageert op alle verzoeken."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse kan de door jou aangevraagde URL niet goed laden omdat de pagina niet meer reageert."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "De URL die je hebt opgegeven, bevat geen geldig beveiligingscertificaat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome heeft laden van pagina met interstitial voorkomen. Zorg ervoor dat je de juiste URL test en dat de server correct reageert op alle verzoeken."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse kan de aangevraagde pagina niet goed laden. Zorg ervoor dat je de juiste URL test en dat de server correct reageert op alle verzoeken. (Details: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse kan de aangevraagde pagina niet goed laden. Zorg ervoor dat je de juiste URL test en dat de server correct reageert op alle verzoeken. (Statuscode: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Het laden van je pagina duurde te lang. Volg de suggesties in het rapport om de laadtijd van je pagina te beperken. Voer Lighthouse daarna opnieuw uit. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Tijdens het wachten op een reactie van het DevTools-protocol is de toegewezen tijd overschreden. (Methode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Voor het ophalen van broncontent is de toegewezen tijd overschreden"}, "core/lib/lh-error.js | urlInvalid": {"message": "Het lijkt erop dat je een ongeldige URL hebt opgegeven."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Het MIME-type van de pagina is XHTML: Lighthouse ondersteunt dit documenttype niet expliciet"}, "core/user-flow.js | defaultFlowName": {"message": "Gebruikersstroom ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navigatierapport ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Momentopnamerapport ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Tijdsduurrapport ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Alle rapporten"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorieën"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Toegankelijkheid"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Praktische tips"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Prestaties"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive web-app"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Desktop"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Het rapport van de Lighthouse-stroom begrijpen"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Be<PERSON><PERSON><PERSON><PERSON> hoe stromen werken"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Navigatierapporten gebruiken…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Momentopnamerapporten gebruiken…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Tijdsduurrapporten gebruiken…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Lighthouse-prestatiescore ophalen."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Prestatiestatistieken voor het laden van de pagina meten, zoals de Grootste weergave met content (LCP) en de snelheidsindex."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Mogelijkheden van progressive web-apps evalueren."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Toegankelijkheidsproblemen opsporen in apps met <PERSON><PERSON> pagina of in complexe formulieren."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Praktische tips evalueren voor menu's en UI-elementen die achter interactie verborgen zijn."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Indelingsverschuivingen en de JavaScript-uitvoeringstijd meten voor verschillende interacties."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Ontdek mogelijkheden om de functionaliteit van langdurige pagina's en apps met één pagina te verbeteren."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Hoogste impact"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informatieve controle}other{{numInformative} informatieve controles}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobiel"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON> van pagina"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "In navigatierapporten wordt het laden van één pagina geanalyseerd, net als in de oorspronkelijke Lighthouse-rapporten."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigatierapport"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navigatierapport}other{{numNavigation} navigatierapporten}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} controle die kan worden doorstaan}other{{numPassableAudits} controles die kunnen worden doorstaan}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} controle doorstaan}other{{numPassed} controles doorstaan}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Fout"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Slecht"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Goed"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Opsla<PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Vastgelegde staat van pagina"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "In momentopnamerapporten worden pagina's in een bepaalde situatie geanalyseerd, doorgaans na interactie van de geb<PERSON>iker."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Momentopnamerapport"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} momentopnamerapport}other{{numSnapshot} momentopnamerapporten}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Overzicht"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Gebruikersinteracties"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "In tijdsduurrapporten wordt een bepaalde tijdsduur geanalyseerd die meestal gebruikersinteracties omvat."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Perioderapport"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} perioderapport}other{{numTimespan} perioderapporten}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse-rapport over gebruikersstroom"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Gebruik in het geval van content met animatie [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) om het CPU-gebruik te minimaliseren als de content niet in beeld is."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Overweeg alle [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-componenten weer te geven in WebP-indelingen, terwijl je een geschikte reserve specificeert voor andere browsers. [Meer informatie](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Zorg dat je [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) gebruikt voor afbeeldingen die automatisch worden geladen via lazy loading. [Meer informatie](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Gebruik tools zoals [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) om [AMP-lay-outs aan de serverzijde te renderen](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Bekijk de [AMP-documentatie](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) om er zeker van te zijn dat al je stijlen worden ondersteund."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "De component [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ondersteunt het kenmerk [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) om te specificeren welke afbeeldingsitems moeten worden gebruikt op basis van het schermformaat. [Meer informatie](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Overweeg virtueel scrollen met de Component Dev Kit (CDK) als er zeer grote lijsten worden weergegeven. [Meer informatie](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Pas [codesplitsing op routeniveau](https://web.dev/route-level-code-splitting-in-angular/) toe om de grootte van je JavaScript-bundels te minimaliseren. Je kunt ook vooraf items cachen met de [Angular-service worker](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Als je Angular CLI gebruikt, moet je ervoor zorgen dat de builds worden gegenereerd in de productiemodus. [Meer informatie](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Als je Angular CLI gebruikt, moet je bronkaarten opnemen in je productiebuild om je bundels te inspecteren. [Meer informatie](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Laad routes vooraf om de navigatie te versnellen. [Meer informatie](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Overweeg het hulpprogramma `BreakpointObserver` in de Component Dev Kit (CDK) te gebruiken voor beheer van breakpoints in afbeeldingen. [Meer informatie](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Overweeg je gif te uploaden naar een service waarmee het mogelijk is de gif in te sluiten als html5-video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Geef `@font-display` op als je aangepaste lettertypen definieert in je thema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Overweeg [WebP-afbeeldingsindelingen met een stijl voor het converteren van afbeeldingen](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) in te stellen op je site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installeer [een <PERSON>upal-module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) voor lazy loading van afbeeldingen. Met dergelijke modules kunnen afbeeldingen die niet in beeld zijn, worden uitgesteld om de prestaties te verbeteren."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Overweeg een module te gebruiken om kritieke css en JavaScript inline te plaatsen of items potentieel asynchroon te laden via JavaScript, zoals de module [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Bedenk wel dat de optimalisatie die deze module biedt, de werking van je site kan verstoren. Het is daarom goed mogelijk dat je de code moet wijzigen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Thema's, modules en serverspecificaties zijn allemaal van invloed op de reactietijd van de server. Overweeg een thema te gebruiken dat beter is geoptimaliseerd, kies met zorg een optimalisatiemodule en/of upgrade je server. Je hostingservers moeten gebruikmaken van PHP-opcode-caching, geheugencaching (zoals Redis of Memcached) om de databasequerytijden te beperken en geoptimaliseerde app-logica om pagina's sneller voor te bereiden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Overweeg [responsieve afbeeldingsstijlen](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) te gebruiken om de grootte van geladen afbeeldingen op je pagina te verkleinen. Als je Views gebruikt om meerdere contentitems op een pagina te tonen, kun je overwegen paginering te gebruiken om het aantal zichtbare contentitems op een pagina te beperken."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Zorg ervoor dat je Aggregate CSS files (Css-bestanden aggregeren) hebt aangezet op de pagina Administration » Configuration » Development (Beheer > Configuratie > Ontwikkeling). Je kunt ook meer geavanceerde aggregatie-opties instellen via [extra modules](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) om je site te versnellen door je css-stijlen in te korten, te verkleinen en te comprimeren."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Zorg ervoor dat je Aggregate JavaScript files (JavaScript-bestanden aggregeren) hebt aangezet op de pagina Administration » Configuration » Development (Beheer > Configuratie > Ontwikkeling). Je kunt ook meer geavanceerde aggregatie-opties instellen via [extra modules](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) om je site te versnellen door je JavaScript-items in te korten, te verkleinen en te comprimeren."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Overweeg ongebruikte css-regels te verwijderen en alleen de benodigde Drupal-bibliotheken bij te voegen bij de relevante pagina of component op een pagina. Klik op de [link naar de Drupal-documentatie](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) voor meer informatie. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je bijgevoegde bibliotheken wilt identificeren die extra css toevoegen. Je kunt identificeren welk thema/welke module verantwoordelijk is voor de URL van de stylesheet als css-aggregatie is uitgezet op je Drupal-site. Ga in de lijst op zoek naar thema's/modules met veel stylesheets en veel rood in de codedekking. Een thema/module zou een stylesheet alleen in de wachtrij moeten plaatsen als de stylesheet daadwerkelijk wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Overweeg ongebruikte JavaScript-items te verwijderen en alleen de benodigde Drupal-bibliotheken bij te voegen bij de relevante pagina of component op een pagina. Klik op de [link naar de Drupal-documentatie](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) voor meer informatie. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je bijgevoegde bibliotheken wilt identificeren die extra JavaScript toevoegen. Je kunt identificeren welk thema/welke module verantwoordelijk is voor de URL van het script als JavaScript-aggregatie is uitgezet op je Drupal-site. Ga in de lijst op zoek naar thema's/modules met veel scripts en veel rood in de codedekking. Een thema/module zou een script alleen in de wachtrij moeten plaatsen als het script da<PERSON><PERSON><PERSON><PERSON><PERSON> wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Stel 'Browser and proxy cache maximum age' (Maximum leeftijd van browser- en proxycache) in op de pagina Administration » Configuration » Development (Beheer > Configuratie > Ontwikkeling). Bekijk meer informatie over [Drupal-cache en optimalisatie voor prestaties](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Overweeg [een module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) te gebruiken waarmee de grootte van afbeeldingen die via de site worden geüpload automatisch wordt geoptimaliseerd en verkleind terwijl de kwaliteit behouden blijft. Zorg er ook voor dat je de eigen [stijlen voor responsieve afbeeldingen](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) die bij Drupal worden geleverd (beschikbaar in Drupal 8 en hoger) gebruikt voor alle afbeeldingen die op de site worden gerenderd."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Preconnect- of dns-prefetch-resourcehints kunnen worden toegevoegd door [een module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) te installeren en te configureren die voorzieningen biedt voor user-agent-resourcehints."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Zorg ervoor dat je de eigen [stijlen voor responsieve afbeeldingen](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) gebruikt die bij Drupal worden geleverd (be<PERSON><PERSON><PERSON><PERSON> in Drupal 8 en hoger). Gebruik de stijlen voor responsieve afbeeldingen voor het renderen van afbeeldingsvelden via weergavemodi, weergaven of afbeeldingen die worden geüpload via de WYSIWYG-editor."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Gebruik [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) en zet `Optimize Fonts` aan om de css-functie `font-display` automatisch te gebruiken om te zorgen dat tekst zichtbaar is voor gebruikers terwijl weblettertypen worden geladen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Next-Gen Formats` aan om afbeeldingen te converteren naar WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Gebruik [E<PERSON> Leap](https://pubdash.ezoic.com/speed) en zet `<PERSON>zy Load Images` aan om het laden van afbeeldingen buiten het scherm uit te stellen totdat ze nodig zijn."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Gebruik [E<PERSON> Leap](https://pubdash.ezoic.com/speed) en zet `Critical CSS` en `Script Delay` aan om niet-kritieke JavaScript/css uit te stellen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Gebruik [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) om je content te cachen in onze wereldwijde netwerk om zo de tijd tot de eerste byte te verbeteren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Minify CSS` aan om je css automatisch te verkleinen om de omvang van netwerkpayloads te verkleinen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Gebruik [E<PERSON> Leap](https://pubdash.ezoic.com/speed) en zet `Minify Javascript` aan om je JavaScript automatisch te verkleinen om de omvang van netwerkpayloads te verkleinen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Remove Unused CSS` aan om te helpen met dit probleem. Hiermee worden de css-klassen gevonden die daadwerkelijk worden gebruikt op elke pagina van je site en worden andere verwijderd om de bestandsgrootte zo klein mogelijk te houden."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Efficient Static Cache Policy` aan om aanbevolen waarden in te stellen in de cachingheader voor statische items."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Next-Gen Formats` aan om afbeeldingen te converteren naar WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Pre-Connect Origins` aan om automatisch `preconnect`-resourcehints toe te voegen om vroege verbindingen met belangrijke externe herkomsten mogelijk te maken."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Preload Fonts` en `Preload Background Images` aan om `preload`-links toe te voegen om het ophalen van resources die momenteel zijn a<PERSON>ev<PERSON>, later tijdens het laden van de pagina te prioriteren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Gebruik [Ezoic Leap](https://pubdash.ezoic.com/speed) en zet `Resize Images` aan om het formaat van afbeeldingen aan te passen tot een geschikt formaat voor apparaten om zo de omvang van netwerkpayloads te verkleinen."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Overweeg je gif te uploaden naar een service waarmee het mogelijk is de gif in te sluiten als html5-video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Overweeg een [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) of service te gebruiken die je geüploade afbeeldingen automatisch converteert naar de optimale formaten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installeer een [Jo<PERSON><PERSON>-plug-in voor lazy loading](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) waarmee afbeeldingen die niet in beeld zijn, kunnen worden uitgesteld, of schakel over naar een template met deze functionaliteit. Vanaf Joomla 4.0 krijgen alle nieuwe afbeeldingen [automatisch](https://github.com/joomla/joomla-cms/pull/30748) het kenmerk `loading` vanuit de kern."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Er zijn een aantal Joomla-plug-ins om [kritieke items inline te plaatsen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) of [minder belangrijke bronnen uit te stellen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Bedenk wel dat de optimalisaties die deze plug-ins bieden, bepaalde functies van je templates of plug-ins kunnen verstoren. Je moet ze daarom grondig testen."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Templates, extensies en serverspecificaties zijn allemaal van invloed op de reactietijd van de server. Overweeg een template te gebruiken die beter is geoptimaliseerd, kies met zorg een optimalisatie-extensie en/of upgrade je server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Overweeg fragmenten te tonen in je artikelcategorieën (bijvoorbeeld via de link 'meer lezen'), zodat er per pagina minder artikelen worden getoond. Ook kun je lange posts verdelen over meerdere pagina's of een plug-in voor lazy loading van reacties gebruiken."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Er zijn een aantal [Joomla-extensies](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) die je site sneller kunnen maken door je css-stijlen in te korten, te verkleinen of te comprimeren. Er zijn ook templates die deze functionaliteit bieden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Er zijn een aantal [Joomla-extensies](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) die je site sneller kunnen maken door je scripts in te korten, te verkleinen of te comprimeren. Er zijn ook templates die deze functionaliteit bieden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Overweeg gebruik te maken van minder of andere [Joomla-extensies](https://extensions.joomla.org/) die ongebruikte css laden op je pagina. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je extensies wilt identificeren die extra css toevoegen. Via de URL van de stylesheet kun je identificeren welk thema/welke plug-in verantwoordelijk is. Ga in de lijst op zoek naar plug-ins met veel stylesheets en veel rood in de codedekking. Een plug-in zou een stylesheet alleen in de wachtrij moeten plaatsen als de stylesheet daadwerkelijk wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Overweeg gebruik te maken van minder of andere [Joomla-extensies](https://extensions.joomla.org/) die ongebruikte JavaScript laden op je pagina. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je plug-ins wilt identificeren die extra JavaScript toevoegen. Via de URL van het script kun je identificeren welke extensie verantwoordelijk is. Ga in de lijst op zoek naar extensies met veel scripts en veel rood in de codedekking. Een extensie zou een script alleen in de wachtrij moeten plaatsen als het script daadwerkelijk wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Bekijk meer informatie over [browsercaching in Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Overweeg een [beeldoptimalisatieplug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) te gebruiken om je afbeeldingen te comprimeren terwijl de beeldkwaliteit behouden blijft."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Overweeg een [plug-in voor responsieve afbeeldingen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) te gebruiken als je responsieve afbeeldingen wilt gebruiken in je content."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Je kunt tekstcompressie aanzetten door Gzip-paginacompressie in Joomla aan te zetten via System > Global configuration > Server (Systeem > Algemene configuratie > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Als je je JavaScript-items niet bundelt, kun je [baler](https://github.com/magento/baler) gebruiken."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Zet de ingebouwde [JavaScript-bundeling en -verkleining](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) van Magento uit en overweeg in plaats daarvan [baler](https://github.com/magento/baler/) te gebruiken."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Specificeer `@font-display` als je [aangepaste lettertypen definieert](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Overweeg te zoeken in de [Magento-marktplaats](https://marketplace.magento.com/catalogsearch/result/?q=webp) voor verschillende extensies van derden om nieuwere afbeeldingsindelingen te gebruiken."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Overweeg om je product- en catalogustemplates te wijzigen voor gebruik van de functie van het webplatform voor [lazy loading](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> de [Varnish-integratie](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) van Ma<PERSON>o."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Zet de optie 'Css-bestanden verkleinen' in de ontwikkelaarsinstellingen van je winkel aan. [Meer informatie](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Geb<PERSON>ik [Terse<PERSON>](https://www.npmjs.com/package/terser) om alle JavaScript-items van implementaties van statische content te verkleinen en de ingebouwde verkleiningsfunctie uit te zetten."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Zet de ingebouwde [JavaScript-bundeling](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) van Magento uit."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Je kunt in de [Magento-marktplaats](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) zoeken naar verschillende extensies van derden om afbeeldingen te optimaliseren."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Preconnect- of dns-prefetch-resourcehints kunnen worden toegevoegd door [de lay-out van een thema te wijzigen](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>`-tags kunnen worden toegevoegd door [de lay-out van een thema te wijzigen](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Gebruik de component `next/image` in plaats van `<img>` om de afbeeldingsindeling automatisch te optimaliseren. [Meer informatie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Gebruik de component `next/image` in plaats van `<img>` om afbeeldingen automatisch te laden via lazy loading. [Meer informatie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Gebruik de component `next/image` en stel 'priority' in op True om de LCP-afbeelding vooraf te laden. [Meer informatie](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Gebruik de component `next/script` om het laden van niet-kritieke scripts van derden uit te stellen. [Meer informatie](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Gebruik de component `next/image` om te zorgen dat afbeeldingen altijd het juiste formaat hebben. [Meer informatie](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Je kunt eventueel `PurgeCSS` instellen in de `Next.js`-instellingen om niet-gebruikte regels uit stylesheets te verwijderen. [Meer informatie](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Gebruik `Webpack Bundle Analyzer` om ongebruikte JavaScript-code waar te nemen. [Meer informatie](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Je kunt eventueel `Next.js Analytics` gebruiken om de daadwerkelijke prestaties van je app te meten. [Meer informatie](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Opslaan in het cachegeheugen instellen voor onveranderbare items en `Server-side Rendered`-pagina's (SSR). [Meer informatie](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Gebruik de component `next/image` in plaats van `<img>` om de afbeeldingskwaliteit aan te passen. [Meer informatie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Gebruik de component `next/image` om de juiste `sizes` in te stellen. [Meer informatie](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Zet de compressie op je Next.js-server aan. [Meer informatie](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Gebruik de component `nuxt/image` en stel `format=\"webp\"` in. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Gebruik de component `nuxt/image` en stel `loading=\"lazy\"` in voor afbeeldingen buiten het scherm. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Gebruik de component `nuxt/image` en geef `preload` voor de LCP-afbeelding op. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Gebruik de component `nuxt/image` en geef expliciet `width` en `height` op. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Gebruik de component `nuxt/image` en stel de juiste `quality` in. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Gebruik de component `nuxt/image` en stel de juiste `sizes` in. [Meer informatie](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Vervang gean<PERSON>erde gif's door video](https://web.dev/replace-gifs-with-videos/) zodat de webpagina sneller laadt en overweeg moderne bestandsindelingen zoals [webm](https://web.dev/replace-gifs-with-videos/#create-webm-videos) of [av1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) te gebruiken voor een tot 30% efficiëntere compressie vergeleken met de huidige geavanceerde videocodec, vp9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Overweeg een [plug-in](https://octobercms.com/plugins?search=image) of service te gebruiken die de geüploade afbeeldingen automatisch converteert naar de optimale indelingen. [Webp-afbeeldingen zonder verlies](https://developers.google.com/speed/webp) zijn 26% kleiner vergele<PERSON> met png-afbeeldingen en zijn 25-34% kleiner dan vergelijkbare jpeg-afbeeldingen met dezelfde SSIM-kwaliteitsindex. Je kunt ook de moderne bestandsindeling [avif](https://jakearchibald.com/2020/avif-has-landed/) overwegen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Overweeg een [plug-in voor lazy loading van afbeeldingen](https://octobercms.com/plugins?search=lazy) te installeren waarmee je afbeeldingen die niet in beeld zijn, kunt uitstellen of kunt overschakelen naar een thema met deze functionaliteit. Je kunt ook overwegen [de AMP-plug-in](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) te gebruiken."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Er zijn veel plug-ins waarmee [essentiële inline items](https://octobercms.com/plugins?search=css) beter werken. Deze plug-ins kunnen andere plug-ins verstoren. Je moet ze daarom grondig testen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Thema's, plug-ins en serverspecificaties zijn allemaal van invloed op de reactietijd van de server. Overweeg een thema te gebruiken dat beter is geoptimaliseerd, kies met zorg een optimalisatieplug-in en/of upgrade de server. Met de CMS van oktober kunnen ontwikkelaars ook [`Queues`](https://octobercms.com/docs/services/queues) gebruiken om de verwerking van tijdrovende taken uit te stellen, zoals het sturen van een e-mail. Dit kan webverzoeken drastisch versnellen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Overweeg fragmenten te tonen in de postlijsten (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met een knop `show more`), zodat er per webpagina minder posts worden getoond. Ook kun je lange posts verdelen over meerdere webpagina's of een plug-in voor lazy loading van reacties gebruiken."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON>r zijn veel [plug-ins](https://octobercms.com/plugins?search=css) die een website sneller kunnen maken door de stijlen in te korten, te verkleinen of te comprimeren. Het ontwikkelen kan sneller gaan als je een ontwerpproces gebruikt dat deze verkleining van tevoren toepast."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON>r zijn veel [plug-ins](https://octobercms.com/plugins?search=javascript) die een website sneller kunnen maken door de scripts in te korten, te verkleinen of te comprimeren. Het ontwikkelen kan sneller gaan als je een ontwerpproces gebruikt dat deze verkleining van tevoren toepast."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Overweeg de [plug-ins](https://octobercms.com/plugins) te checken die ongebruikte css laden op de website. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je plug-ins wilt identificeren die onnodige css toevoegen. Identificeer via de URL van de stylesheet welk thema/welke plug-in verantwoordelijk is. Zoek naar plug-ins met veel stylesheets en veel rood in de codedekking. Een plug-in zou een stylesheet alleen moeten toevoegen als het daadwerkelijk wordt gebruikt op de webpagina."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Overweeg de [plug-ins](https://octobercms.com/plugins?search=javascript) te checken die ongebruikte JavaScript laden op de webpagina. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je plug-ins wilt identificeren die onnodige JavaScript toevoegen. Identificeer via de URL van het script welk thema/welke plug-in verantwoordelijk is. Zoek naar plug-ins met veel scripts en veel rood in de codedekking. Een plug-in zou een script alleen moeten toevoegen als het daadwerkelijk wordt gebruikt op de webpagina."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Meer informatie over [hoe je onnodige netwerkverzoeken voorkomt met de HTTP-cache](https://web.dev/http-cache/#caching-checklist) Er zijn veel [plug-ins](https://octobercms.com/plugins?search=Caching) waarmee je caching kunt versnellen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Overweeg een [beeldoptimalisatieplug-in](https://octobercms.com/plugins?search=image) te gebruiken om de afbeeldingen te comprimeren terwijl de beeldkwaliteit behouden blijft."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Upload afbeeldingen rechtstreeks in de mediabeheerder om er zeker van te zijn dat de vereiste afbeeldingsformaten beschikbaar zijn. Overweeg het [filter](https://octobercms.com/docs/markup/filter-resize) of een [plug-in](https://octobercms.com/plugins?search=image) voor het aanpassen van het formaat te gebruiken zodat je de optimale afbeeldingsformaten gebruikt."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "<PERSON><PERSON><PERSON> kun je tekstcompressie aanzetten in de webserverinstellingen."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Overweeg een vensterbibliotheek zoals `react-window` te gebruiken om het aantal gemaakte DOM-nodes te minimaliseren als je veel herhaalde elementen op de pagina weergeeft. [Meer informatie](https://web.dev/virtualize-long-lists-react-window/) Minimaliseer daarnaast onnodige nieuwe weergaven met [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) of [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) en [sla effecten alleen over](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) totdat bepaalde afhankelijkheden zijn gewijzigd als je de `Effect`-hook gebruikt om de runtime-prestaties te verbeteren."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Als je React Router gebruik<PERSON>, moet je het gebruik van <PERSON><Redirect>`-component voor [routenavigatie](https://reacttraining.com/react-router/web/api/Redirect) minimaliseren."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Als je React-componenten aan de serverzi<PERSON>de rendert, kun je `renderToPipeableStream()` of `renderToStaticNodeStream()` gebruiken om de client toestemming te geven verschillende onderdelen van de opmaak te ontvangen en verwerken, in plaats van alles tegelijk. [Meer informatie](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Als je buildsysteem css-bestanden automatisch verkleint, moet je ervoor zorgen dat je de productiebuild van je app implementeert. Je kunt dit checken via de React Developer Tools-extensie. [Meer informatie](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Als je buildsysteem JS-bestanden automatisch verkleint, moet je ervoor zorgen dat je de productiebuild van je app implementeert. Je kunt dit checken via de React Developer Tools-extensie. [Meer informatie](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Als er niet aan de serverzijde wordt gerenderd, kun je [je JavaScript-bundels splitsen](https://web.dev/code-splitting-suspense/) met `React.lazy()`. In andere gevallen kun je de code splitsen via een externe bibliotheek, zoals [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Gebruik de React DevTools Profiler, die gebruikmaakt van de Profiler API, om de weergaveprestaties van je componenten te meten. [Meer informatie.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Overweeg je gif te uploaden naar een service waarmee het mogelijk is de gif in te sluiten als html5-video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Overweeg de plug-in [Prestatielab](https://wordpress.org/plugins/performance-lab/) te gebruiken om je geüploade jpeg-afbeeldingen automatisch te converteren naar WebP als dit wordt ondersteund."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installeer een [WordPress-plug-in voor lazy loading](https://wordpress.org/plugins/search/lazy+load/) waarmee afbeeldingen die niet in beeld zijn, kunnen worden uitgesteld, of schakel over naar een thema met deze functionaliteit. Je kunt ook overwegen [de AMP-plug-in](https://wordpress.org/plugins/amp/) te gebruiken."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Er zijn een aantal WordPress-plug-ins om [kritieke items inline te plaatsen](https://wordpress.org/plugins/search/critical+css/) of [minder belangrijke bronnen uit te stellen](https://wordpress.org/plugins/search/defer+css+javascript/). Bedenk wel dat de optimalisatie die deze plug-ins bieden, bepaalde functies van je thema of plug-ins kunnen verstoren. Het is daarom goed mogelijk dat je de code moet wijzigen."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Thema's, plug-ins en serverspecificaties zijn allemaal van invloed op de reactietijd van de server. Overweeg een thema te gebruiken dat beter is geoptimaliseerd, kies met zorg een optimalisatieplug-in en/of upgrade je server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Overweeg fragmenten te tonen in je postlijsten (bijvoorbeeld via de tag 'more'), zodat er per pagina minder posts worden getoond. Ook kun je lange posts verdelen over meerdere pagina's of een plug-in voor lazy loading van reacties gebruiken."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON><PERSON> <PERSON>i<PERSON> en<PERSON> [WordPress-plug-ins](https://wordpress.org/plugins/search/minify+css/) die je site sneller kunnen maken door stijlen in te korten, te verkleinen of te comprimeren. Mogelijk wil je ook een ontwerpproces gebruiken om deze verkleining van tevoren toe te passen."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON><PERSON> <PERSON>i<PERSON> en<PERSON> [WordPress-plug-ins](https://wordpress.org/plugins/search/minify+javascript/) die je site sneller kunnen maken door je scripts in te korten, te verkleinen of te comprimeren. Indien mogelijk is het bovendien handig zulke verkleining al mee te nemen in het ontwerpproces."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Overweeg gebruik te maken van minder of andere [WordPress-plug-ins](https://wordpress.org/plugins/) die ongebruikte css laden op je pagina. Voer [codedekking](https://developer.chrome.com/docs/devtools/coverage/) uit in Chrome DevTools als je plug-ins wilt identificeren die extra css toevoegen. Via de URL van de stylesheet kun je identificeren welk thema/welke plug-in verantwoordelijk is. Ga in de lijst op zoek naar plug-ins met veel stylesheets en veel rood in de codedekking. Een plug-in zou een stylesheet alleen in de wachtrij moeten plaatsen als deze daadwerkelijk wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Overweeg gebruik te maken van minder of andere [WordPress-plug-ins](https://wordpress.org/plugins/) die ongebruikt JavaScript laden op je pagina. Voer [codedekking](https://developer.chrome.com/docs/devtools/coverage/) uit in Chrome DevTools als je plug-ins wilt identificeren die extra JS toevoegen. Via de URL van het script kun je identificeren welk thema/welke plug-in verantwoordelijk is. Ga in de lijst op zoek naar plug-ins met veel scripts en veel rood in de codedekking. Een plug-in zou een script alleen in de wachtrij moeten plaatsen als het daadwerkelijk wordt gebruikt op de pagina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> meer over [browsercaching in WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Overweeg een [beeldoptimalisatieplug-in voor WordPress](https://wordpress.org/plugins/search/optimize+images/) te gebruiken om je afbeeldingen te comprimeren terwijl de beeldkwaliteit behouden blijft."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Upload afbeeldingen rechtstreeks via de [mediabibliotheek](https://wordpress.org/support/article/media-library-screen/) om er zeker van te zijn dat de vereiste afbeeldingsformaten beschikbaar zijn. Voeg de afbeeldingen vervolgens in vanuit de mediabibliotheek. Ook kun je de afbeeldingswidget gebruiken om ervoor te zorgen dat de optimale afbeeldingsformaten worden gebruikt (inclusief voor responsieve breakpoints). Vermijd `Full Size`-afbeeldingen, tenzij de afmetingen geschikt zijn voor waar ze worden geplaatst. [Meer informatie](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Je kunt tekstcompressie in de webserverinstellingen aanzetten."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Zet Imagify aan op het tabblad voor afbeeldingsoptimalisatie in WP Rocket om je afbeeldingen te converteren naar WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Zet [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) aan in WP Rocket om deze aanbeveling te verhelpen. Deze functie stelt het laden van de afbeeldingen uit totdat de bezoeker omlaag scrollt op de pagina om de afbeeldingen te bekijken."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Zet [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) en [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) aan in WP Rocket om deze aanbeveling te verwerken. Deze functies optimaliseren respectievelijk de css- en JavaScript-bestanden, zodat ze de weergave van je pagina niet blokkeren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Zet [Css-bestanden verkleinen](https://docs.wp-rocket.me/article/1350-css-minify-combine) aan in WP Rocket om dit probleem te verhelpen. Ruimtes en reacties in de css-bestanden van je site worden verwijderd, zodat de bestanden kleiner worden en sneller kunnen worden gedownload."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Zet [JavaScript-bestanden verkleinen](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) aan in WP Rocket om dit probleem te verhelpen. Lege ruimtes en reacties worden verwijderd uit JavaScript-bestanden, zodat de bestanden kleiner worden en sneller kunnen worden gedownload."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Zet [Niet-gebruikte css verwijderen](https://docs.wp-rocket.me/article/1529-remove-unused-css) aan in WP Rocket om dit probleem te verhelpen. Hiermee wordt de paginagrootte verkleind door alle css- en stylesheets te verwijderen die niet worden gebruikt, en alleen de gebruikte css voor elke pagina te behouden."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Zet [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) aan in WP Rocket om dit probleem te verhelpen. Pagina's worden hierdoor beter geladen, omdat de uitvoering van scripts wordt uitgesteld tot interactie met de gebruiker plaatsvindt. Als je site iframes heeft, kun je [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) van WP Rocket gebruiken en [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Zet Imagify aan op het tabblad voor afbeeldingsoptimalisatie in WP Rocket en voer Bulkoptimalisatie uit om je afbeeldingen te comprimeren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Gebruik [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) in WP Rocket om 'dns-prefetch' toe te voegen en de verbinding met externe domeinen te versnellen. Met WP Rocket wordt ook automatisch 'preconnect' toegevoegd aan het [Google Fonts-domein](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) en worden eventuele CNAME-records toegevoegd via de functie [CDN aanzetten](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "<PERSON>et [<PERSON><PERSON>-gebruikte css verwijderen](https://docs.wp-rocket.me/article/1529-remove-unused-css) aan in WP Rocket om dit probleem voor lettertypen te verhelpen. De kritieke lettertypen van je site worden met prioriteit vooraf geladen."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Rekenmachine bekijken"}, "report/renderer/report-utils.js | collapseView": {"message": "Weergave samen<PERSON>uwen"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Beginnavigatie"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximum latentie voor kritiek pad:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON> kopiëren"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Donker thema aan-/uitzetten"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Afdrukvenster uitvouwen"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Afdrukoverzicht"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON><PERSON> als gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Opslaan als HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "<PERSON><PERSON><PERSON> als json"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON>en in viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "Fout"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Rapportfout: geen controlegegevens"}, "report/renderer/report-utils.js | expandView": {"message": "Weergave uitvouwen"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON>en probleem melden"}, "report/renderer/report-utils.js | hide": {"message": "Verbergen"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Labgegevens"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Analyse met [Lighthouse](https://developers.google.com/web/tools/lighthouse/) van de huidige pagina via een geëmuleerd mobiel netwerk. Waarden worden geschat en kunnen variëren."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Aanvullende items om handmatig te controleren"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "N.v.t."}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Aanbeveling"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> besparing"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Geslaagde controles"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Eerste keer laden van de pagina"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Aangepaste throttling"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Desktopemulatie"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON> emula<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-versie"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Niet-beperkte CPU/geheugencapaciteit"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU-throttling"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Apparaat"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Netwerkbeperking"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Schermemulatie"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User-agent (netwerk)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "<PERSON><PERSON> pagina"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Deze gegevens zijn afkomstig van het laden van één pagina, in tegenstelling tot veldgegevens die veel sessies samenvatten."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Langzame 4G-throttling"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Onbekend"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Audits laten zien die relevant zijn voor:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Fragment samenvouwen"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Fragment uitvouwen"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON><PERSON> van <PERSON> tonen"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "<PERSON><PERSON><PERSON> door omgeving"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "<PERSON>r zijn problemen opgetreden bij deze uitvoering van Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Waarden worden geschat en kunnen variëren. De [prestatiescore wordt rechtstreeks berekend](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) op basis van deze statistieken."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Oorspronkelijke tracering bekijken"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Tracering bekijken"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Treemap bekijken"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Geslaagd voor controles maar met waarschuwingen"}, "report/renderer/report-utils.js | warningHeader": {"message": "Waarschuwingen: "}, "treemap/app/src/util.js | allLabel": {"message": "Alle"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Alle scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Dekking"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Dubbele modules"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Resourcebytes"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON><PERSON> schakelen"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Ongebruikte bytes"}}