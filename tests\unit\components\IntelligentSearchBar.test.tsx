import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IntelligentSearchBar } from '@/components/search/IntelligentSearchBar'
import { searchService } from '@/lib/services/searchService'

// Mock del servicio de búsqueda
jest.mock('@/lib/services/searchService', () => ({
  searchService: {
    search: jest.fn(),
    getPopularSearches: jest.fn()
  }
}))

// Mock del hook useDebounce
jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: (value: any) => value // Sin delay para tests
}))

const mockSearchService = searchService as jest.Mocked<typeof searchService>

const mockResults = [
  {
    id: '1',
    name: 'Licencia de construcción',
    type: 'TRAMITE' as const,
    description: 'Autorización para construcción de obras civiles',
    dependency: 'Secretaría de Planeación',
    cost: '$419.000',
    responseTime: '45 días hábiles',
    popularity: 95,
    matchScore: 0.9
  },
  {
    id: '2',
    name: 'Certificado de residencia',
    type: 'TRAMITE' as const,
    description: 'Certificado que acredita la residencia en el municipio',
    dependency: 'Secretaría General',
    cost: 'Gratuito',
    responseTime: '1 día hábil',
    popularity: 88,
    matchScore: 0.8
  }
]

const mockPopularSearches = [
  'licencia construcción',
  'certificado residencia',
  'impuesto predial'
]

describe('IntelligentSearchBar', () => {
  const mockOnSearch = jest.fn()
  const mockOnResultSelect = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockSearchService.search.mockResolvedValue(mockResults)
    mockSearchService.getPopularSearches.mockResolvedValue(mockPopularSearches)
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => JSON.stringify(['búsqueda reciente'])),
        setItem: jest.fn(),
      },
      writable: true,
    })
  })

  it('debería renderizar correctamente', () => {
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    expect(screen.getByRole('combobox')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/buscar trámites/i)).toBeInTheDocument()
  })

  it('debería mostrar placeholder personalizado', () => {
    const customPlaceholder = 'Buscar servicios municipales...'
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
        placeholder={customPlaceholder}
      />
    )

    expect(screen.getByPlaceholderText(customPlaceholder)).toBeInTheDocument()
  })

  it('debería realizar búsqueda al escribir', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(mockSearchService.search).toHaveBeenCalledWith(
        'licencia',
        {},
        {
          limit: 8,
          includeHighlight: true,
          sortBy: 'relevance'
        }
      )
    })

    expect(mockOnSearch).toHaveBeenCalledWith('licencia', mockResults)
  })

  it('debería mostrar resultados de búsqueda', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(screen.getByText('Licencia de construcción')).toBeInTheDocument()
      expect(screen.getByText('Certificado de residencia')).toBeInTheDocument()
    })

    // Verificar que se muestran los detalles
    expect(screen.getByText('$419.000')).toBeInTheDocument()
    expect(screen.getByText('45 días hábiles')).toBeInTheDocument()
    expect(screen.getByText('Secretaría de Planeación')).toBeInTheDocument()
  })

  it('debería manejar selección de resultado', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(screen.getByText('Licencia de construcción')).toBeInTheDocument()
    })

    await user.click(screen.getByText('Licencia de construcción'))

    expect(mockOnResultSelect).toHaveBeenCalledWith(mockResults[0])
  })

  it('debería mostrar búsquedas populares al hacer focus', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.click(input)

    await waitFor(() => {
      expect(mockSearchService.getPopularSearches).toHaveBeenCalled()
    })
  })

  it('debería limpiar búsqueda al hacer clic en el botón X', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    // Esperar a que aparezca el botón de limpiar
    await waitFor(() => {
      expect(screen.getByLabelText('Limpiar búsqueda')).toBeInTheDocument()
    })

    await user.click(screen.getByLabelText('Limpiar búsqueda'))

    expect(input).toHaveValue('')
  })

  it('debería manejar navegación con teclado', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(screen.getByText('Licencia de construcción')).toBeInTheDocument()
    })

    // Navegar hacia abajo
    await user.keyboard('{ArrowDown}')
    
    // Presionar Enter para seleccionar
    await user.keyboard('{Enter}')

    expect(mockOnResultSelect).toHaveBeenCalledWith(mockResults[0])
  })

  it('debería cerrar dropdown con Escape', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(screen.getByText('Licencia de construcción')).toBeInTheDocument()
    })

    await user.keyboard('{Escape}')

    await waitFor(() => {
      expect(screen.queryByText('Licencia de construcción')).not.toBeInTheDocument()
    })
  })

  it('debería mostrar estado de carga', async () => {
    // Mock para simular búsqueda lenta
    mockSearchService.search.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockResults), 100))
    )

    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    // Verificar que se muestra el indicador de carga
    expect(screen.getByTestId('loading-spinner') || screen.getByRole('status')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('Licencia de construcción')).toBeInTheDocument()
    }, { timeout: 200 })
  })

  it('debería mostrar mensaje cuando no hay resultados', async () => {
    mockSearchService.search.mockResolvedValue([])
    
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'xyz123')

    await waitFor(() => {
      expect(screen.getByText(/no se encontraron resultados/i)).toBeInTheDocument()
    })
  })

  it('debería respetar maxResults prop', async () => {
    const user = userEvent.setup()
    
    render(
      <IntelligentSearchBar
        onSearch={mockOnSearch}
        onResultSelect={mockOnResultSelect}
        maxResults={1}
      />
    )

    const input = screen.getByRole('combobox')
    await user.type(input, 'licencia')

    await waitFor(() => {
      expect(mockSearchService.search).toHaveBeenCalledWith(
        'licencia',
        {},
        {
          limit: 1,
          includeHighlight: true,
          sortBy: 'relevance'
        }
      )
    })
  })
})
