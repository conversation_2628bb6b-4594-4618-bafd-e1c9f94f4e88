'use client'

import React, { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Building2,
  FileText,
  BarChart3,
  Network,
  Phone,
  Mail,
  MapPin,
  Clock,
  DollarSign,
  ExternalLink,
  ChevronRight,
  Users,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ModalLoading, TabContentLoading } from '@/components/ui/loading-states'
import { ErrorState } from '@/components/ui/error-states'
import { DependencyBreadcrumb } from '@/components/ui/breadcrumb'
import DependencyService, { type Dependency, type ProceduresByDependency } from '@/lib/services/dependencyService'

interface DependencyDetailModalProps {
  dependency: Dependency | null
  isOpen: boolean
  onClose: () => void
  onProcedureSelect?: (procedure: any) => void
  showBreadcrumbs?: boolean
  onNavigateBack?: () => void
}

export function DependencyDetailModal({
  dependency,
  isOpen,
  onClose,
  onProcedureSelect,
  showBreadcrumbs = true,
  onNavigateBack
}: DependencyDetailModalProps) {
  const [proceduresData, setProceduresData] = useState<ProceduresByDependency | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  // Cargar datos detallados cuando se abre el modal
  useEffect(() => {
    if (isOpen && dependency) {
      loadProceduresData()
    }
  }, [isOpen, dependency])

  const loadProceduresData = async () => {
    if (!dependency) return

    setIsLoading(true)
    setError(null)

    try {
      const dependencyService = DependencyService.getInstance()
      await dependencyService.initialize()
      const data = await dependencyService.getProceduresByDependency(dependency.id)
      setProceduresData(data)
    } catch (err) {
      console.error('Error cargando datos de procedimientos:', err)
      setError(err instanceof Error ? err.message : 'Error al cargar los datos')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRetry = () => {
    loadProceduresData()
  }

  const handleProcedureClick = (procedure: any) => {
    if (onProcedureSelect) {
      onProcedureSelect(procedure)
    }
  }

  if (!dependency) return null

  // Obtener icono de la dependencia
  const getIcon = () => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      crown: Building2,
      shield: Building2,
      banknote: Building2,
      map: Building2,
      users: Users,
      'graduation-cap': Building2,
      'heart-pulse': Building2,
      car: Building2,
      building: Building2,
      folder: Building2
    }
    
    const IconComponent = iconMap[dependency.icon || 'building'] || Building2
    return <IconComponent className="h-6 w-6" />
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          {/* Breadcrumbs */}
          {showBreadcrumbs && (
            <div className="mb-4">
              <DependencyBreadcrumb
                dependency={{ name: dependency.name, id: dependency.id }}
                onNavigate={onNavigateBack}
              />
            </div>
          )}

          <div className="flex items-center space-x-3">
            <div className={cn(
              "w-12 h-12 rounded-lg flex items-center justify-center",
              dependency.color === 'blue' ? 'bg-chia-blue-100 text-chia-blue-600' :
              dependency.color === 'green' ? 'bg-chia-green-100 text-chia-green-600' :
              dependency.color === 'purple' ? 'bg-purple-100 text-purple-600' :
              dependency.color === 'red' ? 'bg-red-100 text-red-600' :
              dependency.color === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
              'bg-gray-100 text-gray-600'
            )}>
              {getIcon()}
            </div>
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                {dependency.name}
              </DialogTitle>
              <DialogDescription className="text-base">
                {dependency.sigla && (
                  <Badge variant="outline" className="mr-2">
                    {dependency.sigla}
                  </Badge>
                )}
                {dependency.description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="procedures">Procedimientos</TabsTrigger>
            <TabsTrigger value="contact">Contacto</TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[60vh] mt-4">
            <TabsContent value="overview" className="space-y-4">
              {/* Estadísticas generales */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <FileText className="h-8 w-8 text-chia-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-chia-green-700">
                      {dependency.tramitesCount}
                    </div>
                    <div className="text-sm text-gray-600">Trámites</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-700">
                      {dependency.opasCount}
                    </div>
                    <div className="text-sm text-gray-600">OPAs</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <Network className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-purple-700">
                      {dependency.subdependenciasCount}
                    </div>
                    <div className="text-sm text-gray-600">Subdependencias</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <Building2 className="h-8 w-8 text-chia-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-chia-blue-700">
                      {dependency.totalProcedures}
                    </div>
                    <div className="text-sm text-gray-600">Total</div>
                  </CardContent>
                </Card>
              </div>

              {/* Descripción detallada */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building2 className="h-5 w-5" />
                    <span>Acerca de esta dependencia</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">
                    {dependency.description || 
                     `La ${dependency.name} es una dependencia municipal encargada de brindar servicios y trámites especializados a los ciudadanos de Chía. Cuenta con ${dependency.totalProcedures} procedimientos disponibles distribuidos en ${dependency.tramitesCount} trámites y ${dependency.opasCount} otras prestaciones administrativas.`}
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="procedures" className="space-y-4">
              {isLoading ? (
                <TabContentLoading message="Cargando procedimientos..." />
              ) : error ? (
                <ErrorState
                  type="server"
                  title="Error al cargar procedimientos"
                  description={error}
                  onRetry={handleRetry}
                  size="md"
                />
              ) : proceduresData ? (
                <div className="space-y-6">
                  {/* Trámites */}
                  {proceduresData.tramites.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <FileText className="h-5 w-5 text-chia-green-600 mr-2" />
                        Trámites ({proceduresData.tramites.length})
                      </h3>
                      <div className="space-y-2">
                        {proceduresData.tramites.map((tramite, index) => (
                          <Card 
                            key={index}
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => handleProcedureClick(tramite)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900 mb-1">
                                    {tramite.Nombre}
                                  </h4>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                                    {tramite['¿Tiene pago?'] && (
                                      <div className="flex items-center space-x-1">
                                        <DollarSign className="h-4 w-4" />
                                        <span>{tramite['¿Tiene pago?'] === 'No' ? 'Gratuito' : tramite['¿Tiene pago?']}</span>
                                      </div>
                                    )}
                                    {tramite['Tiempo de respuesta'] && (
                                      <div className="flex items-center space-x-1">
                                        <Clock className="h-4 w-4" />
                                        <span>{tramite['Tiempo de respuesta']}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* OPAs */}
                  {proceduresData.opas.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                        Otras Prestaciones Administrativas ({proceduresData.opas.length})
                      </h3>
                      <div className="space-y-2">
                        {proceduresData.opas.map((opa, index) => (
                          <Card 
                            key={index}
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => handleProcedureClick(opa)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900 mb-1">
                                    {opa.OPA}
                                  </h4>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                                    <span>{opa.subdependencia}</span>
                                    <div className="flex items-center space-x-1">
                                      <DollarSign className="h-4 w-4" />
                                      <span>Gratuito</span>
                                    </div>
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No hay procedimientos disponibles
                  </h3>
                  <p className="text-gray-600">
                    Esta dependencia no tiene procedimientos registrados actualmente.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="contact" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Phone className="h-5 w-5" />
                    <span>Información de Contacto</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-chia-blue-600" />
                      <div>
                        <div className="font-medium">Teléfono</div>
                        <div className="text-gray-600">(*************</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-chia-blue-600" />
                      <div>
                        <div className="font-medium">Email</div>
                        <div className="text-gray-600"><EMAIL></div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-chia-blue-600" />
                      <div>
                        <div className="font-medium">Dirección</div>
                        <div className="text-gray-600">Carrera 11 No. 17-25, Chía, Cundinamarca</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Clock className="h-5 w-5 text-chia-blue-600" />
                      <div>
                        <div className="font-medium">Horario de Atención</div>
                        <div className="text-gray-600">Lunes a Viernes: 8:00 AM - 5:00 PM</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <Button className="w-full bg-chia-blue-600 hover:bg-chia-blue-700">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Visitar Página Web Oficial
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
