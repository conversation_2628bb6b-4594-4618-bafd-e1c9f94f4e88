{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/common/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AASH,sDAAyC;AACzC,iDAAyC;AACzC,uDAAiD;AAGjD,yCAAiC;AACjC,yDAAoD;AACpD,2CAAyC;AAGzC,+CAA0C;AAE1C;;GAEG;AACU,QAAA,UAAU,GAAG,IAAA,gBAAK,EAAC,iBAAiB,CAAC,CAAC;AAEnD;;GAEG;AACH,SAAgB,mBAAmB,CACjC,gBAAmD;IAEnD,IAAI,gBAAgB,CAAC,SAAS,EAAE;QAC9B,OAAO,CACL,gBAAgB,CAAC,SAAS,CAAC,WAAW,IAAI,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAC3E,CAAC;KACH;IACD,IAAI,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC;IACpC,IAAI,gBAAgB,CAAC,UAAU,EAAE;QAC/B,KAAK,MAAM,SAAS,IAAI,gBAAgB,CAAC,UAAU,CAAC,UAAU,EAAE;YAC9D,MAAM,QAAQ,GACZ,SAAS,CAAC,GAAG;gBACb,GAAG;gBACH,SAAS,CAAC,UAAU;gBACpB,GAAG;gBACH,SAAS,CAAC,YAAY,CAAC;YACzB,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,IAAI,aAAa,CAAC;YAC7D,OAAO,IAAI,YAAY,YAAY,KAAK,QAAQ,GAAG,CAAC;SACrD;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAtBD,kDAsBC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,YAA2C;IAE3C,IAAA,kBAAM,EAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,6CAA6C,CAAC,CAAC;IAC9E,IAAI,YAAY,CAAC,mBAAmB,EAAE;QACpC,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE;YAClC,OAAO,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;SAClE;QACD,QAAQ,YAAY,CAAC,mBAAmB,EAAE;YACxC,KAAK,IAAI;gBACP,OAAO,CAAC,CAAC,CAAC;YACZ,KAAK,KAAK;gBACR,OAAO,GAAG,CAAC;YACb,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,CAAC,QAAQ,CAAC;YACnB;gBACE,MAAM,IAAI,KAAK,CACb,oCAAoC;oBAClC,YAAY,CAAC,mBAAmB,CACnC,CAAC;SACL;KACF;IACD,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAzBD,sDAyBC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,MAAkB,EAClB,YAA2C;IAE3C,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;QAC1B,OAAO;KACR;IACD,MAAM,MAAM;SACT,IAAI,CAAC,uBAAuB,EAAE,EAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAC,CAAC;SAChE,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,sEAAsE;QACtE,iFAAiF;QACjF,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC;AAdD,sCAcC;AAWD;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,OAA2B,EAC3B,SAA0B,EAC1B,OAAiC;IAEjC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/B,OAAO,EAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAC,CAAC;AACvC,CAAC;AAPD,4CAOC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,SAIE;IAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;KACvE;IACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,CAAC;AAXD,oDAWC;AAED;;GAEG;AACI,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,CAAC;AAC1D,CAAC,CAAC;AAFW,QAAA,QAAQ,YAEnB;AAEF;;GAEG;AACI,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,CAAC;AAC1D,CAAC,CAAC;AAFW,QAAA,QAAQ,YAEnB;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAAC,GAAY,EAA+B,EAAE;IACzE,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,WAAW,MAAK,MAAM,CAAC;AAChE,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAEF;;GAEG;AACI,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,WAAW,MAAK,MAAM,CAAC;AAChE,CAAC,CAAC;AAFW,QAAA,QAAQ,YAEnB;AAEF;;GAEG;AACI,MAAM,MAAM,GAAG,CAAC,GAAY,EAAe,EAAE;IAClD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,WAAW,MAAK,IAAI,CAAC;AAC9D,CAAC,CAAC;AAFW,QAAA,MAAM,UAEjB;AAEF;;GAEG;AACI,KAAK,UAAU,YAAY,CAChC,OAA2B,EAC3B,SAA0B,EAC1B,SAAmD,EACnD,OAAe,EACf,YAA4B;IAE5B,IAAI,YAA4B,CAAC;IACjC,IAAI,eAAoD,CAAC;IACzD,IAAI,cAAsC,CAAC;IAC3C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,eAAe,GAAG,OAAO,CAAC;QAC1B,cAAc,GAAG,MAAM,CAAC;IAC1B,CAAC,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;QAClE,IAAI,CAAC,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7B,OAAO;SACR;QACD,eAAe,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,IAAI,OAAO,EAAE;QACX,YAAY,GAAG,UAAU,CAAC,GAAG,EAAE;YAC7B,cAAc,CACZ,IAAI,wBAAY,CAAC,0CAA0C,CAAC,CAC7D,CAAC;QACJ,CAAC,EAAE,OAAO,CAAC,CAAC;KACb;IACD,SAAS,OAAO;QACd,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAC7D,CAAC,CAAC,EAAE;QACF,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,CAAC;IACX,CAAC,EACD,KAAK,CAAC,EAAE;QACN,OAAO,EAAE,CAAC;QACV,MAAM,KAAK,CAAC;IACd,CAAC,CACF,CAAC;IACF,IAAI,IAAA,0BAAW,EAAC,MAAM,CAAC,EAAE;QACvB,MAAM,MAAM,CAAC;KACd;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AA9CD,oCA8CC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,OAAyB,EACzB,YAA2C;IAE3C,IAAI,YAAY,CAAC,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;QACrD,OAAO,IAAI,mCAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;KAC5E;IACD,OAAO,IAAI,yBAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAChD,CAAC;AARD,wCAQC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,GAAsB,EACtB,GAAG,IAAe;IAElB,IAAI,IAAA,gBAAQ,EAAC,GAAG,CAAC,EAAE;QACjB,IAAA,kBAAM,EAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,yCAAyC,CAAC,CAAC;QACrE,OAAO,GAAG,CAAC;KACZ;IAED,SAAS,iBAAiB,CAAC,GAAY;QACrC,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE;YAC7B,OAAO,WAAW,CAAC;SACpB;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAC9D,CAAC;AAjBD,4CAiBC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,IAAY,EAAE,IAAY;IACvD,2BAA2B;IAC3B,4CAA4C;IAC5C,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAEjC,uDAAuD;IACvD,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;QACxB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAe;;YACvB,iCAAiC;YACjC,4CAA4C;YAC5C,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YACvC,MAAA,aAAa,CAAC,IAAI,oCAAlB,aAAa,CAAC,IAAI,GAAK,IAAI,GAAG,EAAE,EAAC;YACjC,MAAA,aAAa,CAAC,SAAS,oCAAvB,aAAa,CAAC,SAAS,GAAK,IAAI,GAAG,EAAE,EAAC;YAEtC,MAAM,GAAG,GAAG,CAAC,MAAA,aAAa,CAAC,OAAO,mCAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7C,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAElC,OAAO,CACL,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI;gBACJ,IAAI;gBACJ,GAAG;gBACH,IAAI;gBACJ,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC5B,OAAO,KAAK,YAAY,IAAI,CAAC;gBAC/B,CAAC,CAAC;aACH,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC/B,OAAO,CAAC,KAAc;wBACpB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC/B,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC;oBACD,MAAM,CAAC,KAAe;wBACpB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC/B,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AA5CD,wCA4CC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,IAAY,EAAE,IAAY;IAC9D,OAAO,gBAAgB,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACtD,CAAC;AAFD,sDAEC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CACnC,OAAmB,EACnB,QAAgB,EAChB,OAAe;IAEf,IAAI,MAAgC,CAAC;IACrC,MAAM,YAAY,GAAG,IAAI,wBAAY,CACnC,eAAe,QAAQ,oBAAoB,OAAO,aAAa,CAChE,CAAC;IACF,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QACnD,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,OAAO,EAAE;QACX,YAAY,GAAG,UAAU,CAAC,GAAG,EAAE;YAC7B,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC,EAAE,OAAO,CAAC,CAAC;KACb;IACD,IAAI;QACF,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;KACtD;YAAS;QACR,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,YAAY,CAAC,CAAC;SAC5B;KACF;AACH,CAAC;AAzBD,0CAyBC;AAED;;GAEG;AACH,IAAI,EAAE,GAAwC,IAAI,CAAC;AACnD;;GAEG;AACI,KAAK,UAAU,gBAAgB;IAGpC,IAAI,CAAC,EAAE,EAAE;QACP,IAAI;YACF,EAAE,GAAG,wDAAa,aAAa,GAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAC;aACH;YACD,MAAM,KAAK,CAAC;SACb;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAhBD,4CAgBC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CACvC,QAAkB,EAClB,IAAa;IAEb,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,IAAI,EAAE;QACR,MAAM,EAAE,GAAG,MAAM,gBAAgB,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,MAAM,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;KAC1B;SAAM;QACL,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACrB;KACF;IACD,IAAI;QACF,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC/B;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAvBD,kDAuBC;AAED;;GAEG;AACI,KAAK,UAAU,6BAA6B,CACjD,MAAkB,EAClB,MAAc;IAEd,6EAA6E;IAC7E,kBAAkB;IAClB,IAAI,CAAC,uBAAM,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IAED,MAAM,EAAC,QAAQ,EAAC,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAE1C,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,OAAO,IAAI,QAAQ,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,IAAY;YACrB,IAAI,GAAG,EAAE;gBACP,OAAO;aACR;YAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACxE,IAAI,QAAQ,CAAC,GAAG,EAAE;gBAChB,GAAG,GAAG,IAAI,CAAC;gBACX,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAC,MAAM,EAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjB;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AA5BD,sEA4BC;AAED;;GAEG;AACI,KAAK,UAAU,cAAc,CAClC,IAA4B,EAC5B,OAAe;IAEf,oFAAoF;IACpF,iDAAiD;IACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC1B,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrB,QAAQ,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC,EAAE,OAAO,CAAC,CAAC;AACd,CAAC;AAXD,wCAWC"}