{"version": 3, "file": "compiler.js", "sourceRoot": "", "sources": ["../src/compiler.ts"], "names": [], "mappings": "AAAA;;;;EAIE;;;;;;;;;;;;;;AAmCF;IAYE,kBACE,OAA0B,EAC1B,OAAgB,EAChB,UAAsB;QAdhB,YAAO,GAAsB,EAAE,CAAC;QAChC,YAAO,GAAY;YACzB,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,EAAE;SACT,CAAC;QACM,uBAAkB,GAA6B,IAAI,CAAC;QACpD,kBAAa,GAAuC,IAAI,CAAC;QACzD,gBAAW,GAA8C,EAAE,CAAC;QAQlE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,0BAAO,GAAP,UAAQ,GAAyB;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,iCAAc,GAAd,UAAe,GAAyB;QAAxC,iBAoBC;QAnBC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,sBAAsB,CAAC,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;QACO,IAAA,uBAAQ,CAAS;QACzB,IAAM,OAAO,GAAG,QAAQ;aACrB,MAAM,CACL,UAAC,EAAE;YACD,OAAA,EAAE,CAAC,IAAI,KAAK,oBAAoB,IAAI,EAAE,CAAC,IAAI,KAAK,iBAAiB;QAAjE,CAAiE,CACpE;aACA,GAAG,CAAC,UAAA,EAAE;YACL,OAAA,EAAE,CAAC,IAAI,KAAK,oBAAoB;gBAC9B,CAAC,CAAC,KAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7B,CAAC,CAAC,KAAI,CAAC,eAAe,CAAC,EAAE,CAAC;QAF5B,CAE4B,CAC7B,CAAC;QACJ,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,qCAAkB,GAAlB,UAAmB,OAA2B;QAC5C,2EAA2E;QAC3E,yEAAyE;QACzE,oDAAoD;QACpD,IAAI,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC3D,oEAAoE;YACpE,+CAA+C;YAC/C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC/D;YAED,OAAO,IAAI,kBAAkB,CAC3B,IAAI,CAAC,aAAa,CAAC,EAAE,EACpB,IAAI,CAAC,aAAa,CAAC,MAA6B,CAAC,MAAM,EACxD,IAAI,CAAC,kBAAkB,EACvB,OAAO,CAAC,KAAK,CACd,CAAC;SACH;QAED,iDAAiD;QACjD,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,kCAAe,GAAf,UAAgB,OAAwB;QAC9B,IAAA,uBAAM,EAAE,eAAE,CAAa;QACvB,IAAA,4BAAU,CAAU;QAE5B,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC;SAC7B;QAEK,IAAA,SAA2B,EAAzB,oBAAO,EAAE,oBAAgB,CAAC;QAClC,QAAQ,MAAM,CAAC,IAAI,EAAE;YACnB,KAAK,cAAc;gBACjB,OAAO;oBACL,EAAE,IAAA;oBACF,MAAM,EAAE,UAAU,CAAC,eAAe,CAChC,OAAO,EACP,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAC7B,CAAC,MAAM;iBACT,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO;oBACL,EAAE,IAAA;oBACF,MAAM,EAAE,UAAU,CAAC,iBAAiB,CAClC,OAAO,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAC3B,CAAC,MAAM;iBACT,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO;oBACL,EAAE,IAAA;oBACF,MAAM,EAAE,UAAU,CAAC,iBAAiB,CAClC,OAAO,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAC3B,CAAC,MAAM;iBACT,CAAC;YAEJ,KAAK,cAAc;gBACjB,OAAO,IAAI,YAAY,CACrB,EAAE,EACF,MAAM,CAAC,MAAM,EACb,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAC5B,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE;oBACjC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;iBAC9C,CAAC,CACH,CAAC;YAEJ,KAAK,cAAc;gBACjB,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;YAE5D;gBACE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACxE;IACH,CAAC;IAED,iCAAc,GAAd,UAAe,OAAwB;QAAvC,iBAsBC;QArBC,IAAM,MAAM,GAAG,OAAO,CAAC,MAAiD,CAAC;QACjE,IAAA,wBAAO,CAAY;QAE3B,2EAA2E;QAC3E,yEAAyE;QACzE,6CAA6C;QAC7C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACrE,IAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAChC,UAAC,GAAmC,EAAE,MAAM;YAC1C,oEAAoE;YACpE,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CACH,CAAC;QAEF,sEAAsE;QACtE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAE5C,OAAO,WAAW,CAAC;IACrB,CAAC;IACH,eAAC;AAAD,CAAC,AAzJD,IAyJC;;AAED,gFAAgF;AAEhF;IAEE,mBAAY,EAAU;QACpB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAEH,gBAAC;AAAD,CAAC,AAND,IAMC;AAED;IAA2B,gCAAS;IAApC;;IAQA,CAAC;IAPC,6BAAM,GAAN,UAAO,KAAsB;QAC3B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QAED,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3D,CAAC;IACH,mBAAC;AAAD,CAAC,AARD,CAA2B,SAAS,GAQnC;AAED;IAKE,sBACE,EAAU,EACV,MAAc,EACd,OAAkC,EAClC,WAA6B;QAE7B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,gCAAS,GAAT,UAAU,KAAa;QACb,IAAA,sBAAO,CAAU;QAEzB,IAAM,MAAM,GACV,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAExD,OAAO,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC;IACjC,CAAC;IACH,mBAAC;AAAD,CAAC,AA1BD,IA0BC;AAED;IAAwC,sCAAS;IAI/C,4BACE,EAAU,EACV,MAAc,EACd,YAA+B,EAC/B,MAAc;QAJhB,YAME,kBAAM,EAAE,CAAC,SAIV;QAHC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACvB,CAAC;IAED,mCAAM,GAAN,UAAO,KAAa;QAClB,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC,MAAM;aACf,OAAO,CAAC,aAAa,EAAE,IAAI,GAAG,MAAM,CAAC;aACrC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC;IACH,yBAAC;AAAD,CAAC,AAvBD,CAAwC,SAAS,GAuBhD;;AAED;IAGE,sBAAY,EAAU,EAAE,OAAkC;QACxD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,gCAAS,GAAT,UAAU,KAAa;QACb,IAAA,sBAAO,CAAU;QACzB,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;IACzC,CAAC;IACH,mBAAC;AAAD,CAAC,AAZD,IAYC;;AAED,MAAM,UAAU,sBAAsB,CACpC,CAAM;IAEN,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACrB,CAAC"}