{"version": 3, "file": "BrowserFetcher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/BrowserFetcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAsBH,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAkC7C;;;;GAIG;AACH,MAAM,MAAM,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;AA4DvE;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB;;;;OAIG;IACH,OAAO,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC/B;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IACvB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,OAAO,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AAEH,qBAAa,cAAc;;IAMzB;;OAEG;gBACS,OAAO,EAAE,qBAAqB;IA6C1C;;;OAGG;IACH,QAAQ,IAAI,QAAQ;IAIpB;;;OAGG;IACH,OAAO,IAAI,OAAO;IAIlB;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;;;;;;OAOG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAuB/C;;;;;;;;;OASG;IACG,QAAQ,CACZ,QAAQ,EAAE,MAAM,EAChB,gBAAgB,GAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,IAAqB,GAChE,OAAO,CAAC,0BAA0B,GAAG,SAAS,CAAC;IAsClD;;;;;OAKG;IACH,cAAc,IAAI,MAAM,EAAE;IAiB1B;;;;;;OAMG;IACG,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAS7C;;;OAGG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,0BAA0B;IAqF1D;;OAEG;IACH,eAAe,IAAI,MAAM;CAG1B"}