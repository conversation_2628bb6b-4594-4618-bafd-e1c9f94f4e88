{"version": 3, "file": "ProductLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ProductLauncher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,2BAA8B;AAC9B,yCAA8B;AAC9B,+BAA0B;AAE1B,kDAK6B;AAG7B,qDAAgD;AAChD,2DAAmD;AACnD,mDAAiD;AACjD,mFAAiG;AAEjG,+CAA6C;AAO7C,yDAAiD;AAajD;;;;GAIG;AACH,MAAa,eAAe;IAa1B;;OAEG;IACH,YAAY,SAAwB,EAAE,OAAgB;QAftD,2CAAkB;QAgBhB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,uBAAA,IAAI,4BAAY,OAAO,MAAA,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,gCAAS,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAAsC,EAAE;QACnD,MAAM,EACJ,MAAM,GAAG,KAAK,EACd,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,EACnB,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK,EACf,kBAAkB,GAAG,IAAI,EACzB,QAAQ,EACR,eAAe,GAChB,GAAG,OAAO,CAAC;QAEZ,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAE9D,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QAEpE,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,WAAW,EAAE;gBAClD,MAAM,EAAE,UAAU,CAAC,iBAAiB;aACrC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,IAAA,iBAAM,EAAC;YAC5B,cAAc,EAAE,UAAU,CAAC,cAAc;YACzC,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,MAAM;YACN,GAAG;YACH,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;QAEH,IAAI,OAAgB,CAAC;QACrB,IAAI,UAAsB,CAAC;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE;YACtC,IAAI,OAAO,EAAE;gBACX,OAAO;aACR;YACD,OAAO,GAAG,IAAI,CAAC;YACf,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC;QAEF,IAAI;YACF,IAAI,uBAAA,IAAI,gCAAS,KAAK,SAAS,IAAI,QAAQ,KAAK,eAAe,EAAE;gBAC/D,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACpC,cAAc,EACd,oBAAoB,EACpB;oBACE,OAAO;oBACP,eAAe;oBACf,MAAM;iBACP,CACF,CAAC;aACH;iBAAM;gBACL,IAAI,OAAO,EAAE;oBACX,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE;wBAC9D,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;iBACJ;qBAAM;oBACL,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE;wBAChE,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;iBACJ;gBACD,IAAI,QAAQ,KAAK,eAAe,EAAE;oBAChC,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC3C,cAAc,EACd,UAAU,EACV,oBAAoB,CACrB,CAAC;iBACH;qBAAM;oBACL,OAAO,GAAG,MAAM,uBAAU,CAAC,OAAO,CAChC,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,EAAE,EACF,iBAAiB,EACjB,eAAe,EACf,cAAc,CAAC,WAAW,EAC1B,oBAAoB,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;iBACH;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,oBAAoB,EAAE,CAAC;YACvB,IAAI,KAAK,YAAY,uBAAoB,EAAE;gBACzC,MAAM,IAAI,wBAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aACvC;YACD,MAAM,KAAK,CAAC;SACb;QAED,IAAI,kBAAkB,IAAI,QAAQ,KAAK,eAAe,EAAE;YACtD,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAChD;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAGD,cAAc;QACZ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAGD,WAAW;QACT,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAQS,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IASS,KAAK,CAAC,gBAAgB;QAC9B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,YAAY,CAC1B,cAAyC,EACzC,UAAuB;QAEvB,IAAI,UAAU,EAAE;YACd,0CAA0C;YAC1C,IAAI;gBACF,MAAM,UAAU,CAAC,YAAY,EAAE,CAAC;gBAChC,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;aAClC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;aAC9B;SACF;aAAM;YACL,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,OAAgB,EAChB,OAAe;QAEf,IAAI;YACF,MAAM,OAAO,CAAC,aAAa,CACzB,CAAC,CAAC,EAAE;gBACF,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;YAC7B,CAAC,EACD,EAAC,OAAO,EAAC,CACV,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,yBAAyB,CACvC,cAAyC,EACzC,IAA4E;QAE5E,MAAM,iBAAiB,GAAG,MAAM,cAAc,CAAC,iBAAiB,CAC9D,uCAA4B,EAC5B,IAAI,CAAC,OAAO,CACb,CAAC;QACF,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,OAAO,IAAI,0BAAU,CACnB,iBAAiB,EACjB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACrC,cAAyC,EACzC,IAA4E;QAE5E,0EAA0E;QAC1E,mCAAmC;QACnC,MAAM,EAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;QACrE,MAAM,SAAS,GAAG,IAAI,gCAAa,CACjC,SAAkC,EAClC,QAAiC,CAClC,CAAC;QACF,OAAO,IAAI,0BAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB,CACtC,cAAyC,EACzC,UAAsB,EACtB,aAAmC;QAEnC,MAAM,IAAI,GAAG;QACX,yBAAyB,CAAC,wBAAwB,GACnD,CAAC;QACF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACjE,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,UAAU,EAAE,cAAc;YAC1B,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,cAAyC,EACzC,aAAmC,EACnC,IAA4E;QAE5E,MAAM,iBAAiB,GACrB,CAAC,MAAM,cAAc,CAAC,iBAAiB,CACrC,kDAAuC,EACvC,IAAI,CAAC,OAAO,CACb,CAAC,GAAG,UAAU,CAAC;QAClB,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,MAAM,IAAI,GAAG;QACX,yBAAyB,CAAC,wBAAwB,GACnD,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,UAAU,CACxC,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,UAAU,EAAE,cAAc;YAC1B,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,cAAc;;QACtB,OAAO,IAAA,WAAI,EACT,MAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,mCAAI,IAAA,WAAM,GAAE,EAC3D,iBAAiB,IAAI,CAAC,OAAO,WAAW,CACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,CAAC;QACnE,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,IAAA,eAAU,EAAC,cAAc,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CACb,qDAAqD,cAAc,iCAAiC,CACrG,CAAC;aACH;YACD,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,kBAAkB,GAAG,2BAA2B,CAAC;QACvD,IACE,IAAI,CAAC,OAAO,KAAK,QAAQ;YACzB,YAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ;YAC1B,YAAE,CAAC,IAAI,EAAE,KAAK,OAAO;YACrB,IAAA,eAAU,EAAC,kBAAkB,CAAC,EAC9B;YACA,OAAO,kBAAkB,CAAC;SAC3B;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;YACzD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAoB;SAC1C,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAC9C,IAAI,CAAC,SAAS,CAAC,eAAe,CAC/B,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YACvB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,EAAE;gBAChD,MAAM,IAAI,KAAK,CACb,qDAAqD,YAAY,CAAC,cAAc,kBAAkB,IAAI,CAAC,SAAS,CAAC,eAAe,gCAAgC,CACjK,CAAC;aACH;YACD,QAAQ,IAAI,CAAC,OAAO,EAAE;gBACpB,KAAK,QAAQ;oBACX,MAAM,IAAI,KAAK,CACb,iCAAiC,IAAI,CAAC,SAAS,CAAC,eAAe,+BAA+B;wBAC5F,6FAA6F;wBAC7F,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;gBACJ,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CACb,gCAAgC,IAAI,CAAC,SAAS,CAAC,eAAe,+BAA+B;wBAC3F,mIAAmI;wBACnI,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;aACL;SACF;QACD,OAAO,YAAY,CAAC,cAAc,CAAC;IACrC,CAAC;CACF;AA/WD,0CA+WC"}