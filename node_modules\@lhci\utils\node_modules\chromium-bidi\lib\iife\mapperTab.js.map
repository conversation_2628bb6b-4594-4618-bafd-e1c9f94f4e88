{"version": 3, "file": "mapperTab.js", "sources": ["../../node_modules/zod/lib/helpers/util.js", "../../node_modules/zod/lib/ZodError.js", "../../node_modules/zod/lib/locales/en.js", "../../node_modules/zod/lib/errors.js", "../../node_modules/zod/lib/helpers/parseUtil.js", "../../node_modules/zod/lib/helpers/typeAliases.js", "../../node_modules/zod/lib/helpers/errorUtil.js", "../../node_modules/zod/lib/types.js", "../../node_modules/zod/lib/external.js", "../../node_modules/zod/lib/index.js", "../cjs/protocol/protocol.js", "../cjs/protocol-parser/protocol-parser.js", "../cjs/utils/EventEmitter.js", "../../node_modules/mitt/dist/mitt.js", "../cjs/utils/log.js", "../cjs/utils/processingQueue.js", "../cjs/utils/unitConversions.js", "../cjs/utils/deferred.js", "../cjs/bidiMapper/domains/script/scriptEvaluator.js", "../cjs/bidiMapper/domains/script/realm.js", "../cjs/bidiMapper/domains/context/browsingContextImpl.js", "../cjs/bidiMapper/domains/log/logHelper.js", "../cjs/bidiMapper/domains/log/logManager.js", "../cjs/utils/DefaultMap.js", "../cjs/bidiMapper/domains/network/networkRequest.js", "../cjs/bidiMapper/domains/network/networkProcessor.js", "../cjs/bidiMapper/domains/context/cdpTarget.js", "../cjs/bidiMapper/domains/context/browsingContextProcessor.js", "../cjs/bidiMapper/OutgoingBidiMessage.js", "../cjs/bidiMapper/CommandProcessor.js", "../cjs/bidiMapper/domains/context/browsingContextStorage.js", "../cjs/utils/buffer.js", "../cjs/utils/idWrapper.js", "../cjs/bidiMapper/domains/events/SubscriptionManager.js", "../cjs/bidiMapper/domains/events/EventManager.js", "../cjs/bidiMapper/domains/script/realmStorage.js", "../cjs/bidiMapper/BidiServer.js", "../cjs/cdp/cdpClient.js", "../cjs/cdp/cdpConnection.js", "../cjs/utils/websocketTransport.js", "../cjs/cdp/index.js", "../cjs/bidiTab/mapperTabPage.js", "../cjs/bidiTab/bidiTab.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getParsedType = exports.ZodParsedType = exports.objectUtil = exports.util = void 0;\nvar util;\n(function (util) {\n    util.assertEqual = (val) => val;\n    function assertIs(_arg) { }\n    util.assertIs = assertIs;\n    function assertNever(_x) {\n        throw new Error();\n    }\n    util.assertNever = assertNever;\n    util.arrayToEnum = (items) => {\n        const obj = {};\n        for (const item of items) {\n            obj[item] = item;\n        }\n        return obj;\n    };\n    util.getValidEnumValues = (obj) => {\n        const validKeys = util.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== \"number\");\n        const filtered = {};\n        for (const k of validKeys) {\n            filtered[k] = obj[k];\n        }\n        return util.objectValues(filtered);\n    };\n    util.objectValues = (obj) => {\n        return util.objectKeys(obj).map(function (e) {\n            return obj[e];\n        });\n    };\n    util.objectKeys = typeof Object.keys === \"function\"\n        ? (obj) => Object.keys(obj)\n        : (object) => {\n            const keys = [];\n            for (const key in object) {\n                if (Object.prototype.hasOwnProperty.call(object, key)) {\n                    keys.push(key);\n                }\n            }\n            return keys;\n        };\n    util.find = (arr, checker) => {\n        for (const item of arr) {\n            if (checker(item))\n                return item;\n        }\n        return undefined;\n    };\n    util.isInteger = typeof Number.isInteger === \"function\"\n        ? (val) => Number.isInteger(val)\n        : (val) => typeof val === \"number\" && isFinite(val) && Math.floor(val) === val;\n    function joinValues(array, separator = \" | \") {\n        return array\n            .map((val) => (typeof val === \"string\" ? `'${val}'` : val))\n            .join(separator);\n    }\n    util.joinValues = joinValues;\n    util.jsonStringifyReplacer = (_, value) => {\n        if (typeof value === \"bigint\") {\n            return value.toString();\n        }\n        return value;\n    };\n})(util = exports.util || (exports.util = {}));\nvar objectUtil;\n(function (objectUtil) {\n    objectUtil.mergeShapes = (first, second) => {\n        return {\n            ...first,\n            ...second,\n        };\n    };\n})(objectUtil = exports.objectUtil || (exports.objectUtil = {}));\nexports.ZodParsedType = util.arrayToEnum([\n    \"string\",\n    \"nan\",\n    \"number\",\n    \"integer\",\n    \"float\",\n    \"boolean\",\n    \"date\",\n    \"bigint\",\n    \"symbol\",\n    \"function\",\n    \"undefined\",\n    \"null\",\n    \"array\",\n    \"object\",\n    \"unknown\",\n    \"promise\",\n    \"void\",\n    \"never\",\n    \"map\",\n    \"set\",\n]);\nconst getParsedType = (data) => {\n    const t = typeof data;\n    switch (t) {\n        case \"undefined\":\n            return exports.ZodParsedType.undefined;\n        case \"string\":\n            return exports.ZodParsedType.string;\n        case \"number\":\n            return isNaN(data) ? exports.ZodParsedType.nan : exports.ZodParsedType.number;\n        case \"boolean\":\n            return exports.ZodParsedType.boolean;\n        case \"function\":\n            return exports.ZodParsedType.function;\n        case \"bigint\":\n            return exports.ZodParsedType.bigint;\n        case \"symbol\":\n            return exports.ZodParsedType.symbol;\n        case \"object\":\n            if (Array.isArray(data)) {\n                return exports.ZodParsedType.array;\n            }\n            if (data === null) {\n                return exports.ZodParsedType.null;\n            }\n            if (data.then &&\n                typeof data.then === \"function\" &&\n                data.catch &&\n                typeof data.catch === \"function\") {\n                return exports.ZodParsedType.promise;\n            }\n            if (typeof Map !== \"undefined\" && data instanceof Map) {\n                return exports.ZodParsedType.map;\n            }\n            if (typeof Set !== \"undefined\" && data instanceof Set) {\n                return exports.ZodParsedType.set;\n            }\n            if (typeof Date !== \"undefined\" && data instanceof Date) {\n                return exports.ZodParsedType.date;\n            }\n            return exports.ZodParsedType.object;\n        default:\n            return exports.ZodParsedType.unknown;\n    }\n};\nexports.getParsedType = getParsedType;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ZodError = exports.quotelessJson = exports.ZodIssueCode = void 0;\nconst util_1 = require(\"./helpers/util\");\nexports.ZodIssueCode = util_1.util.arrayToEnum([\n    \"invalid_type\",\n    \"invalid_literal\",\n    \"custom\",\n    \"invalid_union\",\n    \"invalid_union_discriminator\",\n    \"invalid_enum_value\",\n    \"unrecognized_keys\",\n    \"invalid_arguments\",\n    \"invalid_return_type\",\n    \"invalid_date\",\n    \"invalid_string\",\n    \"too_small\",\n    \"too_big\",\n    \"invalid_intersection_types\",\n    \"not_multiple_of\",\n    \"not_finite\",\n]);\nconst quotelessJson = (obj) => {\n    const json = JSON.stringify(obj, null, 2);\n    return json.replace(/\"([^\"]+)\":/g, \"$1:\");\n};\nexports.quotelessJson = quotelessJson;\nclass ZodError extends Error {\n    constructor(issues) {\n        super();\n        this.issues = [];\n        this.addIssue = (sub) => {\n            this.issues = [...this.issues, sub];\n        };\n        this.addIssues = (subs = []) => {\n            this.issues = [...this.issues, ...subs];\n        };\n        const actualProto = new.target.prototype;\n        if (Object.setPrototypeOf) {\n            Object.setPrototypeOf(this, actualProto);\n        }\n        else {\n            this.__proto__ = actualProto;\n        }\n        this.name = \"ZodError\";\n        this.issues = issues;\n    }\n    get errors() {\n        return this.issues;\n    }\n    format(_mapper) {\n        const mapper = _mapper ||\n            function (issue) {\n                return issue.message;\n            };\n        const fieldErrors = { _errors: [] };\n        const processError = (error) => {\n            for (const issue of error.issues) {\n                if (issue.code === \"invalid_union\") {\n                    issue.unionErrors.map(processError);\n                }\n                else if (issue.code === \"invalid_return_type\") {\n                    processError(issue.returnTypeError);\n                }\n                else if (issue.code === \"invalid_arguments\") {\n                    processError(issue.argumentsError);\n                }\n                else if (issue.path.length === 0) {\n                    fieldErrors._errors.push(mapper(issue));\n                }\n                else {\n                    let curr = fieldErrors;\n                    let i = 0;\n                    while (i < issue.path.length) {\n                        const el = issue.path[i];\n                        const terminal = i === issue.path.length - 1;\n                        if (!terminal) {\n                            curr[el] = curr[el] || { _errors: [] };\n                        }\n                        else {\n                            curr[el] = curr[el] || { _errors: [] };\n                            curr[el]._errors.push(mapper(issue));\n                        }\n                        curr = curr[el];\n                        i++;\n                    }\n                }\n            }\n        };\n        processError(this);\n        return fieldErrors;\n    }\n    toString() {\n        return this.message;\n    }\n    get message() {\n        return JSON.stringify(this.issues, util_1.util.jsonStringifyReplacer, 2);\n    }\n    get isEmpty() {\n        return this.issues.length === 0;\n    }\n    flatten(mapper = (issue) => issue.message) {\n        const fieldErrors = {};\n        const formErrors = [];\n        for (const sub of this.issues) {\n            if (sub.path.length > 0) {\n                fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n                fieldErrors[sub.path[0]].push(mapper(sub));\n            }\n            else {\n                formErrors.push(mapper(sub));\n            }\n        }\n        return { formErrors, fieldErrors };\n    }\n    get formErrors() {\n        return this.flatten();\n    }\n}\nexports.ZodError = ZodError;\nZodError.create = (issues) => {\n    const error = new ZodError(issues);\n    return error;\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../helpers/util\");\nconst ZodError_1 = require(\"../ZodError\");\nconst errorMap = (issue, _ctx) => {\n    let message;\n    switch (issue.code) {\n        case ZodError_1.ZodIssueCode.invalid_type:\n            if (issue.received === util_1.ZodParsedType.undefined) {\n                message = \"Required\";\n            }\n            else {\n                message = `Expected ${issue.expected}, received ${issue.received}`;\n            }\n            break;\n        case ZodError_1.ZodIssueCode.invalid_literal:\n            message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util_1.util.jsonStringifyReplacer)}`;\n            break;\n        case ZodError_1.ZodIssueCode.unrecognized_keys:\n            message = `Unrecognized key(s) in object: ${util_1.util.joinValues(issue.keys, \", \")}`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_union:\n            message = `Invalid input`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_union_discriminator:\n            message = `Invalid discriminator value. Expected ${util_1.util.joinValues(issue.options)}`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_enum_value:\n            message = `Invalid enum value. Expected ${util_1.util.joinValues(issue.options)}, received '${issue.received}'`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_arguments:\n            message = `Invalid function arguments`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_return_type:\n            message = `Invalid function return type`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_date:\n            message = `Invalid date`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_string:\n            if (typeof issue.validation === \"object\") {\n                if (\"includes\" in issue.validation) {\n                    message = `Invalid input: must include \"${issue.validation.includes}\"`;\n                    if (typeof issue.validation.position === \"number\") {\n                        message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;\n                    }\n                }\n                else if (\"startsWith\" in issue.validation) {\n                    message = `Invalid input: must start with \"${issue.validation.startsWith}\"`;\n                }\n                else if (\"endsWith\" in issue.validation) {\n                    message = `Invalid input: must end with \"${issue.validation.endsWith}\"`;\n                }\n                else {\n                    util_1.util.assertNever(issue.validation);\n                }\n            }\n            else if (issue.validation !== \"regex\") {\n                message = `Invalid ${issue.validation}`;\n            }\n            else {\n                message = \"Invalid\";\n            }\n            break;\n        case ZodError_1.ZodIssueCode.too_small:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${issue.minimum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${new Date(Number(issue.minimum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodError_1.ZodIssueCode.too_big:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"bigint\")\n                message = `BigInt must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `smaller than or equal to`\n                        : `smaller than`} ${new Date(Number(issue.maximum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodError_1.ZodIssueCode.custom:\n            message = `Invalid input`;\n            break;\n        case ZodError_1.ZodIssueCode.invalid_intersection_types:\n            message = `Intersection results could not be merged`;\n            break;\n        case ZodError_1.ZodIssueCode.not_multiple_of:\n            message = `Number must be a multiple of ${issue.multipleOf}`;\n            break;\n        case ZodError_1.ZodIssueCode.not_finite:\n            message = \"Number must be finite\";\n            break;\n        default:\n            message = _ctx.defaultError;\n            util_1.util.assertNever(issue);\n    }\n    return { message };\n};\nexports.default = errorMap;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getErrorMap = exports.setErrorMap = exports.defaultErrorMap = void 0;\nconst en_1 = __importDefault(require(\"./locales/en\"));\nexports.defaultErrorMap = en_1.default;\nlet overrideErrorMap = en_1.default;\nfunction setErrorMap(map) {\n    overrideErrorMap = map;\n}\nexports.setErrorMap = setErrorMap;\nfunction getErrorMap() {\n    return overrideErrorMap;\n}\nexports.getErrorMap = getErrorMap;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isAsync = exports.isValid = exports.isDirty = exports.isAborted = exports.OK = exports.DIRTY = exports.INVALID = exports.ParseStatus = exports.addIssueToContext = exports.EMPTY_PATH = exports.makeIssue = void 0;\nconst errors_1 = require(\"../errors\");\nconst en_1 = __importDefault(require(\"../locales/en\"));\nconst makeIssue = (params) => {\n    const { data, path, errorMaps, issueData } = params;\n    const fullPath = [...path, ...(issueData.path || [])];\n    const fullIssue = {\n        ...issueData,\n        path: fullPath,\n    };\n    let errorMessage = \"\";\n    const maps = errorMaps\n        .filter((m) => !!m)\n        .slice()\n        .reverse();\n    for (const map of maps) {\n        errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;\n    }\n    return {\n        ...issueData,\n        path: fullPath,\n        message: issueData.message || errorMessage,\n    };\n};\nexports.makeIssue = makeIssue;\nexports.EMPTY_PATH = [];\nfunction addIssueToContext(ctx, issueData) {\n    const issue = (0, exports.makeIssue)({\n        issueData: issueData,\n        data: ctx.data,\n        path: ctx.path,\n        errorMaps: [\n            ctx.common.contextualErrorMap,\n            ctx.schemaErrorMap,\n            (0, errors_1.getErrorMap)(),\n            en_1.default,\n        ].filter((x) => !!x),\n    });\n    ctx.common.issues.push(issue);\n}\nexports.addIssueToContext = addIssueToContext;\nclass ParseStatus {\n    constructor() {\n        this.value = \"valid\";\n    }\n    dirty() {\n        if (this.value === \"valid\")\n            this.value = \"dirty\";\n    }\n    abort() {\n        if (this.value !== \"aborted\")\n            this.value = \"aborted\";\n    }\n    static mergeArray(status, results) {\n        const arrayValue = [];\n        for (const s of results) {\n            if (s.status === \"aborted\")\n                return exports.INVALID;\n            if (s.status === \"dirty\")\n                status.dirty();\n            arrayValue.push(s.value);\n        }\n        return { status: status.value, value: arrayValue };\n    }\n    static async mergeObjectAsync(status, pairs) {\n        const syncPairs = [];\n        for (const pair of pairs) {\n            syncPairs.push({\n                key: await pair.key,\n                value: await pair.value,\n            });\n        }\n        return ParseStatus.mergeObjectSync(status, syncPairs);\n    }\n    static mergeObjectSync(status, pairs) {\n        const finalObject = {};\n        for (const pair of pairs) {\n            const { key, value } = pair;\n            if (key.status === \"aborted\")\n                return exports.INVALID;\n            if (value.status === \"aborted\")\n                return exports.INVALID;\n            if (key.status === \"dirty\")\n                status.dirty();\n            if (value.status === \"dirty\")\n                status.dirty();\n            if (typeof value.value !== \"undefined\" || pair.alwaysSet) {\n                finalObject[key.value] = value.value;\n            }\n        }\n        return { status: status.value, value: finalObject };\n    }\n}\nexports.ParseStatus = ParseStatus;\nexports.INVALID = Object.freeze({\n    status: \"aborted\",\n});\nconst DIRTY = (value) => ({ status: \"dirty\", value });\nexports.DIRTY = DIRTY;\nconst OK = (value) => ({ status: \"valid\", value });\nexports.OK = OK;\nconst isAborted = (x) => x.status === \"aborted\";\nexports.isAborted = isAborted;\nconst isDirty = (x) => x.status === \"dirty\";\nexports.isDirty = isDirty;\nconst isValid = (x) => x.status === \"valid\";\nexports.isValid = isValid;\nconst isAsync = (x) => typeof Promise !== \"undefined\" && x instanceof Promise;\nexports.isAsync = isAsync;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.errorUtil = void 0;\nvar errorUtil;\n(function (errorUtil) {\n    errorUtil.errToObj = (message) => typeof message === \"string\" ? { message } : message || {};\n    errorUtil.toString = (message) => typeof message === \"string\" ? message : message === null || message === void 0 ? void 0 : message.message;\n})(errorUtil = exports.errorUtil || (exports.errorUtil = {}));\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.discriminatedUnion = exports.date = exports.boolean = exports.bigint = exports.array = exports.any = exports.coerce = exports.ZodFirstPartyTypeKind = exports.late = exports.ZodSchema = exports.Schema = exports.custom = exports.ZodPipeline = exports.ZodBranded = exports.BRAND = exports.ZodNaN = exports.ZodCatch = exports.ZodDefault = exports.ZodNullable = exports.ZodOptional = exports.ZodTransformer = exports.ZodEffects = exports.ZodPromise = exports.ZodNativeEnum = exports.ZodEnum = exports.ZodLiteral = exports.ZodLazy = exports.ZodFunction = exports.ZodSet = exports.ZodMap = exports.ZodRecord = exports.ZodTuple = exports.ZodIntersection = exports.ZodDiscriminatedUnion = exports.ZodUnion = exports.ZodObject = exports.ZodArray = exports.ZodVoid = exports.ZodNever = exports.ZodUnknown = exports.ZodAny = exports.ZodNull = exports.ZodUndefined = exports.ZodSymbol = exports.ZodDate = exports.ZodBoolean = exports.ZodBigInt = exports.ZodNumber = exports.ZodString = exports.ZodType = void 0;\nexports.NEVER = exports.void = exports.unknown = exports.union = exports.undefined = exports.tuple = exports.transformer = exports.symbol = exports.string = exports.strictObject = exports.set = exports.record = exports.promise = exports.preprocess = exports.pipeline = exports.ostring = exports.optional = exports.onumber = exports.oboolean = exports.object = exports.number = exports.nullable = exports.null = exports.never = exports.nativeEnum = exports.nan = exports.map = exports.literal = exports.lazy = exports.intersection = exports.instanceof = exports.function = exports.enum = exports.effect = void 0;\nconst errors_1 = require(\"./errors\");\nconst errorUtil_1 = require(\"./helpers/errorUtil\");\nconst parseUtil_1 = require(\"./helpers/parseUtil\");\nconst util_1 = require(\"./helpers/util\");\nconst ZodError_1 = require(\"./ZodError\");\nclass ParseInputLazyPath {\n    constructor(parent, value, path, key) {\n        this._cachedPath = [];\n        this.parent = parent;\n        this.data = value;\n        this._path = path;\n        this._key = key;\n    }\n    get path() {\n        if (!this._cachedPath.length) {\n            if (this._key instanceof Array) {\n                this._cachedPath.push(...this._path, ...this._key);\n            }\n            else {\n                this._cachedPath.push(...this._path, this._key);\n            }\n        }\n        return this._cachedPath;\n    }\n}\nconst handleResult = (ctx, result) => {\n    if ((0, parseUtil_1.isValid)(result)) {\n        return { success: true, data: result.value };\n    }\n    else {\n        if (!ctx.common.issues.length) {\n            throw new Error(\"Validation failed but no issues detected.\");\n        }\n        return {\n            success: false,\n            get error() {\n                if (this._error)\n                    return this._error;\n                const error = new ZodError_1.ZodError(ctx.common.issues);\n                this._error = error;\n                return this._error;\n            },\n        };\n    }\n};\nfunction processCreateParams(params) {\n    if (!params)\n        return {};\n    const { errorMap, invalid_type_error, required_error, description } = params;\n    if (errorMap && (invalid_type_error || required_error)) {\n        throw new Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`);\n    }\n    if (errorMap)\n        return { errorMap: errorMap, description };\n    const customMap = (iss, ctx) => {\n        if (iss.code !== \"invalid_type\")\n            return { message: ctx.defaultError };\n        if (typeof ctx.data === \"undefined\") {\n            return { message: required_error !== null && required_error !== void 0 ? required_error : ctx.defaultError };\n        }\n        return { message: invalid_type_error !== null && invalid_type_error !== void 0 ? invalid_type_error : ctx.defaultError };\n    };\n    return { errorMap: customMap, description };\n}\nclass ZodType {\n    constructor(def) {\n        this.spa = this.safeParseAsync;\n        this._def = def;\n        this.parse = this.parse.bind(this);\n        this.safeParse = this.safeParse.bind(this);\n        this.parseAsync = this.parseAsync.bind(this);\n        this.safeParseAsync = this.safeParseAsync.bind(this);\n        this.spa = this.spa.bind(this);\n        this.refine = this.refine.bind(this);\n        this.refinement = this.refinement.bind(this);\n        this.superRefine = this.superRefine.bind(this);\n        this.optional = this.optional.bind(this);\n        this.nullable = this.nullable.bind(this);\n        this.nullish = this.nullish.bind(this);\n        this.array = this.array.bind(this);\n        this.promise = this.promise.bind(this);\n        this.or = this.or.bind(this);\n        this.and = this.and.bind(this);\n        this.transform = this.transform.bind(this);\n        this.brand = this.brand.bind(this);\n        this.default = this.default.bind(this);\n        this.catch = this.catch.bind(this);\n        this.describe = this.describe.bind(this);\n        this.pipe = this.pipe.bind(this);\n        this.isNullable = this.isNullable.bind(this);\n        this.isOptional = this.isOptional.bind(this);\n    }\n    get description() {\n        return this._def.description;\n    }\n    _getType(input) {\n        return (0, util_1.getParsedType)(input.data);\n    }\n    _getOrReturnCtx(input, ctx) {\n        return (ctx || {\n            common: input.parent.common,\n            data: input.data,\n            parsedType: (0, util_1.getParsedType)(input.data),\n            schemaErrorMap: this._def.errorMap,\n            path: input.path,\n            parent: input.parent,\n        });\n    }\n    _processInputParams(input) {\n        return {\n            status: new parseUtil_1.ParseStatus(),\n            ctx: {\n                common: input.parent.common,\n                data: input.data,\n                parsedType: (0, util_1.getParsedType)(input.data),\n                schemaErrorMap: this._def.errorMap,\n                path: input.path,\n                parent: input.parent,\n            },\n        };\n    }\n    _parseSync(input) {\n        const result = this._parse(input);\n        if ((0, parseUtil_1.isAsync)(result)) {\n            throw new Error(\"Synchronous parse encountered promise.\");\n        }\n        return result;\n    }\n    _parseAsync(input) {\n        const result = this._parse(input);\n        return Promise.resolve(result);\n    }\n    parse(data, params) {\n        const result = this.safeParse(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    safeParse(data, params) {\n        var _a;\n        const ctx = {\n            common: {\n                issues: [],\n                async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: (0, util_1.getParsedType)(data),\n        };\n        const result = this._parseSync({ data, path: ctx.path, parent: ctx });\n        return handleResult(ctx, result);\n    }\n    async parseAsync(data, params) {\n        const result = await this.safeParseAsync(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    async safeParseAsync(data, params) {\n        const ctx = {\n            common: {\n                issues: [],\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n                async: true,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: (0, util_1.getParsedType)(data),\n        };\n        const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });\n        const result = await ((0, parseUtil_1.isAsync)(maybeAsyncResult)\n            ? maybeAsyncResult\n            : Promise.resolve(maybeAsyncResult));\n        return handleResult(ctx, result);\n    }\n    refine(check, message) {\n        const getIssueProperties = (val) => {\n            if (typeof message === \"string\" || typeof message === \"undefined\") {\n                return { message };\n            }\n            else if (typeof message === \"function\") {\n                return message(val);\n            }\n            else {\n                return message;\n            }\n        };\n        return this._refinement((val, ctx) => {\n            const result = check(val);\n            const setError = () => ctx.addIssue({\n                code: ZodError_1.ZodIssueCode.custom,\n                ...getIssueProperties(val),\n            });\n            if (typeof Promise !== \"undefined\" && result instanceof Promise) {\n                return result.then((data) => {\n                    if (!data) {\n                        setError();\n                        return false;\n                    }\n                    else {\n                        return true;\n                    }\n                });\n            }\n            if (!result) {\n                setError();\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    refinement(check, refinementData) {\n        return this._refinement((val, ctx) => {\n            if (!check(val)) {\n                ctx.addIssue(typeof refinementData === \"function\"\n                    ? refinementData(val, ctx)\n                    : refinementData);\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    _refinement(refinement) {\n        return new ZodEffects({\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"refinement\", refinement },\n        });\n    }\n    superRefine(refinement) {\n        return this._refinement(refinement);\n    }\n    optional() {\n        return ZodOptional.create(this, this._def);\n    }\n    nullable() {\n        return ZodNullable.create(this, this._def);\n    }\n    nullish() {\n        return this.nullable().optional();\n    }\n    array() {\n        return ZodArray.create(this, this._def);\n    }\n    promise() {\n        return ZodPromise.create(this, this._def);\n    }\n    or(option) {\n        return ZodUnion.create([this, option], this._def);\n    }\n    and(incoming) {\n        return ZodIntersection.create(this, incoming, this._def);\n    }\n    transform(transform) {\n        return new ZodEffects({\n            ...processCreateParams(this._def),\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"transform\", transform },\n        });\n    }\n    default(def) {\n        const defaultValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodDefault({\n            ...processCreateParams(this._def),\n            innerType: this,\n            defaultValue: defaultValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodDefault,\n        });\n    }\n    brand() {\n        return new ZodBranded({\n            typeName: ZodFirstPartyTypeKind.ZodBranded,\n            type: this,\n            ...processCreateParams(this._def),\n        });\n    }\n    catch(def) {\n        const catchValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodCatch({\n            ...processCreateParams(this._def),\n            innerType: this,\n            catchValue: catchValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodCatch,\n        });\n    }\n    describe(description) {\n        const This = this.constructor;\n        return new This({\n            ...this._def,\n            description,\n        });\n    }\n    pipe(target) {\n        return ZodPipeline.create(this, target);\n    }\n    isOptional() {\n        return this.safeParse(undefined).success;\n    }\n    isNullable() {\n        return this.safeParse(null).success;\n    }\n}\nexports.ZodType = ZodType;\nexports.Schema = ZodType;\nexports.ZodSchema = ZodType;\nconst cuidRegex = /^c[^\\s-]{8,}$/i;\nconst cuid2Regex = /^[a-z][a-z0-9]*$/;\nconst ulidRegex = /[0-9A-HJKMNP-TV-Z]{26}/;\nconst uuidRegex = /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;\nconst emailRegex = /^(([^<>()[\\]\\\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@((\\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\])|(\\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\\.[A-Za-z]{2,})+))$/;\nconst emojiRegex = /^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$/u;\nconst ipv4Regex = /^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/;\nconst ipv6Regex = /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;\nconst datetimeRegex = (args) => {\n    if (args.precision) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}Z$`);\n        }\n    }\n    else if (args.precision === 0) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}Z$`);\n        }\n    }\n    else {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?Z$`);\n        }\n    }\n};\nfunction isValidIP(ip, version) {\n    if ((version === \"v4\" || !version) && ipv4Regex.test(ip)) {\n        return true;\n    }\n    if ((version === \"v6\" || !version) && ipv6Regex.test(ip)) {\n        return true;\n    }\n    return false;\n}\nclass ZodString extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._regex = (regex, validation, message) => this.refinement((data) => regex.test(data), {\n            validation,\n            code: ZodError_1.ZodIssueCode.invalid_string,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n        this.nonempty = (message) => this.min(1, errorUtil_1.errorUtil.errToObj(message));\n        this.trim = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"trim\" }],\n        });\n        this.toLowerCase = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toLowerCase\" }],\n        });\n        this.toUpperCase = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toUpperCase\" }],\n        });\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = String(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.string) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.string,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const status = new parseUtil_1.ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.length < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.length > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"length\") {\n                const tooBig = input.data.length > check.value;\n                const tooSmall = input.data.length < check.value;\n                if (tooBig || tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    if (tooBig) {\n                        (0, parseUtil_1.addIssueToContext)(ctx, {\n                            code: ZodError_1.ZodIssueCode.too_big,\n                            maximum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    else if (tooSmall) {\n                        (0, parseUtil_1.addIssueToContext)(ctx, {\n                            code: ZodError_1.ZodIssueCode.too_small,\n                            minimum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"email\") {\n                if (!emailRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"email\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"emoji\") {\n                if (!emojiRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"emoji\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"uuid\") {\n                if (!uuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"uuid\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid\") {\n                if (!cuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"cuid\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid2\") {\n                if (!cuid2Regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"cuid2\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ulid\") {\n                if (!ulidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"ulid\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"url\") {\n                try {\n                    new URL(input.data);\n                }\n                catch (_a) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"url\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"regex\") {\n                check.regex.lastIndex = 0;\n                const testResult = check.regex.test(input.data);\n                if (!testResult) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"regex\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"trim\") {\n                input.data = input.data.trim();\n            }\n            else if (check.kind === \"includes\") {\n                if (!input.data.includes(check.value, check.position)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        validation: { includes: check.value, position: check.position },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"toLowerCase\") {\n                input.data = input.data.toLowerCase();\n            }\n            else if (check.kind === \"toUpperCase\") {\n                input.data = input.data.toUpperCase();\n            }\n            else if (check.kind === \"startsWith\") {\n                if (!input.data.startsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        validation: { startsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"endsWith\") {\n                if (!input.data.endsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        validation: { endsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"datetime\") {\n                const regex = datetimeRegex(check);\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        validation: \"datetime\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ip\") {\n                if (!isValidIP(input.data, check.version)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        validation: \"ip\",\n                        code: ZodError_1.ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util_1.util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    _addCheck(check) {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    email(message) {\n        return this._addCheck({ kind: \"email\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    url(message) {\n        return this._addCheck({ kind: \"url\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    emoji(message) {\n        return this._addCheck({ kind: \"emoji\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    uuid(message) {\n        return this._addCheck({ kind: \"uuid\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    cuid(message) {\n        return this._addCheck({ kind: \"cuid\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    cuid2(message) {\n        return this._addCheck({ kind: \"cuid2\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    ulid(message) {\n        return this._addCheck({ kind: \"ulid\", ...errorUtil_1.errorUtil.errToObj(message) });\n    }\n    ip(options) {\n        return this._addCheck({ kind: \"ip\", ...errorUtil_1.errorUtil.errToObj(options) });\n    }\n    datetime(options) {\n        var _a;\n        if (typeof options === \"string\") {\n            return this._addCheck({\n                kind: \"datetime\",\n                precision: null,\n                offset: false,\n                message: options,\n            });\n        }\n        return this._addCheck({\n            kind: \"datetime\",\n            precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,\n            ...errorUtil_1.errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    regex(regex, message) {\n        return this._addCheck({\n            kind: \"regex\",\n            regex: regex,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    includes(value, options) {\n        return this._addCheck({\n            kind: \"includes\",\n            value: value,\n            position: options === null || options === void 0 ? void 0 : options.position,\n            ...errorUtil_1.errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    startsWith(value, message) {\n        return this._addCheck({\n            kind: \"startsWith\",\n            value: value,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    endsWith(value, message) {\n        return this._addCheck({\n            kind: \"endsWith\",\n            value: value,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    min(minLength, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minLength,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    max(maxLength, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxLength,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    length(len, message) {\n        return this._addCheck({\n            kind: \"length\",\n            value: len,\n            ...errorUtil_1.errorUtil.errToObj(message),\n        });\n    }\n    get isDatetime() {\n        return !!this._def.checks.find((ch) => ch.kind === \"datetime\");\n    }\n    get isEmail() {\n        return !!this._def.checks.find((ch) => ch.kind === \"email\");\n    }\n    get isURL() {\n        return !!this._def.checks.find((ch) => ch.kind === \"url\");\n    }\n    get isEmoji() {\n        return !!this._def.checks.find((ch) => ch.kind === \"emoji\");\n    }\n    get isUUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"uuid\");\n    }\n    get isCUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid\");\n    }\n    get isCUID2() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid2\");\n    }\n    get isULID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ulid\");\n    }\n    get isIP() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ip\");\n    }\n    get minLength() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxLength() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nexports.ZodString = ZodString;\nZodString.create = (params) => {\n    var _a;\n    return new ZodString({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodString,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\nfunction floatSafeRemainder(val, step) {\n    const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n    const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n    const valInt = parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n    const stepInt = parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n    return (valInt % stepInt) / Math.pow(10, decCount);\n}\nclass ZodNumber extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n        this.step = this.multipleOf;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Number(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.number) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.number,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        let ctx = undefined;\n        const status = new parseUtil_1.ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"int\") {\n                if (!util_1.util.isInteger(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.invalid_type,\n                        expected: \"integer\",\n                        received: \"float\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (floatSafeRemainder(input.data, check.value) !== 0) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"finite\") {\n                if (!Number.isFinite(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.not_finite,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util_1.util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil_1.errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil_1.errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil_1.errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil_1.errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil_1.errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    int(message) {\n        return this._addCheck({\n            kind: \"int\",\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value: value,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    finite(message) {\n        return this._addCheck({\n            kind: \"finite\",\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    safe(message) {\n        return this._addCheck({\n            kind: \"min\",\n            inclusive: true,\n            value: Number.MIN_SAFE_INTEGER,\n            message: errorUtil_1.errorUtil.toString(message),\n        })._addCheck({\n            kind: \"max\",\n            inclusive: true,\n            value: Number.MAX_SAFE_INTEGER,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n    get isInt() {\n        return !!this._def.checks.find((ch) => ch.kind === \"int\" ||\n            (ch.kind === \"multipleOf\" && util_1.util.isInteger(ch.value)));\n    }\n    get isFinite() {\n        let max = null, min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"finite\" ||\n                ch.kind === \"int\" ||\n                ch.kind === \"multipleOf\") {\n                return true;\n            }\n            else if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n            else if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return Number.isFinite(min) && Number.isFinite(max);\n    }\n}\nexports.ZodNumber = ZodNumber;\nZodNumber.create = (params) => {\n    return new ZodNumber({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodNumber,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBigInt extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = BigInt(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.bigint) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.bigint,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        let ctx = undefined;\n        const status = new parseUtil_1.ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_small,\n                        type: \"bigint\",\n                        minimum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_big,\n                        type: \"bigint\",\n                        maximum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (input.data % check.value !== BigInt(0)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util_1.util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil_1.errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil_1.errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil_1.errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil_1.errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil_1.errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value,\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nexports.ZodBigInt = ZodBigInt;\nZodBigInt.create = (params) => {\n    var _a;\n    return new ZodBigInt({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodBigInt,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBoolean extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Boolean(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.boolean) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.boolean,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodBoolean = ZodBoolean;\nZodBoolean.create = (params) => {\n    return new ZodBoolean({\n        typeName: ZodFirstPartyTypeKind.ZodBoolean,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDate extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = new Date(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.date) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.date,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (isNaN(input.data.getTime())) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_date,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const status = new parseUtil_1.ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.getTime() < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_small,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        minimum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.getTime() > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.too_big,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        maximum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util_1.util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: new Date(input.data.getTime()),\n        };\n    }\n    _addCheck(check) {\n        return new ZodDate({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    min(minDate, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minDate.getTime(),\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    max(maxDate, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxDate.getTime(),\n            message: errorUtil_1.errorUtil.toString(message),\n        });\n    }\n    get minDate() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min != null ? new Date(min) : null;\n    }\n    get maxDate() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max != null ? new Date(max) : null;\n    }\n}\nexports.ZodDate = ZodDate;\nZodDate.create = (params) => {\n    return new ZodDate({\n        checks: [],\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        typeName: ZodFirstPartyTypeKind.ZodDate,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSymbol extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.symbol) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.symbol,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodSymbol = ZodSymbol;\nZodSymbol.create = (params) => {\n    return new ZodSymbol({\n        typeName: ZodFirstPartyTypeKind.ZodSymbol,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUndefined extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.undefined,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodUndefined = ZodUndefined;\nZodUndefined.create = (params) => {\n    return new ZodUndefined({\n        typeName: ZodFirstPartyTypeKind.ZodUndefined,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNull extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.null) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.null,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodNull = ZodNull;\nZodNull.create = (params) => {\n    return new ZodNull({\n        typeName: ZodFirstPartyTypeKind.ZodNull,\n        ...processCreateParams(params),\n    });\n};\nclass ZodAny extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._any = true;\n    }\n    _parse(input) {\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodAny = ZodAny;\nZodAny.create = (params) => {\n    return new ZodAny({\n        typeName: ZodFirstPartyTypeKind.ZodAny,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnknown extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._unknown = true;\n    }\n    _parse(input) {\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodUnknown = ZodUnknown;\nZodUnknown.create = (params) => {\n    return new ZodUnknown({\n        typeName: ZodFirstPartyTypeKind.ZodUnknown,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNever extends ZodType {\n    _parse(input) {\n        const ctx = this._getOrReturnCtx(input);\n        (0, parseUtil_1.addIssueToContext)(ctx, {\n            code: ZodError_1.ZodIssueCode.invalid_type,\n            expected: util_1.ZodParsedType.never,\n            received: ctx.parsedType,\n        });\n        return parseUtil_1.INVALID;\n    }\n}\nexports.ZodNever = ZodNever;\nZodNever.create = (params) => {\n    return new ZodNever({\n        typeName: ZodFirstPartyTypeKind.ZodNever,\n        ...processCreateParams(params),\n    });\n};\nclass ZodVoid extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.void,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n}\nexports.ZodVoid = ZodVoid;\nZodVoid.create = (params) => {\n    return new ZodVoid({\n        typeName: ZodFirstPartyTypeKind.ZodVoid,\n        ...processCreateParams(params),\n    });\n};\nclass ZodArray extends ZodType {\n    _parse(input) {\n        const { ctx, status } = this._processInputParams(input);\n        const def = this._def;\n        if (ctx.parsedType !== util_1.ZodParsedType.array) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (def.exactLength !== null) {\n            const tooBig = ctx.data.length > def.exactLength.value;\n            const tooSmall = ctx.data.length < def.exactLength.value;\n            if (tooBig || tooSmall) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: tooBig ? ZodError_1.ZodIssueCode.too_big : ZodError_1.ZodIssueCode.too_small,\n                    minimum: (tooSmall ? def.exactLength.value : undefined),\n                    maximum: (tooBig ? def.exactLength.value : undefined),\n                    type: \"array\",\n                    inclusive: true,\n                    exact: true,\n                    message: def.exactLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.minLength !== null) {\n            if (ctx.data.length < def.minLength.value) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: ZodError_1.ZodIssueCode.too_small,\n                    minimum: def.minLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxLength !== null) {\n            if (ctx.data.length > def.maxLength.value) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: ZodError_1.ZodIssueCode.too_big,\n                    maximum: def.maxLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.all([...ctx.data].map((item, i) => {\n                return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n            })).then((result) => {\n                return parseUtil_1.ParseStatus.mergeArray(status, result);\n            });\n        }\n        const result = [...ctx.data].map((item, i) => {\n            return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n        });\n        return parseUtil_1.ParseStatus.mergeArray(status, result);\n    }\n    get element() {\n        return this._def.type;\n    }\n    min(minLength, message) {\n        return new ZodArray({\n            ...this._def,\n            minLength: { value: minLength, message: errorUtil_1.errorUtil.toString(message) },\n        });\n    }\n    max(maxLength, message) {\n        return new ZodArray({\n            ...this._def,\n            maxLength: { value: maxLength, message: errorUtil_1.errorUtil.toString(message) },\n        });\n    }\n    length(len, message) {\n        return new ZodArray({\n            ...this._def,\n            exactLength: { value: len, message: errorUtil_1.errorUtil.toString(message) },\n        });\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nexports.ZodArray = ZodArray;\nZodArray.create = (schema, params) => {\n    return new ZodArray({\n        type: schema,\n        minLength: null,\n        maxLength: null,\n        exactLength: null,\n        typeName: ZodFirstPartyTypeKind.ZodArray,\n        ...processCreateParams(params),\n    });\n};\nfunction deepPartialify(schema) {\n    if (schema instanceof ZodObject) {\n        const newShape = {};\n        for (const key in schema.shape) {\n            const fieldSchema = schema.shape[key];\n            newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));\n        }\n        return new ZodObject({\n            ...schema._def,\n            shape: () => newShape,\n        });\n    }\n    else if (schema instanceof ZodArray) {\n        return new ZodArray({\n            ...schema._def,\n            type: deepPartialify(schema.element),\n        });\n    }\n    else if (schema instanceof ZodOptional) {\n        return ZodOptional.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodNullable) {\n        return ZodNullable.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodTuple) {\n        return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));\n    }\n    else {\n        return schema;\n    }\n}\nclass ZodObject extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._cached = null;\n        this.nonstrict = this.passthrough;\n        this.augment = this.extend;\n    }\n    _getCached() {\n        if (this._cached !== null)\n            return this._cached;\n        const shape = this._def.shape();\n        const keys = util_1.util.objectKeys(shape);\n        return (this._cached = { shape, keys });\n    }\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.object) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const { status, ctx } = this._processInputParams(input);\n        const { shape, keys: shapeKeys } = this._getCached();\n        const extraKeys = [];\n        if (!(this._def.catchall instanceof ZodNever &&\n            this._def.unknownKeys === \"strip\")) {\n            for (const key in ctx.data) {\n                if (!shapeKeys.includes(key)) {\n                    extraKeys.push(key);\n                }\n            }\n        }\n        const pairs = [];\n        for (const key of shapeKeys) {\n            const keyValidator = shape[key];\n            const value = ctx.data[key];\n            pairs.push({\n                key: { status: \"valid\", value: key },\n                value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n                alwaysSet: key in ctx.data,\n            });\n        }\n        if (this._def.catchall instanceof ZodNever) {\n            const unknownKeys = this._def.unknownKeys;\n            if (unknownKeys === \"passthrough\") {\n                for (const key of extraKeys) {\n                    pairs.push({\n                        key: { status: \"valid\", value: key },\n                        value: { status: \"valid\", value: ctx.data[key] },\n                    });\n                }\n            }\n            else if (unknownKeys === \"strict\") {\n                if (extraKeys.length > 0) {\n                    (0, parseUtil_1.addIssueToContext)(ctx, {\n                        code: ZodError_1.ZodIssueCode.unrecognized_keys,\n                        keys: extraKeys,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (unknownKeys === \"strip\") {\n            }\n            else {\n                throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);\n            }\n        }\n        else {\n            const catchall = this._def.catchall;\n            for (const key of extraKeys) {\n                const value = ctx.data[key];\n                pairs.push({\n                    key: { status: \"valid\", value: key },\n                    value: catchall._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n                    alwaysSet: key in ctx.data,\n                });\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.resolve()\n                .then(async () => {\n                const syncPairs = [];\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    syncPairs.push({\n                        key,\n                        value: await pair.value,\n                        alwaysSet: pair.alwaysSet,\n                    });\n                }\n                return syncPairs;\n            })\n                .then((syncPairs) => {\n                return parseUtil_1.ParseStatus.mergeObjectSync(status, syncPairs);\n            });\n        }\n        else {\n            return parseUtil_1.ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get shape() {\n        return this._def.shape();\n    }\n    strict(message) {\n        errorUtil_1.errorUtil.errToObj;\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strict\",\n            ...(message !== undefined\n                ? {\n                    errorMap: (issue, ctx) => {\n                        var _a, _b, _c, _d;\n                        const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;\n                        if (issue.code === \"unrecognized_keys\")\n                            return {\n                                message: (_d = errorUtil_1.errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError,\n                            };\n                        return {\n                            message: defaultError,\n                        };\n                    },\n                }\n                : {}),\n        });\n    }\n    strip() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strip\",\n        });\n    }\n    passthrough() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"passthrough\",\n        });\n    }\n    extend(augmentation) {\n        return new ZodObject({\n            ...this._def,\n            shape: () => ({\n                ...this._def.shape(),\n                ...augmentation,\n            }),\n        });\n    }\n    merge(merging) {\n        const merged = new ZodObject({\n            unknownKeys: merging._def.unknownKeys,\n            catchall: merging._def.catchall,\n            shape: () => ({\n                ...this._def.shape(),\n                ...merging._def.shape(),\n            }),\n            typeName: ZodFirstPartyTypeKind.ZodObject,\n        });\n        return merged;\n    }\n    setKey(key, schema) {\n        return this.augment({ [key]: schema });\n    }\n    catchall(index) {\n        return new ZodObject({\n            ...this._def,\n            catchall: index,\n        });\n    }\n    pick(mask) {\n        const shape = {};\n        util_1.util.objectKeys(mask).forEach((key) => {\n            if (mask[key] && this.shape[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    omit(mask) {\n        const shape = {};\n        util_1.util.objectKeys(this.shape).forEach((key) => {\n            if (!mask[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    deepPartial() {\n        return deepPartialify(this);\n    }\n    partial(mask) {\n        const newShape = {};\n        util_1.util.objectKeys(this.shape).forEach((key) => {\n            const fieldSchema = this.shape[key];\n            if (mask && !mask[key]) {\n                newShape[key] = fieldSchema;\n            }\n            else {\n                newShape[key] = fieldSchema.optional();\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    required(mask) {\n        const newShape = {};\n        util_1.util.objectKeys(this.shape).forEach((key) => {\n            if (mask && !mask[key]) {\n                newShape[key] = this.shape[key];\n            }\n            else {\n                const fieldSchema = this.shape[key];\n                let newField = fieldSchema;\n                while (newField instanceof ZodOptional) {\n                    newField = newField._def.innerType;\n                }\n                newShape[key] = newField;\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    keyof() {\n        return createZodEnum(util_1.util.objectKeys(this.shape));\n    }\n}\nexports.ZodObject = ZodObject;\nZodObject.create = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.strictCreate = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strict\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.lazycreate = (shape, params) => {\n    return new ZodObject({\n        shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const options = this._def.options;\n        function handleResults(results) {\n            for (const result of results) {\n                if (result.result.status === \"valid\") {\n                    return result.result;\n                }\n            }\n            for (const result of results) {\n                if (result.result.status === \"dirty\") {\n                    ctx.common.issues.push(...result.ctx.common.issues);\n                    return result.result;\n                }\n            }\n            const unionErrors = results.map((result) => new ZodError_1.ZodError(result.ctx.common.issues));\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (ctx.common.async) {\n            return Promise.all(options.map(async (option) => {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                return {\n                    result: await option._parseAsync({\n                        data: ctx.data,\n                        path: ctx.path,\n                        parent: childCtx,\n                    }),\n                    ctx: childCtx,\n                };\n            })).then(handleResults);\n        }\n        else {\n            let dirty = undefined;\n            const issues = [];\n            for (const option of options) {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                const result = option._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: childCtx,\n                });\n                if (result.status === \"valid\") {\n                    return result;\n                }\n                else if (result.status === \"dirty\" && !dirty) {\n                    dirty = { result, ctx: childCtx };\n                }\n                if (childCtx.common.issues.length) {\n                    issues.push(childCtx.common.issues);\n                }\n            }\n            if (dirty) {\n                ctx.common.issues.push(...dirty.ctx.common.issues);\n                return dirty.result;\n            }\n            const unionErrors = issues.map((issues) => new ZodError_1.ZodError(issues));\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return parseUtil_1.INVALID;\n        }\n    }\n    get options() {\n        return this._def.options;\n    }\n}\nexports.ZodUnion = ZodUnion;\nZodUnion.create = (types, params) => {\n    return new ZodUnion({\n        options: types,\n        typeName: ZodFirstPartyTypeKind.ZodUnion,\n        ...processCreateParams(params),\n    });\n};\nconst getDiscriminator = (type) => {\n    if (type instanceof ZodLazy) {\n        return getDiscriminator(type.schema);\n    }\n    else if (type instanceof ZodEffects) {\n        return getDiscriminator(type.innerType());\n    }\n    else if (type instanceof ZodLiteral) {\n        return [type.value];\n    }\n    else if (type instanceof ZodEnum) {\n        return type.options;\n    }\n    else if (type instanceof ZodNativeEnum) {\n        return Object.keys(type.enum);\n    }\n    else if (type instanceof ZodDefault) {\n        return getDiscriminator(type._def.innerType);\n    }\n    else if (type instanceof ZodUndefined) {\n        return [undefined];\n    }\n    else if (type instanceof ZodNull) {\n        return [null];\n    }\n    else {\n        return null;\n    }\n};\nclass ZodDiscriminatedUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.object) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const discriminator = this.discriminator;\n        const discriminatorValue = ctx.data[discriminator];\n        const option = this.optionsMap.get(discriminatorValue);\n        if (!option) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_union_discriminator,\n                options: Array.from(this.optionsMap.keys()),\n                path: [discriminator],\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (ctx.common.async) {\n            return option._parseAsync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n        else {\n            return option._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n    }\n    get discriminator() {\n        return this._def.discriminator;\n    }\n    get options() {\n        return this._def.options;\n    }\n    get optionsMap() {\n        return this._def.optionsMap;\n    }\n    static create(discriminator, options, params) {\n        const optionsMap = new Map();\n        for (const type of options) {\n            const discriminatorValues = getDiscriminator(type.shape[discriminator]);\n            if (!discriminatorValues) {\n                throw new Error(`A discriminator value for key \\`${discriminator}\\` could not be extracted from all schema options`);\n            }\n            for (const value of discriminatorValues) {\n                if (optionsMap.has(value)) {\n                    throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);\n                }\n                optionsMap.set(value, type);\n            }\n        }\n        return new ZodDiscriminatedUnion({\n            typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,\n            discriminator,\n            options,\n            optionsMap,\n            ...processCreateParams(params),\n        });\n    }\n}\nexports.ZodDiscriminatedUnion = ZodDiscriminatedUnion;\nfunction mergeValues(a, b) {\n    const aType = (0, util_1.getParsedType)(a);\n    const bType = (0, util_1.getParsedType)(b);\n    if (a === b) {\n        return { valid: true, data: a };\n    }\n    else if (aType === util_1.ZodParsedType.object && bType === util_1.ZodParsedType.object) {\n        const bKeys = util_1.util.objectKeys(b);\n        const sharedKeys = util_1.util\n            .objectKeys(a)\n            .filter((key) => bKeys.indexOf(key) !== -1);\n        const newObj = { ...a, ...b };\n        for (const key of sharedKeys) {\n            const sharedValue = mergeValues(a[key], b[key]);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newObj[key] = sharedValue.data;\n        }\n        return { valid: true, data: newObj };\n    }\n    else if (aType === util_1.ZodParsedType.array && bType === util_1.ZodParsedType.array) {\n        if (a.length !== b.length) {\n            return { valid: false };\n        }\n        const newArray = [];\n        for (let index = 0; index < a.length; index++) {\n            const itemA = a[index];\n            const itemB = b[index];\n            const sharedValue = mergeValues(itemA, itemB);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newArray.push(sharedValue.data);\n        }\n        return { valid: true, data: newArray };\n    }\n    else if (aType === util_1.ZodParsedType.date &&\n        bType === util_1.ZodParsedType.date &&\n        +a === +b) {\n        return { valid: true, data: a };\n    }\n    else {\n        return { valid: false };\n    }\n}\nclass ZodIntersection extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const handleParsed = (parsedLeft, parsedRight) => {\n            if ((0, parseUtil_1.isAborted)(parsedLeft) || (0, parseUtil_1.isAborted)(parsedRight)) {\n                return parseUtil_1.INVALID;\n            }\n            const merged = mergeValues(parsedLeft.value, parsedRight.value);\n            if (!merged.valid) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: ZodError_1.ZodIssueCode.invalid_intersection_types,\n                });\n                return parseUtil_1.INVALID;\n            }\n            if ((0, parseUtil_1.isDirty)(parsedLeft) || (0, parseUtil_1.isDirty)(parsedRight)) {\n                status.dirty();\n            }\n            return { status: status.value, value: merged.data };\n        };\n        if (ctx.common.async) {\n            return Promise.all([\n                this._def.left._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n                this._def.right._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n            ]).then(([left, right]) => handleParsed(left, right));\n        }\n        else {\n            return handleParsed(this._def.left._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }), this._def.right._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }));\n        }\n    }\n}\nexports.ZodIntersection = ZodIntersection;\nZodIntersection.create = (left, right, params) => {\n    return new ZodIntersection({\n        left: left,\n        right: right,\n        typeName: ZodFirstPartyTypeKind.ZodIntersection,\n        ...processCreateParams(params),\n    });\n};\nclass ZodTuple extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.array) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (ctx.data.length < this._def.items.length) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.too_small,\n                minimum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            return parseUtil_1.INVALID;\n        }\n        const rest = this._def.rest;\n        if (!rest && ctx.data.length > this._def.items.length) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.too_big,\n                maximum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            status.dirty();\n        }\n        const items = [...ctx.data]\n            .map((item, itemIndex) => {\n            const schema = this._def.items[itemIndex] || this._def.rest;\n            if (!schema)\n                return null;\n            return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));\n        })\n            .filter((x) => !!x);\n        if (ctx.common.async) {\n            return Promise.all(items).then((results) => {\n                return parseUtil_1.ParseStatus.mergeArray(status, results);\n            });\n        }\n        else {\n            return parseUtil_1.ParseStatus.mergeArray(status, items);\n        }\n    }\n    get items() {\n        return this._def.items;\n    }\n    rest(rest) {\n        return new ZodTuple({\n            ...this._def,\n            rest,\n        });\n    }\n}\nexports.ZodTuple = ZodTuple;\nZodTuple.create = (schemas, params) => {\n    if (!Array.isArray(schemas)) {\n        throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");\n    }\n    return new ZodTuple({\n        items: schemas,\n        typeName: ZodFirstPartyTypeKind.ZodTuple,\n        rest: null,\n        ...processCreateParams(params),\n    });\n};\nclass ZodRecord extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.object) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const pairs = [];\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        for (const key in ctx.data) {\n            pairs.push({\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),\n                value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),\n            });\n        }\n        if (ctx.common.async) {\n            return parseUtil_1.ParseStatus.mergeObjectAsync(status, pairs);\n        }\n        else {\n            return parseUtil_1.ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get element() {\n        return this._def.valueType;\n    }\n    static create(first, second, third) {\n        if (second instanceof ZodType) {\n            return new ZodRecord({\n                keyType: first,\n                valueType: second,\n                typeName: ZodFirstPartyTypeKind.ZodRecord,\n                ...processCreateParams(third),\n            });\n        }\n        return new ZodRecord({\n            keyType: ZodString.create(),\n            valueType: first,\n            typeName: ZodFirstPartyTypeKind.ZodRecord,\n            ...processCreateParams(second),\n        });\n    }\n}\nexports.ZodRecord = ZodRecord;\nclass ZodMap extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.map) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.map,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        const pairs = [...ctx.data.entries()].map(([key, value], index) => {\n            return {\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, \"key\"])),\n                value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, \"value\"])),\n            };\n        });\n        if (ctx.common.async) {\n            const finalMap = new Map();\n            return Promise.resolve().then(async () => {\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    const value = await pair.value;\n                    if (key.status === \"aborted\" || value.status === \"aborted\") {\n                        return parseUtil_1.INVALID;\n                    }\n                    if (key.status === \"dirty\" || value.status === \"dirty\") {\n                        status.dirty();\n                    }\n                    finalMap.set(key.value, value.value);\n                }\n                return { status: status.value, value: finalMap };\n            });\n        }\n        else {\n            const finalMap = new Map();\n            for (const pair of pairs) {\n                const key = pair.key;\n                const value = pair.value;\n                if (key.status === \"aborted\" || value.status === \"aborted\") {\n                    return parseUtil_1.INVALID;\n                }\n                if (key.status === \"dirty\" || value.status === \"dirty\") {\n                    status.dirty();\n                }\n                finalMap.set(key.value, value.value);\n            }\n            return { status: status.value, value: finalMap };\n        }\n    }\n}\nexports.ZodMap = ZodMap;\nZodMap.create = (keyType, valueType, params) => {\n    return new ZodMap({\n        valueType,\n        keyType,\n        typeName: ZodFirstPartyTypeKind.ZodMap,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSet extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.set) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.set,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const def = this._def;\n        if (def.minSize !== null) {\n            if (ctx.data.size < def.minSize.value) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: ZodError_1.ZodIssueCode.too_small,\n                    minimum: def.minSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minSize.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxSize !== null) {\n            if (ctx.data.size > def.maxSize.value) {\n                (0, parseUtil_1.addIssueToContext)(ctx, {\n                    code: ZodError_1.ZodIssueCode.too_big,\n                    maximum: def.maxSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxSize.message,\n                });\n                status.dirty();\n            }\n        }\n        const valueType = this._def.valueType;\n        function finalizeSet(elements) {\n            const parsedSet = new Set();\n            for (const element of elements) {\n                if (element.status === \"aborted\")\n                    return parseUtil_1.INVALID;\n                if (element.status === \"dirty\")\n                    status.dirty();\n                parsedSet.add(element.value);\n            }\n            return { status: status.value, value: parsedSet };\n        }\n        const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));\n        if (ctx.common.async) {\n            return Promise.all(elements).then((elements) => finalizeSet(elements));\n        }\n        else {\n            return finalizeSet(elements);\n        }\n    }\n    min(minSize, message) {\n        return new ZodSet({\n            ...this._def,\n            minSize: { value: minSize, message: errorUtil_1.errorUtil.toString(message) },\n        });\n    }\n    max(maxSize, message) {\n        return new ZodSet({\n            ...this._def,\n            maxSize: { value: maxSize, message: errorUtil_1.errorUtil.toString(message) },\n        });\n    }\n    size(size, message) {\n        return this.min(size, message).max(size, message);\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nexports.ZodSet = ZodSet;\nZodSet.create = (valueType, params) => {\n    return new ZodSet({\n        valueType,\n        minSize: null,\n        maxSize: null,\n        typeName: ZodFirstPartyTypeKind.ZodSet,\n        ...processCreateParams(params),\n    });\n};\nclass ZodFunction extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.validate = this.implement;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.function) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.function,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        function makeArgsIssue(args, error) {\n            return (0, parseUtil_1.makeIssue)({\n                data: args,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    (0, errors_1.getErrorMap)(),\n                    errors_1.defaultErrorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodError_1.ZodIssueCode.invalid_arguments,\n                    argumentsError: error,\n                },\n            });\n        }\n        function makeReturnsIssue(returns, error) {\n            return (0, parseUtil_1.makeIssue)({\n                data: returns,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    (0, errors_1.getErrorMap)(),\n                    errors_1.defaultErrorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodError_1.ZodIssueCode.invalid_return_type,\n                    returnTypeError: error,\n                },\n            });\n        }\n        const params = { errorMap: ctx.common.contextualErrorMap };\n        const fn = ctx.data;\n        if (this._def.returns instanceof ZodPromise) {\n            return (0, parseUtil_1.OK)(async (...args) => {\n                const error = new ZodError_1.ZodError([]);\n                const parsedArgs = await this._def.args\n                    .parseAsync(args, params)\n                    .catch((e) => {\n                    error.addIssue(makeArgsIssue(args, e));\n                    throw error;\n                });\n                const result = await fn(...parsedArgs);\n                const parsedReturns = await this._def.returns._def.type\n                    .parseAsync(result, params)\n                    .catch((e) => {\n                    error.addIssue(makeReturnsIssue(result, e));\n                    throw error;\n                });\n                return parsedReturns;\n            });\n        }\n        else {\n            return (0, parseUtil_1.OK)((...args) => {\n                const parsedArgs = this._def.args.safeParse(args, params);\n                if (!parsedArgs.success) {\n                    throw new ZodError_1.ZodError([makeArgsIssue(args, parsedArgs.error)]);\n                }\n                const result = fn(...parsedArgs.data);\n                const parsedReturns = this._def.returns.safeParse(result, params);\n                if (!parsedReturns.success) {\n                    throw new ZodError_1.ZodError([makeReturnsIssue(result, parsedReturns.error)]);\n                }\n                return parsedReturns.data;\n            });\n        }\n    }\n    parameters() {\n        return this._def.args;\n    }\n    returnType() {\n        return this._def.returns;\n    }\n    args(...items) {\n        return new ZodFunction({\n            ...this._def,\n            args: ZodTuple.create(items).rest(ZodUnknown.create()),\n        });\n    }\n    returns(returnType) {\n        return new ZodFunction({\n            ...this._def,\n            returns: returnType,\n        });\n    }\n    implement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    strictImplement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    static create(args, returns, params) {\n        return new ZodFunction({\n            args: (args\n                ? args\n                : ZodTuple.create([]).rest(ZodUnknown.create())),\n            returns: returns || ZodUnknown.create(),\n            typeName: ZodFirstPartyTypeKind.ZodFunction,\n            ...processCreateParams(params),\n        });\n    }\n}\nexports.ZodFunction = ZodFunction;\nclass ZodLazy extends ZodType {\n    get schema() {\n        return this._def.getter();\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const lazySchema = this._def.getter();\n        return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });\n    }\n}\nexports.ZodLazy = ZodLazy;\nZodLazy.create = (getter, params) => {\n    return new ZodLazy({\n        getter: getter,\n        typeName: ZodFirstPartyTypeKind.ZodLazy,\n        ...processCreateParams(params),\n    });\n};\nclass ZodLiteral extends ZodType {\n    _parse(input) {\n        if (input.data !== this._def.value) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                received: ctx.data,\n                code: ZodError_1.ZodIssueCode.invalid_literal,\n                expected: this._def.value,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n    get value() {\n        return this._def.value;\n    }\n}\nexports.ZodLiteral = ZodLiteral;\nZodLiteral.create = (value, params) => {\n    return new ZodLiteral({\n        value: value,\n        typeName: ZodFirstPartyTypeKind.ZodLiteral,\n        ...processCreateParams(params),\n    });\n};\nfunction createZodEnum(values, params) {\n    return new ZodEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodEnum,\n        ...processCreateParams(params),\n    });\n}\nclass ZodEnum extends ZodType {\n    _parse(input) {\n        if (typeof input.data !== \"string\") {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                expected: util_1.util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodError_1.ZodIssueCode.invalid_type,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (this._def.values.indexOf(input.data) === -1) {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                received: ctx.data,\n                code: ZodError_1.ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n    get options() {\n        return this._def.values;\n    }\n    get enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Values() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    extract(values) {\n        return ZodEnum.create(values);\n    }\n    exclude(values) {\n        return ZodEnum.create(this.options.filter((opt) => !values.includes(opt)));\n    }\n}\nexports.ZodEnum = ZodEnum;\nZodEnum.create = createZodEnum;\nclass ZodNativeEnum extends ZodType {\n    _parse(input) {\n        const nativeEnumValues = util_1.util.getValidEnumValues(this._def.values);\n        const ctx = this._getOrReturnCtx(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.string &&\n            ctx.parsedType !== util_1.ZodParsedType.number) {\n            const expectedValues = util_1.util.objectValues(nativeEnumValues);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                expected: util_1.util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodError_1.ZodIssueCode.invalid_type,\n            });\n            return parseUtil_1.INVALID;\n        }\n        if (nativeEnumValues.indexOf(input.data) === -1) {\n            const expectedValues = util_1.util.objectValues(nativeEnumValues);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                received: ctx.data,\n                code: ZodError_1.ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return (0, parseUtil_1.OK)(input.data);\n    }\n    get enum() {\n        return this._def.values;\n    }\n}\nexports.ZodNativeEnum = ZodNativeEnum;\nZodNativeEnum.create = (values, params) => {\n    return new ZodNativeEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodNativeEnum,\n        ...processCreateParams(params),\n    });\n};\nclass ZodPromise extends ZodType {\n    unwrap() {\n        return this._def.type;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== util_1.ZodParsedType.promise &&\n            ctx.common.async === false) {\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.promise,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        const promisified = ctx.parsedType === util_1.ZodParsedType.promise\n            ? ctx.data\n            : Promise.resolve(ctx.data);\n        return (0, parseUtil_1.OK)(promisified.then((data) => {\n            return this._def.type.parseAsync(data, {\n                path: ctx.path,\n                errorMap: ctx.common.contextualErrorMap,\n            });\n        }));\n    }\n}\nexports.ZodPromise = ZodPromise;\nZodPromise.create = (schema, params) => {\n    return new ZodPromise({\n        type: schema,\n        typeName: ZodFirstPartyTypeKind.ZodPromise,\n        ...processCreateParams(params),\n    });\n};\nclass ZodEffects extends ZodType {\n    innerType() {\n        return this._def.schema;\n    }\n    sourceType() {\n        return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects\n            ? this._def.schema.sourceType()\n            : this._def.schema;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const effect = this._def.effect || null;\n        if (effect.type === \"preprocess\") {\n            const processed = effect.transform(ctx.data);\n            if (ctx.common.async) {\n                return Promise.resolve(processed).then((processed) => {\n                    return this._def.schema._parseAsync({\n                        data: processed,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                });\n            }\n            else {\n                return this._def.schema._parseSync({\n                    data: processed,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n            }\n        }\n        const checkCtx = {\n            addIssue: (arg) => {\n                (0, parseUtil_1.addIssueToContext)(ctx, arg);\n                if (arg.fatal) {\n                    status.abort();\n                }\n                else {\n                    status.dirty();\n                }\n            },\n            get path() {\n                return ctx.path;\n            },\n        };\n        checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);\n        if (effect.type === \"refinement\") {\n            const executeRefinement = (acc) => {\n                const result = effect.refinement(acc, checkCtx);\n                if (ctx.common.async) {\n                    return Promise.resolve(result);\n                }\n                if (result instanceof Promise) {\n                    throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");\n                }\n                return acc;\n            };\n            if (ctx.common.async === false) {\n                const inner = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inner.status === \"aborted\")\n                    return parseUtil_1.INVALID;\n                if (inner.status === \"dirty\")\n                    status.dirty();\n                executeRefinement(inner.value);\n                return { status: status.value, value: inner.value };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((inner) => {\n                    if (inner.status === \"aborted\")\n                        return parseUtil_1.INVALID;\n                    if (inner.status === \"dirty\")\n                        status.dirty();\n                    return executeRefinement(inner.value).then(() => {\n                        return { status: status.value, value: inner.value };\n                    });\n                });\n            }\n        }\n        if (effect.type === \"transform\") {\n            if (ctx.common.async === false) {\n                const base = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (!(0, parseUtil_1.isValid)(base))\n                    return base;\n                const result = effect.transform(base.value, checkCtx);\n                if (result instanceof Promise) {\n                    throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);\n                }\n                return { status: status.value, value: result };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((base) => {\n                    if (!(0, parseUtil_1.isValid)(base))\n                        return base;\n                    return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));\n                });\n            }\n        }\n        util_1.util.assertNever(effect);\n    }\n}\nexports.ZodEffects = ZodEffects;\nexports.ZodTransformer = ZodEffects;\nZodEffects.create = (schema, effect, params) => {\n    return new ZodEffects({\n        schema,\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        effect,\n        ...processCreateParams(params),\n    });\n};\nZodEffects.createWithPreprocess = (preprocess, schema, params) => {\n    return new ZodEffects({\n        schema,\n        effect: { type: \"preprocess\", transform: preprocess },\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        ...processCreateParams(params),\n    });\n};\nclass ZodOptional extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === util_1.ZodParsedType.undefined) {\n            return (0, parseUtil_1.OK)(undefined);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nexports.ZodOptional = ZodOptional;\nZodOptional.create = (type, params) => {\n    return new ZodOptional({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodOptional,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNullable extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === util_1.ZodParsedType.null) {\n            return (0, parseUtil_1.OK)(null);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nexports.ZodNullable = ZodNullable;\nZodNullable.create = (type, params) => {\n    return new ZodNullable({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodNullable,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDefault extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        let data = ctx.data;\n        if (ctx.parsedType === util_1.ZodParsedType.undefined) {\n            data = this._def.defaultValue();\n        }\n        return this._def.innerType._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    removeDefault() {\n        return this._def.innerType;\n    }\n}\nexports.ZodDefault = ZodDefault;\nZodDefault.create = (type, params) => {\n    return new ZodDefault({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodDefault,\n        defaultValue: typeof params.default === \"function\"\n            ? params.default\n            : () => params.default,\n        ...processCreateParams(params),\n    });\n};\nclass ZodCatch extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const newCtx = {\n            ...ctx,\n            common: {\n                ...ctx.common,\n                issues: [],\n            },\n        };\n        const result = this._def.innerType._parse({\n            data: newCtx.data,\n            path: newCtx.path,\n            parent: {\n                ...newCtx,\n            },\n        });\n        if ((0, parseUtil_1.isAsync)(result)) {\n            return result.then((result) => {\n                return {\n                    status: \"valid\",\n                    value: result.status === \"valid\"\n                        ? result.value\n                        : this._def.catchValue({\n                            get error() {\n                                return new ZodError_1.ZodError(newCtx.common.issues);\n                            },\n                            input: newCtx.data,\n                        }),\n                };\n            });\n        }\n        else {\n            return {\n                status: \"valid\",\n                value: result.status === \"valid\"\n                    ? result.value\n                    : this._def.catchValue({\n                        get error() {\n                            return new ZodError_1.ZodError(newCtx.common.issues);\n                        },\n                        input: newCtx.data,\n                    }),\n            };\n        }\n    }\n    removeCatch() {\n        return this._def.innerType;\n    }\n}\nexports.ZodCatch = ZodCatch;\nZodCatch.create = (type, params) => {\n    return new ZodCatch({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodCatch,\n        catchValue: typeof params.catch === \"function\" ? params.catch : () => params.catch,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNaN extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== util_1.ZodParsedType.nan) {\n            const ctx = this._getOrReturnCtx(input);\n            (0, parseUtil_1.addIssueToContext)(ctx, {\n                code: ZodError_1.ZodIssueCode.invalid_type,\n                expected: util_1.ZodParsedType.nan,\n                received: ctx.parsedType,\n            });\n            return parseUtil_1.INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n}\nexports.ZodNaN = ZodNaN;\nZodNaN.create = (params) => {\n    return new ZodNaN({\n        typeName: ZodFirstPartyTypeKind.ZodNaN,\n        ...processCreateParams(params),\n    });\n};\nexports.BRAND = Symbol(\"zod_brand\");\nclass ZodBranded extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const data = ctx.data;\n        return this._def.type._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    unwrap() {\n        return this._def.type;\n    }\n}\nexports.ZodBranded = ZodBranded;\nclass ZodPipeline extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.common.async) {\n            const handleAsync = async () => {\n                const inResult = await this._def.in._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inResult.status === \"aborted\")\n                    return parseUtil_1.INVALID;\n                if (inResult.status === \"dirty\") {\n                    status.dirty();\n                    return (0, parseUtil_1.DIRTY)(inResult.value);\n                }\n                else {\n                    return this._def.out._parseAsync({\n                        data: inResult.value,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                }\n            };\n            return handleAsync();\n        }\n        else {\n            const inResult = this._def.in._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n            if (inResult.status === \"aborted\")\n                return parseUtil_1.INVALID;\n            if (inResult.status === \"dirty\") {\n                status.dirty();\n                return {\n                    status: \"dirty\",\n                    value: inResult.value,\n                };\n            }\n            else {\n                return this._def.out._parseSync({\n                    data: inResult.value,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n            }\n        }\n    }\n    static create(a, b) {\n        return new ZodPipeline({\n            in: a,\n            out: b,\n            typeName: ZodFirstPartyTypeKind.ZodPipeline,\n        });\n    }\n}\nexports.ZodPipeline = ZodPipeline;\nconst custom = (check, params = {}, fatal) => {\n    if (check)\n        return ZodAny.create().superRefine((data, ctx) => {\n            var _a, _b;\n            if (!check(data)) {\n                const p = typeof params === \"function\"\n                    ? params(data)\n                    : typeof params === \"string\"\n                        ? { message: params }\n                        : params;\n                const _fatal = (_b = (_a = p.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;\n                const p2 = typeof p === \"string\" ? { message: p } : p;\n                ctx.addIssue({ code: \"custom\", ...p2, fatal: _fatal });\n            }\n        });\n    return ZodAny.create();\n};\nexports.custom = custom;\nexports.late = {\n    object: ZodObject.lazycreate,\n};\nvar ZodFirstPartyTypeKind;\n(function (ZodFirstPartyTypeKind) {\n    ZodFirstPartyTypeKind[\"ZodString\"] = \"ZodString\";\n    ZodFirstPartyTypeKind[\"ZodNumber\"] = \"ZodNumber\";\n    ZodFirstPartyTypeKind[\"ZodNaN\"] = \"ZodNaN\";\n    ZodFirstPartyTypeKind[\"ZodBigInt\"] = \"ZodBigInt\";\n    ZodFirstPartyTypeKind[\"ZodBoolean\"] = \"ZodBoolean\";\n    ZodFirstPartyTypeKind[\"ZodDate\"] = \"ZodDate\";\n    ZodFirstPartyTypeKind[\"ZodSymbol\"] = \"ZodSymbol\";\n    ZodFirstPartyTypeKind[\"ZodUndefined\"] = \"ZodUndefined\";\n    ZodFirstPartyTypeKind[\"ZodNull\"] = \"ZodNull\";\n    ZodFirstPartyTypeKind[\"ZodAny\"] = \"ZodAny\";\n    ZodFirstPartyTypeKind[\"ZodUnknown\"] = \"ZodUnknown\";\n    ZodFirstPartyTypeKind[\"ZodNever\"] = \"ZodNever\";\n    ZodFirstPartyTypeKind[\"ZodVoid\"] = \"ZodVoid\";\n    ZodFirstPartyTypeKind[\"ZodArray\"] = \"ZodArray\";\n    ZodFirstPartyTypeKind[\"ZodObject\"] = \"ZodObject\";\n    ZodFirstPartyTypeKind[\"ZodUnion\"] = \"ZodUnion\";\n    ZodFirstPartyTypeKind[\"ZodDiscriminatedUnion\"] = \"ZodDiscriminatedUnion\";\n    ZodFirstPartyTypeKind[\"ZodIntersection\"] = \"ZodIntersection\";\n    ZodFirstPartyTypeKind[\"ZodTuple\"] = \"ZodTuple\";\n    ZodFirstPartyTypeKind[\"ZodRecord\"] = \"ZodRecord\";\n    ZodFirstPartyTypeKind[\"ZodMap\"] = \"ZodMap\";\n    ZodFirstPartyTypeKind[\"ZodSet\"] = \"ZodSet\";\n    ZodFirstPartyTypeKind[\"ZodFunction\"] = \"ZodFunction\";\n    ZodFirstPartyTypeKind[\"ZodLazy\"] = \"ZodLazy\";\n    ZodFirstPartyTypeKind[\"ZodLiteral\"] = \"ZodLiteral\";\n    ZodFirstPartyTypeKind[\"ZodEnum\"] = \"ZodEnum\";\n    ZodFirstPartyTypeKind[\"ZodEffects\"] = \"ZodEffects\";\n    ZodFirstPartyTypeKind[\"ZodNativeEnum\"] = \"ZodNativeEnum\";\n    ZodFirstPartyTypeKind[\"ZodOptional\"] = \"ZodOptional\";\n    ZodFirstPartyTypeKind[\"ZodNullable\"] = \"ZodNullable\";\n    ZodFirstPartyTypeKind[\"ZodDefault\"] = \"ZodDefault\";\n    ZodFirstPartyTypeKind[\"ZodCatch\"] = \"ZodCatch\";\n    ZodFirstPartyTypeKind[\"ZodPromise\"] = \"ZodPromise\";\n    ZodFirstPartyTypeKind[\"ZodBranded\"] = \"ZodBranded\";\n    ZodFirstPartyTypeKind[\"ZodPipeline\"] = \"ZodPipeline\";\n})(ZodFirstPartyTypeKind = exports.ZodFirstPartyTypeKind || (exports.ZodFirstPartyTypeKind = {}));\nclass Class {\n    constructor(..._) { }\n}\nconst instanceOfType = (cls, params = {\n    message: `Input not instance of ${cls.name}`,\n}) => (0, exports.custom)((data) => data instanceof cls, params);\nexports.instanceof = instanceOfType;\nconst stringType = ZodString.create;\nexports.string = stringType;\nconst numberType = ZodNumber.create;\nexports.number = numberType;\nconst nanType = ZodNaN.create;\nexports.nan = nanType;\nconst bigIntType = ZodBigInt.create;\nexports.bigint = bigIntType;\nconst booleanType = ZodBoolean.create;\nexports.boolean = booleanType;\nconst dateType = ZodDate.create;\nexports.date = dateType;\nconst symbolType = ZodSymbol.create;\nexports.symbol = symbolType;\nconst undefinedType = ZodUndefined.create;\nexports.undefined = undefinedType;\nconst nullType = ZodNull.create;\nexports.null = nullType;\nconst anyType = ZodAny.create;\nexports.any = anyType;\nconst unknownType = ZodUnknown.create;\nexports.unknown = unknownType;\nconst neverType = ZodNever.create;\nexports.never = neverType;\nconst voidType = ZodVoid.create;\nexports.void = voidType;\nconst arrayType = ZodArray.create;\nexports.array = arrayType;\nconst objectType = ZodObject.create;\nexports.object = objectType;\nconst strictObjectType = ZodObject.strictCreate;\nexports.strictObject = strictObjectType;\nconst unionType = ZodUnion.create;\nexports.union = unionType;\nconst discriminatedUnionType = ZodDiscriminatedUnion.create;\nexports.discriminatedUnion = discriminatedUnionType;\nconst intersectionType = ZodIntersection.create;\nexports.intersection = intersectionType;\nconst tupleType = ZodTuple.create;\nexports.tuple = tupleType;\nconst recordType = ZodRecord.create;\nexports.record = recordType;\nconst mapType = ZodMap.create;\nexports.map = mapType;\nconst setType = ZodSet.create;\nexports.set = setType;\nconst functionType = ZodFunction.create;\nexports.function = functionType;\nconst lazyType = ZodLazy.create;\nexports.lazy = lazyType;\nconst literalType = ZodLiteral.create;\nexports.literal = literalType;\nconst enumType = ZodEnum.create;\nexports.enum = enumType;\nconst nativeEnumType = ZodNativeEnum.create;\nexports.nativeEnum = nativeEnumType;\nconst promiseType = ZodPromise.create;\nexports.promise = promiseType;\nconst effectsType = ZodEffects.create;\nexports.effect = effectsType;\nexports.transformer = effectsType;\nconst optionalType = ZodOptional.create;\nexports.optional = optionalType;\nconst nullableType = ZodNullable.create;\nexports.nullable = nullableType;\nconst preprocessType = ZodEffects.createWithPreprocess;\nexports.preprocess = preprocessType;\nconst pipelineType = ZodPipeline.create;\nexports.pipeline = pipelineType;\nconst ostring = () => stringType().optional();\nexports.ostring = ostring;\nconst onumber = () => numberType().optional();\nexports.onumber = onumber;\nconst oboolean = () => booleanType().optional();\nexports.oboolean = oboolean;\nexports.coerce = {\n    string: ((arg) => ZodString.create({ ...arg, coerce: true })),\n    number: ((arg) => ZodNumber.create({ ...arg, coerce: true })),\n    boolean: ((arg) => ZodBoolean.create({\n        ...arg,\n        coerce: true,\n    })),\n    bigint: ((arg) => ZodBigInt.create({ ...arg, coerce: true })),\n    date: ((arg) => ZodDate.create({ ...arg, coerce: true })),\n};\nexports.NEVER = parseUtil_1.INVALID;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./errors\"), exports);\n__exportStar(require(\"./helpers/parseUtil\"), exports);\n__exportStar(require(\"./helpers/typeAliases\"), exports);\n__exportStar(require(\"./helpers/util\"), exports);\n__exportStar(require(\"./types\"), exports);\n__exportStar(require(\"./ZodError\"), exports);\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.z = void 0;\nconst z = __importStar(require(\"./external\"));\nexports.z = z;\n__exportStar(require(\"./external\"), exports);\nexports.default = z;\n", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CDP = exports.Network = exports.Log = exports.BrowsingContext = exports.Script = exports.Message = void 0;\nvar Message;\n(function (Message) {\n    // keep-sorted end;\n    let ErrorCode;\n    (function (ErrorCode) {\n        // keep-sorted start\n        ErrorCode[\"InvalidArgument\"] = \"invalid argument\";\n        ErrorCode[\"InvalidSessionId\"] = \"invalid session id\";\n        ErrorCode[\"NoSuchAlert\"] = \"no such alert\";\n        ErrorCode[\"NoSuchFrame\"] = \"no such frame\";\n        ErrorCode[\"NoSuchHandle\"] = \"no such handle\";\n        ErrorCode[\"NoSuchNode\"] = \"no such node\";\n        ErrorCode[\"NoSuchScript\"] = \"no such script\";\n        ErrorCode[\"SessionNotCreated\"] = \"session not created\";\n        ErrorCode[\"UnknownCommand\"] = \"unknown command\";\n        ErrorCode[\"UnknownError\"] = \"unknown error\";\n        ErrorCode[\"UnsupportedOperation\"] = \"unsupported operation\";\n        // keep-sorted end\n    })(ErrorCode = Message.ErrorCode || (Message.ErrorCode = {}));\n    class ErrorResponse {\n        error;\n        message;\n        stacktrace;\n        constructor(error, message, stacktrace) {\n            this.error = error;\n            this.message = message;\n            this.stacktrace = stacktrace;\n        }\n        toErrorResponse(commandId) {\n            return {\n                id: commandId,\n                error: this.error,\n                message: this.message,\n                stacktrace: this.stacktrace,\n            };\n        }\n    }\n    Message.ErrorResponse = ErrorResponse;\n    class InvalidArgumentException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.InvalidArgument, message, stacktrace);\n        }\n    }\n    Message.InvalidArgumentException = InvalidArgumentException;\n    class NoSuchHandleException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.NoSuchHandle, message, stacktrace);\n        }\n    }\n    Message.NoSuchHandleException = NoSuchHandleException;\n    class InvalidSessionIdException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.InvalidSessionId, message, stacktrace);\n        }\n    }\n    Message.InvalidSessionIdException = InvalidSessionIdException;\n    class NoSuchAlertException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.NoSuchAlert, message, stacktrace);\n        }\n    }\n    Message.NoSuchAlertException = NoSuchAlertException;\n    class NoSuchFrameException extends ErrorResponse {\n        constructor(message) {\n            super(ErrorCode.NoSuchFrame, message);\n        }\n    }\n    Message.NoSuchFrameException = NoSuchFrameException;\n    class NoSuchNodeException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.NoSuchNode, message, stacktrace);\n        }\n    }\n    Message.NoSuchNodeException = NoSuchNodeException;\n    class NoSuchScriptException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.NoSuchScript, message, stacktrace);\n        }\n    }\n    Message.NoSuchScriptException = NoSuchScriptException;\n    class SessionNotCreatedException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.SessionNotCreated, message, stacktrace);\n        }\n    }\n    Message.SessionNotCreatedException = SessionNotCreatedException;\n    class UnknownCommandException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.UnknownCommand, message, stacktrace);\n        }\n    }\n    Message.UnknownCommandException = UnknownCommandException;\n    class UnknownErrorException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.UnknownError, message, stacktrace);\n        }\n    }\n    Message.UnknownErrorException = UnknownErrorException;\n    class UnsupportedOperationException extends ErrorResponse {\n        constructor(message, stacktrace) {\n            super(ErrorCode.UnsupportedOperation, message, stacktrace);\n        }\n    }\n    Message.UnsupportedOperationException = UnsupportedOperationException;\n})(Message = exports.Message || (exports.Message = {}));\n/** @see https://w3c.github.io/webdriver-bidi/#module-script */\nvar Script;\n(function (Script) {\n    let EventNames;\n    (function (EventNames) {\n        EventNames[\"MessageEvent\"] = \"script.message\";\n    })(EventNames = Script.EventNames || (Script.EventNames = {}));\n    Script.AllEvents = 'script';\n})(Script = exports.Script || (exports.Script = {}));\n// https://w3c.github.io/webdriver-bidi/#module-browsingContext\nvar BrowsingContext;\n(function (BrowsingContext) {\n    let EventNames;\n    (function (EventNames) {\n        EventNames[\"LoadEvent\"] = \"browsingContext.load\";\n        EventNames[\"DomContentLoadedEvent\"] = \"browsingContext.domContentLoaded\";\n        EventNames[\"ContextCreatedEvent\"] = \"browsingContext.contextCreated\";\n        EventNames[\"ContextDestroyedEvent\"] = \"browsingContext.contextDestroyed\";\n    })(EventNames = BrowsingContext.EventNames || (BrowsingContext.EventNames = {}));\n    BrowsingContext.AllEvents = 'browsingContext';\n})(BrowsingContext = exports.BrowsingContext || (exports.BrowsingContext = {}));\n/** @see https://w3c.github.io/webdriver-bidi/#module-log */\nvar Log;\n(function (Log) {\n    Log.AllEvents = 'log';\n    let EventNames;\n    (function (EventNames) {\n        EventNames[\"LogEntryAddedEvent\"] = \"log.entryAdded\";\n    })(EventNames = Log.EventNames || (Log.EventNames = {}));\n})(Log = exports.Log || (exports.Log = {}));\nvar Network;\n(function (Network) {\n    Network.AllEvents = 'network';\n    let EventNames;\n    (function (EventNames) {\n        EventNames[\"BeforeRequestSentEvent\"] = \"network.beforeRequestSent\";\n        EventNames[\"ResponseCompletedEvent\"] = \"network.responseCompleted\";\n        EventNames[\"FetchErrorEvent\"] = \"network.fetchError\";\n    })(EventNames = Network.EventNames || (Network.EventNames = {}));\n})(Network = exports.Network || (exports.Network = {}));\nvar CDP;\n(function (CDP) {\n    CDP.AllEvents = 'cdp';\n    let EventNames;\n    (function (EventNames) {\n        EventNames[\"EventReceivedEvent\"] = \"cdp.eventReceived\";\n    })(EventNames = CDP.EventNames || (CDP.EventNames = {}));\n})(CDP = exports.CDP || (exports.CDP = {}));\n//# sourceMappingURL=protocol.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Session = exports.CDP = exports.BrowsingContext = exports.Script = exports.CommonDataTypes = exports.parseObject = void 0;\n/**\n * @fileoverview Provides parsing and validator for WebDriver BiDi protocol.\n * Parser types should match the `../protocol` types.\n */\nconst zod_1 = require(\"zod\");\nconst protocol_js_1 = require(\"../protocol/protocol.js\");\nconst MAX_INT = 9007199254740991;\nfunction parseObject(obj, schema) {\n    const parseResult = schema.safeParse(obj);\n    if (parseResult.success) {\n        return parseResult.data;\n    }\n    const errorMessage = parseResult.error.errors\n        .map((e) => `${e.message} in ` +\n        `${e.path.map((p) => JSON.stringify(p)).join('/')}.`)\n        .join(' ');\n    throw new protocol_js_1.Message.InvalidArgumentException(errorMessage);\n}\nexports.parseObject = parseObject;\nvar CommonDataTypes;\n(function (CommonDataTypes) {\n    CommonDataTypes.SharedReferenceSchema = zod_1.z.object({\n        sharedId: zod_1.z.string().min(1),\n    });\n    CommonDataTypes.RemoteReferenceSchema = zod_1.z.object({\n        handle: zod_1.z.string().min(1),\n    });\n    // UndefinedValue = {\n    //   type: \"undefined\",\n    // }\n    const UndefinedValueSchema = zod_1.z.object({ type: zod_1.z.literal('undefined') });\n    // NullValue = {\n    //   type: \"null\",\n    // }\n    const NullValueSchema = zod_1.z.object({ type: zod_1.z.literal('null') });\n    // StringValue = {\n    //   type: \"string\",\n    //   value: text,\n    // }\n    const StringValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('string'),\n        value: zod_1.z.string(),\n    });\n    // SpecialNumber = \"NaN\" / \"-0\" / \"Infinity\" / \"-Infinity\";\n    const SpecialNumberSchema = zod_1.z.enum(['NaN', '-0', 'Infinity', '-Infinity']);\n    // NumberValue = {\n    //   type: \"number\",\n    //   value: number / SpecialNumber,\n    // }\n    const NumberValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('number'),\n        value: zod_1.z.union([SpecialNumberSchema, zod_1.z.number()]),\n    });\n    // BooleanValue = {\n    //   type: \"boolean\",\n    //   value: bool,\n    // }\n    const BooleanValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('boolean'),\n        value: zod_1.z.boolean(),\n    });\n    // BigIntValue = {\n    //   type: \"bigint\",\n    //   value: text,\n    // }\n    const BigIntValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('bigint'),\n        value: zod_1.z.string(),\n    });\n    const PrimitiveProtocolValueSchema = zod_1.z.union([\n        UndefinedValueSchema,\n        NullValueSchema,\n        StringValueSchema,\n        NumberValueSchema,\n        BooleanValueSchema,\n        BigIntValueSchema,\n    ]);\n    CommonDataTypes.LocalValueSchema = zod_1.z.lazy(() => zod_1.z.union([\n        PrimitiveProtocolValueSchema,\n        ArrayLocalValueSchema,\n        DateLocalValueSchema,\n        MapLocalValueSchema,\n        ObjectLocalValueSchema,\n        RegExpLocalValueSchema,\n        SetLocalValueSchema,\n    ]));\n    // Order is important, as `parse` is processed in the same order.\n    // `SharedReferenceSchema`->`RemoteReferenceSchema`->`LocalValueSchema`.\n    const LocalOrRemoteValueSchema = zod_1.z.union([\n        CommonDataTypes.SharedReferenceSchema,\n        CommonDataTypes.RemoteReferenceSchema,\n        CommonDataTypes.LocalValueSchema,\n    ]);\n    // ListLocalValue = [*LocalValue];\n    const ListLocalValueSchema = zod_1.z.array(LocalOrRemoteValueSchema);\n    // ArrayLocalValue = {\n    //   type: \"array\",\n    //   value: ListLocalValue,\n    // }\n    const ArrayLocalValueSchema = zod_1.z.lazy(() => zod_1.z.object({\n        type: zod_1.z.literal('array'),\n        value: ListLocalValueSchema,\n    }));\n    // DateLocalValue = {\n    //   type: \"date\",\n    //   value: text\n    // }\n    const DateLocalValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('date'),\n        value: zod_1.z.string().min(1),\n    });\n    // MappingLocalValue = [*[(LocalValue / text), LocalValue]];\n    const MappingLocalValueSchema = zod_1.z.lazy(() => zod_1.z.tuple([\n        zod_1.z.union([zod_1.z.string(), LocalOrRemoteValueSchema]),\n        LocalOrRemoteValueSchema,\n    ]));\n    // MapLocalValue = {\n    //   type: \"map\",\n    //   value: MappingLocalValue,\n    // }\n    const MapLocalValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('map'),\n        value: zod_1.z.array(MappingLocalValueSchema),\n    });\n    // ObjectLocalValue = {\n    //   type: \"object\",\n    //   value: MappingLocalValue,\n    // }\n    const ObjectLocalValueSchema = zod_1.z.object({\n        type: zod_1.z.literal('object'),\n        value: zod_1.z.array(MappingLocalValueSchema),\n    });\n    // RegExpLocalValue = {\n    //   type: \"regexp\",\n    //   value: RegExpValue,\n    // }\n    const RegExpLocalValueSchema = zod_1.z.lazy(() => zod_1.z.object({\n        type: zod_1.z.literal('regexp'),\n        value: zod_1.z.object({\n            pattern: zod_1.z.string(),\n            flags: zod_1.z.string().optional(),\n        }),\n    }));\n    // SetLocalValue = {\n    //   type: \"set\",\n    //   value: ListLocalValue,\n    // }\n    const SetLocalValueSchema = zod_1.z.lazy(() => zod_1.z.object({\n        type: zod_1.z.literal('set'),\n        value: ListLocalValueSchema,\n    }));\n    // BrowsingContext = text;\n    CommonDataTypes.BrowsingContextSchema = zod_1.z.string();\n    CommonDataTypes.MaxDepthSchema = zod_1.z.number().int().nonnegative().max(MAX_INT);\n})(CommonDataTypes = exports.CommonDataTypes || (exports.CommonDataTypes = {}));\n/** @see https://w3c.github.io/webdriver-bidi/#module-script */\nvar Script;\n(function (Script) {\n    const RealmTypeSchema = zod_1.z.enum([\n        'window',\n        'dedicated-worker',\n        'shared-worker',\n        'service-worker',\n        'worker',\n        'paint-worklet',\n        'audio-worklet',\n        'worklet',\n    ]);\n    Script.GetRealmsParametersSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema.optional(),\n        type: RealmTypeSchema.optional(),\n    });\n    function parseGetRealmsParams(params) {\n        return parseObject(params, Script.GetRealmsParametersSchema);\n    }\n    Script.parseGetRealmsParams = parseGetRealmsParams;\n    // ContextTarget = {\n    //   context: BrowsingContext,\n    //   ?sandbox: text\n    // }\n    const ContextTargetSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n        sandbox: zod_1.z.string().optional(),\n    });\n    // RealmTarget = {realm: Realm};\n    const RealmTargetSchema = zod_1.z.object({\n        realm: zod_1.z.string().min(1),\n    });\n    // Target = (\n    //   RealmTarget //\n    //   ContextTarget\n    // );\n    // Order is important, as `parse` is processed in the same order.\n    // `RealmTargetSchema` has higher priority.\n    const TargetSchema = zod_1.z.union([RealmTargetSchema, ContextTargetSchema]);\n    // ResultOwnership = \"root\" / \"none\"\n    const ResultOwnershipSchema = zod_1.z.enum(['root', 'none']);\n    // ScriptEvaluateParameters = {\n    //   expression: text;\n    //   target: Target;\n    //   ?awaitPromise: bool;\n    //   ?resultOwnership: ResultOwnership;\n    // }\n    const EvaluateParametersSchema = zod_1.z.object({\n        expression: zod_1.z.string(),\n        awaitPromise: zod_1.z.boolean(),\n        target: TargetSchema,\n        resultOwnership: ResultOwnershipSchema.optional(),\n    });\n    function parseEvaluateParams(params) {\n        return parseObject(params, EvaluateParametersSchema);\n    }\n    Script.parseEvaluateParams = parseEvaluateParams;\n    // DisownParameters = {\n    //   handles: [Handle]\n    //   target: script.Target;\n    // }\n    const DisownParametersSchema = zod_1.z.object({\n        target: TargetSchema,\n        handles: zod_1.z.array(zod_1.z.string()),\n    });\n    function parseDisownParams(params) {\n        return parseObject(params, DisownParametersSchema);\n    }\n    Script.parseDisownParams = parseDisownParams;\n    Script.PreloadScriptSchema = zod_1.z.string();\n    Script.AddPreloadScriptParametersSchema = zod_1.z.object({\n        expression: zod_1.z.string(),\n        sandbox: zod_1.z.string().optional(),\n        context: CommonDataTypes.BrowsingContextSchema.optional(),\n    });\n    function parseAddPreloadScriptParams(params) {\n        return parseObject(params, Script.AddPreloadScriptParametersSchema);\n    }\n    Script.parseAddPreloadScriptParams = parseAddPreloadScriptParams;\n    Script.RemovePreloadScriptParametersSchema = zod_1.z.object({\n        script: Script.PreloadScriptSchema,\n    });\n    function parseRemovePreloadScriptParams(params) {\n        return parseObject(params, Script.RemovePreloadScriptParametersSchema);\n    }\n    Script.parseRemovePreloadScriptParams = parseRemovePreloadScriptParams;\n    const ChannelIdSchema = zod_1.z.string();\n    const ChannelPropertiesSchema = zod_1.z.object({\n        channel: ChannelIdSchema,\n        // TODO(#294): maxDepth: CommonDataTypes.MaxDepthSchema.optional(),\n        // See: https://github.com/w3c/webdriver-bidi/pull/361/files#r1141961142\n        maxDepth: zod_1.z.number().int().min(1).max(1).optional(),\n        ownership: ResultOwnershipSchema.optional(),\n    });\n    Script.ChannelSchema = zod_1.z.object({\n        type: zod_1.z.literal('channel'),\n        value: ChannelPropertiesSchema,\n    });\n    // ArgumentValue = (\n    //   RemoteReference //\n    //   LocalValue //\n    //   script.Channel\n    // );\n    const ArgumentValueSchema = zod_1.z.union([\n        CommonDataTypes.RemoteReferenceSchema,\n        CommonDataTypes.SharedReferenceSchema,\n        CommonDataTypes.LocalValueSchema,\n        Script.ChannelSchema,\n    ]);\n    // CallFunctionParameters = {\n    //   functionDeclaration: text;\n    //   awaitPromise: bool;\n    //   target: script.Target;\n    //   ?arguments: [*script.ArgumentValue];\n    //   ?this: script.ArgumentValue;\n    //   ?resultOwnership: script.ResultOwnership;\n    // }\n    const CallFunctionParametersSchema = zod_1.z.object({\n        functionDeclaration: zod_1.z.string(),\n        target: TargetSchema,\n        arguments: zod_1.z.array(ArgumentValueSchema).optional(),\n        this: ArgumentValueSchema.optional(),\n        awaitPromise: zod_1.z.boolean(),\n        resultOwnership: ResultOwnershipSchema.optional(),\n    });\n    function parseCallFunctionParams(params) {\n        return parseObject(params, CallFunctionParametersSchema);\n    }\n    Script.parseCallFunctionParams = parseCallFunctionParams;\n})(Script = exports.Script || (exports.Script = {}));\n/** @see https://w3c.github.io/webdriver-bidi/#module-browsingContext */\nvar BrowsingContext;\n(function (BrowsingContext) {\n    // GetTreeParameters = {\n    //   ?maxDepth: js-uint,\n    //   ?root: browsingContext.BrowsingContext,\n    // }\n    const GetTreeParametersSchema = zod_1.z.object({\n        maxDepth: CommonDataTypes.MaxDepthSchema.optional(),\n        root: CommonDataTypes.BrowsingContextSchema.optional(),\n    });\n    function parseGetTreeParams(params) {\n        return parseObject(params, GetTreeParametersSchema);\n    }\n    BrowsingContext.parseGetTreeParams = parseGetTreeParams;\n    // ReadinessState = \"none\" / \"interactive\" / \"complete\"\n    const ReadinessStateSchema = zod_1.z.enum(['none', 'interactive', 'complete']);\n    // BrowsingContextNavigateParameters = {\n    //   context: BrowsingContext,\n    //   url: text,\n    //   ?wait: ReadinessState,\n    // }\n    // ReadinessState = \"none\" / \"interactive\" / \"complete\"\n    const NavigateParametersSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n        url: zod_1.z.string().url(),\n        wait: ReadinessStateSchema.optional(),\n    });\n    function parseNavigateParams(params) {\n        return parseObject(params, NavigateParametersSchema);\n    }\n    BrowsingContext.parseNavigateParams = parseNavigateParams;\n    // BrowsingContextCreateType = \"tab\" / \"window\"\n    // BrowsingContextCreateParameters = {\n    //   type: BrowsingContextCreateType\n    // }\n    const CreateParametersSchema = zod_1.z.object({\n        type: zod_1.z.enum(['tab', 'window']),\n        referenceContext: CommonDataTypes.BrowsingContextSchema.optional(),\n    });\n    function parseCreateParams(params) {\n        return parseObject(params, CreateParametersSchema);\n    }\n    BrowsingContext.parseCreateParams = parseCreateParams;\n    // BrowsingContextCloseParameters = {\n    //   context: BrowsingContext\n    // }\n    const CloseParametersSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n    });\n    function parseCloseParams(params) {\n        return parseObject(params, CloseParametersSchema);\n    }\n    BrowsingContext.parseCloseParams = parseCloseParams;\n    // browsingContext.CaptureScreenshotParameters = {\n    //   context: browsingContext.BrowsingContext\n    // }\n    const CaptureScreenshotParametersSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n    });\n    function parseCaptureScreenshotParams(params) {\n        return parseObject(params, CaptureScreenshotParametersSchema);\n    }\n    BrowsingContext.parseCaptureScreenshotParams = parseCaptureScreenshotParams;\n    // All units are in cm.\n    // PrintPageParameters = {\n    //   ?height: (float .ge 0.0) .default 27.94,\n    //   ?width: (float .ge 0.0) .default 21.59,\n    // }\n    const PrintPageParametersSchema = zod_1.z.object({\n        height: zod_1.z.number().min(0.0).default(27.94).optional(),\n        width: zod_1.z.number().min(0.0).default(21.59).optional(),\n    });\n    // All units are in cm.\n    // PrintMarginParameters = {\n    //   ?bottom: (float .ge 0.0) .default 1.0,\n    //   ?left: (float .ge 0.0) .default 1.0,\n    //   ?right: (float .ge 0.0) .default 1.0,\n    //   ?top: (float .ge 0.0) .default 1.0,\n    // }\n    const PrintMarginParametersSchema = zod_1.z.object({\n        bottom: zod_1.z.number().min(0.0).default(1.0).optional(),\n        left: zod_1.z.number().min(0.0).default(1.0).optional(),\n        right: zod_1.z.number().min(0.0).default(1.0).optional(),\n        top: zod_1.z.number().min(0.0).default(1.0).optional(),\n    });\n    /** @see https://w3c.github.io/webdriver/#dfn-parse-a-page-range */\n    const PrintPageRangesSchema = zod_1.z\n        .array(zod_1.z.union([zod_1.z.string().min(1), zod_1.z.number().int().nonnegative()]))\n        .refine((pageRanges) => {\n        return pageRanges.every((pageRange) => {\n            const match = String(pageRange).match(\n            // matches: '2' | '2-' | '-2' | '2-4'\n            /^(?:(?:\\d+)|(?:\\d+[-])|(?:[-]\\d+)|(?:(?<start>\\d+)[-](?<end>\\d+)))$/);\n            // If a page range is specified, validate start <= end.\n            const { start, end } = match?.groups ?? {};\n            if (start && end && Number(start) > Number(end)) {\n                return false;\n            }\n            return match;\n        });\n    });\n    // PrintParameters = {\n    //   context: browsingContext.BrowsingContext,\n    //   ?background: bool .default false,\n    //   ?margin: browsingContext.PrintMarginParameters,\n    //   ?orientation: (\"portrait\" / \"landscape\") .default \"portrait\",\n    //   ?page: browsingContext.PrintPageParameters,\n    //   ?pageRanges: [*(js-uint / text)],\n    //   ?scale: 0.1..2.0 .default 1.0,\n    //   ?shrinkToFit: bool .default true,\n    // }\n    const PrintParametersSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n        background: zod_1.z.boolean().default(false).optional(),\n        margin: PrintMarginParametersSchema.optional(),\n        orientation: zod_1.z\n            .enum(['portrait', 'landscape'])\n            .default('portrait')\n            .optional(),\n        page: PrintPageParametersSchema.optional(),\n        pageRanges: PrintPageRangesSchema.default([]).optional(),\n        scale: zod_1.z.number().min(0.1).max(2.0).default(1.0).optional(),\n        shrinkToFit: zod_1.z.boolean().default(true).optional(),\n    });\n    function parsePrintParams(params) {\n        return parseObject(params, PrintParametersSchema);\n    }\n    BrowsingContext.parsePrintParams = parsePrintParams;\n})(BrowsingContext = exports.BrowsingContext || (exports.BrowsingContext = {}));\nvar CDP;\n(function (CDP) {\n    const SendCommandParamsSchema = zod_1.z.object({\n        // Allowing any cdpMethod, and casting to proper type later on.\n        cdpMethod: zod_1.z.string(),\n        // `passthrough` allows object to have any fields.\n        // https://github.com/colinhacks/zod#passthrough\n        cdpParams: zod_1.z.object({}).passthrough(),\n        cdpSession: zod_1.z.string().optional(),\n    });\n    function parseSendCommandParams(params) {\n        return parseObject(params, SendCommandParamsSchema);\n    }\n    CDP.parseSendCommandParams = parseSendCommandParams;\n    const GetSessionParamsSchema = zod_1.z.object({\n        context: CommonDataTypes.BrowsingContextSchema,\n    });\n    function parseGetSessionParams(params) {\n        return parseObject(params, GetSessionParamsSchema);\n    }\n    CDP.parseGetSessionParams = parseGetSessionParams;\n})(CDP = exports.CDP || (exports.CDP = {}));\n/** @see https://w3c.github.io/webdriver-bidi/#module-session */\nvar Session;\n(function (Session) {\n    const SubscriptionRequestParametersEventsSchema = zod_1.z.enum([\n        protocol_js_1.BrowsingContext.AllEvents,\n        ...Object.values(protocol_js_1.BrowsingContext.EventNames),\n        protocol_js_1.Log.AllEvents,\n        ...Object.values(protocol_js_1.Log.EventNames),\n        protocol_js_1.CDP.AllEvents,\n        ...Object.values(protocol_js_1.CDP.EventNames),\n        protocol_js_1.Network.AllEvents,\n        ...Object.values(protocol_js_1.Network.EventNames),\n        protocol_js_1.Script.AllEvents,\n        ...Object.values(protocol_js_1.Script.EventNames),\n    ]);\n    // SessionSubscribeParameters = {\n    //   events: [*text],\n    //   ?contexts: [*BrowsingContext],\n    // }\n    const SubscriptionRequestParametersSchema = zod_1.z.object({\n        events: zod_1.z.array(SubscriptionRequestParametersEventsSchema),\n        contexts: zod_1.z.array(CommonDataTypes.BrowsingContextSchema).optional(),\n    });\n    function parseSubscribeParams(params) {\n        return parseObject(params, SubscriptionRequestParametersSchema);\n    }\n    Session.parseSubscribeParams = parseSubscribeParams;\n})(Session = exports.Session || (exports.Session = {}));\n//# sourceMappingURL=protocol-parser.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventEmitter = void 0;\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst mitt_1 = __importDefault(require(\"mitt\"));\nclass EventEmitter {\n    #emitter = (0, mitt_1.default)();\n    on(type, handler) {\n        this.#emitter.on(type, handler);\n        return this;\n    }\n    /**\n     * Like `on` but the listener will only be fired once and then it will be removed.\n     * @param event The event you'd like to listen to\n     * @param handler The handler function to run when the event occurs\n     * @return `this` to enable chaining method calls.\n     */\n    once(event, handler) {\n        const onceHandler = (eventData) => {\n            handler(eventData);\n            this.off(event, onceHandler);\n        };\n        return this.on(event, onceHandler);\n    }\n    off(type, handler) {\n        this.#emitter.off(type, handler);\n        return this;\n    }\n    /**\n     * Emits an event and call any associated listeners.\n     *\n     * @param event The event to emit.\n     * @param eventData Any data to emit with the event.\n     * @return `true` if there are any listeners, `false` otherwise.\n     */\n    emit(event, eventData) {\n        this.#emitter.emit(event, eventData);\n    }\n}\nexports.EventEmitter = EventEmitter;\n//# sourceMappingURL=EventEmitter.js.map", "module.exports=function(n){return{all:n=n||new Map,on:function(e,t){var i=n.get(e);i?i.push(t):n.set(e,[t])},off:function(e,t){var i=n.get(e);i&&(t?i.splice(i.indexOf(t)>>>0,1):n.set(e,[]))},emit:function(e,t){var i=n.get(e);i&&i.slice().map(function(n){n(t)}),(i=n.get(\"*\"))&&i.slice().map(function(n){n(e,t)})}}};\n//# sourceMappingURL=mitt.js.map\n", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LogType = void 0;\nvar LogType;\n(function (LogType) {\n    // keep-sorted start\n    LogType[\"bidi\"] = \"BiDi Messages\";\n    LogType[\"browsingContexts\"] = \"Browsing Contexts\";\n    LogType[\"cdp\"] = \"CDP\";\n    LogType[\"system\"] = \"System\";\n    // keep-sorted end\n})(LogType = exports.LogType || (exports.LogType = {}));\n//# sourceMappingURL=log.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProcessingQueue = void 0;\nconst log_js_1 = require(\"./log.js\");\nclass ProcessingQueue {\n    #catch;\n    #logger;\n    #processor;\n    #queue = [];\n    // Flag to keep only 1 active processor.\n    #isProcessing = false;\n    constructor(processor, _catch = () => Promise.resolve(), logger) {\n        this.#catch = _catch;\n        this.#processor = processor;\n        this.#logger = logger;\n    }\n    add(entry) {\n        this.#queue.push(entry);\n        // No need in waiting. Just initialise processor if needed.\n        void this.#processIfNeeded();\n    }\n    async #processIfNeeded() {\n        if (this.#isProcessing) {\n            return;\n        }\n        this.#isProcessing = true;\n        while (this.#queue.length > 0) {\n            const entryPromise = this.#queue.shift();\n            if (entryPromise !== undefined) {\n                await entryPromise\n                    .then((entry) => this.#processor(entry))\n                    .catch((e) => {\n                    this.#logger?.(log_js_1.LogType.system, 'Event was not processed:', e);\n                    this.#catch(e);\n                });\n            }\n        }\n        this.#isProcessing = false;\n    }\n}\nexports.ProcessingQueue = ProcessingQueue;\n//# sourceMappingURL=processingQueue.js.map", "\"use strict\";\n/**\n * Copyright 2023 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.inchesFromCm = void 0;\n/** @return Given an input in cm, convert it to inches. */\nfunction inchesFromCm(cm) {\n    return cm / 2.54;\n}\nexports.inchesFromCm = inchesFromCm;\n//# sourceMappingURL=unitConversions.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Deferred = void 0;\nclass Deferred {\n    #isFinished = false;\n    #promise;\n    #resolve = () => { };\n    #reject = () => { };\n    get isFinished() {\n        return this.#isFinished;\n    }\n    constructor() {\n        this.#promise = new Promise((resolve, reject) => {\n            this.#resolve = resolve;\n            this.#reject = reject;\n        });\n        // Needed to avoid `Uncaught (in promise)`. The promises returned by `then`\n        // and `catch` will be rejected anyway.\n        this.#promise.catch(() => { });\n    }\n    then(onFulfilled, onRejected) {\n        return this.#promise.then(onFulfilled, onRejected);\n    }\n    catch(onRejected) {\n        return this.#promise.catch(onRejected);\n    }\n    resolve(value) {\n        this.#isFinished = true;\n        this.#resolve(value);\n    }\n    reject(reason) {\n        this.#isFinished = true;\n        this.#reject(reason);\n    }\n    finally(onFinally) {\n        return this.#promise.finally(onFinally);\n    }\n    [Symbol.toStringTag] = 'Promise';\n}\nexports.Deferred = Deferred;\n//# sourceMappingURL=deferred.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ScriptEvaluator = exports.SHARED_ID_DIVIDER = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\n// As `script.evaluate` wraps call into serialization script, `lineNumber`\n// should be adjusted.\nconst CALL_FUNCTION_STACKTRACE_LINE_OFFSET = 1;\nconst EVALUATE_STACKTRACE_LINE_OFFSET = 0;\nexports.SHARED_ID_DIVIDER = '_element_';\nclass ScriptEvaluator {\n    #eventManager;\n    constructor(eventManager) {\n        this.#eventManager = eventManager;\n    }\n    /**\n     * Gets the string representation of an object. This is equivalent to\n     * calling toString() on the object value.\n     * @param cdpObject CDP remote object representing an object.\n     * @param realm\n     * @return string The stringified object.\n     */\n    static async stringifyObject(cdpObject, realm) {\n        const stringifyResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n            functionDeclaration: String((obj) => {\n                return String(obj);\n            }),\n            awaitPromise: false,\n            arguments: [cdpObject],\n            returnByValue: true,\n            executionContextId: realm.executionContextId,\n        });\n        return stringifyResult.result.value;\n    }\n    /**\n     * Serializes a given CDP object into BiDi, keeping references in the\n     * target's `globalThis`.\n     * @param cdpRemoteObject CDP remote object to be serialized.\n     * @param resultOwnership Indicates desired ResultOwnership.\n     * @param realm\n     */\n    async serializeCdpObject(cdpRemoteObject, resultOwnership, realm) {\n        const arg = ScriptEvaluator.#cdpRemoteObjectToCallArgument(cdpRemoteObject);\n        const cdpWebDriverValue = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n            functionDeclaration: String((obj) => obj),\n            awaitPromise: false,\n            arguments: [arg],\n            generateWebDriverValue: true,\n            executionContextId: realm.executionContextId,\n        });\n        return realm.cdpToBidiValue(cdpWebDriverValue, resultOwnership);\n    }\n    async scriptEvaluate(realm, expression, awaitPromise, resultOwnership) {\n        const cdpEvaluateResult = await realm.cdpClient.sendCommand('Runtime.evaluate', {\n            contextId: realm.executionContextId,\n            expression,\n            awaitPromise,\n            generateWebDriverValue: true,\n        });\n        if (cdpEvaluateResult.exceptionDetails) {\n            // Serialize exception details.\n            return {\n                exceptionDetails: await this.#serializeCdpExceptionDetails(cdpEvaluateResult.exceptionDetails, EVALUATE_STACKTRACE_LINE_OFFSET, resultOwnership, realm),\n                type: 'exception',\n                realm: realm.realmId,\n            };\n        }\n        return {\n            type: 'success',\n            result: realm.cdpToBidiValue(cdpEvaluateResult, resultOwnership),\n            realm: realm.realmId,\n        };\n    }\n    async callFunction(realm, functionDeclaration, _this, _arguments, awaitPromise, resultOwnership) {\n        const callFunctionAndSerializeScript = `(...args)=>{ return _callFunction((\\n${functionDeclaration}\\n), args);\n      function _callFunction(f, args) {\n        const deserializedThis = args.shift();\n        const deserializedArgs = args;\n        return f.apply(deserializedThis, deserializedArgs);\n      }}`;\n        const thisAndArgumentsList = [\n            await this.#deserializeToCdpArg(_this, realm),\n        ];\n        thisAndArgumentsList.push(...(await Promise.all(_arguments.map(async (a) => {\n            return this.#deserializeToCdpArg(a, realm);\n        }))));\n        let cdpCallFunctionResult;\n        try {\n            cdpCallFunctionResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                functionDeclaration: callFunctionAndSerializeScript,\n                awaitPromise,\n                arguments: thisAndArgumentsList,\n                generateWebDriverValue: true,\n                executionContextId: realm.executionContextId,\n            });\n        }\n        catch (e) {\n            // Heuristic to determine if the problem is in the argument.\n            // The check can be done on the `deserialization` step, but this approach\n            // helps to save round-trips.\n            if (e.code === -32000 &&\n                [\n                    'Could not find object with given id',\n                    'Argument should belong to the same JavaScript world as target object',\n                    'Invalid remote object id',\n                ].includes(e.message)) {\n                throw new protocol_js_1.Message.NoSuchHandleException('Handle was not found.');\n            }\n            throw e;\n        }\n        if (cdpCallFunctionResult.exceptionDetails) {\n            // Serialize exception details.\n            return {\n                exceptionDetails: await this.#serializeCdpExceptionDetails(cdpCallFunctionResult.exceptionDetails, CALL_FUNCTION_STACKTRACE_LINE_OFFSET, resultOwnership, realm),\n                type: 'exception',\n                realm: realm.realmId,\n            };\n        }\n        return {\n            type: 'success',\n            result: realm.cdpToBidiValue(cdpCallFunctionResult, resultOwnership),\n            realm: realm.realmId,\n        };\n    }\n    static #cdpRemoteObjectToCallArgument(cdpRemoteObject) {\n        if (cdpRemoteObject.objectId !== undefined) {\n            return { objectId: cdpRemoteObject.objectId };\n        }\n        if (cdpRemoteObject.unserializableValue !== undefined) {\n            return { unserializableValue: cdpRemoteObject.unserializableValue };\n        }\n        return { value: cdpRemoteObject.value };\n    }\n    async #deserializeToCdpArg(argumentValue, realm) {\n        if ('sharedId' in argumentValue) {\n            const [navigableId, rawBackendNodeId] = argumentValue.sharedId.split(exports.SHARED_ID_DIVIDER);\n            const backendNodeId = parseInt(rawBackendNodeId ?? '');\n            if (isNaN(backendNodeId) ||\n                backendNodeId === undefined ||\n                navigableId === undefined) {\n                throw new protocol_js_1.Message.InvalidArgumentException(`SharedId \"${argumentValue.sharedId}\" should have format \"{navigableId}${exports.SHARED_ID_DIVIDER}{backendNodeId}\".`);\n            }\n            if (realm.navigableId !== navigableId) {\n                throw new protocol_js_1.Message.NoSuchNodeException(`SharedId \"${argumentValue.sharedId}\" belongs to different document. Current document is ${realm.navigableId}.`);\n            }\n            try {\n                const obj = await realm.cdpClient.sendCommand('DOM.resolveNode', {\n                    backendNodeId,\n                    executionContextId: realm.executionContextId,\n                });\n                // TODO(#375): Release `obj.object.objectId` after using.\n                return { objectId: obj.object.objectId };\n            }\n            catch (e) {\n                // Heuristic to detect \"no such node\" exception. Based on the  specific\n                // CDP implementation.\n                if (e.code === -32000 && e.message === 'No node with given id found') {\n                    throw new protocol_js_1.Message.NoSuchNodeException(`SharedId \"${argumentValue.sharedId}\" was not found.`);\n                }\n                throw e;\n            }\n        }\n        if ('handle' in argumentValue) {\n            return { objectId: argumentValue.handle };\n        }\n        switch (argumentValue.type) {\n            // Primitive Protocol Value\n            // https://w3c.github.io/webdriver-bidi/#data-types-protocolValue-primitiveProtocolValue\n            case 'undefined':\n                return { unserializableValue: 'undefined' };\n            case 'null':\n                return { unserializableValue: 'null' };\n            case 'string':\n                return { value: argumentValue.value };\n            case 'number':\n                if (argumentValue.value === 'NaN') {\n                    return { unserializableValue: 'NaN' };\n                }\n                else if (argumentValue.value === '-0') {\n                    return { unserializableValue: '-0' };\n                }\n                else if (argumentValue.value === 'Infinity') {\n                    return { unserializableValue: 'Infinity' };\n                }\n                else if (argumentValue.value === '-Infinity') {\n                    return { unserializableValue: '-Infinity' };\n                }\n                return {\n                    value: argumentValue.value,\n                };\n            case 'boolean':\n                return { value: Boolean(argumentValue.value) };\n            case 'bigint':\n                return {\n                    unserializableValue: `BigInt(${JSON.stringify(argumentValue.value)})`,\n                };\n            case 'date':\n                return {\n                    unserializableValue: `new Date(Date.parse(${JSON.stringify(argumentValue.value)}))`,\n                };\n            case 'regexp':\n                return {\n                    unserializableValue: `new RegExp(${JSON.stringify(argumentValue.value.pattern)}, ${JSON.stringify(argumentValue.value.flags)})`,\n                };\n            case 'map': {\n                // TODO(sadym): If none of the nested keys and values has a remote\n                // reference, serialize to `unserializableValue` without CDP roundtrip.\n                const keyValueArray = await this.#flattenKeyValuePairs(argumentValue.value, realm);\n                const argEvalResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String((...args) => {\n                        const result = new Map();\n                        for (let i = 0; i < args.length; i += 2) {\n                            result.set(args[i], args[i + 1]);\n                        }\n                        return result;\n                    }),\n                    awaitPromise: false,\n                    arguments: keyValueArray,\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                });\n                // TODO(#375): Release `argEvalResult.result.objectId` after using.\n                return { objectId: argEvalResult.result.objectId };\n            }\n            case 'object': {\n                // TODO(sadym): If none of the nested keys and values has a remote\n                //  reference, serialize to `unserializableValue` without CDP roundtrip.\n                const keyValueArray = await this.#flattenKeyValuePairs(argumentValue.value, realm);\n                const argEvalResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String((...args) => {\n                        const result = {};\n                        for (let i = 0; i < args.length; i += 2) {\n                            // Key should be either `string`, `number`, or `symbol`.\n                            const key = args[i];\n                            result[key] = args[i + 1];\n                        }\n                        return result;\n                    }),\n                    awaitPromise: false,\n                    arguments: keyValueArray,\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                });\n                // TODO(#375): Release `argEvalResult.result.objectId` after using.\n                return { objectId: argEvalResult.result.objectId };\n            }\n            case 'array': {\n                // TODO(sadym): If none of the nested items has a remote reference,\n                // serialize to `unserializableValue` without CDP roundtrip.\n                const args = await this.#flattenValueList(argumentValue.value, realm);\n                const argEvalResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String((...args) => {\n                        return args;\n                    }),\n                    awaitPromise: false,\n                    arguments: args,\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                });\n                // TODO(#375): Release `argEvalResult.result.objectId` after using.\n                return { objectId: argEvalResult.result.objectId };\n            }\n            case 'set': {\n                // TODO(sadym): if none of the nested items has a remote reference,\n                // serialize to `unserializableValue` without CDP roundtrip.\n                const args = await this.#flattenValueList(argumentValue.value, realm);\n                const argEvalResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String((...args) => {\n                        return new Set(args);\n                    }),\n                    awaitPromise: false,\n                    arguments: args,\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                });\n                // TODO(#375): Release `argEvalResult.result.objectId` after using.\n                return { objectId: argEvalResult.result.objectId };\n            }\n            case 'channel': {\n                const createChannelHandleResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String(() => {\n                        const queue = [];\n                        let queueNonEmptyResolver = null;\n                        return {\n                            /**\n                             * Gets a promise, which is resolved as soon as a message occurs\n                             * in the queue.\n                             */\n                            async getMessage() {\n                                const onMessage = queue.length > 0\n                                    ? Promise.resolve()\n                                    : new Promise((resolve) => {\n                                        queueNonEmptyResolver = resolve;\n                                    });\n                                await onMessage;\n                                return queue.shift();\n                            },\n                            /**\n                             * Adds a message to the queue.\n                             * Resolves the pending promise if needed.\n                             */\n                            sendMessage(message) {\n                                queue.push(message);\n                                if (queueNonEmptyResolver !== null) {\n                                    queueNonEmptyResolver();\n                                    queueNonEmptyResolver = null;\n                                }\n                            },\n                        };\n                    }),\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                    generateWebDriverValue: false,\n                });\n                const channelHandle = createChannelHandleResult.result.objectId;\n                // Long-poll the message queue asynchronously.\n                void this.#initChannelListener(argumentValue, channelHandle, realm);\n                const sendMessageArgResult = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                    functionDeclaration: String((channelHandle) => {\n                        return channelHandle.sendMessage;\n                    }),\n                    arguments: [\n                        {\n                            objectId: channelHandle,\n                        },\n                    ],\n                    returnByValue: false,\n                    executionContextId: realm.executionContextId,\n                    generateWebDriverValue: false,\n                });\n                return { objectId: sendMessageArgResult.result.objectId };\n            }\n            // TODO(#375): Dispose of nested objects.\n            default:\n                throw new Error(`Value ${JSON.stringify(argumentValue)} is not deserializable.`);\n        }\n    }\n    async #flattenKeyValuePairs(mapping, realm) {\n        const keyValueArray = [];\n        for (const [key, value] of mapping) {\n            let keyArg;\n            if (typeof key === 'string') {\n                // Key is a string.\n                keyArg = { value: key };\n            }\n            else {\n                // Key is a serialized value.\n                keyArg = await this.#deserializeToCdpArg(key, realm);\n            }\n            const valueArg = await this.#deserializeToCdpArg(value, realm);\n            keyValueArray.push(keyArg);\n            keyValueArray.push(valueArg);\n        }\n        return keyValueArray;\n    }\n    async #flattenValueList(list, realm) {\n        return Promise.all(list.map((value) => this.#deserializeToCdpArg(value, realm)));\n    }\n    async #initChannelListener(channel, channelHandle, realm) {\n        const channelId = channel.value.channel;\n        // TODO(#294): Remove this loop after the realm is destroyed.\n        // Rely on the CDP throwing exception in such a case.\n        for (;;) {\n            const message = await realm.cdpClient.sendCommand('Runtime.callFunctionOn', {\n                functionDeclaration: String(async (channelHandle) => channelHandle.getMessage()),\n                arguments: [\n                    {\n                        objectId: channelHandle,\n                    },\n                ],\n                awaitPromise: true,\n                executionContextId: realm.executionContextId,\n                generateWebDriverValue: true,\n            });\n            this.#eventManager.registerEvent({\n                method: protocol_js_1.Script.EventNames.MessageEvent,\n                params: {\n                    channel: channelId,\n                    data: realm.cdpToBidiValue(message, channel.value.ownership ?? 'none'),\n                    source: {\n                        realm: realm.realmId,\n                        context: realm.browsingContextId,\n                    },\n                },\n            }, realm.browsingContextId);\n        }\n    }\n    async #serializeCdpExceptionDetails(cdpExceptionDetails, lineOffset, resultOwnership, realm) {\n        const callFrames = cdpExceptionDetails.stackTrace?.callFrames.map((frame) => ({\n            url: frame.url,\n            functionName: frame.functionName,\n            // As `script.evaluate` wraps call into serialization script, so\n            // `lineNumber` should be adjusted.\n            lineNumber: frame.lineNumber - lineOffset,\n            columnNumber: frame.columnNumber,\n        }));\n        const exception = await this.serializeCdpObject(\n        // Exception should always be there.\n        cdpExceptionDetails.exception, resultOwnership, realm);\n        const text = await ScriptEvaluator.stringifyObject(cdpExceptionDetails.exception, realm);\n        return {\n            exception,\n            columnNumber: cdpExceptionDetails.columnNumber,\n            // As `script.evaluate` wraps call into serialization script, so\n            // `lineNumber` should be adjusted.\n            lineNumber: cdpExceptionDetails.lineNumber - lineOffset,\n            stackTrace: {\n                callFrames: callFrames || [],\n            },\n            text: text || cdpExceptionDetails.text,\n        };\n    }\n}\nexports.ScriptEvaluator = ScriptEvaluator;\n//# sourceMappingURL=scriptEvaluator.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Realm = void 0;\nconst scriptEvaluator_js_1 = require(\"./scriptEvaluator.js\");\nclass Realm {\n    #realmStorage;\n    #browsingContextStorage;\n    #realmId;\n    #browsingContextId;\n    #executionContextId;\n    #origin;\n    #type;\n    #cdpClient;\n    #eventManager;\n    #scriptEvaluator;\n    sandbox;\n    cdpSessionId;\n    constructor(realmStorage, browsingContextStorage, realmId, browsingContextId, executionContextId, origin, type, sandbox, cdpSessionId, cdpClient, eventManager) {\n        this.#realmId = realmId;\n        this.#browsingContextId = browsingContextId;\n        this.#executionContextId = executionContextId;\n        this.sandbox = sandbox;\n        this.#origin = origin;\n        this.#type = type;\n        this.cdpSessionId = cdpSessionId;\n        this.#cdpClient = cdpClient;\n        this.#realmStorage = realmStorage;\n        this.#browsingContextStorage = browsingContextStorage;\n        this.#eventManager = eventManager;\n        this.#scriptEvaluator = new scriptEvaluator_js_1.ScriptEvaluator(this.#eventManager);\n        this.#realmStorage.realmMap.set(this.#realmId, this);\n    }\n    async disown(handle) {\n        // Disowning an object from different realm does nothing.\n        if (this.#realmStorage.knownHandlesToRealm.get(handle) !== this.realmId) {\n            return;\n        }\n        try {\n            await this.cdpClient.sendCommand('Runtime.releaseObject', {\n                objectId: handle,\n            });\n        }\n        catch (e) {\n            // Heuristic to determine if the problem is in the unknown handler.\n            // Ignore the error if so.\n            if (!(e.code === -32000 && e.message === 'Invalid remote object id')) {\n                throw e;\n            }\n        }\n        this.#realmStorage.knownHandlesToRealm.delete(handle);\n    }\n    cdpToBidiValue(cdpValue, resultOwnership) {\n        const cdpWebDriverValue = cdpValue.result.webDriverValue;\n        const bidiValue = this.webDriverValueToBiDi(cdpWebDriverValue);\n        if (cdpValue.result.objectId) {\n            const objectId = cdpValue.result.objectId;\n            if (resultOwnership === 'root') {\n                // Extend BiDi value with `handle` based on required `resultOwnership`\n                // and  CDP response but not on the actual BiDi type.\n                bidiValue.handle = objectId;\n                // Remember all the handles sent to client.\n                this.#realmStorage.knownHandlesToRealm.set(objectId, this.realmId);\n            }\n            else {\n                // No need in awaiting for the object to be released.\n                void this.cdpClient.sendCommand('Runtime.releaseObject', { objectId });\n            }\n        }\n        return bidiValue;\n    }\n    webDriverValueToBiDi(webDriverValue) {\n        // This relies on the CDP to implement proper BiDi serialization, except\n        // backendNodeId/sharedId and `platformobject`.\n        const result = webDriverValue;\n        // Platform object is a special case. It should have only `{type: object}`\n        // without `value` field.\n        if (result.type === 'platformobject') {\n            return { type: 'object' };\n        }\n        const bidiValue = result.value;\n        if (bidiValue === undefined) {\n            return result;\n        }\n        if (result.type === 'node') {\n            if (Object.hasOwn(bidiValue, 'backendNodeId')) {\n                // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n                bidiValue.sharedId = `${this.navigableId}${scriptEvaluator_js_1.SHARED_ID_DIVIDER}${bidiValue.backendNodeId}`;\n                delete bidiValue['backendNodeId'];\n            }\n            if (Object.hasOwn(bidiValue, 'children')) {\n                for (const i in bidiValue.children) {\n                    bidiValue.children[i] = this.webDriverValueToBiDi(bidiValue.children[i]);\n                }\n            }\n        }\n        // Recursively update the nested values.\n        if (['array', 'set'].includes(webDriverValue.type)) {\n            for (const i in bidiValue) {\n                bidiValue[i] = this.webDriverValueToBiDi(bidiValue[i]);\n            }\n        }\n        if (['object', 'map'].includes(webDriverValue.type)) {\n            for (const i in bidiValue) {\n                bidiValue[i] = [\n                    this.webDriverValueToBiDi(bidiValue[i][0]),\n                    this.webDriverValueToBiDi(bidiValue[i][1]),\n                ];\n            }\n        }\n        return result;\n    }\n    toBiDi() {\n        return {\n            realm: this.realmId,\n            origin: this.origin,\n            type: this.type,\n            context: this.browsingContextId,\n            ...(this.sandbox === undefined ? {} : { sandbox: this.sandbox }),\n        };\n    }\n    get realmId() {\n        return this.#realmId;\n    }\n    get navigableId() {\n        return (this.#browsingContextStorage.findContext(this.#browsingContextId)\n            ?.navigableId ?? 'UNKNOWN');\n    }\n    get browsingContextId() {\n        return this.#browsingContextId;\n    }\n    get executionContextId() {\n        return this.#executionContextId;\n    }\n    get origin() {\n        return this.#origin;\n    }\n    get type() {\n        return this.#type;\n    }\n    get cdpClient() {\n        return this.#cdpClient;\n    }\n    async callFunction(functionDeclaration, _this, _arguments, awaitPromise, resultOwnership) {\n        const context = this.#browsingContextStorage.getContext(this.browsingContextId);\n        await context.awaitUnblocked();\n        return {\n            result: await this.#scriptEvaluator.callFunction(this, functionDeclaration, _this, _arguments, awaitPromise, resultOwnership),\n        };\n    }\n    async scriptEvaluate(expression, awaitPromise, resultOwnership) {\n        const context = this.#browsingContextStorage.getContext(this.browsingContextId);\n        await context.awaitUnblocked();\n        return {\n            result: await this.#scriptEvaluator.scriptEvaluate(this, expression, awaitPromise, resultOwnership),\n        };\n    }\n    /**\n     * Serializes a given CDP object into BiDi, keeping references in the\n     * target's `globalThis`.\n     * @param cdpObject CDP remote object to be serialized.\n     * @param resultOwnership Indicates desired ResultOwnership.\n     */\n    async serializeCdpObject(cdpObject, resultOwnership) {\n        return this.#scriptEvaluator.serializeCdpObject(cdpObject, resultOwnership, this);\n    }\n    /**\n     * Gets the string representation of an object. This is equivalent to\n     * calling toString() on the object value.\n     * @param cdpObject CDP remote object representing an object.\n     * @return string The stringified object.\n     */\n    async stringifyObject(cdpObject) {\n        return scriptEvaluator_js_1.ScriptEvaluator.stringifyObject(cdpObject, this);\n    }\n}\nexports.Realm = Realm;\n//# sourceMappingURL=realm.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BrowsingContextImpl = void 0;\nconst unitConversions_js_1 = require(\"../../../utils/unitConversions.js\");\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\nconst log_js_1 = require(\"../../../utils/log.js\");\nconst deferred_js_1 = require(\"../../../utils/deferred.js\");\nconst realm_js_1 = require(\"../script/realm.js\");\nclass BrowsingContextImpl {\n    /** The ID of the current context. */\n    #contextId;\n    /**\n     * The ID of the parent context.\n     * If null, this is a top-level context.\n     */\n    #parentId;\n    /**\n     * Children contexts.\n     * Map from children context ID to context implementation.\n     */\n    #children = new Map();\n    #browsingContextStorage;\n    #defers = {\n        documentInitialized: new deferred_js_1.Deferred(),\n        Page: {\n            navigatedWithinDocument: new deferred_js_1.Deferred(),\n            lifecycleEvent: {\n                DOMContentLoaded: new deferred_js_1.Deferred(),\n                load: new deferred_js_1.Deferred(),\n            },\n        },\n    };\n    #url = 'about:blank';\n    #eventManager;\n    #realmStorage;\n    #loaderId = null;\n    #cdpTarget;\n    #maybeDefaultRealm;\n    #logger;\n    constructor(cdpTarget, realmStorage, contextId, parentId, eventManager, browsingContextStorage, logger) {\n        this.#cdpTarget = cdpTarget;\n        this.#realmStorage = realmStorage;\n        this.#contextId = contextId;\n        this.#parentId = parentId;\n        this.#eventManager = eventManager;\n        this.#browsingContextStorage = browsingContextStorage;\n        this.#logger = logger;\n        this.#initListeners();\n    }\n    static create(cdpTarget, realmStorage, contextId, parentId, eventManager, browsingContextStorage, logger) {\n        const context = new BrowsingContextImpl(cdpTarget, realmStorage, contextId, parentId, eventManager, browsingContextStorage, logger);\n        browsingContextStorage.addContext(context);\n        eventManager.registerEvent({\n            method: protocol_js_1.BrowsingContext.EventNames.ContextCreatedEvent,\n            params: context.serializeToBidiValue(),\n        }, context.contextId);\n        return context;\n    }\n    /**\n     * @see https://html.spec.whatwg.org/multipage/document-sequences.html#navigable\n     */\n    get navigableId() {\n        return this.#loaderId;\n    }\n    delete() {\n        this.#deleteChildren();\n        this.#realmStorage.deleteRealms({\n            browsingContextId: this.contextId,\n        });\n        // Remove context from the parent.\n        if (this.parentId !== null) {\n            const parent = this.#browsingContextStorage.getContext(this.parentId);\n            parent.#children.delete(this.contextId);\n        }\n        this.#eventManager.registerEvent({\n            method: protocol_js_1.BrowsingContext.EventNames.ContextDestroyedEvent,\n            params: this.serializeToBidiValue(),\n        }, this.contextId);\n        this.#browsingContextStorage.deleteContext(this.contextId);\n    }\n    /** Returns the ID of this context. */\n    get contextId() {\n        return this.#contextId;\n    }\n    /** Returns the parent context ID. */\n    get parentId() {\n        return this.#parentId;\n    }\n    /** Returns all children contexts. */\n    get children() {\n        return Array.from(this.#children.values());\n    }\n    /**\n     * Returns true if this is a top-level context.\n     * This is the case whenever the parent context ID is null.\n     */\n    isTopLevelContext() {\n        return this.#parentId === null;\n    }\n    addChild(child) {\n        this.#children.set(child.contextId, child);\n    }\n    #deleteChildren() {\n        this.children.map((child) => child.delete());\n    }\n    get #defaultRealm() {\n        if (this.#maybeDefaultRealm === undefined) {\n            throw new Error(`No default realm for browsing context ${this.#contextId}`);\n        }\n        return this.#maybeDefaultRealm;\n    }\n    get cdpTarget() {\n        return this.#cdpTarget;\n    }\n    updateCdpTarget(cdpTarget) {\n        this.#cdpTarget = cdpTarget;\n        this.#initListeners();\n    }\n    get url() {\n        return this.#url;\n    }\n    async awaitLoaded() {\n        await this.#defers.Page.lifecycleEvent.load;\n    }\n    awaitUnblocked() {\n        return this.#cdpTarget.targetUnblocked;\n    }\n    async getOrCreateSandbox(sandbox) {\n        if (sandbox === undefined || sandbox === '') {\n            return this.#defaultRealm;\n        }\n        let maybeSandboxes = this.#realmStorage.findRealms({\n            browsingContextId: this.contextId,\n            sandbox,\n        });\n        if (maybeSandboxes.length === 0) {\n            await this.#cdpTarget.cdpClient.sendCommand('Page.createIsolatedWorld', {\n                frameId: this.contextId,\n                worldName: sandbox,\n            });\n            // `Runtime.executionContextCreated` should be emitted by the time the\n            // previous command is done.\n            maybeSandboxes = this.#realmStorage.findRealms({\n                browsingContextId: this.contextId,\n                sandbox,\n            });\n        }\n        if (maybeSandboxes.length !== 1) {\n            throw Error(`Sandbox ${sandbox} wasn't created.`);\n        }\n        return maybeSandboxes[0];\n    }\n    serializeToBidiValue(maxDepth = 0, addParentFiled = true) {\n        return {\n            context: this.#contextId,\n            url: this.url,\n            children: maxDepth > 0\n                ? this.children.map((c) => c.serializeToBidiValue(maxDepth - 1, false))\n                : null,\n            ...(addParentFiled ? { parent: this.#parentId } : {}),\n        };\n    }\n    #initListeners() {\n        this.#cdpTarget.cdpClient.on('Target.targetInfoChanged', (params) => {\n            if (this.contextId !== params.targetInfo.targetId) {\n                return;\n            }\n            this.#url = params.targetInfo.url;\n        });\n        this.#cdpTarget.cdpClient.on('Page.frameNavigated', (params) => {\n            if (this.contextId !== params.frame.id) {\n                return;\n            }\n            this.#url = params.frame.url + (params.frame.urlFragment ?? '');\n            // At the point the page is initiated, all the nested iframes from the\n            // previous page are detached and realms are destroyed.\n            // Remove context's children.\n            this.#deleteChildren();\n        });\n        this.#cdpTarget.cdpClient.on('Page.navigatedWithinDocument', (params) => {\n            if (this.contextId !== params.frameId) {\n                return;\n            }\n            this.#url = params.url;\n            this.#defers.Page.navigatedWithinDocument.resolve(params);\n        });\n        this.#cdpTarget.cdpClient.on('Page.lifecycleEvent', (params) => {\n            if (this.contextId !== params.frameId) {\n                return;\n            }\n            // `timestamp` from the event is MonotonicTime, not real time, so\n            // the best Mapper can do is to set the timestamp to the epoch time\n            // of the event arrived.\n            // https://chromedevtools.github.io/devtools-protocol/tot/Network/#type-MonotonicTime\n            const timestamp = new Date().getTime();\n            if (params.name === 'init') {\n                this.#documentChanged(params.loaderId);\n                this.#defers.documentInitialized.resolve();\n            }\n            if (params.name === 'commit') {\n                this.#loaderId = params.loaderId;\n                return;\n            }\n            if (params.loaderId !== this.#loaderId) {\n                return;\n            }\n            switch (params.name) {\n                case 'DOMContentLoaded':\n                    this.#defers.Page.lifecycleEvent.DOMContentLoaded.resolve(params);\n                    this.#eventManager.registerEvent({\n                        method: protocol_js_1.BrowsingContext.EventNames.DomContentLoadedEvent,\n                        params: {\n                            context: this.contextId,\n                            navigation: this.#loaderId,\n                            timestamp,\n                            url: this.#url,\n                        },\n                    }, this.contextId);\n                    break;\n                case 'load':\n                    this.#defers.Page.lifecycleEvent.load.resolve(params);\n                    this.#eventManager.registerEvent({\n                        method: protocol_js_1.BrowsingContext.EventNames.LoadEvent,\n                        params: {\n                            context: this.contextId,\n                            navigation: this.#loaderId,\n                            timestamp,\n                            url: this.#url,\n                        },\n                    }, this.contextId);\n                    break;\n            }\n        });\n        this.#cdpTarget.cdpClient.on('Runtime.executionContextCreated', (params) => {\n            if (params.context.auxData.frameId !== this.contextId) {\n                return;\n            }\n            // Only this execution contexts are supported for now.\n            if (!['default', 'isolated'].includes(params.context.auxData.type)) {\n                return;\n            }\n            const realm = new realm_js_1.Realm(this.#realmStorage, this.#browsingContextStorage, params.context.uniqueId, this.contextId, params.context.id, this.#getOrigin(params), \n            // TODO: differentiate types.\n            'window', \n            // Sandbox name for isolated world.\n            params.context.auxData.type === 'isolated'\n                ? params.context.name\n                : undefined, this.#cdpTarget.cdpSessionId, this.#cdpTarget.cdpClient, this.#eventManager);\n            if (params.context.auxData.isDefault) {\n                this.#maybeDefaultRealm = realm;\n            }\n        });\n        this.#cdpTarget.cdpClient.on('Runtime.executionContextDestroyed', (params) => {\n            this.#realmStorage.deleteRealms({\n                cdpSessionId: this.#cdpTarget.cdpSessionId,\n                executionContextId: params.executionContextId,\n            });\n        });\n        this.#cdpTarget.cdpClient.on('Runtime.executionContextsCleared', () => {\n            this.#realmStorage.deleteRealms({\n                cdpSessionId: this.#cdpTarget.cdpSessionId,\n            });\n        });\n    }\n    #getOrigin(params) {\n        if (params.context.auxData.type === 'isolated') {\n            // Sandbox should have the same origin as the context itself, but in CDP\n            // it has an empty one.\n            return this.#defaultRealm.origin;\n        }\n        // https://html.spec.whatwg.org/multipage/origin.html#ascii-serialisation-of-an-origin\n        return ['://', ''].includes(params.context.origin)\n            ? 'null'\n            : params.context.origin;\n    }\n    #documentChanged(loaderId) {\n        // Same document navigation.\n        if (loaderId === undefined || this.#loaderId === loaderId) {\n            if (this.#defers.Page.navigatedWithinDocument.isFinished) {\n                this.#defers.Page.navigatedWithinDocument =\n                    new deferred_js_1.Deferred();\n            }\n            return;\n        }\n        if (this.#defers.documentInitialized.isFinished) {\n            this.#defers.documentInitialized = new deferred_js_1.Deferred();\n        }\n        else {\n            this.#logger?.(log_js_1.LogType.browsingContexts, 'Document changed');\n        }\n        if (this.#defers.Page.lifecycleEvent.DOMContentLoaded.isFinished) {\n            this.#defers.Page.lifecycleEvent.DOMContentLoaded =\n                new deferred_js_1.Deferred();\n        }\n        else {\n            this.#logger?.(log_js_1.LogType.browsingContexts, 'Document changed');\n        }\n        if (this.#defers.Page.lifecycleEvent.load.isFinished) {\n            this.#defers.Page.lifecycleEvent.load =\n                new deferred_js_1.Deferred();\n        }\n        else {\n            this.#logger?.(log_js_1.LogType.browsingContexts, 'Document changed');\n        }\n        this.#loaderId = loaderId;\n    }\n    async navigate(url, wait) {\n        await this.awaitUnblocked();\n        // TODO: handle loading errors.\n        const cdpNavigateResult = await this.#cdpTarget.cdpClient.sendCommand('Page.navigate', {\n            url,\n            frameId: this.contextId,\n        });\n        if (cdpNavigateResult.errorText) {\n            throw new protocol_js_1.Message.UnknownErrorException(cdpNavigateResult.errorText);\n        }\n        this.#documentChanged(cdpNavigateResult.loaderId);\n        // Wait for `wait` condition.\n        switch (wait) {\n            case 'none':\n                break;\n            case 'interactive':\n                // No `loaderId` means same-document navigation.\n                if (cdpNavigateResult.loaderId === undefined) {\n                    await this.#defers.Page.navigatedWithinDocument;\n                }\n                else {\n                    await this.#defers.Page.lifecycleEvent.DOMContentLoaded;\n                }\n                break;\n            case 'complete':\n                // No `loaderId` means same-document navigation.\n                if (cdpNavigateResult.loaderId === undefined) {\n                    await this.#defers.Page.navigatedWithinDocument;\n                }\n                else {\n                    await this.#defers.Page.lifecycleEvent.load;\n                }\n                break;\n        }\n        return {\n            result: {\n                navigation: cdpNavigateResult.loaderId || null,\n                url,\n            },\n        };\n    }\n    async captureScreenshot() {\n        const [, result] = await Promise.all([\n            // TODO: Either make this a proposal in the BiDi spec, or focus the\n            // original tab right after the screenshot is taken.\n            // The screenshot command gets blocked until we focus the active tab.\n            this.#cdpTarget.cdpClient.sendCommand('Page.bringToFront'),\n            this.#cdpTarget.cdpClient.sendCommand('Page.captureScreenshot', {}),\n        ]);\n        return {\n            result: {\n                data: result.data,\n            },\n        };\n    }\n    async print(params) {\n        const printToPdfCdpParams = {\n            printBackground: params.background,\n            landscape: params.orientation === 'landscape',\n            pageRanges: params.pageRanges?.join(',') ?? '',\n            scale: params.scale,\n            preferCSSPageSize: !params.shrinkToFit,\n        };\n        if (params.margin?.bottom) {\n            printToPdfCdpParams.marginBottom = (0, unitConversions_js_1.inchesFromCm)(params.margin.bottom);\n        }\n        if (params.margin?.left) {\n            printToPdfCdpParams.marginLeft = (0, unitConversions_js_1.inchesFromCm)(params.margin.left);\n        }\n        if (params.margin?.right) {\n            printToPdfCdpParams.marginRight = (0, unitConversions_js_1.inchesFromCm)(params.margin.right);\n        }\n        if (params.margin?.top) {\n            printToPdfCdpParams.marginTop = (0, unitConversions_js_1.inchesFromCm)(params.margin.top);\n        }\n        if (params.page?.height) {\n            printToPdfCdpParams.paperHeight = (0, unitConversions_js_1.inchesFromCm)(params.page.height);\n        }\n        if (params.page?.width) {\n            printToPdfCdpParams.paperWidth = (0, unitConversions_js_1.inchesFromCm)(params.page.width);\n        }\n        const result = await this.#cdpTarget.cdpClient.sendCommand('Page.printToPDF', printToPdfCdpParams);\n        return {\n            result: {\n                data: result.data,\n            },\n        };\n    }\n    async addPreloadScript(params) {\n        const result = await this.#cdpTarget.cdpClient.sendCommand('Page.addScriptToEvaluateOnNewDocument', {\n            // The spec provides a function, and CDP expects an evaluation.\n            source: `(${params.expression})();`,\n            worldName: params.sandbox,\n        });\n        return {\n            result: {\n                script: result.identifier,\n            },\n        };\n    }\n}\nexports.BrowsingContextImpl = BrowsingContextImpl;\n//# sourceMappingURL=browsingContextImpl.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRemoteValuesText = exports.logMessageFormatter = void 0;\nconst specifiers = ['%s', '%d', '%i', '%f', '%o', '%O', '%c'];\nfunction isFormmatSpecifier(str) {\n    return specifiers.some((spec) => str.includes(spec));\n}\n/**\n * @param args input remote values to be format printed\n * @return parsed text of the remote values in specific format\n */\nfunction logMessageFormatter(args) {\n    let output = '';\n    const argFormat = args[0].value.toString();\n    const argValues = args.slice(1, undefined);\n    const tokens = argFormat.split(new RegExp(specifiers.map((spec) => `(${spec})`).join('|'), 'g'));\n    for (const token of tokens) {\n        if (token === undefined || token === '') {\n            continue;\n        }\n        if (isFormmatSpecifier(token)) {\n            const arg = argValues.shift();\n            // raise an exception when less value is provided\n            if (arg === undefined) {\n                throw new Error(`Less value is provided: \"${getRemoteValuesText(args, false)}\"`);\n            }\n            if (token === '%s') {\n                output += stringFromArg(arg);\n            }\n            else if (token === '%d' || token === '%i') {\n                if (arg.type === 'bigint' ||\n                    arg.type === 'number' ||\n                    arg.type === 'string') {\n                    output += parseInt(arg.value.toString(), 10);\n                }\n                else {\n                    output += 'NaN';\n                }\n            }\n            else if (token === '%f') {\n                if (arg.type === 'bigint' ||\n                    arg.type === 'number' ||\n                    arg.type === 'string') {\n                    output += parseFloat(arg.value.toString());\n                }\n                else {\n                    output += 'NaN';\n                }\n            }\n            else {\n                // %o, %O, %c\n                output += toJson(arg);\n            }\n        }\n        else {\n            output += token;\n        }\n    }\n    // raise an exception when more value is provided\n    if (argValues.length > 0) {\n        throw new Error(`More value is provided: \"${getRemoteValuesText(args, false)}\"`);\n    }\n    return output;\n}\nexports.logMessageFormatter = logMessageFormatter;\n/**\n * @param arg input remote value to be parsed\n * @return parsed text of the remote value\n *\n * input: {\"type\": \"number\", \"value\": 1}\n * output: 1\n *\n * input: {\"type\": \"string\", \"value\": \"abc\"}\n * output: \"abc\"\n *\n * input: {\"type\": \"object\",  \"value\": [[\"id\", {\"type\": \"number\", \"value\": 1}]]}\n * output: '{\"id\": 1}'\n *\n * input: {\"type\": \"object\", \"value\": [[\"font-size\", {\"type\": \"string\", \"value\": \"20px\"}]]}\n * output: '{\"font-size\": \"20px\"}'\n */\nfunction toJson(arg) {\n    // arg type validation\n    if (arg.type !== 'array' &&\n        arg.type !== 'bigint' &&\n        arg.type !== 'date' &&\n        arg.type !== 'number' &&\n        arg.type !== 'object' &&\n        arg.type !== 'string') {\n        return stringFromArg(arg);\n    }\n    if (arg.type === 'bigint') {\n        return `${arg.value.toString()}n`;\n    }\n    if (arg.type === 'number') {\n        return arg.value.toString();\n    }\n    if (['date', 'string'].includes(arg.type)) {\n        return JSON.stringify(arg.value);\n    }\n    if (arg.type === 'object') {\n        return `{${arg.value\n            .map((pair) => {\n            return `${JSON.stringify(pair[0])}:${toJson(pair[1])}`;\n        })\n            .join(',')}}`;\n    }\n    if (arg.type === 'array') {\n        return `[${arg.value?.map((val) => toJson(val)).join(',') ?? ''}]`;\n    }\n    throw Error(`Invalid value type: ${arg.toString()}`);\n}\nfunction stringFromArg(arg) {\n    if (!Object.hasOwn(arg, 'value')) {\n        return arg.type;\n    }\n    switch (arg.type) {\n        case 'string':\n        case 'number':\n        case 'boolean':\n        case 'bigint':\n            return String(arg.value);\n        case 'regexp':\n            return `/${arg.value.pattern}/${arg.value.flags ?? ''}`;\n        case 'date':\n            return new Date(arg.value).toString();\n        case 'object':\n            return `Object(${arg.value?.length ?? ''})`;\n        case 'array':\n            return `Array(${arg.value?.length ?? ''})`;\n        case 'map':\n            return `Map(${arg.value.length})`;\n        case 'set':\n            return `Set(${arg.value.length})`;\n        case 'node':\n            return 'node';\n        default:\n            return arg.type;\n    }\n}\nfunction getRemoteValuesText(args, formatText) {\n    const arg = args[0];\n    if (!arg) {\n        return '';\n    }\n    // if args[0] is a format specifier, format the args as output\n    if (arg.type === 'string' &&\n        isFormmatSpecifier(arg.value.toString()) &&\n        formatText) {\n        return logMessageFormatter(args);\n    }\n    // if args[0] is not a format specifier, just join the args with \\u0020 (unicode 'SPACE')\n    return args\n        .map((arg) => {\n        return stringFromArg(arg);\n    })\n        .join('\\u0020');\n}\nexports.getRemoteValuesText = getRemoteValuesText;\n//# sourceMappingURL=logHelper.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LogManager = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\nconst logHelper_js_1 = require(\"./logHelper.js\");\n/** Converts CDP StackTrace object to BiDi StackTrace object. */\nfunction getBidiStackTrace(cdpStackTrace) {\n    const stackFrames = cdpStackTrace?.callFrames.map((callFrame) => {\n        return {\n            columnNumber: callFrame.columnNumber,\n            functionName: callFrame.functionName,\n            lineNumber: callFrame.lineNumber,\n            url: callFrame.url,\n        };\n    });\n    return stackFrames ? { callFrames: stackFrames } : undefined;\n}\nfunction getLogLevel(consoleApiType) {\n    if (['assert', 'error'].includes(consoleApiType)) {\n        return 'error';\n    }\n    if (['debug', 'trace'].includes(consoleApiType)) {\n        return 'debug';\n    }\n    if (['warn', 'warning'].includes(consoleApiType)) {\n        return 'warn';\n    }\n    return 'info';\n}\nclass LogManager {\n    #eventManager;\n    #realmStorage;\n    #cdpTarget;\n    constructor(cdpTarget, realmStorage, eventManager) {\n        this.#cdpTarget = cdpTarget;\n        this.#realmStorage = realmStorage;\n        this.#eventManager = eventManager;\n    }\n    static create(cdpTarget, realmStorage, eventManager) {\n        const logManager = new LogManager(cdpTarget, realmStorage, eventManager);\n        logManager.#initialize();\n        return logManager;\n    }\n    #initialize() {\n        this.#initializeLogEntryAddedEventListener();\n    }\n    #initializeLogEntryAddedEventListener() {\n        this.#cdpTarget.cdpClient.on('Runtime.consoleAPICalled', (params) => {\n            // Try to find realm by `cdpSessionId` and `executionContextId`,\n            // if provided.\n            const realm = this.#realmStorage.findRealm({\n                cdpSessionId: this.#cdpTarget.cdpSessionId,\n                executionContextId: params.executionContextId,\n            });\n            const argsPromise = realm === undefined\n                ? Promise.resolve(params.args)\n                : // Properly serialize arguments if possible.\n                    Promise.all(params.args.map((arg) => {\n                        return realm.serializeCdpObject(arg, 'none');\n                    }));\n            this.#eventManager.registerPromiseEvent(argsPromise.then((args) => ({\n                method: protocol_js_1.Log.EventNames.LogEntryAddedEvent,\n                params: {\n                    level: getLogLevel(params.type),\n                    source: {\n                        realm: realm?.realmId ?? 'UNKNOWN',\n                        context: realm?.browsingContextId ?? 'UNKNOWN',\n                    },\n                    text: (0, logHelper_js_1.getRemoteValuesText)(args, true),\n                    timestamp: Math.round(params.timestamp),\n                    stackTrace: getBidiStackTrace(params.stackTrace),\n                    type: 'console',\n                    // Console method is `warn`, not `warning`.\n                    method: params.type === 'warning' ? 'warn' : params.type,\n                    args,\n                },\n            })), realm?.browsingContextId ?? 'UNKNOWN', protocol_js_1.Log.EventNames.LogEntryAddedEvent);\n        });\n        this.#cdpTarget.cdpClient.on('Runtime.exceptionThrown', (params) => {\n            // Try to find realm by `cdpSessionId` and `executionContextId`,\n            // if provided.\n            const realm = this.#realmStorage.findRealm({\n                cdpSessionId: this.#cdpTarget.cdpSessionId,\n                executionContextId: params.exceptionDetails.executionContextId,\n            });\n            // Try all the best to get the exception text.\n            const textPromise = (async () => {\n                if (!params.exceptionDetails.exception) {\n                    return params.exceptionDetails.text;\n                }\n                if (realm === undefined) {\n                    return JSON.stringify(params.exceptionDetails.exception);\n                }\n                return realm.stringifyObject(params.exceptionDetails.exception);\n            })();\n            this.#eventManager.registerPromiseEvent(textPromise.then((text) => ({\n                method: protocol_js_1.Log.EventNames.LogEntryAddedEvent,\n                params: {\n                    level: 'error',\n                    source: {\n                        realm: realm?.realmId ?? 'UNKNOWN',\n                        context: realm?.browsingContextId ?? 'UNKNOWN',\n                    },\n                    text,\n                    timestamp: Math.round(params.timestamp),\n                    stackTrace: getBidiStackTrace(params.exceptionDetails.stackTrace),\n                    type: 'javascript',\n                },\n            })), realm?.browsingContextId ?? 'UNKNOWN', protocol_js_1.Log.EventNames.LogEntryAddedEvent);\n        });\n    }\n}\nexports.LogManager = LogManager;\n//# sourceMappingURL=logManager.js.map", "\"use strict\";\n/**\n * Copyright 2023 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DefaultMap = void 0;\n/**\n * A subclass of Map whose functionality is almost the same as its parent\n * except for the fact that DefaultMap never returns undefined. It provides a\n * default value for keys that do not exist.\n */\nclass DefaultMap extends Map {\n    /** The default value to return whenever a key is not present in the map. */\n    #getDefaultValue;\n    constructor(getDefaultValue, entries) {\n        super(entries);\n        this.#getDefaultValue = getDefaultValue;\n    }\n    get(key) {\n        if (!this.has(key)) {\n            this.set(key, this.#getDefaultValue(key));\n        }\n        return super.get(key);\n    }\n}\nexports.DefaultMap = DefaultMap;\n//# sourceMappingURL=DefaultMap.js.map", "\"use strict\";\n/*\n * Copyright 2023 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NetworkRequest = void 0;\nconst deferred_1 = require(\"../../../utils/deferred\");\nconst protocol_1 = require(\"../../../protocol/protocol\");\nclass NetworkRequest {\n    static #unknown = 'UNKNOWN';\n    requestId;\n    #eventManager;\n    #requestWillBeSentEvent;\n    #requestWillBeSentExtraInfoEvent;\n    #responseReceivedEvent;\n    #responseReceivedExtraInfoEvent;\n    #beforeRequestSentDeferred;\n    #responseReceivedDeferred;\n    constructor(requestId, eventManager) {\n        this.requestId = requestId;\n        this.#eventManager = eventManager;\n        this.#beforeRequestSentDeferred = new deferred_1.Deferred();\n        this.#responseReceivedDeferred = new deferred_1.Deferred();\n    }\n    onRequestWillBeSentEvent(requestWillBeSentEvent) {\n        if (this.#requestWillBeSentEvent !== undefined) {\n            throw new Error('RequestWillBeSentEvent is already set');\n        }\n        this.#requestWillBeSentEvent = requestWillBeSentEvent;\n        if (this.#requestWillBeSentExtraInfoEvent !== undefined) {\n            this.#beforeRequestSentDeferred.resolve();\n        }\n        this.#sendBeforeRequestEvent();\n    }\n    onRequestWillBeSentExtraInfoEvent(requestWillBeSentExtraInfoEvent) {\n        if (this.#requestWillBeSentExtraInfoEvent !== undefined) {\n            throw new Error('RequestWillBeSentExtraInfoEvent is already set');\n        }\n        this.#requestWillBeSentExtraInfoEvent = requestWillBeSentExtraInfoEvent;\n        if (this.#requestWillBeSentEvent !== undefined) {\n            this.#beforeRequestSentDeferred.resolve();\n        }\n    }\n    onResponseReceivedEvent(responseReceivedEvent) {\n        if (this.#responseReceivedEvent !== undefined) {\n            throw new Error('ResponseReceivedEvent is already set');\n        }\n        this.#responseReceivedEvent = responseReceivedEvent;\n        if (this.#responseReceivedExtraInfoEvent !== undefined) {\n            this.#responseReceivedDeferred.resolve();\n        }\n        this.#sendResponseReceivedEvent();\n    }\n    onResponseReceivedEventExtraInfo(responseReceivedExtraInfoEvent) {\n        if (this.#responseReceivedExtraInfoEvent !== undefined) {\n            throw new Error('ResponseReceivedExtraInfoEvent is already set');\n        }\n        this.#responseReceivedExtraInfoEvent = responseReceivedExtraInfoEvent;\n        if (this.#responseReceivedEvent !== undefined) {\n            this.#responseReceivedDeferred.resolve();\n        }\n    }\n    onLoadingFailedEvent(loadingFailedEvent) {\n        this.#beforeRequestSentDeferred.resolve();\n        this.#responseReceivedDeferred.reject(loadingFailedEvent);\n        const params = {\n            ...this.#getBaseEventParams(),\n            errorText: loadingFailedEvent.errorText,\n        };\n        this.#eventManager.registerEvent({\n            method: protocol_1.Network.EventNames.FetchErrorEvent,\n            params,\n        }, this.#requestWillBeSentEvent?.frameId ?? null);\n    }\n    #sendBeforeRequestEvent() {\n        if (!this.#isIgnoredEvent()) {\n            this.#eventManager.registerPromiseEvent(this.#beforeRequestSentDeferred.then(() => this.#getBeforeRequestEvent()), this.#requestWillBeSentEvent?.frameId ?? null, protocol_1.Network.EventNames.BeforeRequestSentEvent);\n        }\n    }\n    #getBeforeRequestEvent() {\n        if (this.#requestWillBeSentEvent === undefined) {\n            throw new Error('RequestWillBeSentEvent is not set');\n        }\n        const params = {\n            ...this.#getBaseEventParams(),\n            initiator: { type: this.#getInitiatorType() },\n        };\n        return {\n            method: protocol_1.Network.EventNames.BeforeRequestSentEvent,\n            params,\n        };\n    }\n    #getBaseEventParams() {\n        return {\n            context: this.#requestWillBeSentEvent?.frameId ?? null,\n            navigation: this.#requestWillBeSentEvent?.loaderId ?? null,\n            // TODO: implement.\n            redirectCount: 0,\n            request: this.#getRequestData(),\n            // Timestamp should be in milliseconds, while CDP provides it in seconds.\n            timestamp: Math.round((this.#requestWillBeSentEvent?.wallTime ?? 0) * 1000),\n        };\n    }\n    #getRequestData() {\n        const cookies = this.#requestWillBeSentExtraInfoEvent === undefined\n            ? []\n            : NetworkRequest.#getCookies(this.#requestWillBeSentExtraInfoEvent.associatedCookies);\n        return {\n            request: this.#requestWillBeSentEvent?.requestId ?? NetworkRequest.#unknown,\n            url: this.#requestWillBeSentEvent?.request.url ?? NetworkRequest.#unknown,\n            method: this.#requestWillBeSentEvent?.request.method ?? NetworkRequest.#unknown,\n            headers: Object.keys(this.#requestWillBeSentEvent?.request.headers ?? []).map((key) => ({\n                name: key,\n                value: this.#requestWillBeSentEvent?.request.headers[key],\n            })),\n            cookies,\n            // TODO: implement.\n            headersSize: -1,\n            // TODO: implement.\n            bodySize: 0,\n            timings: {\n                // TODO: implement.\n                timeOrigin: 0,\n                // TODO: implement.\n                requestTime: 0,\n                // TODO: implement.\n                redirectStart: 0,\n                // TODO: implement.\n                redirectEnd: 0,\n                // TODO: implement.\n                fetchStart: 0,\n                // TODO: implement.\n                dnsStart: 0,\n                // TODO: implement.\n                dnsEnd: 0,\n                // TODO: implement.\n                connectStart: 0,\n                // TODO: implement.\n                connectEnd: 0,\n                // TODO: implement.\n                tlsStart: 0,\n                // TODO: implement.\n                tlsEnd: 0,\n                // TODO: implement.\n                requestStart: 0,\n                // TODO: implement.\n                responseStart: 0,\n                // TODO: implement.\n                responseEnd: 0,\n            },\n        };\n    }\n    #getInitiatorType() {\n        switch (this.#requestWillBeSentEvent?.initiator.type) {\n            case 'parser':\n            case 'script':\n            case 'preflight':\n                return this.#requestWillBeSentEvent.initiator.type;\n            default:\n                return 'other';\n        }\n    }\n    static #getCookiesSameSite(cdpSameSiteValue) {\n        switch (cdpSameSiteValue) {\n            case 'Strict':\n                return 'strict';\n            case 'Lax':\n                return 'lax';\n            default:\n                return 'none';\n        }\n    }\n    static #getCookies(associatedCookies) {\n        return associatedCookies.map((cookieInfo) => {\n            return {\n                name: cookieInfo.cookie.name,\n                value: cookieInfo.cookie.value,\n                domain: cookieInfo.cookie.domain,\n                path: cookieInfo.cookie.path,\n                expires: cookieInfo.cookie.expires,\n                size: cookieInfo.cookie.size,\n                httpOnly: cookieInfo.cookie.httpOnly,\n                secure: cookieInfo.cookie.secure,\n                sameSite: NetworkRequest.#getCookiesSameSite(cookieInfo.cookie.sameSite),\n            };\n        });\n    }\n    #sendResponseReceivedEvent() {\n        if (!this.#isIgnoredEvent()) {\n            // Wait for both ResponseReceived and ResponseReceivedExtraInfo events.\n            this.#eventManager.registerPromiseEvent(this.#responseReceivedDeferred.then(() => this.#getResponseReceivedEvent()), this.#responseReceivedEvent?.frameId ?? null, protocol_1.Network.EventNames.ResponseCompletedEvent);\n        }\n    }\n    #getResponseReceivedEvent() {\n        if (this.#responseReceivedEvent === undefined) {\n            throw new Error('ResponseReceivedEvent is not set');\n        }\n        if (this.#requestWillBeSentEvent === undefined) {\n            throw new Error('RequestWillBeSentEvent is not set');\n        }\n        return {\n            method: protocol_1.Network.EventNames.ResponseCompletedEvent,\n            params: {\n                ...this.#getBaseEventParams(),\n                response: {\n                    url: this.#responseReceivedEvent.response.url,\n                    protocol: this.#responseReceivedEvent.response.protocol,\n                    status: this.#responseReceivedEvent.response.status,\n                    statusText: this.#responseReceivedEvent.response.statusText,\n                    // Check if this is correct.\n                    fromCache: this.#responseReceivedEvent.response.fromDiskCache ||\n                        this.#responseReceivedEvent.response.fromPrefetchCache,\n                    // TODO: implement.\n                    headers: this.#getHeaders(this.#responseReceivedEvent.response.headers),\n                    mimeType: this.#responseReceivedEvent.response.mimeType,\n                    bytesReceived: this.#responseReceivedEvent.response.encodedDataLength,\n                    headersSize: this.#responseReceivedExtraInfoEvent?.headersText?.length ?? -1,\n                    // TODO: consider removing from spec.\n                    bodySize: -1,\n                    content: {\n                        // TODO: consider removing from spec.\n                        size: -1,\n                    },\n                },\n            },\n        };\n    }\n    #getHeaders(headers) {\n        return Object.keys(headers).map((key) => ({\n            name: key,\n            value: headers[key],\n        }));\n    }\n    #isIgnoredEvent() {\n        return (this.#requestWillBeSentEvent?.request.url.endsWith('/favicon.ico') ??\n            false);\n    }\n}\nexports.NetworkRequest = NetworkRequest;\n//# sourceMappingURL=networkRequest.js.map", "\"use strict\";\n/*\n * Copyright 2023 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NetworkProcessor = void 0;\nconst DefaultMap_1 = require(\"../../../utils/DefaultMap\");\nconst networkRequest_1 = require(\"./networkRequest\");\nclass NetworkProcessor {\n    #eventManager;\n    /**\n     * Map of request ID to NetworkRequest objects. Needed as long as information\n     * about requests comes from different events.\n     */\n    #requestMap;\n    constructor(eventManager) {\n        this.#eventManager = eventManager;\n        this.#requestMap = new DefaultMap_1.DefaultMap((requestId) => new networkRequest_1.NetworkRequest(requestId, this.#eventManager));\n    }\n    static async create(cdpClient, eventManager) {\n        const networkProcessor = new NetworkProcessor(eventManager);\n        cdpClient.on('Network.requestWillBeSent', (params) => {\n            networkProcessor\n                .#getOrCreateNetworkRequest(params.requestId)\n                .onRequestWillBeSentEvent(params);\n        });\n        cdpClient.on('Network.requestWillBeSentExtraInfo', (params) => {\n            networkProcessor\n                .#getOrCreateNetworkRequest(params.requestId)\n                .onRequestWillBeSentExtraInfoEvent(params);\n        });\n        cdpClient.on('Network.responseReceived', (params) => {\n            networkProcessor\n                .#getOrCreateNetworkRequest(params.requestId)\n                .onResponseReceivedEvent(params);\n        });\n        cdpClient.on('Network.responseReceivedExtraInfo', (params) => {\n            networkProcessor\n                .#getOrCreateNetworkRequest(params.requestId)\n                .onResponseReceivedEventExtraInfo(params);\n        });\n        cdpClient.on('Network.loadingFailed', (params) => {\n            networkProcessor\n                .#getOrCreateNetworkRequest(params.requestId)\n                .onLoadingFailedEvent(params);\n        });\n        await cdpClient.sendCommand('Network.enable');\n        return networkProcessor;\n    }\n    #getOrCreateNetworkRequest(requestId) {\n        return this.#requestMap.get(requestId);\n    }\n}\nexports.NetworkProcessor = NetworkProcessor;\n//# sourceMappingURL=networkProcessor.js.map", "\"use strict\";\n/*\n * Copyright 2023 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CdpTarget = void 0;\nconst logManager_1 = require(\"../log/logManager\");\nconst protocol_1 = require(\"../../../protocol/protocol\");\nconst deferred_1 = require(\"../../../utils/deferred\");\nconst networkProcessor_1 = require(\"../network/networkProcessor\");\nclass CdpTarget {\n    #targetUnblocked;\n    #targetId;\n    #cdpClient;\n    #eventManager;\n    #cdpSessionId;\n    #networkDomainActivated;\n    static create(targetId, cdpClient, cdpSessionId, realmStorage, eventManager) {\n        const cdpTarget = new CdpTarget(targetId, cdpClient, cdpSessionId, eventManager);\n        logManager_1.LogManager.create(cdpTarget, realmStorage, eventManager);\n        cdpTarget.#setEventListeners();\n        // No need in waiting. Deferred will be resolved when the target is unblocked.\n        void cdpTarget.#unblock();\n        return cdpTarget;\n    }\n    constructor(targetId, cdpClient, cdpSessionId, eventManager) {\n        this.#targetId = targetId;\n        this.#cdpClient = cdpClient;\n        this.#cdpSessionId = cdpSessionId;\n        this.#eventManager = eventManager;\n        this.#networkDomainActivated = false;\n        this.#targetUnblocked = new deferred_1.Deferred();\n    }\n    /**\n     * Returns a promise that resolves when the target is unblocked.\n     */\n    get targetUnblocked() {\n        return this.#targetUnblocked;\n    }\n    get targetId() {\n        return this.#targetId;\n    }\n    get cdpClient() {\n        return this.#cdpClient;\n    }\n    /**\n     * Needed for CDP escape path.\n     */\n    get cdpSessionId() {\n        return this.#cdpSessionId;\n    }\n    /**\n     * Enables all the required CDP domains and unblocks the target.\n     */\n    async #unblock() {\n        // Enable Network domain, if it is enabled globally.\n        // TODO: enable Network domain for OOPiF targets.\n        if (this.#eventManager.isNetworkDomainEnabled) {\n            await this.enableNetworkDomain();\n        }\n        await this.#cdpClient.sendCommand('Runtime.enable');\n        await this.#cdpClient.sendCommand('Page.enable');\n        await this.#cdpClient.sendCommand('Page.setLifecycleEventsEnabled', {\n            enabled: true,\n        });\n        await this.#cdpClient.sendCommand('Target.setAutoAttach', {\n            autoAttach: true,\n            waitForDebuggerOnStart: true,\n            flatten: true,\n        });\n        await this.#cdpClient.sendCommand('Runtime.runIfWaitingForDebugger');\n        this.#targetUnblocked.resolve();\n    }\n    /**\n     * Enables the Network domain (creates NetworkProcessor on the target's cdp\n     * client) if it is not enabled yet.\n     */\n    async enableNetworkDomain() {\n        if (!this.#networkDomainActivated) {\n            this.#networkDomainActivated = true;\n            await networkProcessor_1.NetworkProcessor.create(this.cdpClient, this.#eventManager);\n        }\n    }\n    #setEventListeners() {\n        this.#cdpClient.on('*', (method, params) => {\n            this.#eventManager.registerEvent({\n                method: protocol_1.CDP.EventNames.EventReceivedEvent,\n                params: {\n                    cdpMethod: method,\n                    cdpParams: params || {},\n                    cdpSession: this.#cdpSessionId,\n                },\n            }, null);\n        });\n    }\n}\nexports.CdpTarget = CdpTarget;\n//# sourceMappingURL=cdpTarget.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BrowsingContextProcessor = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\nconst log_js_1 = require(\"../../../utils/log.js\");\nconst browsingContextImpl_js_1 = require(\"./browsingContextImpl.js\");\nconst cdpTarget_js_1 = require(\"./cdpTarget.js\");\nclass BrowsingContextProcessor {\n    #browsingContextStorage;\n    #cdpConnection;\n    #eventManager;\n    #logger;\n    #realmStorage;\n    #selfTargetId;\n    constructor(realmStorage, cdpConnection, selfTargetId, eventManager, browsingContextStorage, logger) {\n        this.#browsingContextStorage = browsingContextStorage;\n        this.#cdpConnection = cdpConnection;\n        this.#eventManager = eventManager;\n        this.#logger = logger;\n        this.#realmStorage = realmStorage;\n        this.#selfTargetId = selfTargetId;\n        this.#setEventListeners(this.#cdpConnection.browserClient());\n    }\n    /**\n     * This method is called for each CDP session, since this class is responsible\n     * for creating and destroying all targets and browsing contexts.\n     */\n    #setEventListeners(cdpClient) {\n        cdpClient.on('Target.attachedToTarget', (params) => {\n            this.#handleAttachedToTargetEvent(params, cdpClient);\n        });\n        cdpClient.on('Target.detachedFromTarget', (params) => {\n            this.#handleDetachedFromTargetEvent(params);\n        });\n        cdpClient.on('Page.frameAttached', (params) => {\n            this.#handleFrameAttachedEvent(params);\n        });\n        cdpClient.on('Page.frameDetached', (params) => {\n            this.#handleFrameDetachedEvent(params);\n        });\n    }\n    // { \"method\": \"Page.frameAttached\",\n    //   \"params\": {\n    //     \"frameId\": \"0A639AB1D9A392DF2CE02C53CC4ED3A6\",\n    //     \"parentFrameId\": \"722BB0526C73B067A479BED6D0DB1156\" } }\n    #handleFrameAttachedEvent(params) {\n        const parentBrowsingContext = this.#browsingContextStorage.findContext(params.parentFrameId);\n        if (parentBrowsingContext !== undefined) {\n            browsingContextImpl_js_1.BrowsingContextImpl.create(parentBrowsingContext.cdpTarget, this.#realmStorage, params.frameId, params.parentFrameId, this.#eventManager, this.#browsingContextStorage, this.#logger);\n        }\n    }\n    // { \"method\": \"Page.frameDetached\",\n    //   \"params\": {\n    //     \"frameId\": \"0A639AB1D9A392DF2CE02C53CC4ED3A6\",\n    //     \"reason\": \"swap\" } }\n    #handleFrameDetachedEvent(params) {\n        // In case of OOPiF no need in deleting BrowsingContext.\n        if (params.reason === 'swap') {\n            return;\n        }\n        this.#browsingContextStorage.findContext(params.frameId)?.delete();\n    }\n    // { \"method\": \"Target.attachedToTarget\",\n    //   \"params\": {\n    //     \"sessionId\": \"EA999F39BDCABD7D45C9FEB787413BBA\",\n    //     \"targetInfo\": {\n    //       \"targetId\": \"722BB0526C73B067A479BED6D0DB1156\",\n    //       \"type\": \"page\",\n    //       \"title\": \"about:blank\",\n    //       \"url\": \"about:blank\",\n    //       \"attached\": true,\n    //       \"canAccessOpener\": false,\n    //       \"browserContextId\": \"1B5244080EC3FF28D03BBDA73138C0E2\" },\n    //     \"waitingForDebugger\": false } }\n    #handleAttachedToTargetEvent(params, parentSessionCdpClient) {\n        const { sessionId, targetInfo } = params;\n        const targetCdpClient = this.#cdpConnection.getCdpClient(sessionId);\n        if (!this.#isValidTarget(targetInfo)) {\n            // DevTools or some other not supported by BiDi target. Just release\n            // debugger  and ignore them.\n            void targetCdpClient\n                .sendCommand('Runtime.runIfWaitingForDebugger')\n                .then(() => parentSessionCdpClient.sendCommand('Target.detachFromTarget', params));\n            return;\n        }\n        this.#logger?.(log_js_1.LogType.browsingContexts, 'AttachedToTarget event received:', JSON.stringify(params, null, 2));\n        this.#setEventListeners(targetCdpClient);\n        const cdpTarget = cdpTarget_js_1.CdpTarget.create(targetInfo.targetId, targetCdpClient, sessionId, this.#realmStorage, this.#eventManager);\n        if (this.#browsingContextStorage.hasContext(targetInfo.targetId)) {\n            // OOPiF.\n            this.#browsingContextStorage\n                .getContext(targetInfo.targetId)\n                .updateCdpTarget(cdpTarget);\n        }\n        else {\n            browsingContextImpl_js_1.BrowsingContextImpl.create(cdpTarget, this.#realmStorage, targetInfo.targetId, null, this.#eventManager, this.#browsingContextStorage, this.#logger);\n        }\n    }\n    // { \"method\": \"Target.detachedFromTarget\",\n    //   \"params\": {\n    //     \"sessionId\": \"7EFBFB2A4942A8989B3EADC561BC46E9\",\n    //     \"targetId\": \"19416886405CBA4E03DBB59FA67FF4E8\" } }\n    #handleDetachedFromTargetEvent(params) {\n        // TODO: params.targetId is deprecated. Update this class to track using\n        // params.sessionId instead.\n        // https://github.com/GoogleChromeLabs/chromium-bidi/issues/60\n        const contextId = params.targetId;\n        this.#browsingContextStorage.findContext(contextId)?.delete();\n    }\n    async #getRealm(target) {\n        if ('realm' in target) {\n            return this.#realmStorage.getRealm({\n                realmId: target.realm,\n            });\n        }\n        const context = this.#browsingContextStorage.getContext(target.context);\n        return context.getOrCreateSandbox(target.sandbox);\n    }\n    process_browsingContext_getTree(params) {\n        const resultContexts = params.root === undefined\n            ? this.#browsingContextStorage.getTopLevelContexts()\n            : [this.#browsingContextStorage.getContext(params.root)];\n        return {\n            result: {\n                contexts: resultContexts.map((c) => c.serializeToBidiValue(params.maxDepth ?? Number.MAX_VALUE)),\n            },\n        };\n    }\n    async process_browsingContext_create(params) {\n        const browserCdpClient = this.#cdpConnection.browserClient();\n        let referenceContext = undefined;\n        if (params.referenceContext !== undefined) {\n            referenceContext = this.#browsingContextStorage.getContext(params.referenceContext);\n            if (!referenceContext.isTopLevelContext()) {\n                throw new protocol_js_1.Message.InvalidArgumentException(`referenceContext should be a top-level context`);\n            }\n        }\n        const result = await browserCdpClient.sendCommand('Target.createTarget', {\n            url: 'about:blank',\n            newWindow: params.type === 'window',\n        });\n        // Wait for the new tab to be loaded to avoid race conditions in the\n        // `browsingContext` events, when the `browsingContext.domContentLoaded` and\n        // `browsingContext.load` events from the initial `about:blank` navigation\n        // are emitted after the next navigation is started.\n        // Details: https://github.com/web-platform-tests/wpt/issues/35846\n        const contextId = result.targetId;\n        const context = this.#browsingContextStorage.getContext(contextId);\n        await context.awaitLoaded();\n        return {\n            result: context.serializeToBidiValue(1),\n        };\n    }\n    process_browsingContext_navigate(params) {\n        const context = this.#browsingContextStorage.getContext(params.context);\n        return context.navigate(params.url, params.wait === undefined ? 'none' : params.wait);\n    }\n    async process_browsingContext_captureScreenshot(params) {\n        const context = this.#browsingContextStorage.getContext(params.context);\n        return context.captureScreenshot();\n    }\n    async process_browsingContext_print(params) {\n        const context = this.#browsingContextStorage.getContext(params.context);\n        return context.print(params);\n    }\n    async process_script_addPreloadScript(params) {\n        const contexts = [];\n        const scripts = [];\n        if (params.context) {\n            // TODO(#293): Handle edge case with OOPiF. Whenever a frame is moved out\n            // of process, we have to add those scripts as well.\n            contexts.push(this.#browsingContextStorage.getContext(params.context));\n        }\n        else {\n            // Add all contexts.\n            // TODO(#293): Add preload scripts to all new browsing contexts as well.\n            contexts.push(...this.#browsingContextStorage.getAllContexts());\n        }\n        scripts.push(...(await Promise.all(contexts.map((context) => context.addPreloadScript(params)))));\n        // TODO(#293): What to return whenever there are multiple contexts?\n        return scripts[0];\n    }\n    // eslint-disable-next-line @typescript-eslint/require-await\n    async process_script_removePreloadScript(_params) {\n        throw new protocol_js_1.Message.UnknownErrorException('Not implemented.');\n        return {};\n    }\n    async process_script_evaluate(params) {\n        const realm = await this.#getRealm(params.target);\n        return realm.scriptEvaluate(params.expression, params.awaitPromise, params.resultOwnership ?? 'none');\n    }\n    process_script_getRealms(params) {\n        if (params.context !== undefined) {\n            // Make sure the context is known.\n            this.#browsingContextStorage.getContext(params.context);\n        }\n        const realms = this.#realmStorage\n            .findRealms({\n            browsingContextId: params.context,\n            type: params.type,\n        })\n            .map((realm) => realm.toBiDi());\n        return { result: { realms } };\n    }\n    async process_script_callFunction(params) {\n        const realm = await this.#getRealm(params.target);\n        return realm.callFunction(params.functionDeclaration, params.this || {\n            type: 'undefined',\n        }, // `this` is `undefined` by default.\n        params.arguments || [], // `arguments` is `[]` by default.\n        params.awaitPromise, params.resultOwnership ?? 'none');\n    }\n    async process_script_disown(params) {\n        const realm = await this.#getRealm(params.target);\n        await Promise.all(params.handles.map(async (h) => realm.disown(h)));\n        return { result: {} };\n    }\n    async process_browsingContext_close(commandParams) {\n        const browserCdpClient = this.#cdpConnection.browserClient();\n        const context = this.#browsingContextStorage.getContext(commandParams.context);\n        if (!context.isTopLevelContext()) {\n            throw new protocol_js_1.Message.InvalidArgumentException('A top-level browsing context cannot be closed.');\n        }\n        const detachedFromTargetPromise = new Promise((resolve) => {\n            const onContextDestroyed = (eventParams) => {\n                if (eventParams.targetId === commandParams.context) {\n                    browserCdpClient.off('Target.detachedFromTarget', onContextDestroyed);\n                    resolve();\n                }\n            };\n            browserCdpClient.on('Target.detachedFromTarget', onContextDestroyed);\n        });\n        await browserCdpClient.sendCommand('Target.closeTarget', {\n            targetId: commandParams.context,\n        });\n        // Sometimes CDP command finishes before `detachedFromTarget` event,\n        // sometimes after. Wait for the CDP command to be finished, and then wait\n        // for `detachedFromTarget` if it hasn't emitted.\n        await detachedFromTargetPromise;\n        return { result: {} };\n    }\n    #isValidTarget(target) {\n        if (target.targetId === this.#selfTargetId) {\n            return false;\n        }\n        return ['page', 'iframe'].includes(target.type);\n    }\n    async process_cdp_sendCommand(params) {\n        const client = params.cdpSession\n            ? this.#cdpConnection.getCdpClient(params.cdpSession)\n            : this.#cdpConnection.browserClient();\n        const sendCdpCommandResult = await client.sendCommand(params.cdpMethod, params.cdpParams);\n        return {\n            result: sendCdpCommandResult,\n            cdpSession: params.cdpSession,\n        };\n    }\n    process_cdp_getSession(params) {\n        const context = params.context;\n        const sessionId = this.#browsingContextStorage.getContext(context).cdpTarget.cdpSessionId;\n        if (sessionId === undefined) {\n            return { result: { cdpSession: null } };\n        }\n        return { result: { cdpSession: sessionId } };\n    }\n}\nexports.BrowsingContextProcessor = BrowsingContextProcessor;\n//# sourceMappingURL=browsingContextProcessor.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OutgoingBidiMessage = void 0;\nclass OutgoingBidiMessage {\n    #message;\n    #channel;\n    constructor(message, channel) {\n        this.#message = message;\n        this.#channel = channel;\n    }\n    static async createFromPromise(messagePromise, channel) {\n        return messagePromise.then((message) => new OutgoingBidiMessage(message, channel));\n    }\n    static createResolved(message, channel) {\n        return Promise.resolve(new OutgoingBidiMessage(message, channel));\n    }\n    get message() {\n        return this.#message;\n    }\n    get channel() {\n        return this.#channel;\n    }\n}\nexports.OutgoingBidiMessage = OutgoingBidiMessage;\n//# sourceMappingURL=OutgoingBidiMessage.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommandProcessor = void 0;\nconst protocol_js_1 = require(\"../protocol/protocol.js\");\nconst log_js_1 = require(\"../utils/log.js\");\nconst EventEmitter_js_1 = require(\"../utils/EventEmitter.js\");\nconst browsingContextProcessor_js_1 = require(\"./domains/context/browsingContextProcessor.js\");\nconst OutgoingBidiMessage_js_1 = require(\"./OutgoingBidiMessage.js\");\nclass BidiNoOpParser {\n    parseAddPreloadScriptParams(params) {\n        return params;\n    }\n    parseRemovePreloadScriptParams(params) {\n        return params;\n    }\n    parseGetRealmsParams(params) {\n        return params;\n    }\n    parseCallFunctionParams(params) {\n        return params;\n    }\n    parseEvaluateParams(params) {\n        return params;\n    }\n    parseDisownParams(params) {\n        return params;\n    }\n    parseSendCommandParams(params) {\n        return params;\n    }\n    parseGetSessionParams(params) {\n        return params;\n    }\n    parseSubscribeParams(params) {\n        return params;\n    }\n    parseNavigateParams(params) {\n        return params;\n    }\n    parseGetTreeParams(params) {\n        return params;\n    }\n    parseCreateParams(params) {\n        return params;\n    }\n    parseCloseParams(params) {\n        return params;\n    }\n    parseCaptureScreenshotParams(params) {\n        return params;\n    }\n    parsePrintParams(params) {\n        return params;\n    }\n}\nclass CommandProcessor extends EventEmitter_js_1.EventEmitter {\n    #contextProcessor;\n    #eventManager;\n    #parser;\n    #logger;\n    constructor(realmStorage, cdpConnection, eventManager, selfTargetId, parser = new BidiNoOpParser(), browsingContextStorage, logger) {\n        super();\n        this.#eventManager = eventManager;\n        this.#logger = logger;\n        this.#contextProcessor = new browsingContextProcessor_js_1.BrowsingContextProcessor(realmStorage, cdpConnection, selfTargetId, eventManager, browsingContextStorage, logger);\n        this.#parser = parser;\n    }\n    static #process_session_status() {\n        return { result: { ready: false, message: 'already connected' } };\n    }\n    async #process_session_subscribe(params, channel) {\n        await this.#eventManager.subscribe(params.events, params.contexts ?? [null], channel);\n        return { result: {} };\n    }\n    async #process_session_unsubscribe(params, channel) {\n        await this.#eventManager.unsubscribe(params.events, params.contexts ?? [null], channel);\n        return { result: {} };\n    }\n    async #processCommand(commandData) {\n        switch (commandData.method) {\n            case 'session.status':\n                return CommandProcessor.#process_session_status();\n            case 'session.subscribe':\n                return this.#process_session_subscribe(this.#parser.parseSubscribeParams(commandData.params), commandData.channel ?? null);\n            case 'session.unsubscribe':\n                return this.#process_session_unsubscribe(this.#parser.parseSubscribeParams(commandData.params), commandData.channel ?? null);\n            case 'browsingContext.create':\n                return this.#contextProcessor.process_browsingContext_create(this.#parser.parseCreateParams(commandData.params));\n            case 'browsingContext.close':\n                return this.#contextProcessor.process_browsingContext_close(this.#parser.parseCloseParams(commandData.params));\n            case 'browsingContext.getTree':\n                return this.#contextProcessor.process_browsingContext_getTree(this.#parser.parseGetTreeParams(commandData.params));\n            case 'browsingContext.navigate':\n                return this.#contextProcessor.process_browsingContext_navigate(this.#parser.parseNavigateParams(commandData.params));\n            case 'browsingContext.captureScreenshot':\n                return this.#contextProcessor.process_browsingContext_captureScreenshot(this.#parser.parseCaptureScreenshotParams(commandData.params));\n            case 'browsingContext.print':\n                return this.#contextProcessor.process_browsingContext_print(this.#parser.parsePrintParams(commandData.params));\n            case 'script.addPreloadScript':\n                return this.#contextProcessor.process_script_addPreloadScript(this.#parser.parseAddPreloadScriptParams(commandData.params));\n            case 'script.removePreloadScript':\n                return this.#contextProcessor.process_script_removePreloadScript(this.#parser.parseRemovePreloadScriptParams(commandData.params));\n            case 'script.getRealms':\n                return this.#contextProcessor.process_script_getRealms(this.#parser.parseGetRealmsParams(commandData.params));\n            case 'script.callFunction':\n                return this.#contextProcessor.process_script_callFunction(this.#parser.parseCallFunctionParams(commandData.params));\n            case 'script.evaluate':\n                return this.#contextProcessor.process_script_evaluate(this.#parser.parseEvaluateParams(commandData.params));\n            case 'script.disown':\n                return this.#contextProcessor.process_script_disown(this.#parser.parseDisownParams(commandData.params));\n            case 'cdp.sendCommand':\n                return this.#contextProcessor.process_cdp_sendCommand(this.#parser.parseSendCommandParams(commandData.params));\n            case 'cdp.getSession':\n                return this.#contextProcessor.process_cdp_getSession(this.#parser.parseGetSessionParams(commandData.params));\n            default:\n                throw new protocol_js_1.Message.UnknownCommandException(`Unknown command '${commandData.method}'.`);\n        }\n    }\n    async processCommand(command) {\n        try {\n            const result = await this.#processCommand(command);\n            const response = {\n                id: command.id,\n                ...result,\n            };\n            this.emit('response', OutgoingBidiMessage_js_1.OutgoingBidiMessage.createResolved(response, command.channel ?? null));\n        }\n        catch (e) {\n            if (e instanceof protocol_js_1.Message.ErrorResponse) {\n                const errorResponse = e;\n                this.emit('response', OutgoingBidiMessage_js_1.OutgoingBidiMessage.createResolved(errorResponse.toErrorResponse(command.id), command.channel ?? null));\n            }\n            else {\n                const error = e;\n                this.#logger?.(log_js_1.LogType.bidi, error);\n                this.emit('response', OutgoingBidiMessage_js_1.OutgoingBidiMessage.createResolved(new protocol_js_1.Message.ErrorResponse(protocol_js_1.Message.ErrorCode.UnknownError, error.message).toErrorResponse(command.id), command.channel ?? null));\n            }\n        }\n    }\n}\nexports.CommandProcessor = CommandProcessor;\n//# sourceMappingURL=CommandProcessor.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BrowsingContextStorage = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\n/** Container class for browsing contexts. */\nclass BrowsingContextStorage {\n    /** Map from context ID to context implementation. */\n    #contexts = new Map();\n    /** Gets all top-level contexts, i.e. those with no parent. */\n    getTopLevelContexts() {\n        return this.getAllContexts().filter((c) => c.isTopLevelContext());\n    }\n    /** Gets all contexts. */\n    getAllContexts() {\n        return Array.from(this.#contexts.values());\n    }\n    /** Deletes the context with the given ID. */\n    deleteContext(contextId) {\n        this.#contexts.delete(contextId);\n    }\n    /** Adds the given context. */\n    addContext(context) {\n        this.#contexts.set(context.contextId, context);\n        if (!context.isTopLevelContext()) {\n            this.getContext(context.parentId).addChild(context);\n        }\n    }\n    /** Returns true whether there is an existing context with the given ID. */\n    hasContext(contextId) {\n        return this.#contexts.has(contextId);\n    }\n    /** Gets the context with the given ID, if any. */\n    findContext(contextId) {\n        return this.#contexts.get(contextId);\n    }\n    /** Returns the top-level context ID of the given context, if any. */\n    findTopLevelContextId(contextId) {\n        if (contextId === null) {\n            return null;\n        }\n        const maybeContext = this.findContext(contextId);\n        const parentId = maybeContext?.parentId ?? null;\n        if (parentId === null) {\n            return contextId;\n        }\n        return this.findTopLevelContextId(parentId);\n    }\n    /** Gets the context with the given ID, if any, otherwise throws. */\n    getContext(contextId) {\n        const result = this.findContext(contextId);\n        if (result === undefined) {\n            throw new protocol_js_1.Message.NoSuchFrameException(`Context ${contextId} not found`);\n        }\n        return result;\n    }\n}\nexports.BrowsingContextStorage = BrowsingContextStorage;\n//# sourceMappingURL=browsingContextStorage.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Buffer = void 0;\n/**\n * Implements a FIFO buffer with a fixed size.\n */\nclass Buffer {\n    #capacity;\n    #entries = [];\n    #onItemRemoved;\n    /**\n     * @param capacity\n     * @param onItemRemoved optional delegate called for each removed element.\n     */\n    constructor(capacity, onItemRemoved = () => { }) {\n        this.#capacity = capacity;\n        this.#onItemRemoved = onItemRemoved;\n    }\n    get() {\n        return this.#entries;\n    }\n    add(value) {\n        this.#entries.push(value);\n        while (this.#entries.length > this.#capacity) {\n            const item = this.#entries.shift();\n            if (item !== undefined) {\n                this.#onItemRemoved(item);\n            }\n        }\n    }\n}\nexports.Buffer = Buffer;\n//# sourceMappingURL=buffer.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IdWrapper = void 0;\n/**\n * Creates an object with a positive unique incrementing id.\n */\nclass IdWrapper {\n    static #counter = 0;\n    #id;\n    constructor() {\n        this.#id = ++IdWrapper.#counter;\n    }\n    get id() {\n        return this.#id;\n    }\n}\nexports.IdWrapper = IdWrapper;\n//# sourceMappingURL=idWrapper.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SubscriptionManager = exports.unrollEvents = exports.cartesianProduct = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\n/**\n * Returns the cartesian product of the given arrays.\n *\n * Example:\n *   cartesian([1, 2], ['a', 'b']); => [[1, 'a'], [1, 'b'], [2, 'a'], [2, 'b']]\n */\nfunction cartesianProduct(...a) {\n    return a.reduce((a, b) => a.flatMap((d) => b.map((e) => [d, e].flat())));\n}\nexports.cartesianProduct = cartesianProduct;\n/** Expands \"AllEvents\" events into atomic events. */\nfunction unrollEvents(events) {\n    const allEvents = [];\n    for (const event of events) {\n        switch (event) {\n            case protocol_js_1.BrowsingContext.AllEvents:\n                allEvents.push(...Object.values(protocol_js_1.BrowsingContext.EventNames));\n                break;\n            case protocol_js_1.CDP.AllEvents:\n                allEvents.push(...Object.values(protocol_js_1.CDP.EventNames));\n                break;\n            case protocol_js_1.Log.AllEvents:\n                allEvents.push(...Object.values(protocol_js_1.Log.EventNames));\n                break;\n            case protocol_js_1.Network.AllEvents:\n                allEvents.push(...Object.values(protocol_js_1.Network.EventNames));\n                break;\n            case protocol_js_1.Script.AllEvents:\n                allEvents.push(...Object.values(protocol_js_1.Script.EventNames));\n                break;\n            default:\n                allEvents.push(event);\n        }\n    }\n    return allEvents;\n}\nexports.unrollEvents = unrollEvents;\nclass SubscriptionManager {\n    #subscriptionPriority = 0;\n    // BrowsingContext `null` means the event has subscription across all the\n    // browsing contexts.\n    // Channel `null` means no `channel` should be added.\n    #channelToContextToEventMap = new Map();\n    #browsingContextStorage;\n    constructor(browsingContextStorage) {\n        this.#browsingContextStorage = browsingContextStorage;\n    }\n    getChannelsSubscribedToEvent(eventMethod, contextId) {\n        const prioritiesAndChannels = Array.from(this.#channelToContextToEventMap.keys())\n            .map((channel) => ({\n            priority: this.#getEventSubscriptionPriorityForChannel(eventMethod, contextId, channel),\n            channel,\n        }))\n            .filter(({ priority }) => priority !== null);\n        // Sort channels by priority.\n        return prioritiesAndChannels\n            .sort((a, b) => a.priority - b.priority)\n            .map(({ channel }) => channel);\n    }\n    #getEventSubscriptionPriorityForChannel(eventMethod, contextId, channel) {\n        const contextToEventMap = this.#channelToContextToEventMap.get(channel);\n        if (contextToEventMap === undefined) {\n            return null;\n        }\n        const maybeTopLevelContextId = this.#browsingContextStorage.findTopLevelContextId(contextId);\n        // `null` covers global subscription.\n        const relevantContexts = [...new Set([null, maybeTopLevelContextId])];\n        // Get all the subscription priorities.\n        const priorities = relevantContexts\n            .map((c) => contextToEventMap.get(c)?.get(eventMethod))\n            .filter((p) => p !== undefined);\n        if (priorities.length === 0) {\n            // Not subscribed, return null.\n            return null;\n        }\n        // Return minimal priority.\n        return Math.min(...priorities);\n    }\n    subscribe(event, contextId, channel) {\n        // All the subscriptions are handled on the top-level contexts.\n        contextId = this.#browsingContextStorage.findTopLevelContextId(contextId);\n        if (event === protocol_js_1.BrowsingContext.AllEvents) {\n            Object.values(protocol_js_1.BrowsingContext.EventNames).map((specificEvent) => this.subscribe(specificEvent, contextId, channel));\n            return;\n        }\n        if (event === protocol_js_1.CDP.AllEvents) {\n            Object.values(protocol_js_1.CDP.EventNames).map((specificEvent) => this.subscribe(specificEvent, contextId, channel));\n            return;\n        }\n        if (event === protocol_js_1.Log.AllEvents) {\n            Object.values(protocol_js_1.Log.EventNames).map((specificEvent) => this.subscribe(specificEvent, contextId, channel));\n            return;\n        }\n        if (event === protocol_js_1.Network.AllEvents) {\n            Object.values(protocol_js_1.Network.EventNames).map((specificEvent) => this.subscribe(specificEvent, contextId, channel));\n            return;\n        }\n        if (event === protocol_js_1.Script.AllEvents) {\n            Object.values(protocol_js_1.Script.EventNames).map((specificEvent) => this.subscribe(specificEvent, contextId, channel));\n            return;\n        }\n        if (!this.#channelToContextToEventMap.has(channel)) {\n            this.#channelToContextToEventMap.set(channel, new Map());\n        }\n        const contextToEventMap = this.#channelToContextToEventMap.get(channel);\n        if (!contextToEventMap.has(contextId)) {\n            contextToEventMap.set(contextId, new Map());\n        }\n        const eventMap = contextToEventMap.get(contextId);\n        // Do not re-subscribe to events to keep the priority.\n        if (eventMap.has(event)) {\n            return;\n        }\n        eventMap.set(event, this.#subscriptionPriority++);\n    }\n    /**\n     * Unsubscribes atomically from all events in the given contexts and channel.\n     */\n    unsubscribeAll(events, contextIds, channel) {\n        // Assert all contexts are known.\n        for (const contextId of contextIds) {\n            if (contextId !== null) {\n                this.#browsingContextStorage.getContext(contextId);\n            }\n        }\n        const eventContextPairs = cartesianProduct(unrollEvents(events), contextIds);\n        // Assert all unsubscriptions are valid.\n        // If any of the unsubscriptions are invalid, do not unsubscribe from anything.\n        eventContextPairs\n            .map(([event, contextId]) => this.#checkUnsubscribe(event, contextId, channel))\n            .forEach((unsubscribe) => unsubscribe());\n    }\n    /**\n     * Unsubscribes from the event in the given context and channel.\n     * Syntactic sugar for \"unsubscribeAll\".\n     */\n    unsubscribe(eventName, contextId, channel) {\n        this.unsubscribeAll([eventName], [contextId], channel);\n    }\n    #checkUnsubscribe(event, contextId, channel) {\n        // All the subscriptions are handled on the top-level contexts.\n        contextId = this.#browsingContextStorage.findTopLevelContextId(contextId);\n        if (!this.#channelToContextToEventMap.has(channel)) {\n            throw new protocol_js_1.Message.InvalidArgumentException(`Cannot unsubscribe from ${event}, ${contextId === null ? 'null' : contextId}. No subscription found.`);\n        }\n        const contextToEventMap = this.#channelToContextToEventMap.get(channel);\n        if (!contextToEventMap.has(contextId)) {\n            throw new protocol_js_1.Message.InvalidArgumentException(`Cannot unsubscribe from ${event}, ${contextId === null ? 'null' : contextId}. No subscription found.`);\n        }\n        const eventMap = contextToEventMap.get(contextId);\n        if (!eventMap.has(event)) {\n            throw new protocol_js_1.Message.InvalidArgumentException(`Cannot unsubscribe from ${event}, ${contextId === null ? 'null' : contextId}. No subscription found.`);\n        }\n        return () => {\n            eventMap.delete(event);\n            // Clean up maps if empty.\n            if (eventMap.size === 0) {\n                contextToEventMap.delete(event);\n            }\n            if (contextToEventMap.size === 0) {\n                this.#channelToContextToEventMap.delete(channel);\n            }\n        };\n    }\n}\nexports.SubscriptionManager = SubscriptionManager;\n//# sourceMappingURL=SubscriptionManager.js.map", "\"use strict\";\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventManager = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\nconst buffer_js_1 = require(\"../../../utils/buffer.js\");\nconst idWrapper_js_1 = require(\"../../../utils/idWrapper.js\");\nconst OutgoingBidiMessage_js_1 = require(\"../../OutgoingBidiMessage.js\");\nconst DefaultMap_js_1 = require(\"../../../utils/DefaultMap.js\");\nconst SubscriptionManager_js_1 = require(\"./SubscriptionManager.js\");\nclass EventWrapper {\n    #idWrapper;\n    #contextId;\n    #event;\n    constructor(event, contextId) {\n        this.#idWrapper = new idWrapper_js_1.IdWrapper();\n        this.#contextId = contextId;\n        this.#event = event;\n    }\n    get id() {\n        return this.#idWrapper.id;\n    }\n    get contextId() {\n        return this.#contextId;\n    }\n    get event() {\n        return this.#event;\n    }\n}\n/**\n * Maps event name to a desired buffer length.\n */\nconst eventBufferLength = new Map([\n    [protocol_js_1.Log.EventNames.LogEntryAddedEvent, 100],\n]);\nclass EventManager {\n    static #NETWORK_DOMAIN_PREFIX = 'network';\n    /**\n     * Maps event name to a set of contexts where this event already happened.\n     * Needed for getting buffered events from all the contexts in case of\n     * subscripting to all contexts.\n     */\n    #eventToContextsMap = new DefaultMap_js_1.DefaultMap(() => new Set());\n    /**\n     * Maps `eventName` + `browsingContext` to buffer. Used to get buffered events\n     * during subscription. Channel-agnostic.\n     */\n    #eventBuffers = new Map();\n    /**\n     * Maps `eventName` + `browsingContext` + `channel` to last sent event id.\n     * Used to avoid sending duplicated events when user\n     * subscribes -> unsubscribes -> subscribes.\n     */\n    #lastMessageSent = new Map();\n    #subscriptionManager;\n    #bidiServer;\n    #isNetworkDomainEnabled;\n    constructor(bidiServer) {\n        this.#bidiServer = bidiServer;\n        this.#subscriptionManager = new SubscriptionManager_js_1.SubscriptionManager(bidiServer.getBrowsingContextStorage());\n        this.#isNetworkDomainEnabled = false;\n    }\n    get isNetworkDomainEnabled() {\n        return this.#isNetworkDomainEnabled;\n    }\n    /**\n     * Returns consistent key to be used to access value maps.\n     */\n    static #getMapKey(eventName, browsingContext, channel) {\n        return JSON.stringify({ eventName, browsingContext, channel });\n    }\n    registerEvent(event, contextId) {\n        this.registerPromiseEvent(Promise.resolve(event), contextId, event.method);\n    }\n    registerPromiseEvent(event, contextId, eventName) {\n        const eventWrapper = new EventWrapper(event, contextId);\n        const sortedChannels = this.#subscriptionManager.getChannelsSubscribedToEvent(eventName, contextId);\n        this.#bufferEvent(eventWrapper, eventName);\n        // Send events to channels in the subscription priority.\n        for (const channel of sortedChannels) {\n            this.#bidiServer.emitOutgoingMessage(OutgoingBidiMessage_js_1.OutgoingBidiMessage.createFromPromise(event, channel));\n            this.#markEventSent(eventWrapper, channel, eventName);\n        }\n    }\n    async subscribe(eventNames, contextIds, channel) {\n        // First check if all the contexts are known.\n        for (const contextId of contextIds) {\n            if (contextId !== null) {\n                // Assert the context is known. Throw exception otherwise.\n                this.#bidiServer.getBrowsingContextStorage().getContext(contextId);\n            }\n        }\n        for (const eventName of eventNames) {\n            for (const contextId of contextIds) {\n                await this.#handleDomains(eventName, contextId);\n                this.#subscriptionManager.subscribe(eventName, contextId, channel);\n                for (const eventWrapper of this.#getBufferedEvents(eventName, contextId, channel)) {\n                    // The order of the events is important.\n                    this.#bidiServer.emitOutgoingMessage(OutgoingBidiMessage_js_1.OutgoingBidiMessage.createFromPromise(eventWrapper.event, channel));\n                    this.#markEventSent(eventWrapper, channel, eventName);\n                }\n            }\n        }\n    }\n    /**\n     * Enables domains for the subscribed event in the required contexts or\n     * globally.\n     */\n    async #handleDomains(eventName, contextId) {\n        // Enable network domain if user subscribed to any of network events.\n        if (eventName.startsWith(EventManager.#NETWORK_DOMAIN_PREFIX)) {\n            // Enable for all the contexts.\n            if (contextId === null) {\n                this.#isNetworkDomainEnabled = true;\n                await Promise.all(this.#bidiServer\n                    .getBrowsingContextStorage()\n                    .getAllContexts()\n                    .map((context) => context.cdpTarget.enableNetworkDomain()));\n            }\n            else {\n                await this.#bidiServer\n                    .getBrowsingContextStorage()\n                    .getContext(contextId)\n                    .cdpTarget.enableNetworkDomain();\n            }\n        }\n    }\n    unsubscribe(eventNames, contextIds, channel) {\n        this.#subscriptionManager.unsubscribeAll(eventNames, contextIds, channel);\n    }\n    /**\n     * If the event is buffer-able, put it in the buffer.\n     */\n    #bufferEvent(eventWrapper, eventName) {\n        if (!eventBufferLength.has(eventName)) {\n            // Do nothing if the event is no buffer-able.\n            return;\n        }\n        const bufferMapKey = EventManager.#getMapKey(eventName, eventWrapper.contextId);\n        if (!this.#eventBuffers.has(bufferMapKey)) {\n            this.#eventBuffers.set(bufferMapKey, new buffer_js_1.Buffer(eventBufferLength.get(eventName)));\n        }\n        this.#eventBuffers.get(bufferMapKey).add(eventWrapper);\n        // Add the context to the list of contexts having `eventName` events.\n        this.#eventToContextsMap.get(eventName).add(eventWrapper.contextId);\n    }\n    /**\n     * If the event is buffer-able, mark it as sent to the given contextId and channel.\n     */\n    #markEventSent(eventWrapper, channel, eventName) {\n        if (!eventBufferLength.has(eventName)) {\n            // Do nothing if the event is no buffer-able.\n            return;\n        }\n        const lastSentMapKey = EventManager.#getMapKey(eventName, eventWrapper.contextId, channel);\n        this.#lastMessageSent.set(lastSentMapKey, Math.max(this.#lastMessageSent.get(lastSentMapKey) ?? 0, eventWrapper.id));\n    }\n    /**\n     * Returns events which are buffered and not yet sent to the given channel events.\n     */\n    #getBufferedEvents(eventName, contextId, channel) {\n        const bufferMapKey = EventManager.#getMapKey(eventName, contextId);\n        const lastSentMapKey = EventManager.#getMapKey(eventName, contextId, channel);\n        const lastSentMessageId = this.#lastMessageSent.get(lastSentMapKey) ?? -Infinity;\n        const result = this.#eventBuffers\n            .get(bufferMapKey)\n            ?.get()\n            .filter((wrapper) => wrapper.id > lastSentMessageId) ?? [];\n        if (contextId === null) {\n            // For global subscriptions, events buffered in each context should be sent back.\n            Array.from(this.#eventToContextsMap.get(eventName).keys())\n                .filter((_contextId) => \n            // Events without context are already in the result.\n            _contextId !== null &&\n                // Events from deleted contexts should not be sent.\n                this.#bidiServer.getBrowsingContextStorage().hasContext(_contextId))\n                .map((_contextId) => this.#getBufferedEvents(eventName, _contextId, channel))\n                .forEach((events) => result.push(...events));\n        }\n        return result.sort((e1, e2) => e1.id - e2.id);\n    }\n}\nexports.EventManager = EventManager;\n//# sourceMappingURL=EventManager.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.RealmStorage = void 0;\nconst protocol_js_1 = require(\"../../../protocol/protocol.js\");\nclass RealmStorage {\n    /** Tracks handles and their realms sent to the client. */\n    #knownHandlesToRealm = new Map();\n    /** Map from realm ID to Realm. */\n    #realmMap = new Map();\n    get knownHandlesToRealm() {\n        return this.#knownHandlesToRealm;\n    }\n    get realmMap() {\n        return this.#realmMap;\n    }\n    findRealms(filter) {\n        return Array.from(this.#realmMap.values()).filter((realm) => {\n            if (filter.realmId !== undefined && filter.realmId !== realm.realmId) {\n                return false;\n            }\n            if (filter.browsingContextId !== undefined &&\n                filter.browsingContextId !== realm.browsingContextId) {\n                return false;\n            }\n            if (filter.navigableId !== undefined &&\n                filter.navigableId !== realm.navigableId) {\n                return false;\n            }\n            if (filter.executionContextId !== undefined &&\n                filter.executionContextId !== realm.executionContextId) {\n                return false;\n            }\n            if (filter.origin !== undefined && filter.origin !== realm.origin) {\n                return false;\n            }\n            if (filter.type !== undefined && filter.type !== realm.type) {\n                return false;\n            }\n            if (filter.sandbox !== undefined && filter.sandbox !== realm.sandbox) {\n                return false;\n            }\n            if (filter.cdpSessionId !== undefined &&\n                filter.cdpSessionId !== realm.cdpSessionId) {\n                return false;\n            }\n            return true;\n        });\n    }\n    findRealm(filter) {\n        const maybeRealms = this.findRealms(filter);\n        if (maybeRealms.length !== 1) {\n            return undefined;\n        }\n        return maybeRealms[0];\n    }\n    getRealm(filter) {\n        const maybeRealm = this.findRealm(filter);\n        if (maybeRealm === undefined) {\n            throw new protocol_js_1.Message.NoSuchFrameException(`Realm ${JSON.stringify(filter)} not found`);\n        }\n        return maybeRealm;\n    }\n    deleteRealms(filter) {\n        this.findRealms(filter).map((realm) => {\n            this.#realmMap.delete(realm.realmId);\n            Array.from(this.#knownHandlesToRealm.entries())\n                .filter(([, r]) => r === realm.realmId)\n                .map(([h]) => this.#knownHandlesToRealm.delete(h));\n        });\n    }\n}\nexports.RealmStorage = RealmStorage;\n//# sourceMappingURL=realmStorage.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BidiServer = void 0;\nconst EventEmitter_js_1 = require(\"../utils/EventEmitter.js\");\nconst processingQueue_js_1 = require(\"../utils/processingQueue.js\");\nconst CommandProcessor_js_1 = require(\"./CommandProcessor.js\");\nconst browsingContextStorage_js_1 = require(\"./domains/context/browsingContextStorage.js\");\nconst EventManager_js_1 = require(\"./domains/events/EventManager.js\");\nconst realmStorage_js_1 = require(\"./domains/script/realmStorage.js\");\nclass BidiServer extends EventEmitter_js_1.EventEmitter {\n    #messageQueue;\n    #transport;\n    #commandProcessor;\n    #browsingContextStorage;\n    #realmStorage;\n    #logger;\n    #handleIncomingMessage = (message) => {\n        this.#commandProcessor.processCommand(message);\n    };\n    #processOutgoingMessage = async (messageEntry) => {\n        const message = messageEntry.message;\n        if (messageEntry.channel !== null) {\n            message['channel'] = messageEntry.channel;\n        }\n        await this.#transport.sendMessage(message);\n    };\n    constructor(bidiTransport, cdpConnection, selfTargetId, parser, logger) {\n        super();\n        this.#logger = logger;\n        this.#browsingContextStorage = new browsingContextStorage_js_1.BrowsingContextStorage();\n        this.#realmStorage = new realmStorage_js_1.RealmStorage();\n        this.#messageQueue = new processingQueue_js_1.ProcessingQueue(this.#processOutgoingMessage, () => Promise.resolve(), this.#logger);\n        this.#transport = bidiTransport;\n        this.#transport.setOnMessage(this.#handleIncomingMessage);\n        this.#commandProcessor = new CommandProcessor_js_1.CommandProcessor(this.#realmStorage, cdpConnection, new EventManager_js_1.EventManager(this), selfTargetId, parser, this.#browsingContextStorage, this.#logger);\n        this.#commandProcessor.on('response', (response) => {\n            this.emitOutgoingMessage(response);\n        });\n    }\n    static async createAndStart(bidiTransport, cdpConnection, selfTargetId, parser, logger) {\n        const server = new BidiServer(bidiTransport, cdpConnection, selfTargetId, parser, logger);\n        const cdpClient = cdpConnection.browserClient();\n        // Needed to get events about new targets.\n        await cdpClient.sendCommand('Target.setDiscoverTargets', { discover: true });\n        // Needed to automatically attach to new targets.\n        await cdpClient.sendCommand('Target.setAutoAttach', {\n            autoAttach: true,\n            waitForDebuggerOnStart: true,\n            flatten: true,\n        });\n        await server.topLevelContextsLoaded();\n        return server;\n    }\n    async topLevelContextsLoaded() {\n        await Promise.all(this.#browsingContextStorage\n            .getTopLevelContexts()\n            .map((c) => c.awaitLoaded()));\n    }\n    /**\n     * Sends BiDi message.\n     */\n    emitOutgoingMessage(messageEntry) {\n        this.#messageQueue.add(messageEntry);\n    }\n    close() {\n        this.#transport.close();\n    }\n    getBrowsingContextStorage() {\n        return this.#browsingContextStorage;\n    }\n}\nexports.BidiServer = BidiServer;\n//# sourceMappingURL=BidiServer.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CdpClient = void 0;\nconst EventEmitter_js_1 = require(\"../utils/EventEmitter.js\");\nclass CdpClient extends EventEmitter_js_1.EventEmitter {\n    #cdpConnection;\n    #sessionId;\n    constructor(cdpConnection, sessionId) {\n        super();\n        this.#cdpConnection = cdpConnection;\n        this.#sessionId = sessionId;\n    }\n    /**\n     * Creates a new CDP client object that communicates with the browser using a given\n     * transport mechanism.\n     * @param transport A transport object that will be used to send and receive raw CDP messages.\n     * @return A connected CDP client object.\n     */\n    static create(cdpConnection, sessionId) {\n        return new CdpClient(cdpConnection, sessionId);\n    }\n    /**\n     * Returns command promise, which will be resolved with the command result after receiving CDP result.\n     * @param method Name of the CDP command to call.\n     * @param params Parameters to pass to the CDP command.\n     */\n    sendCommand(method, ...params) {\n        const param = params[0];\n        return this.#cdpConnection.sendCommand(method, param, this.#sessionId);\n    }\n}\nexports.CdpClient = CdpClient;\n//# sourceMappingURL=cdpClient.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CdpConnection = void 0;\nconst cdpClient_js_1 = require(\"./cdpClient.js\");\n/**\n * Represents a high-level CDP connection to the browser backend.\n * Manages a CdpClient instance for each active CDP session.\n */\nclass CdpConnection {\n    #transport;\n    #browserCdpClient;\n    /** Map from session ID to CdpClient. */\n    #sessionCdpClients = new Map();\n    #commandCallbacks = new Map();\n    #log;\n    #nextId = 0;\n    constructor(transport, log = () => { }) {\n        this.#transport = transport;\n        this.#log = log;\n        this.#transport.setOnMessage(this.#onMessage);\n        this.#browserCdpClient = cdpClient_js_1.CdpClient.create(this, null);\n    }\n    /**\n     * Closes the connection to the browser.\n     */\n    close() {\n        this.#transport.close();\n        for (const [, { reject }] of this.#commandCallbacks) {\n            reject(new Error('Disconnected'));\n        }\n        this.#commandCallbacks.clear();\n        this.#sessionCdpClients.clear();\n    }\n    /**\n     * @return The CdpClient object attached to the root browser session.\n     */\n    browserClient() {\n        return this.#browserCdpClient;\n    }\n    /**\n     * Gets a CdpClient instance by sessionId.\n     * @param sessionId The sessionId of the CdpClient to retrieve.\n     * @return The CdpClient object attached to the given session, or null if the session is not attached.\n     */\n    getCdpClient(sessionId) {\n        const cdpClient = this.#sessionCdpClients.get(sessionId);\n        if (!cdpClient) {\n            throw new Error('Unknown CDP session ID');\n        }\n        return cdpClient;\n    }\n    sendCommand(method, params, sessionId) {\n        return new Promise((resolve, reject) => {\n            const id = this.#nextId++;\n            this.#commandCallbacks.set(id, { resolve, reject });\n            const messageObj = { id, method, params };\n            if (sessionId) {\n                messageObj.sessionId = sessionId;\n            }\n            const messageStr = JSON.stringify(messageObj);\n            const messagePretty = JSON.stringify(messageObj, null, 2);\n            this.#transport.sendMessage(messageStr);\n            this.#log('sent ▸', messagePretty);\n        });\n    }\n    #onMessage = (message) => {\n        const parsed = JSON.parse(message);\n        const messagePretty = JSON.stringify(parsed, null, 2);\n        this.#log('received ◂', messagePretty);\n        // Update client map if a session is attached or detached.\n        // Listen for these events on every session.\n        if (parsed.method === 'Target.attachedToTarget') {\n            const { sessionId } = parsed.params;\n            this.#sessionCdpClients.set(sessionId, cdpClient_js_1.CdpClient.create(this, sessionId));\n        }\n        else if (parsed.method === 'Target.detachedFromTarget') {\n            const { sessionId } = parsed.params;\n            const client = this.#sessionCdpClients.get(sessionId);\n            if (client) {\n                this.#sessionCdpClients.delete(sessionId);\n            }\n        }\n        if (parsed.id !== undefined) {\n            // Handle command response.\n            const callbacks = this.#commandCallbacks.get(parsed.id);\n            if (callbacks) {\n                if (parsed.result) {\n                    callbacks.resolve(parsed.result);\n                }\n                else if (parsed.error) {\n                    callbacks.reject(parsed.error);\n                }\n            }\n        }\n        else if (parsed.method) {\n            const client = parsed.sessionId\n                ? this.#sessionCdpClients.get(parsed.sessionId)\n                : this.#browserCdpClient;\n            if (client) {\n                client.emit(parsed.method, parsed.params || {});\n            }\n        }\n    };\n}\nexports.CdpConnection = CdpConnection;\n//# sourceMappingURL=cdpConnection.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WebSocketTransport = void 0;\nclass WebSocketTransport {\n    #onMessage = null;\n    #websocket;\n    constructor(websocket) {\n        this.#websocket = websocket;\n        this.#websocket.on('message', (message) => {\n            this.#onMessage?.(message);\n        });\n    }\n    setOnMessage(onMessage) {\n        this.#onMessage = onMessage;\n    }\n    sendMessage(message) {\n        this.#websocket.send(message);\n    }\n    close() {\n        this.#onMessage = null;\n        this.#websocket.close();\n    }\n}\nexports.WebSocketTransport = WebSocketTransport;\n//# sourceMappingURL=websocketTransport.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WebSocketTransport = exports.CdpConnection = exports.CdpClient = void 0;\nvar cdpClient_js_1 = require(\"./cdpClient.js\");\nObject.defineProperty(exports, \"CdpClient\", { enumerable: true, get: function () { return cdpClient_js_1.CdpClient; } });\nvar cdpConnection_js_1 = require(\"./cdpConnection.js\");\nObject.defineProperty(exports, \"CdpConnection\", { enumerable: true, get: function () { return cdpConnection_js_1.CdpConnection; } });\nvar websocketTransport_js_1 = require(\"../utils/websocketTransport.js\");\nObject.defineProperty(exports, \"WebSocketTransport\", { enumerable: true, get: function () { return websocketTransport_js_1.WebSocketTransport; } });\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.log = exports.generatePage = void 0;\n/**\n * Copyright 2022 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst log_js_1 = require(\"../utils/log.js\");\n/** HTML source code for the user-facing Mapper tab. */\nconst mapperPageSource = '<!DOCTYPE html><title>BiDi-CDP Mapper</title><style>body{font-family: <PERSON><PERSON>, serif; font-size: 13px; color: #202124;}.log{padding: 12px; font-family: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace; font-size: 11px; line-height: 180%; background: #f1f3f4; border-radius: 4px;}.pre{overflow-wrap: break-word; padding: 10px;}.card{margin: 60px auto; padding: 2px 0; max-width: 900px; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15), 0 1px 6px rgba(0, 0, 0, 0.2); border-radius: 8px;}.divider{height: 1px; background: #f0f0f0;}.item{padding: 16px 20px;}</style><div class=\"card\"><div class=\"item\"><h1>BiDi-CDP Mapper is controlling this tab</h1><p>Closing or reloading it will stop the BiDi process. <a target=\"_blank\" title=\"BiDi-CDP Mapper GitHub Repository\" href=\"https://github.com/GoogleChromeLabs/chromium-bidi\">Details.</a></p></div><div class=\"divider\"></div><details id=\"details\"><summary class=\"item\">Debug information</summary></details></div>';\n/**\n * The following piece of HTML should be added to the `debug` element:\n *\n * <div class=\"divider\"></div>\n * <div class=\"item\">\n * <h3>${name}</h3>\n * <div id=\"${name}_log\" class=\"log\">\n */\nfunction findOrCreateTypeLogContainer(logType) {\n    const containerId = `${logType}_log`;\n    const existingContainer = document.getElementById(containerId);\n    if (existingContainer) {\n        return existingContainer;\n    }\n    const debugElement = document.getElementById('details');\n    const divider = document.createElement('div');\n    divider.className = 'divider';\n    debugElement.appendChild(divider);\n    const htmlItem = document.createElement('div');\n    htmlItem.className = 'item';\n    htmlItem.innerHTML = `<h3>${logType}</h3><div id=\"${containerId}\" class=\"log\"></div>`;\n    debugElement.appendChild(htmlItem);\n    return document.getElementById(containerId);\n}\nfunction generatePage() {\n    // If run not in browser (e.g. unit test), do nothing.\n    if (!globalThis.document.documentElement) {\n        return;\n    }\n    globalThis.document.documentElement.innerHTML = mapperPageSource;\n    // Create main log containers in proper order.\n    findOrCreateTypeLogContainer(log_js_1.LogType.system);\n    findOrCreateTypeLogContainer(log_js_1.LogType.bidi);\n    findOrCreateTypeLogContainer(log_js_1.LogType.browsingContexts);\n    findOrCreateTypeLogContainer(log_js_1.LogType.cdp);\n}\nexports.generatePage = generatePage;\nfunction log(logType, ...messages) {\n    // If run not in browser (e.g. unit test), do nothing.\n    if (!globalThis.document.documentElement) {\n        return;\n    }\n    // If `sendDebugMessage` is defined, send the log message there.\n    global.window?.sendDebugMessage?.(JSON.stringify({ logType, messages }));\n    const typeLogContainer = findOrCreateTypeLogContainer(logType);\n    // This piece of HTML should be added:\n    // <div class=\"pre\">...log message...</div>\n    const lineElement = document.createElement('div');\n    lineElement.className = 'pre';\n    lineElement.textContent = messages.join(' ');\n    typeLogContainer.appendChild(lineElement);\n}\nexports.log = log;\n//# sourceMappingURL=mapperTabPage.js.map", "\"use strict\";\n/**\n * Copyright 2021 Google LLC.\n * Copyright (c) Microsoft Corporation.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * @license\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst Parser = __importStar(require(\"../protocol-parser/protocol-parser.js\"));\nconst protocol_1 = require(\"../protocol/protocol\");\nconst BidiServer_js_1 = require(\"../bidiMapper/BidiServer.js\");\nconst index_js_1 = require(\"../cdp/index.js\");\nconst log_js_1 = require(\"../utils/log.js\");\nconst OutgoingBidiMessage_js_1 = require(\"../bidiMapper/OutgoingBidiMessage.js\");\nconst mapperTabPage_js_1 = require(\"./mapperTabPage.js\");\n// Initiate `setSelfTargetId` as soon as possible to prevent race condition.\nconst waitSelfTargetIdPromise = waitSelfTargetId();\nvoid (async () => {\n    (0, mapperTabPage_js_1.generatePage)();\n    // Needed to filter out info related to BiDi target.\n    const selfTargetId = await waitSelfTargetIdPromise;\n    const bidiServer = await createBidiServer(selfTargetId);\n    (0, mapperTabPage_js_1.log)(log_js_1.LogType.system, 'Launched');\n    bidiServer.emitOutgoingMessage(OutgoingBidiMessage_js_1.OutgoingBidiMessage.createResolved({ launched: true }, null));\n})();\nfunction createCdpConnection() {\n    /**\n     * A CdpTransport implementation that uses the window.cdp bindings\n     * injected by Target.exposeDevToolsProtocol.\n     */\n    class WindowCdpTransport {\n        #onMessage = null;\n        constructor() {\n            window.cdp.onmessage = (message) => {\n                this.#onMessage?.call(null, message);\n            };\n        }\n        setOnMessage(onMessage) {\n            this.#onMessage = onMessage;\n        }\n        sendMessage(message) {\n            window.cdp.send(message);\n        }\n        close() {\n            this.#onMessage = null;\n            window.cdp.onmessage = null;\n        }\n    }\n    return new index_js_1.CdpConnection(new WindowCdpTransport(), (...messages) => {\n        (0, mapperTabPage_js_1.log)(log_js_1.LogType.cdp, ...messages);\n    });\n}\nfunction createBidiServer(selfTargetId) {\n    class WindowBidiTransport {\n        #onMessage = null;\n        constructor() {\n            window.onBidiMessage = (messageStr) => {\n                (0, mapperTabPage_js_1.log)(log_js_1.LogType.bidi, 'received ◂', messageStr);\n                let messageObject;\n                try {\n                    messageObject = WindowBidiTransport.#parseBidiMessage(messageStr);\n                }\n                catch (e) {\n                    // Transport-level error does not provide channel.\n                    this.#respondWithError(messageStr, protocol_1.Message.ErrorCode.InvalidArgument, e.message, null);\n                    return;\n                }\n                this.#onMessage?.call(null, messageObject);\n            };\n        }\n        setOnMessage(onMessage) {\n            this.#onMessage = onMessage;\n        }\n        sendMessage(message) {\n            const messageStr = JSON.stringify(message);\n            window.sendBidiResponse(messageStr);\n            (0, mapperTabPage_js_1.log)(log_js_1.LogType.bidi, 'sent ▸', messageStr);\n        }\n        close() {\n            this.#onMessage = null;\n            window.onBidiMessage = null;\n        }\n        #respondWithError(plainCommandData, errorCode, errorMessage, channel) {\n            const errorResponse = WindowBidiTransport.#getErrorResponse(plainCommandData, errorCode, errorMessage);\n            if (channel) {\n                // TODO: get rid of any, same code existed in BidiServer.\n                this.sendMessage({\n                    ...errorResponse,\n                    channel,\n                });\n            }\n            else {\n                this.sendMessage(errorResponse);\n            }\n        }\n        static #getJsonType(value) {\n            if (value === null) {\n                return 'null';\n            }\n            if (Array.isArray(value)) {\n                return 'array';\n            }\n            return typeof value;\n        }\n        static #getErrorResponse(messageStr, errorCode, errorMessage) {\n            // TODO: this is bizarre per spec. We reparse the payload and\n            // extract the ID, regardless of what kind of value it was.\n            let messageId;\n            try {\n                const messageObj = JSON.parse(messageStr);\n                if (WindowBidiTransport.#getJsonType(messageObj) === 'object' &&\n                    'id' in messageObj) {\n                    messageId = messageObj.id;\n                }\n            }\n            catch { }\n            return {\n                id: messageId,\n                error: errorCode,\n                message: errorMessage,\n                // TODO: optional stacktrace field.\n            };\n        }\n        static #parseBidiMessage(messageStr) {\n            let messageObject;\n            try {\n                messageObject = JSON.parse(messageStr);\n            }\n            catch {\n                throw new Error('Cannot parse data as JSON');\n            }\n            const parsedType = WindowBidiTransport.#getJsonType(messageObject);\n            if (parsedType !== 'object') {\n                throw new Error(`Expected JSON object but got ${parsedType}`);\n            }\n            // Extract and validate id, method and params.\n            const { id, method, params } = messageObject;\n            const idType = WindowBidiTransport.#getJsonType(id);\n            if (idType !== 'number' || !Number.isInteger(id) || id < 0) {\n                // TODO: should uint64_t be the upper limit?\n                // https://tools.ietf.org/html/rfc7049#section-2.1\n                throw new Error(`Expected unsigned integer but got ${idType}`);\n            }\n            const methodType = WindowBidiTransport.#getJsonType(method);\n            if (methodType !== 'string') {\n                throw new Error(`Expected string method but got ${methodType}`);\n            }\n            const paramsType = WindowBidiTransport.#getJsonType(params);\n            if (paramsType !== 'object') {\n                throw new Error(`Expected object params but got ${paramsType}`);\n            }\n            let channel = messageObject.channel;\n            if (channel !== undefined) {\n                const channelType = WindowBidiTransport.#getJsonType(channel);\n                if (channelType !== 'string') {\n                    throw new Error(`Expected string channel but got ${channelType}`);\n                }\n                // Empty string channel is considered as no channel provided.\n                if (channel === '') {\n                    channel = undefined;\n                }\n            }\n            return { id, method, params, channel };\n        }\n    }\n    return BidiServer_js_1.BidiServer.createAndStart(new WindowBidiTransport(), createCdpConnection(), selfTargetId, new BidiParserImpl(), mapperTabPage_js_1.log);\n}\nclass BidiParserImpl {\n    parseAddPreloadScriptParams(params) {\n        return Parser.Script.parseAddPreloadScriptParams(params);\n    }\n    parseRemovePreloadScriptParams(params) {\n        return Parser.Script.parseRemovePreloadScriptParams(params);\n    }\n    parseGetRealmsParams(params) {\n        return Parser.Script.parseGetRealmsParams(params);\n    }\n    parseCallFunctionParams(params) {\n        return Parser.Script.parseCallFunctionParams(params);\n    }\n    parseEvaluateParams(params) {\n        return Parser.Script.parseEvaluateParams(params);\n    }\n    parseDisownParams(params) {\n        return Parser.Script.parseDisownParams(params);\n    }\n    parseSendCommandParams(params) {\n        return Parser.CDP.parseSendCommandParams(params);\n    }\n    parseGetSessionParams(params) {\n        return Parser.CDP.parseGetSessionParams(params);\n    }\n    parseSubscribeParams(params) {\n        return Parser.Session.parseSubscribeParams(params);\n    }\n    parseNavigateParams(params) {\n        return Parser.BrowsingContext.parseNavigateParams(params);\n    }\n    parseGetTreeParams(params) {\n        return Parser.BrowsingContext.parseGetTreeParams(params);\n    }\n    parseCreateParams(params) {\n        return Parser.BrowsingContext.parseCreateParams(params);\n    }\n    parseCloseParams(params) {\n        return Parser.BrowsingContext.parseCloseParams(params);\n    }\n    parseCaptureScreenshotParams(params) {\n        return Parser.BrowsingContext.parseCaptureScreenshotParams(params);\n    }\n    parsePrintParams(params) {\n        return Parser.BrowsingContext.parsePrintParams(params);\n    }\n}\n// Needed to filter out info related to BiDi target.\nasync function waitSelfTargetId() {\n    return new Promise((resolve) => {\n        window.setSelfTargetId = (targetId) => {\n            (0, mapperTabPage_js_1.log)(log_js_1.LogType.system, 'Current target ID:', targetId);\n            resolve(targetId);\n        };\n    });\n}\n//# sourceMappingURL=bidiTab.js.map"], "names": ["util", "Object", "defineProperty", "exports", "value", "getParsedType", "ZodParsedType", "objectUtil", "assertEqual", "val", "assertIs", "_arg", "assertNever", "_x", "Error", "arrayToEnum", "items", "obj", "item", "getValidEnumValues", "validKeys", "objectKeys", "filter", "k", "filtered", "objectValues", "map", "e", "keys", "object", "key", "prototype", "hasOwnProperty", "call", "push", "find", "arr", "checker", "isInteger", "Number", "isFinite", "Math", "floor", "joinValues", "array", "separator", "join", "jsonStringifyReplacer", "_", "toString", "mergeShapes", "first", "second", "data", "undefined", "string", "isNaN", "nan", "number", "boolean", "function", "bigint", "symbol", "Array", "isArray", "null", "then", "catch", "promise", "Map", "Set", "set", "Date", "date", "unknown", "ZodError_1", "ZodError", "quoteless<PERSON><PERSON>", "util_1", "require$$0", "ZodError$1", "ZodIssueCode", "JSON", "stringify", "replace", "constructor", "issues", "super", "this", "addIssue", "sub", "addIssues", "subs", "actualProto", "setPrototypeOf", "__proto__", "name", "errors", "format", "_mapper", "mapper", "issue", "message", "fieldErrors", "_errors", "processError", "error", "code", "unionErrors", "returnTypeError", "argumentsError", "path", "length", "curr", "i", "el", "isEmpty", "flatten", "formErrors", "create", "en", "require$$1", "default", "_ctx", "invalid_type", "received", "expected", "invalid_literal", "unrecognized_keys", "invalid_union", "invalid_union_discriminator", "options", "invalid_enum_value", "invalid_arguments", "invalid_return_type", "invalid_date", "invalid_string", "validation", "includes", "position", "startsWith", "endsWith", "too_small", "type", "exact", "inclusive", "minimum", "too_big", "maximum", "custom", "invalid_intersection_types", "not_multiple_of", "multipleOf", "not_finite", "defaultError", "__importDefault", "mod", "__esModule", "getErrorMap", "setErrorMap", "en_1", "defaultErrorMap", "overrideErrorMap", "isAsync", "<PERSON><PERSON><PERSON><PERSON>", "isDirty", "isAborted", "OK", "DIRTY", "INVALID", "ParseStatus", "addIssueToContext", "EMPTY_PATH", "makeIssue", "errors_1", "params", "errorMaps", "issueData", "fullPath", "fullIssue", "errorMessage", "maps", "m", "slice", "reverse", "ctx", "common", "contextualErrorMap", "schemaErrorMap", "x", "dirty", "abort", "static", "status", "results", "arrayValue", "s", "pairs", "syncPairs", "pair", "mergeObjectSync", "finalObject", "alwaysSet", "freeze", "Promise", "typeAliases", "errorUtil", "errToObj", "discriminatedUnion", "coerce", "ZodFirstPartyTypeKind", "late", "ZodSchema", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BRAND", "ZodNaN", "ZodCatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zod<PERSON>ullable", "ZodTransformer", "ZodEffects", "ZodPromise", "ZodNativeEnum", "ZodEnum", "ZodLiteral", "ZodFunction", "ZodSet", "ZodMap", "ZodRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZodIntersection", "ZodUnion", "ZodObject", "ZodArray", "ZodVoid", "<PERSON><PERSON><PERSON><PERSON>", "ZodUnknown", "ZodNull", "ZodUndefined", "ZodSymbol", "ZodDate", "ZodBoolean", "ZodBigInt", "ZodString", "ZodType", "NEVER", "void", "union", "transformer", "strictObject", "record", "preprocess", "pipeline", "optional", "onumber", "oboolean", "nullable", "never", "nativeEnum", "literal", "lazy", "intersection", "enum", "effect", "errorUtil_1", "parseUtil_1", "require$$2", "require$$3", "require$$4", "ParseInputLazyPath", "parent", "_cachedPath", "_path", "_key", "handleResult", "result", "success", "_error", "processCreateParams", "errorMap", "invalid_type_error", "required_error", "description", "iss", "def", "spa", "safeParseAsync", "_def", "parse", "bind", "safeParse", "parseAsync", "refine", "refinement", "superRefine", "nullish", "or", "and", "transform", "brand", "describe", "pipe", "isNullable", "isOptional", "_getType", "input", "_getOrReturnCtx", "parsedType", "_processInputParams", "_parseSync", "_parse", "_parseAsync", "resolve", "_a", "async", "maybe<PERSON><PERSON><PERSON><PERSON><PERSON>", "check", "getIssueProperties", "_refinement", "setError", "refinementData", "schema", "typeName", "ZodOptional", "option", "incoming", "defaultValueFunc", "innerType", "defaultValue", "catchValueFunc", "catchValue", "This", "target", "Zod<PERSON><PERSON><PERSON><PERSON>", "cuidRegex", "cuid2Regex", "ulidRegex", "uuidRegex", "emailRegex", "emojiRegex", "ipv4Regex", "ipv6Regex", "arguments", "_regex", "regex", "test", "nonempty", "min", "trim", "checks", "kind", "toLowerCase", "toUpperCase", "String", "<PERSON><PERSON><PERSON>", "tooSmall", "URL", "lastIndex", "args", "precision", "offset", "RegExp", "ip", "version", "_addCheck", "email", "url", "emoji", "uuid", "cuid", "cuid2", "ulid", "datetime", "<PERSON><PERSON><PERSON><PERSON>", "max", "max<PERSON><PERSON><PERSON>", "len", "isDatetime", "ch", "isEmail", "isURL", "is<PERSON><PERSON><PERSON>", "isUUID", "isCUID", "isCUID2", "isULID", "isIP", "floatSafeRemainder", "step", "valDecCount", "split", "stepDecCount", "decCount", "parseInt", "toFixed", "pow", "ZodNumber", "gte", "lte", "setLimit", "gt", "lt", "int", "positive", "negative", "nonpositive", "nonnegative", "finite", "safe", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "minValue", "maxValue", "isInt", "BigInt", "Boolean", "getTime", "minDate", "maxDate", "ZodAny", "_any", "_unknown", "exactLength", "all", "mergeArray", "element", "deepPartialify", "newShape", "shape", "fieldSchema", "unwrap", "_cached", "nonstrict", "passthrough", "augment", "extend", "_getCached", "shapeKeys", "extraKeys", "catchall", "<PERSON><PERSON><PERSON><PERSON>", "keyValidator", "strict", "_b", "_c", "_d", "strip", "augmentation", "merge", "merging", "<PERSON><PERSON><PERSON>", "index", "pick", "mask", "for<PERSON>ach", "omit", "deepPartial", "partial", "required", "newField", "keyof", "createZodEnum", "strictCreate", "lazycreate", "childCtx", "types", "getDiscriminator", "ZodLazy", "ZodDiscriminatedUnion", "discriminator", "discriminatorValue", "optionsMap", "get", "from", "discriminatorValues", "has", "mergeValues", "a", "b", "aType", "bType", "valid", "b<PERSON><PERSON><PERSON>", "sharedKeys", "indexOf", "newObj", "sharedValue", "newArray", "handleParsed", "parsedLeft", "parsedRight", "merged", "left", "right", "rest", "itemIndex", "schemas", "keySchema", "keyType", "valueSchema", "valueType", "mergeObjectAsync", "third", "entries", "finalMap", "minSize", "size", "maxSize", "finalizeSet", "elements", "parsedSet", "add", "values", "validate", "implement", "makeArgsIssue", "makeReturnsIssue", "returns", "fn", "parsedArgs", "parsedReturns", "parameters", "returnType", "func", "strictImplement", "getter", "expectedV<PERSON>ues", "enum<PERSON><PERSON><PERSON>", "Values", "Enum", "extract", "exclude", "opt", "nativeEnumValues", "promisified", "sourceType", "processed", "checkCtx", "arg", "fatal", "executeRefinement", "acc", "inner", "base", "createWithPreprocess", "remove<PERSON><PERSON><PERSON>", "newCtx", "removeCatch", "Symbol", "inResult", "in", "out", "handleAsync", "p", "_fatal", "p2", "instanceof", "cls", "stringType", "numberType", "nanType", "bigIntType", "booleanType", "dateType", "symbolType", "undefinedType", "nullType", "anyType", "any", "unknownType", "neverType", "voidType", "arrayType", "objectType", "strictObjectType", "unionType", "discriminatedUnionType", "intersectionType", "tupleType", "tuple", "recordType", "mapType", "setType", "functionType", "lazyType", "literalType", "enumType", "nativeEnumType", "promiseType", "effectsType", "optionalType", "nullableType", "preprocessType", "pipelineType", "ostring", "__createBinding", "o", "k2", "enumerable", "__exportStar", "require$$5", "__setModuleDefault", "v", "__importStar", "z", "<PERSON><PERSON><PERSON>", "BrowsingContext", "EventNames", "Log", "Network", "CDP", "Message", "ErrorCode", "ErrorResponse", "stacktrace", "toErrorResponse", "commandId", "id", "InvalidArgumentException", "InvalidArgument", "NoSuchHandleException", "NoSuchHandle", "InvalidSessionIdException", "InvalidSessionId", "NoSuchAlertException", "NoSuchAlert", "NoSuchFrameException", "NoSuchFrame", "NoSuchNodeException", "NoSuchNode", "NoSuchScriptException", "NoSuchScript", "SessionNotCreatedException", "SessionNotCreated", "UnknownCommandException", "<PERSON><PERSON><PERSON><PERSON>", "UnknownErrorException", "UnknownE<PERSON>r", "UnsupportedOperationException", "UnsupportedOperation", "AllEvents", "Session", "parseObject", "zod_1", "protocol_js_1", "parseResult", "CommonDataTypes", "SharedReferenceSchema", "sharedId", "RemoteReferenceSchema", "handle", "UndefinedValueSchema", "NullValueSchema", "StringValueSchema", "SpecialNumberSchema", "NumberValueSchema", "BooleanValueSchema", "BigIntValueSchema", "PrimitiveProtocolValueSchema", "LocalValueSchema", "ArrayLocalValueSchema", "DateLocalValueSchema", "MapLocalValueSchema", "ObjectLocalValueSchema", "RegExpLocalValueSchema", "SetLocalValueSchema", "LocalOrRemoteValueSchema", "ListLocalValueSchema", "MappingLocalValueSchema", "pattern", "flags", "BrowsingContextSchema", "MaxDepthSchema", "RealmTypeSchema", "GetRealmsParametersSchema", "context", "parseGetRealmsParams", "ContextTargetSchema", "sandbox", "RealmTargetSchema", "realm", "TargetSchema", "ResultOwnershipSchema", "EvaluateParametersSchema", "expression", "await<PERSON><PERSON><PERSON>", "resultOwnership", "parseEvaluateParams", "DisownParametersSchema", "handles", "parseDisownParams", "PreloadScriptSchema", "AddPreloadScriptParametersSchema", "parseAddPreloadScriptParams", "RemovePreloadScriptParametersSchema", "script", "parseRemovePreloadScriptParams", "ChannelIdSchema", "ChannelPropertiesSchema", "channel", "max<PERSON><PERSON><PERSON>", "ownership", "ChannelSchema", "ArgumentValueSchema", "CallFunctionParametersSchema", "functionDeclaration", "parseCallFunctionParams", "GetTreeParametersSchema", "root", "parseGetTreeParams", "ReadinessStateSchema", "NavigateParametersSchema", "wait", "parseNavigateParams", "CreateParametersSchema", "referenceContext", "parseCreateParams", "CloseParametersSchema", "parseCloseParams", "CaptureScreenshotParametersSchema", "parseCaptureScreenshotParams", "PrintPageParametersSchema", "height", "width", "PrintMarginParametersSchema", "bottom", "top", "PrintPageRangesSchema", "pageRanges", "every", "pageRange", "match", "start", "end", "groups", "PrintParametersSchema", "background", "margin", "orientation", "page", "scale", "shrinkToFit", "parsePrintParams", "SendCommandParamsSchema", "cdpMethod", "cdpParams", "cdpSession", "parseSendCommandParams", "GetSessionParamsSchema", "parseGetSessionParams", "SubscriptionRequestParametersEventsSchema", "SubscriptionRequestParametersSchema", "events", "contexts", "parseSubscribeParams", "EventEmitter_1", "EventEmitter", "mitt_1", "n", "on", "t", "off", "splice", "emit", "EventEmitter$1", "emitter", "handler", "once", "event", "once<PERSON><PERSON><PERSON>", "eventData", "LogType", "processingQueue", "ProcessingQueue", "log_js_1", "logger", "processor", "queue", "isProcessing", "_catch", "entry", "processIfNeeded", "entryPromise", "shift", "system", "unitConversions", "inchesFromCm", "cm", "deferred", "Deferred", "isFinished", "reject", "onFulfilled", "onRejected", "reason", "finally", "onFinally", "toStringTag", "ScriptEvaluator", "SHARED_ID_DIVIDER", "eventManager", "cdpObject", "cdpClient", "sendCommand", "returnByValue", "executionContextId", "cdpRemoteObject", "cdpRemoteObjectToCallArgument", "cdpWebDriverValue", "generateWebDriverValue", "cdpToBidiValue", "cdpEvaluateResult", "contextId", "exceptionDetails", "serializeCdpExceptionDetails", "realmId", "_this", "_arguments", "callFunctionAndSerializeScript", "thisAndArgumentsList", "deserializeToCdpArg", "cdpCallFunctionResult", "objectId", "unserializableValue", "argumentValue", "navigableId", "rawBackendNodeId", "backendNodeId", "key<PERSON><PERSON>ueArray", "flattenKeyValuePairs", "flattenValueList", "channelHandle", "queueNonEmptyResolver", "onMessage", "sendMessage", "initChannelListener", "mapping", "keyArg", "valueArg", "list", "channelId", "getMessage", "registerEvent", "method", "MessageEvent", "source", "browsingContextId", "cdpExceptionDetails", "lineOffset", "callFrames", "stackTrace", "frame", "functionName", "lineNumber", "columnNumber", "exception", "serializeCdpObject", "text", "stringifyObject", "Realm", "scriptEvaluator_js_1", "realmStorage", "browsingContextStorage", "origin", "scriptEvaluator", "cdpSessionId", "realmMap", "knownHandlesToRealm", "delete", "cdpValue", "webDriverValue", "bidiValue", "webDriverValueToBiDi", "hasOwn", "children", "toBiDi", "findContext", "getContext", "awaitUnblocked", "callFunction", "scriptEvaluate", "browsingContextImpl", "BrowsingContextImpl", "unitConversions_js_1", "deferred_js_1", "realm_js_1", "parentId", "defers", "documentInitialized", "Page", "navigatedWithinDocument", "lifecycleEvent", "DOMContentLoaded", "load", "loaderId", "cdpTarget", "maybeDefaultRealm", "initListeners", "addContext", "ContextCreatedEvent", "serializeToBidiValue", "deleteC<PERSON><PERSON>n", "deleteRealms", "ContextDestroyedEvent", "deleteContext", "isTopLevelContext", "<PERSON><PERSON><PERSON><PERSON>", "child", "defaultRealm", "updateCdpTarget", "targetUnblocked", "maybeSandboxes", "findRealms", "frameId", "worldName", "addParentFiled", "c", "targetInfo", "targetId", "urlFragment", "timestamp", "documentChanged", "DomContentLoadedEvent", "navigation", "LoadEvent", "auxData", "uniqueId", "<PERSON><PERSON><PERSON><PERSON>", "isDefault", "browsingContexts", "cdpNavigateResult", "errorText", "printToPdfCdpParams", "printBackground", "landscape", "preferCSSPageSize", "marginBottom", "marginLeft", "marginRight", "marginTop", "paperHeight", "paperWidth", "identifier", "log<PERSON>elper", "getRemoteValuesText", "logMessageFormatter", "specifiers", "isFormmatSpecifier", "str", "some", "spec", "output", "argFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokens", "token", "stringFromArg", "parseFloat", "to<PERSON><PERSON>", "formatText", "logManager", "LogManager", "logHelper_js_1", "getBidiStackTrace", "cdpStackTrace", "stackFrames", "callFrame", "initialize", "initializeLogEntryAddedEventListener", "findRealm", "args<PERSON><PERSON><PERSON>", "registerPromiseEvent", "LogEntryAddedEvent", "level", "consoleApiType", "round", "textPromise", "DefaultMap_1", "DefaultMap", "getDefaultValue", "DefaultMap$1", "networkRequest", "NetworkRequest", "deferred_1", "protocol_1", "requestId", "requestWillBeSentEvent", "requestWillBeSentExtraInfoEvent", "responseReceivedEvent", "responseReceivedExtraInfoEvent", "beforeRequestSentDeferred", "responseReceivedDeferred", "onRequestWillBeSentEvent", "sendBeforeRequestEvent", "onRequestWillBeSentExtraInfoEvent", "onResponseReceivedEvent", "sendResponseReceivedEvent", "onResponseReceivedEventExtraInfo", "onLoadingFailedEvent", "loadingFailedEvent", "getBaseEventParams", "FetchErrorEvent", "isIgnoredEvent", "getBeforeRequestEvent", "BeforeRequestSentEvent", "initiator", "getInitiatorType", "redirectCount", "request", "getRequestData", "wallTime", "cookies", "getCookies", "associatedCookies", "headers", "headersSize", "bodySize", "timings", "<PERSON><PERSON><PERSON><PERSON>", "requestTime", "redirectStart", "redirectEnd", "fetchStart", "dnsStart", "dnsEnd", "connectStart", "connectEnd", "tlsStart", "tlsEnd", "requestStart", "responseStart", "responseEnd", "cdpSameSiteValue", "cookieInfo", "cookie", "domain", "expires", "httpOnly", "secure", "sameSite", "getCookiesSameSite", "getResponseReceivedEvent", "ResponseCompletedEvent", "response", "protocol", "statusText", "fromCache", "fromDiskCache", "fromPrefetchCache", "getHeaders", "mimeType", "bytesReceived", "encodedDataLength", "headersText", "content", "networkProcessor", "NetworkProcessor", "networkRequest_1", "requestMap", "getOrCreateNetworkRequest", "CdpTarget", "logManager_1", "networkProcessor_1", "networkDomainActivated", "setEventListeners", "unblock", "isNetworkDomainEnabled", "enableNetworkDomain", "enabled", "autoAttach", "waitForDebuggerOnStart", "EventReceivedEvent", "browsingContextProcessor", "BrowsingContextProcessor", "browsingContextImpl_js_1", "cdpTarget_js_1", "cdpConnection", "selfTargetId", "browserClient", "handleAttachedToTargetEvent", "handleDetachedFromTargetEvent", "handleFrameAttachedEvent", "handleFrameDetachedEvent", "parentBrowsingContext", "parentFrameId", "parentSessionCdpClient", "sessionId", "targetCdpClient", "getCdpClient", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasContext", "getRealm", "getOrCreateSandbox", "process_browsingContext_getTree", "getTopLevelContexts", "MAX_VALUE", "browserCdpClient", "newWindow", "awaitLoaded", "process_browsingContext_navigate", "navigate", "captureScreenshot", "print", "scripts", "getAllContexts", "addPreloadScript", "_params", "process_script_getRealms", "realms", "h", "disown", "commandParams", "detachedFromTargetPromise", "onContextDestroyed", "eventParams", "client", "process_cdp_getSession", "OutgoingBidiMessage_1", "OutgoingBidiMessage", "messagePromise", "OutgoingBidiMessage$1", "CommandProcessor_1", "CommandProcessor", "EventEmitter_js_1", "browsingContextProcessor_js_1", "OutgoingBidiMessage_js_1", "BidiNoOpParser", "contextProcessor", "parser", "ready", "subscribe", "unsubscribe", "commandData", "process_session_status", "process_session_subscribe", "process_session_unsubscribe", "process_browsingContext_create", "process_browsingContext_close", "process_browsingContext_captureScreenshot", "process_browsingContext_print", "process_script_addPreloadScript", "process_script_removePreloadScript", "process_script_callFunction", "process_script_evaluate", "process_script_disown", "process_cdp_sendCommand", "command", "processCommand", "createResolved", "errorResponse", "bidi", "CommandProcessor$1", "BrowsingContextStorage", "findTopLevelContextId", "buffer", "<PERSON><PERSON><PERSON>", "capacity", "onItemRemoved", "idWrapper", "IdWrapper", "counter", "SubscriptionManager_1", "SubscriptionManager", "unrollEvents", "cartesianProduct", "reduce", "flatMap", "d", "flat", "allEvents", "SubscriptionManager$1", "subscriptionPriority", "channelToContextToEventMap", "getChannelsSubscribedToEvent", "eventMethod", "priority", "getEventSubscriptionPriorityForChannel", "sort", "contextToEventMap", "maybeTopLevelContextId", "priorities", "specificEvent", "eventMap", "unsubscribeAll", "contextIds", "checkUnsubscribe", "eventName", "EventManager_1", "EventManager", "buffer_js_1", "idWrapper_js_1", "DefaultMap_js_1", "SubscriptionManager_js_1", "EventWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventToContextsMap", "eventBuffers", "lastMessageSent", "subscriptionManager", "bidiServer", "getBrowsingContextStorage", "browsingContext", "eventWrapper", "sortedChannels", "bufferEvent", "emitOutgoingMessage", "createFromPromise", "markEventSent", "eventNames", "handleDomains", "getBufferedEvents", "NETWORK_DOMAIN_PREFIX", "bufferMapKey", "getMapKey", "lastSentMapKey", "lastSentMessageId", "Infinity", "wrapper", "_contextId", "e1", "e2", "EventManager$1", "RealmStorage", "maybeRealms", "<PERSON><PERSON><PERSON><PERSON>", "r", "BidiServer_1", "BidiServer", "processingQueue_js_1", "CommandProcessor_js_1", "browsingContextStorage_js_1", "EventManager_js_1", "realmStorage_js_1", "messageQueue", "transport", "commandProcessor", "handleIncomingMessage", "processOutgoingMessage", "messageEntry", "bidiTransport", "setOnMessage", "server", "discover", "topLevelContextsLoaded", "close", "BidiServer$1", "CdpClient", "param", "CdpConnection", "cdpClient_js_1", "sessionCdpClients", "commandCallbacks", "log", "nextId", "clear", "messageObj", "messageStr", "message<PERSON><PERSON><PERSON>", "parsed", "callbacks", "websocketTransport", "WebSocketTransport", "websocket", "send", "cdpConnection_js_1", "websocketTransport_js_1", "mapperTabPage", "generatePage", "findOrCreateTypeLogContainer", "logType", "containerId", "existingContainer", "document", "getElementById", "debugElement", "divider", "createElement", "className", "append<PERSON><PERSON><PERSON>", "htmlItem", "innerHTML", "globalThis", "documentElement", "cdp", "messages", "global", "window", "sendDebugMessage", "typeLogContainer", "lineElement", "textContent", "desc", "getOwnPropertyDescriptor", "writable", "configurable", "bidiTab", "<PERSON><PERSON><PERSON>", "BidiServer_js_1", "index_js_1", "mapperTabPage_js_1", "require$$6", "waitSelfTargetIdPromise", "setSelfTargetId", "waitSelfTargetId", "WindowBidiTransport", "onBidiMessage", "messageObject", "parseBidiMessage", "respondWithError", "sendBidiResponse", "plainCommandData", "errorCode", "getErrorResponse", "messageId", "getJsonType", "idType", "methodType", "paramsType", "channelType", "createAndStart", "WindowCdpTransport", "onmessage", "createCdpConnection", "BidiParserImpl", "createBidiServer", "launched"], "mappings": "2OAGA,IAAIA,EAFJC,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAAE,cAAwBF,EAAwBG,cAAAH,EAAAI,WAAqBJ,EAAeH,UAAA,EAEpF,SAAWA,GACPA,EAAKQ,YAAeC,GAAQA,EAE5BT,EAAKU,SADL,SAAkBC,GAAS,EAK3BX,EAAKY,YAHL,SAAqBC,GACjB,MAAM,IAAIC,KACb,EAEDd,EAAKe,YAAeC,IAChB,MAAMC,EAAM,CAAA,EACZ,IAAK,MAAMC,KAAQF,EACfC,EAAIC,GAAQA,EAEhB,OAAOD,CAAG,EAEdjB,EAAKmB,mBAAsBF,IACvB,MAAMG,EAAYpB,EAAKqB,WAAWJ,GAAKK,QAAQC,GAA6B,iBAAhBN,EAAIA,EAAIM,MAC9DC,EAAW,CAAA,EACjB,IAAK,MAAMD,KAAKH,EACZI,EAASD,GAAKN,EAAIM,GAEtB,OAAOvB,EAAKyB,aAAaD,EAAS,EAEtCxB,EAAKyB,aAAgBR,GACVjB,EAAKqB,WAAWJ,GAAKS,KAAI,SAAUC,GACtC,OAAOV,EAAIU,EACvB,IAEI3B,EAAKqB,WAAoC,mBAAhBpB,OAAO2B,KACzBX,GAAQhB,OAAO2B,KAAKX,GACpBY,IACC,MAAMD,EAAO,GACb,IAAK,MAAME,KAAOD,EACV5B,OAAO8B,UAAUC,eAAeC,KAAKJ,EAAQC,IAC7CF,EAAKM,KAAKJ,GAGlB,OAAOF,CAAI,EAEnB5B,EAAKmC,KAAO,CAACC,EAAKC,KACd,IAAK,MAAMnB,KAAQkB,EACf,GAAIC,EAAQnB,GACR,OAAOA,CAEC,EAEpBlB,EAAKsC,UAAwC,mBAArBC,OAAOD,UACxB7B,GAAQ8B,OAAOD,UAAU7B,GACzBA,GAAuB,iBAARA,GAAoB+B,SAAS/B,IAAQgC,KAAKC,MAAMjC,KAASA,EAM/ET,EAAK2C,WALL,SAAoBC,EAAOC,EAAY,OACnC,OAAOD,EACFlB,KAAKjB,GAAwB,iBAARA,EAAmB,IAAIA,KAASA,IACrDqC,KAAKD,EACb,EAED7C,EAAK+C,sBAAwB,CAACC,EAAG5C,IACR,iBAAVA,EACAA,EAAM6C,WAEV7C,CAEd,CA7DD,CA6DGJ,EAAOG,EAAQH,OAASG,EAAeH,KAAA,CAAE,KAS5BG,EAAQI,aAAeJ,EAAqBI,WAAA,CAAA,IAN7C2C,YAAc,CAACC,EAAOC,KACtB,IACAD,KACAC,IAIfjD,EAAwBG,cAAAN,EAAKe,YAAY,CACrC,SACA,MACA,SACA,UACA,QACA,UACA,OACA,SACA,SACA,WACA,YACA,OACA,QACA,SACA,UACA,UACA,OACA,QACA,MACA,QA8CJZ,EAAAE,cA5CuBgD,IAEnB,cADiBA,GAEb,IAAK,YACD,OAAOlD,EAAQG,cAAcgD,UACjC,IAAK,SACD,OAAOnD,EAAQG,cAAciD,OACjC,IAAK,SACD,OAAOC,MAAMH,GAAQlD,EAAQG,cAAcmD,IAAMtD,EAAQG,cAAcoD,OAC3E,IAAK,UACD,OAAOvD,EAAQG,cAAcqD,QACjC,IAAK,WACD,OAAOxD,EAAQG,cAAcsD,SACjC,IAAK,SACD,OAAOzD,EAAQG,cAAcuD,OACjC,IAAK,SACD,OAAO1D,EAAQG,cAAcwD,OACjC,IAAK,SACD,OAAIC,MAAMC,QAAQX,GACPlD,EAAQG,cAAcsC,MAEpB,OAATS,EACOlD,EAAQG,cAAc2D,KAE7BZ,EAAKa,MACgB,mBAAdb,EAAKa,MACZb,EAAKc,OACiB,mBAAfd,EAAKc,MACLhE,EAAQG,cAAc8D,QAEd,oBAARC,KAAuBhB,aAAgBgB,IACvClE,EAAQG,cAAcoB,IAEd,oBAAR4C,KAAuBjB,aAAgBiB,IACvCnE,EAAQG,cAAciE,IAEb,oBAATC,MAAwBnB,aAAgBmB,KACxCrE,EAAQG,cAAcmE,KAE1BtE,EAAQG,cAAcuB,OACjC,QACI,OAAO1B,EAAQG,cAAcoE,QACpC,eC1ILzE,OAAOC,eAAeyE,EAAS,aAAc,CAAEvE,OAAO,IACtDuE,EAAAC,SAAwCD,EAAAE,kCAA0B,EAClE,MAAMC,EAASC,EACfC,EAAAC,aAAuBH,EAAO9E,KAAKe,YAAY,CAC3C,eACA,kBACA,SACA,gBACA,8BACA,qBACA,oBACA,oBACA,sBACA,eACA,iBACA,YACA,UACA,6BACA,kBACA,eAMiBiE,EAAAH,cAJE5D,GACNiE,KAAKC,UAAUlE,EAAK,KAAM,GAC3BmE,QAAQ,cAAe,OAGvC,MAAMR,UAAiB9D,MACnBuE,YAAYC,GACRC,QACAC,KAAKF,OAAS,GACdE,KAAKC,SAAYC,IACbF,KAAKF,OAAS,IAAIE,KAAKF,OAAQI,EAAI,EAEvCF,KAAKG,UAAY,CAACC,EAAO,MACrBJ,KAAKF,OAAS,IAAIE,KAAKF,UAAWM,EAAK,EAE3C,MAAMC,aAAyB9D,UAC3B9B,OAAO6F,eACP7F,OAAO6F,eAAeN,KAAMK,GAG5BL,KAAKO,UAAYF,EAErBL,KAAKQ,KAAO,WACZR,KAAKF,OAASA,CACjB,CACGW,aACA,OAAOT,KAAKF,MACf,CACDY,OAAOC,GACH,MAAMC,EAASD,GACX,SAAUE,GACN,OAAOA,EAAMC,OAC7B,EACcC,EAAc,CAAEC,QAAS,IACzBC,EAAgBC,IAClB,IAAK,MAAML,KAASK,EAAMpB,OACtB,GAAmB,kBAAfe,EAAMM,KACNN,EAAMO,YAAYlF,IAAI+E,QAErB,GAAmB,wBAAfJ,EAAMM,KACXF,EAAaJ,EAAMQ,sBAElB,GAAmB,sBAAfR,EAAMM,KACXF,EAAaJ,EAAMS,qBAElB,GAA0B,IAAtBT,EAAMU,KAAKC,OAChBT,EAAYC,QAAQtE,KAAKkE,EAAOC,QAE/B,CACD,IAAIY,EAAOV,EACPW,EAAI,EACR,KAAOA,EAAIb,EAAMU,KAAKC,QAAQ,CAC1B,MAAMG,EAAKd,EAAMU,KAAKG,GACLA,IAAMb,EAAMU,KAAKC,OAAS,GAKvCC,EAAKE,GAAMF,EAAKE,IAAO,CAAEX,QAAS,IAClCS,EAAKE,GAAIX,QAAQtE,KAAKkE,EAAOC,KAJ7BY,EAAKE,GAAMF,EAAKE,IAAO,CAAEX,QAAS,IAMtCS,EAAOA,EAAKE,GACZD,GACH,CACJ,CACJ,EAGL,OADAT,EAAajB,MACNe,CACV,CACDtD,WACI,OAAOuC,KAAKc,OACf,CACGA,cACA,OAAOpB,KAAKC,UAAUK,KAAKF,OAAQR,EAAO9E,KAAK+C,sBAAuB,EACzE,CACGqE,cACA,OAA8B,IAAvB5B,KAAKF,OAAO0B,MACtB,CACDK,QAAQjB,EAAS,CAACC,GAAUA,EAAMC,UAC9B,MAAMC,EAAc,CAAA,EACde,EAAa,GACnB,IAAK,MAAM5B,KAAOF,KAAKF,OACfI,EAAIqB,KAAKC,OAAS,GAClBT,EAAYb,EAAIqB,KAAK,IAAMR,EAAYb,EAAIqB,KAAK,KAAO,GACvDR,EAAYb,EAAIqB,KAAK,IAAI7E,KAAKkE,EAAOV,KAGrC4B,EAAWpF,KAAKkE,EAAOV,IAG/B,MAAO,CAAE4B,aAAYf,cACxB,CACGe,iBACA,OAAO9B,KAAK6B,SACf,EAEW1C,EAAAC,SAAGA,EACnBA,EAAS2C,OAAUjC,GACD,IAAIV,EAASU,GCxH/BrF,OAAOC,eAAesH,EAAS,aAAc,CAAEpH,OAAO,IACtD,MAAM0E,EAASC,EACTJ,EAAa8C,EA6HnBD,EAAAE,QA5HiB,CAACrB,EAAOsB,KACrB,IAAIrB,EACJ,OAAQD,EAAMM,MACV,KAAKhC,EAAWM,aAAa2C,aAErBtB,EADAD,EAAMwB,WAAa/C,EAAOxE,cAAcgD,UAC9B,WAGA,YAAY+C,EAAMyB,sBAAsBzB,EAAMwB,WAE5D,MACJ,KAAKlD,EAAWM,aAAa8C,gBACzBzB,EAAU,mCAAmCpB,KAAKC,UAAUkB,EAAMyB,SAAUhD,EAAO9E,KAAK+C,yBACxF,MACJ,KAAK4B,EAAWM,aAAa+C,kBACzB1B,EAAU,kCAAkCxB,EAAO9E,KAAK2C,WAAW0D,EAAMzE,KAAM,QAC/E,MACJ,KAAK+C,EAAWM,aAAagD,cACzB3B,EAAU,gBACV,MACJ,KAAK3B,EAAWM,aAAaiD,4BACzB5B,EAAU,yCAAyCxB,EAAO9E,KAAK2C,WAAW0D,EAAM8B,WAChF,MACJ,KAAKxD,EAAWM,aAAamD,mBACzB9B,EAAU,gCAAgCxB,EAAO9E,KAAK2C,WAAW0D,EAAM8B,uBAAuB9B,EAAMwB,YACpG,MACJ,KAAKlD,EAAWM,aAAaoD,kBACzB/B,EAAU,6BACV,MACJ,KAAK3B,EAAWM,aAAaqD,oBACzBhC,EAAU,+BACV,MACJ,KAAK3B,EAAWM,aAAasD,aACzBjC,EAAU,eACV,MACJ,KAAK3B,EAAWM,aAAauD,eACO,iBAArBnC,EAAMoC,WACT,aAAcpC,EAAMoC,YACpBnC,EAAU,gCAAgCD,EAAMoC,WAAWC,YAClB,iBAA9BrC,EAAMoC,WAAWE,WACxBrC,EAAU,GAAGA,uDAA6DD,EAAMoC,WAAWE,aAG1F,eAAgBtC,EAAMoC,WAC3BnC,EAAU,mCAAmCD,EAAMoC,WAAWG,cAEzD,aAAcvC,EAAMoC,WACzBnC,EAAU,iCAAiCD,EAAMoC,WAAWI,YAG5D/D,EAAO9E,KAAKY,YAAYyF,EAAMoC,YAIlCnC,EAD0B,UAArBD,EAAMoC,WACD,WAAWpC,EAAMoC,aAGjB,UAEd,MACJ,KAAK9D,EAAWM,aAAa6D,UAErBxC,EADe,UAAfD,EAAM0C,KACI,sBAAsB1C,EAAM2C,MAAQ,UAAY3C,EAAM4C,UAAY,WAAa,eAAe5C,EAAM6C,qBAC1F,WAAf7C,EAAM0C,KACD,uBAAuB1C,EAAM2C,MAAQ,UAAY3C,EAAM4C,UAAY,WAAa,UAAU5C,EAAM6C,uBACtF,WAAf7C,EAAM0C,KACD,kBAAkB1C,EAAM2C,MAC5B,oBACA3C,EAAM4C,UACF,4BACA,kBAAkB5C,EAAM6C,UACd,SAAf7C,EAAM0C,KACD,gBAAgB1C,EAAM2C,MAC1B,oBACA3C,EAAM4C,UACF,4BACA,kBAAkB,IAAIzE,KAAKjC,OAAO8D,EAAM6C,YAExC,gBACd,MACJ,KAAKvE,EAAWM,aAAakE,QAErB7C,EADe,UAAfD,EAAM0C,KACI,sBAAsB1C,EAAM2C,MAAQ,UAAY3C,EAAM4C,UAAY,UAAY,eAAe5C,EAAM+C,qBACzF,WAAf/C,EAAM0C,KACD,uBAAuB1C,EAAM2C,MAAQ,UAAY3C,EAAM4C,UAAY,UAAY,WAAW5C,EAAM+C,uBACtF,WAAf/C,EAAM0C,KACD,kBAAkB1C,EAAM2C,MAC5B,UACA3C,EAAM4C,UACF,wBACA,eAAe5C,EAAM+C,UACX,WAAf/C,EAAM0C,KACD,kBAAkB1C,EAAM2C,MAC5B,UACA3C,EAAM4C,UACF,wBACA,eAAe5C,EAAM+C,UACX,SAAf/C,EAAM0C,KACD,gBAAgB1C,EAAM2C,MAC1B,UACA3C,EAAM4C,UACF,2BACA,kBAAkB,IAAIzE,KAAKjC,OAAO8D,EAAM+C,YAExC,gBACd,MACJ,KAAKzE,EAAWM,aAAaoE,OACzB/C,EAAU,gBACV,MACJ,KAAK3B,EAAWM,aAAaqE,2BACzBhD,EAAU,2CACV,MACJ,KAAK3B,EAAWM,aAAasE,gBACzBjD,EAAU,gCAAgCD,EAAMmD,aAChD,MACJ,KAAK7E,EAAWM,aAAawE,WACzBnD,EAAU,wBACV,MACJ,QACIA,EAAUqB,EAAK+B,aACf5E,EAAO9E,KAAKY,YAAYyF,GAEhC,MAAO,CAAEC,UAAS,EC7HtB,IAAIqD,EAAmBnE,GAAQA,EAAKmE,iBAAoB,SAAUC,GAC9D,OAAQA,GAAOA,EAAIC,WAAcD,EAAM,CAAElC,QAAWkC,EACxD,EACA3J,OAAOC,eAAe+F,EAAS,aAAc,CAAE7F,OAAO,IACtD6F,EAAA6D,YAAyC7D,EAAA8D,mCAA6B,EACtE,MAAMC,EAAOL,EAAgB5E,GAC7BkB,EAAAgE,gBAA0BD,EAAKtC,QAC/B,IAAIwC,EAAmBF,EAAKtC,QAITzB,EAAA8D,YAHnB,SAAqBrI,GACjBwI,EAAmBxI,CACvB,EAKAuE,EAAA6D,YAHA,WACI,OAAOI,CACX,wBCdA,IAAIP,EAAmBnE,GAAQA,EAAKmE,iBAAoB,SAAUC,GAC9D,OAAQA,GAAOA,EAAIC,WAAcD,EAAM,CAAElC,QAAWkC,EACxD,EACA3J,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAAgK,QAAkBhK,EAAkBiK,QAAAjK,EAAAkK,QAAkBlK,EAAoBmK,UAAAnK,EAAAoK,GAAapK,EAAgBqK,MAAArK,EAAAsK,QAAkBtK,EAAsBuK,YAAAvK,EAAAwK,kBAA4BxK,EAAqByK,WAAAzK,EAAA0K,eAAoB,EACpN,MAAMC,EAAW/F,EACXiF,EAAOL,EAAgBlC,GAsB7BtH,EAAA0K,UArBmBE,IACf,MAAM1H,KAAEA,EAAI0D,KAAEA,EAAIiE,UAAEA,EAASC,UAAEA,GAAcF,EACvCG,EAAW,IAAInE,KAAUkE,EAAUlE,MAAQ,IAC3CoE,EAAY,IACXF,EACHlE,KAAMmE,GAEV,IAAIE,EAAe,GACnB,MAAMC,EAAOL,EACR1J,QAAQgK,KAAQA,IAChBC,QACAC,UACL,IAAK,MAAM9J,KAAO2J,EACdD,EAAe1J,EAAIyJ,EAAW,CAAE9H,OAAMqG,aAAc0B,IAAgB9E,QAExE,MAAO,IACA2E,EACHlE,KAAMmE,EACN5E,QAAS2E,EAAU3E,SAAW8E,EACjC,EAGLjL,EAAAyK,WAAqB,GAerBzK,EAAAwK,kBAdA,SAA2Bc,EAAKR,GAC5B,MAAM5E,GAAQ,EAAIlG,EAAQ0K,WAAW,CACjCI,UAAWA,EACX5H,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACViE,UAAW,CACPS,EAAIC,OAAOC,mBACXF,EAAIG,gBACJ,EAAId,EAAShB,eACbE,EAAKtC,SACPpG,QAAQuK,KAAQA,MAEtBJ,EAAIC,OAAOpG,OAAOpD,KAAKmE,EAC1B,EAED,MAAMqE,EACFrF,cACIG,KAAKpF,MAAQ,OAChB,CACD0L,QACuB,UAAftG,KAAKpF,QACLoF,KAAKpF,MAAQ,QACpB,CACD2L,QACuB,YAAfvG,KAAKpF,QACLoF,KAAKpF,MAAQ,UACpB,CACD4L,kBAAkBC,EAAQC,GACtB,MAAMC,EAAa,GACnB,IAAK,MAAMC,KAAKF,EAAS,CACrB,GAAiB,YAAbE,EAAEH,OACF,OAAO9L,EAAQsK,QACF,UAAb2B,EAAEH,QACFA,EAAOH,QACXK,EAAWjK,KAAKkK,EAAEhM,MACrB,CACD,MAAO,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAO+L,EACzC,CACDH,8BAA8BC,EAAQI,GAClC,MAAMC,EAAY,GAClB,IAAK,MAAMC,KAAQF,EACfC,EAAUpK,KAAK,CACXJ,UAAWyK,EAAKzK,IAChB1B,YAAamM,EAAKnM,QAG1B,OAAOsK,EAAY8B,gBAAgBP,EAAQK,EAC9C,CACDN,uBAAuBC,EAAQI,GAC3B,MAAMI,EAAc,CAAA,EACpB,IAAK,MAAMF,KAAQF,EAAO,CACtB,MAAMvK,IAAEA,EAAG1B,MAAEA,GAAUmM,EACvB,GAAmB,YAAfzK,EAAImK,OACJ,OAAO9L,EAAQsK,QACnB,GAAqB,YAAjBrK,EAAM6L,OACN,OAAO9L,EAAQsK,QACA,UAAf3I,EAAImK,QACJA,EAAOH,QACU,UAAjB1L,EAAM6L,QACNA,EAAOH,cACgB,IAAhB1L,EAAMA,OAAyBmM,EAAKG,aAC3CD,EAAY3K,EAAI1B,OAASA,EAAMA,MAEtC,CACD,MAAO,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAOqM,EACzC,EAELtM,EAAAuK,YAAsBA,EACtBvK,EAAkBsK,QAAAxK,OAAO0M,OAAO,CAC5BV,OAAQ,YAGZ9L,EAAAqK,MADepK,IAAW,CAAE6L,OAAQ,QAAS7L,UAG7CD,EAAAoK,GADYnK,IAAW,CAAE6L,OAAQ,QAAS7L,UAG1CD,EAAAmK,UADmBuB,GAAmB,YAAbA,EAAEI,OAG3B9L,EAAAkK,QADiBwB,GAAmB,UAAbA,EAAEI,OAGzB9L,EAAAiK,QADiByB,GAAmB,UAAbA,EAAEI,OAGzB9L,EAAAgK,QADiB0B,GAAyB,oBAAZe,SAA2Bf,aAAae,qBC/GtE3M,OAAOC,eAAe2M,EAAS,aAAc,CAAEzM,OAAO,wBCAtDH,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAoB2M,eAAA,EAEpB,SAAWA,GACPA,EAAUC,SAAYzG,GAA+B,iBAAZA,EAAuB,CAAEA,WAAYA,GAAW,GACzFwG,EAAU7J,SAAYqD,GAA+B,iBAAZA,EAAuBA,EAAUA,aAAyC,EAASA,EAAQA,OACvI,CAHD,CAGenG,EAAQ2M,YAAc3M,EAAA2M,UAAoB,CAAA,gBCNzD7M,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAA6B6M,mBAAA7M,EAAAsE,KAAetE,UAAkBA,EAAiB0D,OAAA1D,EAAAyC,MAAgBzC,MAAcA,EAAiB8M,OAAA9M,EAAA+M,sBAAgC/M,EAAegN,KAAAhN,EAAAiN,UAAoBjN,EAAiBkN,OAAAlN,EAAAkJ,OAAiBlJ,cAAsBA,EAAqBmN,WAAAnN,EAAAoN,MAAgBpN,EAAiBqN,OAAArN,EAAAsN,SAAmBtN,EAAqBuN,WAAAvN,EAAAwN,YAAsBxN,cAAsBA,EAAyByN,eAAAzN,EAAA0N,WAAqB1N,EAAqB2N,WAAA3N,EAAA4N,cAAwB5N,EAAkB6N,QAAA7N,EAAA8N,WAAqB9N,UAAkBA,EAAsB+N,YAAA/N,EAAAgO,OAAiBhO,EAAiBiO,OAAAjO,EAAAkO,UAAoBlO,EAAmBmO,SAAAnO,EAAAoO,gBAA0BpO,wBAAgCA,EAAmBqO,SAAArO,EAAAsO,UAAoBtO,EAAmBuO,SAAAvO,EAAAwO,QAAkBxO,EAAmByO,SAAAzO,EAAA0O,WAAqB1O,SAAiBA,EAAkB2O,QAAA3O,EAAA4O,aAAuB5O,EAAoB6O,UAAA7O,EAAA8O,QAAkB9O,EAAqB+O,WAAA/O,EAAAgP,UAAoBhP,YAAoBA,EAAoBiP,UAAAjP,EAAAkP,aAAkB,EACv+BlP,EAAAmP,MAAgBnP,EAAeoP,KAAApP,EAAAuE,QAAkBvE,EAAgBqP,MAAArP,EAAAmD,UAAoBnD,QAAgBA,EAAsBsP,YAAAtP,EAAA2D,OAAiB3D,EAAiBoD,OAAApD,EAAAuP,aAAuBvP,MAAcA,EAAiBwP,OAAAxP,EAAAiE,QAAkBjE,EAAqByP,WAAAzP,EAAA0P,SAAmB1P,UAAkBA,EAAmB2P,SAAA3P,EAAA4P,QAAkB5P,EAAmB6P,SAAA7P,EAAA0B,OAAiB1B,SAAiBA,EAAmB8P,SAAA9P,EAAA8D,KAAe9D,EAAgB+P,MAAA/P,EAAAgQ,WAAqBhQ,MAAcA,EAAcuB,IAAAvB,EAAAiQ,QAAkBjQ,EAAekQ,KAAAlQ,EAAAmQ,aAAuBnQ,aAAqBA,EAAmByD,SAAAzD,EAAAoQ,KAAepQ,EAAiBqQ,YAAA,EAC5lB,MAAM1F,EAAW/F,EACX0L,EAAchJ,EACdiJ,EAAcC,EACd7L,EAAS8L,EACTjM,EAAakM,EACnB,MAAMC,EACFzL,YAAY0L,EAAQ3Q,EAAO2G,EAAMjF,GAC7B0D,KAAKwL,YAAc,GACnBxL,KAAKuL,OAASA,EACdvL,KAAKnC,KAAOjD,EACZoF,KAAKyL,MAAQlK,EACbvB,KAAK0L,KAAOpP,CACf,CACGiF,WASA,OARKvB,KAAKwL,YAAYhK,SACdxB,KAAK0L,gBAAgBnN,MACrByB,KAAKwL,YAAY9O,QAAQsD,KAAKyL,SAAUzL,KAAK0L,MAG7C1L,KAAKwL,YAAY9O,QAAQsD,KAAKyL,MAAOzL,KAAK0L,OAG3C1L,KAAKwL,WACf,EAEL,MAAMG,EAAe,CAAC1F,EAAK2F,KACvB,IAAI,EAAIV,EAAYtG,SAASgH,GACzB,MAAO,CAAEC,SAAS,EAAMhO,KAAM+N,EAAOhR,OAGrC,IAAKqL,EAAIC,OAAOpG,OAAO0B,OACnB,MAAM,IAAIlG,MAAM,6CAEpB,MAAO,CACHuQ,SAAS,EACL3K,YACA,GAAIlB,KAAK8L,OACL,OAAO9L,KAAK8L,OAChB,MAAM5K,EAAQ,IAAI/B,EAAWC,SAAS6G,EAAIC,OAAOpG,QAEjD,OADAE,KAAK8L,OAAS5K,EACPlB,KAAK8L,MACf,EAER,EAEL,SAASC,EAAoBxG,GACzB,IAAKA,EACD,MAAO,GACX,MAAMyG,SAAEA,EAAQC,mBAAEA,EAAkBC,eAAEA,EAAcC,YAAEA,GAAgB5G,EACtE,GAAIyG,IAAaC,GAAsBC,GACnC,MAAM,IAAI5Q,MAAM,6FAEpB,GAAI0Q,EACA,MAAO,CAAEA,SAAUA,EAAUG,eASjC,MAAO,CAAEH,SARS,CAACI,EAAKnG,IACH,iBAAbmG,EAAIjL,KACG,CAAEL,QAASmF,EAAI/B,mBACF,IAAb+B,EAAIpI,KACJ,CAAEiD,QAASoL,QAAuDA,EAAiBjG,EAAI/B,cAE3F,CAAEpD,QAASmL,QAA+DA,EAAqBhG,EAAI/B,cAEhFiI,cACjC,CACD,MAAMtC,EACFhK,YAAYwM,GACRrM,KAAKsM,IAAMtM,KAAKuM,eAChBvM,KAAKwM,KAAOH,EACZrM,KAAKyM,MAAQzM,KAAKyM,MAAMC,KAAK1M,MAC7BA,KAAK2M,UAAY3M,KAAK2M,UAAUD,KAAK1M,MACrCA,KAAK4M,WAAa5M,KAAK4M,WAAWF,KAAK1M,MACvCA,KAAKuM,eAAiBvM,KAAKuM,eAAeG,KAAK1M,MAC/CA,KAAKsM,IAAMtM,KAAKsM,IAAII,KAAK1M,MACzBA,KAAK6M,OAAS7M,KAAK6M,OAAOH,KAAK1M,MAC/BA,KAAK8M,WAAa9M,KAAK8M,WAAWJ,KAAK1M,MACvCA,KAAK+M,YAAc/M,KAAK+M,YAAYL,KAAK1M,MACzCA,KAAKsK,SAAWtK,KAAKsK,SAASoC,KAAK1M,MACnCA,KAAKyK,SAAWzK,KAAKyK,SAASiC,KAAK1M,MACnCA,KAAKgN,QAAUhN,KAAKgN,QAAQN,KAAK1M,MACjCA,KAAK5C,MAAQ4C,KAAK5C,MAAMsP,KAAK1M,MAC7BA,KAAKpB,QAAUoB,KAAKpB,QAAQ8N,KAAK1M,MACjCA,KAAKiN,GAAKjN,KAAKiN,GAAGP,KAAK1M,MACvBA,KAAKkN,IAAMlN,KAAKkN,IAAIR,KAAK1M,MACzBA,KAAKmN,UAAYnN,KAAKmN,UAAUT,KAAK1M,MACrCA,KAAKoN,MAAQpN,KAAKoN,MAAMV,KAAK1M,MAC7BA,KAAKkC,QAAUlC,KAAKkC,QAAQwK,KAAK1M,MACjCA,KAAKrB,MAAQqB,KAAKrB,MAAM+N,KAAK1M,MAC7BA,KAAKqN,SAAWrN,KAAKqN,SAASX,KAAK1M,MACnCA,KAAKsN,KAAOtN,KAAKsN,KAAKZ,KAAK1M,MAC3BA,KAAKuN,WAAavN,KAAKuN,WAAWb,KAAK1M,MACvCA,KAAKwN,WAAaxN,KAAKwN,WAAWd,KAAK1M,KAC1C,CACGmM,kBACA,OAAOnM,KAAKwM,KAAKL,WACpB,CACDsB,SAASC,GACL,OAAO,EAAIpO,EAAOzE,eAAe6S,EAAM7P,KAC1C,CACD8P,gBAAgBD,EAAOzH,GACnB,OAAQA,GAAO,CACXC,OAAQwH,EAAMnC,OAAOrF,OACrBrI,KAAM6P,EAAM7P,KACZ+P,YAAY,EAAItO,EAAOzE,eAAe6S,EAAM7P,MAC5CuI,eAAgBpG,KAAKwM,KAAKR,SAC1BzK,KAAMmM,EAAMnM,KACZgK,OAAQmC,EAAMnC,OAErB,CACDsC,oBAAoBH,GAChB,MAAO,CACHjH,OAAQ,IAAIyE,EAAYhG,YACxBe,IAAK,CACDC,OAAQwH,EAAMnC,OAAOrF,OACrBrI,KAAM6P,EAAM7P,KACZ+P,YAAY,EAAItO,EAAOzE,eAAe6S,EAAM7P,MAC5CuI,eAAgBpG,KAAKwM,KAAKR,SAC1BzK,KAAMmM,EAAMnM,KACZgK,OAAQmC,EAAMnC,QAGzB,CACDuC,WAAWJ,GACP,MAAM9B,EAAS5L,KAAK+N,OAAOL,GAC3B,IAAI,EAAIxC,EAAYvG,SAASiH,GACzB,MAAM,IAAItQ,MAAM,0CAEpB,OAAOsQ,CACV,CACDoC,YAAYN,GACR,MAAM9B,EAAS5L,KAAK+N,OAAOL,GAC3B,OAAOtG,QAAQ6G,QAAQrC,EAC1B,CACDa,MAAM5O,EAAM0H,GACR,MAAMqG,EAAS5L,KAAK2M,UAAU9O,EAAM0H,GACpC,GAAIqG,EAAOC,QACP,OAAOD,EAAO/N,KAClB,MAAM+N,EAAO1K,KAChB,CACDyL,UAAU9O,EAAM0H,GACZ,IAAI2I,EACJ,MAAMjI,EAAM,CACRC,OAAQ,CACJpG,OAAQ,GACRqO,MAA+E,QAAvED,EAAK3I,aAAuC,EAASA,EAAO4I,aAA0B,IAAPD,GAAgBA,EACvG/H,mBAAoBZ,aAAuC,EAASA,EAAOyG,UAE/EzK,MAAOgE,aAAuC,EAASA,EAAOhE,OAAS,GACvE6E,eAAgBpG,KAAKwM,KAAKR,SAC1BT,OAAQ,KACR1N,OACA+P,YAAY,EAAItO,EAAOzE,eAAegD,IAEpC+N,EAAS5L,KAAK8N,WAAW,CAAEjQ,OAAM0D,KAAM0E,EAAI1E,KAAMgK,OAAQtF,IAC/D,OAAO0F,EAAa1F,EAAK2F,EAC5B,CACDuC,iBAAiBtQ,EAAM0H,GACnB,MAAMqG,QAAe5L,KAAKuM,eAAe1O,EAAM0H,GAC/C,GAAIqG,EAAOC,QACP,OAAOD,EAAO/N,KAClB,MAAM+N,EAAO1K,KAChB,CACDiN,qBAAqBtQ,EAAM0H,GACvB,MAAMU,EAAM,CACRC,OAAQ,CACJpG,OAAQ,GACRqG,mBAAoBZ,aAAuC,EAASA,EAAOyG,SAC3EmC,OAAO,GAEX5M,MAAOgE,aAAuC,EAASA,EAAOhE,OAAS,GACvE6E,eAAgBpG,KAAKwM,KAAKR,SAC1BT,OAAQ,KACR1N,OACA+P,YAAY,EAAItO,EAAOzE,eAAegD,IAEpCuQ,EAAmBpO,KAAK+N,OAAO,CAAElQ,OAAM0D,KAAM0E,EAAI1E,KAAMgK,OAAQtF,IAC/D2F,SAAgB,EAAIV,EAAYvG,SAASyJ,GACzCA,EACAhH,QAAQ6G,QAAQG,IACtB,OAAOzC,EAAa1F,EAAK2F,EAC5B,CACDiB,OAAOwB,EAAOvN,GACV,MAAMwN,EAAsBrT,GACD,iBAAZ6F,QAA2C,IAAZA,EAC/B,CAAEA,WAEe,mBAAZA,EACLA,EAAQ7F,GAGR6F,EAGf,OAAOd,KAAKuO,aAAY,CAACtT,EAAKgL,KAC1B,MAAM2F,EAASyC,EAAMpT,GACfuT,EAAW,IAAMvI,EAAIhG,SAAS,CAChCkB,KAAMhC,EAAWM,aAAaoE,UAC3ByK,EAAmBrT,KAE1B,MAAuB,oBAAZmM,SAA2BwE,aAAkBxE,QAC7CwE,EAAOlN,MAAMb,KACXA,IACD2Q,KACO,OAOd5C,IACD4C,KACO,EAIV,GAER,CACD1B,WAAWuB,EAAOI,GACd,OAAOzO,KAAKuO,aAAY,CAACtT,EAAKgL,MACrBoI,EAAMpT,KACPgL,EAAIhG,SAAmC,mBAAnBwO,EACdA,EAAexT,EAAKgL,GACpBwI,IACC,IAMlB,CACDF,YAAYzB,GACR,OAAO,IAAIzE,GAAW,CAClBqG,OAAQ1O,KACR2O,SAAUjH,GAAsBW,WAChC2C,OAAQ,CAAEzH,KAAM,aAAcuJ,eAErC,CACDC,YAAYD,GACR,OAAO9M,KAAKuO,YAAYzB,EAC3B,CACDxC,WACI,OAAOsE,GAAY7M,OAAO/B,KAAMA,KAAKwM,KACxC,CACD/B,WACI,OAAOtC,GAAYpG,OAAO/B,KAAMA,KAAKwM,KACxC,CACDQ,UACI,OAAOhN,KAAKyK,WAAWH,UAC1B,CACDlN,QACI,OAAO8L,EAASnH,OAAO/B,KAAMA,KAAKwM,KACrC,CACD5N,UACI,OAAO0J,GAAWvG,OAAO/B,KAAMA,KAAKwM,KACvC,CACDS,GAAG4B,GACC,OAAO7F,EAASjH,OAAO,CAAC/B,KAAM6O,GAAS7O,KAAKwM,KAC/C,CACDU,IAAI4B,GACA,OAAO/F,EAAgBhH,OAAO/B,KAAM8O,EAAU9O,KAAKwM,KACtD,CACDW,UAAUA,GACN,OAAO,IAAI9E,GAAW,IACf0D,EAAoB/L,KAAKwM,MAC5BkC,OAAQ1O,KACR2O,SAAUjH,GAAsBW,WAChC2C,OAAQ,CAAEzH,KAAM,YAAa4J,cAEpC,CACDjL,QAAQmK,GACJ,MAAM0C,EAAkC,mBAAR1C,EAAqBA,EAAM,IAAMA,EACjE,OAAO,IAAInE,GAAW,IACf6D,EAAoB/L,KAAKwM,MAC5BwC,UAAWhP,KACXiP,aAAcF,EACdJ,SAAUjH,GAAsBQ,YAEvC,CACDkF,QACI,OAAO,IAAItF,GAAW,CAClB6G,SAAUjH,GAAsBI,WAChCvE,KAAMvD,QACH+L,EAAoB/L,KAAKwM,OAEnC,CACD7N,MAAM0N,GACF,MAAM6C,EAAgC,mBAAR7C,EAAqBA,EAAM,IAAMA,EAC/D,OAAO,IAAIpE,GAAS,IACb8D,EAAoB/L,KAAKwM,MAC5BwC,UAAWhP,KACXmP,WAAYD,EACZP,SAAUjH,GAAsBO,UAEvC,CACDoF,SAASlB,GAEL,OAAO,IAAIiD,EADEpP,KAAKH,aACF,IACTG,KAAKwM,KACRL,eAEP,CACDmB,KAAK+B,GACD,OAAOC,GAAYvN,OAAO/B,KAAMqP,EACnC,CACD7B,aACI,OAAOxN,KAAK2M,eAAU7O,GAAW+N,OACpC,CACD0B,aACI,OAAOvN,KAAK2M,UAAU,MAAMd,OAC/B,EAELlR,EAAAkP,QAAkBA,EAClBlP,EAAAkN,OAAiBgC,EACjBlP,EAAAiN,UAAoBiC,EACpB,MAAM0F,EAAY,iBACZC,EAAa,mBACbC,EAAY,yBACZC,EAAY,8GACZC,EAAa,unBACbC,EAAa,sDACbC,EAAY,gHACZC,EAAY,+XAoClB,MAAMlG,UAAkBC,EACpBhK,cACIE,SAASgQ,WACT/P,KAAKgQ,OAAS,CAACC,EAAOhN,EAAYnC,IAAYd,KAAK8M,YAAYjP,GAASoS,EAAMC,KAAKrS,IAAO,CACtFoF,aACA9B,KAAMhC,EAAWM,aAAauD,kBAC3BiI,EAAY3D,UAAUC,SAASzG,KAEtCd,KAAKmQ,SAAYrP,GAAYd,KAAKoQ,IAAI,EAAGnF,EAAY3D,UAAUC,SAASzG,IACxEd,KAAKqQ,KAAO,IAAM,IAAIzG,EAAU,IACzB5J,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQ,CAAEC,KAAM,WAE1CvQ,KAAKwQ,YAAc,IAAM,IAAI5G,EAAU,IAChC5J,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQ,CAAEC,KAAM,kBAE1CvQ,KAAKyQ,YAAc,IAAM,IAAI7G,EAAU,IAChC5J,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQ,CAAEC,KAAM,iBAE7C,CACDxC,OAAOL,GACC1N,KAAKwM,KAAK/E,SACViG,EAAM7P,KAAO6S,OAAOhD,EAAM7P,OAG9B,GADmBmC,KAAKyN,SAASC,KACdpO,EAAOxE,cAAciD,OAAQ,CAC5C,MAAMkI,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAciD,OAC/BsE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,MAAMwB,EAAS,IAAIyE,EAAYhG,YAC/B,IAAIe,EACJ,IAAK,MAAMoI,KAASrO,KAAKwM,KAAK8D,OAC1B,GAAmB,QAAfjC,EAAMkC,KACF7C,EAAM7P,KAAK2D,OAAS6M,EAAMzT,QAC1BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS2K,EAAMzT,MACf2I,KAAM,SACNE,WAAW,EACXD,OAAO,EACP1C,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,QAAf+H,EAAMkC,KACP7C,EAAM7P,KAAK2D,OAAS6M,EAAMzT,QAC1BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAASyK,EAAMzT,MACf2I,KAAM,SACNE,WAAW,EACXD,OAAO,EACP1C,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,WAAf+H,EAAMkC,KAAmB,CAC9B,MAAMI,EAASjD,EAAM7P,KAAK2D,OAAS6M,EAAMzT,MACnCgW,EAAWlD,EAAM7P,KAAK2D,OAAS6M,EAAMzT,OACvC+V,GAAUC,KACV3K,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,GAC9B0K,GACA,EAAIzF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAASyK,EAAMzT,MACf2I,KAAM,SACNE,WAAW,EACXD,OAAO,EACP1C,QAASuN,EAAMvN,UAGd8P,IACL,EAAI1F,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS2K,EAAMzT,MACf2I,KAAM,SACNE,WAAW,EACXD,OAAO,EACP1C,QAASuN,EAAMvN,UAGvB2F,EAAOH,QAEd,MACI,GAAmB,UAAf+H,EAAMkC,KACNZ,EAAWO,KAAKxC,EAAM7P,QACvBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,QACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,UAAf+H,EAAMkC,KACNX,EAAWM,KAAKxC,EAAM7P,QACvBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,QACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,SAAf+H,EAAMkC,KACNb,EAAUQ,KAAKxC,EAAM7P,QACtBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,OACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,SAAf+H,EAAMkC,KACNhB,EAAUW,KAAKxC,EAAM7P,QACtBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,OACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,UAAf+H,EAAMkC,KACNf,EAAWU,KAAKxC,EAAM7P,QACvBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,QACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,SAAf+H,EAAMkC,KACNd,EAAUS,KAAKxC,EAAM7P,QACtBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,OACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,QAAf+H,EAAMkC,KACX,IACI,IAAIM,IAAInD,EAAM7P,KAUjB,CARD,MAAOqQ,GACHjI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,MACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,OACV,MAEA,GAAmB,UAAf+H,EAAMkC,KAAkB,CAC7BlC,EAAM4B,MAAMa,UAAY,EACLzC,EAAM4B,MAAMC,KAAKxC,EAAM7P,QAEtCoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,QACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,MACI,GAAmB,SAAf+H,EAAMkC,KACX7C,EAAM7P,KAAO6P,EAAM7P,KAAKwS,YAEvB,GAAmB,aAAfhC,EAAMkC,KACN7C,EAAM7P,KAAKqF,SAASmL,EAAMzT,MAAOyT,EAAMlL,YACxC8C,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAauD,eAC9BC,WAAY,CAAEC,SAAUmL,EAAMzT,MAAOuI,SAAUkL,EAAMlL,UACrDrC,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,gBAAf+H,EAAMkC,KACX7C,EAAM7P,KAAO6P,EAAM7P,KAAK2S,mBAEvB,GAAmB,gBAAfnC,EAAMkC,KACX7C,EAAM7P,KAAO6P,EAAM7P,KAAK4S,mBAEvB,GAAmB,eAAfpC,EAAMkC,KACN7C,EAAM7P,KAAKuF,WAAWiL,EAAMzT,SAC7BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAauD,eAC9BC,WAAY,CAAEG,WAAYiL,EAAMzT,OAChCkG,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,aAAf+H,EAAMkC,KACN7C,EAAM7P,KAAKwF,SAASgL,EAAMzT,SAC3BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAauD,eAC9BC,WAAY,CAAEI,SAAUgL,EAAMzT,OAC9BkG,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,aAAf+H,EAAMkC,KAAqB,GAzQzBQ,EA0QqB1C,GAzQ/B2C,UACDD,EAAKE,OACE,IAAIC,OAAO,oDAAoDH,EAAKC,0CAGpE,IAAIE,OAAO,oDAAoDH,EAAKC,gBAGvD,IAAnBD,EAAKC,UACND,EAAKE,OACE,IAAIC,OAAO,0EAGX,IAAIA,OAAO,gDAIlBH,EAAKE,OACE,IAAIC,OAAO,oFAGX,IAAIA,OAAO,2DAqPHhB,KAAKxC,EAAM7P,QAClBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAauD,eAC9BC,WAAY,WACZnC,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,KACuB,OAAf+H,EAAMkC,MA3PRY,EA4PYzD,EAAM7P,MA3PhB,QADEuT,EA4PoB/C,EAAM+C,UA3PnBA,IAAYvB,EAAUK,KAAKiB,MAGpC,OAAZC,GAAqBA,IAAYtB,EAAUI,KAAKiB,MAyPrClL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpChD,WAAY,KACZ9B,KAAMhC,EAAWM,aAAauD,eAC9BlC,QAASuN,EAAMvN,UAEnB2F,EAAOH,UAIXhH,EAAO9E,KAAKY,YAAYiT,GAvQxC,IAAmB8C,EAAIC,EA1BAL,EAoSf,MAAO,CAAEtK,OAAQA,EAAO7L,MAAOA,MAAO8S,EAAM7P,KAC/C,CACDwT,UAAUhD,GACN,OAAO,IAAIzE,EAAU,IACd5J,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQjC,IAErC,CACDiD,MAAMxQ,GACF,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,WAAYtF,EAAY3D,UAAUC,SAASzG,IAC5E,CACDyQ,IAAIzQ,GACA,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,SAAUtF,EAAY3D,UAAUC,SAASzG,IAC1E,CACD0Q,MAAM1Q,GACF,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,WAAYtF,EAAY3D,UAAUC,SAASzG,IAC5E,CACD2Q,KAAK3Q,GACD,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,UAAWtF,EAAY3D,UAAUC,SAASzG,IAC3E,CACD4Q,KAAK5Q,GACD,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,UAAWtF,EAAY3D,UAAUC,SAASzG,IAC3E,CACD6Q,MAAM7Q,GACF,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,WAAYtF,EAAY3D,UAAUC,SAASzG,IAC5E,CACD8Q,KAAK9Q,GACD,OAAOd,KAAKqR,UAAU,CAAEd,KAAM,UAAWtF,EAAY3D,UAAUC,SAASzG,IAC3E,CACDqQ,GAAGxO,GACC,OAAO3C,KAAKqR,UAAU,CAAEd,KAAM,QAAStF,EAAY3D,UAAUC,SAAS5E,IACzE,CACDkP,SAASlP,GACL,IAAIuL,EACJ,MAAuB,iBAAZvL,EACA3C,KAAKqR,UAAU,CAClBd,KAAM,WACNS,UAAW,KACXC,QAAQ,EACRnQ,QAAS6B,IAGV3C,KAAKqR,UAAU,CAClBd,KAAM,WACNS,eAA4F,KAAzErO,aAAyC,EAASA,EAAQqO,WAA6B,KAAOrO,aAAyC,EAASA,EAAQqO,UAC3KC,OAAoF,QAA3E/C,EAAKvL,aAAyC,EAASA,EAAQsO,cAA2B,IAAP/C,GAAgBA,KACzGjD,EAAY3D,UAAUC,SAAS5E,aAAyC,EAASA,EAAQ7B,UAEnG,CACDmP,MAAMA,EAAOnP,GACT,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,QACNN,MAAOA,KACJhF,EAAY3D,UAAUC,SAASzG,IAEzC,CACDoC,SAAStI,EAAO+H,GACZ,OAAO3C,KAAKqR,UAAU,CAClBd,KAAM,WACN3V,MAAOA,EACPuI,SAAUR,aAAyC,EAASA,EAAQQ,YACjE8H,EAAY3D,UAAUC,SAAS5E,aAAyC,EAASA,EAAQ7B,UAEnG,CACDsC,WAAWxI,EAAOkG,GACd,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,aACN3V,MAAOA,KACJqQ,EAAY3D,UAAUC,SAASzG,IAEzC,CACDuC,SAASzI,EAAOkG,GACZ,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,WACN3V,MAAOA,KACJqQ,EAAY3D,UAAUC,SAASzG,IAEzC,CACDsP,IAAI0B,EAAWhR,GACX,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAOkX,KACJ7G,EAAY3D,UAAUC,SAASzG,IAEzC,CACDiR,IAAIC,EAAWlR,GACX,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAOoX,KACJ/G,EAAY3D,UAAUC,SAASzG,IAEzC,CACDU,OAAOyQ,EAAKnR,GACR,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,SACN3V,MAAOqX,KACJhH,EAAY3D,UAAUC,SAASzG,IAEzC,CACGoR,iBACA,QAASlS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,aAAZA,EAAG5B,MAC7C,CACG6B,cACA,QAASpS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,UAAZA,EAAG5B,MAC7C,CACG8B,YACA,QAASrS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,QAAZA,EAAG5B,MAC7C,CACG+B,cACA,QAAStS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,UAAZA,EAAG5B,MAC7C,CACGgC,aACA,QAASvS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,SAAZA,EAAG5B,MAC7C,CACGiC,aACA,QAASxS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,SAAZA,EAAG5B,MAC7C,CACGkC,cACA,QAASzS,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,UAAZA,EAAG5B,MAC7C,CACGmC,aACA,QAAS1S,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,SAAZA,EAAG5B,MAC7C,CACGoC,WACA,QAAS3S,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,OAAZA,EAAG5B,MAC7C,CACGuB,gBACA,IAAI1B,EAAM,KACV,IAAK,MAAM+B,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARH,GAAgB+B,EAAGvX,MAAQwV,KAC3BA,EAAM+B,EAAGvX,OAGrB,OAAOwV,CACV,CACG4B,gBACA,IAAID,EAAM,KACV,IAAK,MAAMI,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARwB,GAAgBI,EAAGvX,MAAQmX,KAC3BA,EAAMI,EAAGvX,OAGrB,OAAOmX,CACV,EAYL,SAASa,EAAmB3X,EAAK4X,GAC7B,MAAMC,GAAe7X,EAAIwC,WAAWsV,MAAM,KAAK,IAAM,IAAIvR,OACnDwR,GAAgBH,EAAKpV,WAAWsV,MAAM,KAAK,IAAM,IAAIvR,OACrDyR,EAAWH,EAAcE,EAAeF,EAAcE,EAG5D,OAFeE,SAASjY,EAAIkY,QAAQF,GAAUrT,QAAQ,IAAK,KAC3CsT,SAASL,EAAKM,QAAQF,GAAUrT,QAAQ,IAAK,KACjC3C,KAAKmW,IAAI,GAAIH,EAC5C,CAjBDtY,EAAAiP,UAAoBA,EACpBA,EAAU7H,OAAUwD,IAChB,IAAI2I,EACJ,OAAO,IAAItE,EAAU,CACjB0G,OAAQ,GACR3B,SAAUjH,GAAsBkC,UAChCnC,OAAiF,QAAxEyG,EAAK3I,aAAuC,EAASA,EAAOkC,cAA2B,IAAPyG,GAAgBA,KACtGnC,EAAoBxG,IACzB,EAUN,MAAM8N,UAAkBxJ,EACpBhK,cACIE,SAASgQ,WACT/P,KAAKoQ,IAAMpQ,KAAKsT,IAChBtT,KAAK+R,IAAM/R,KAAKuT,IAChBvT,KAAK6S,KAAO7S,KAAKgE,UACpB,CACD+J,OAAOL,GACC1N,KAAKwM,KAAK/E,SACViG,EAAM7P,KAAOd,OAAO2Q,EAAM7P,OAG9B,GADmBmC,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcoD,OAAQ,CAC5C,MAAM+H,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcoD,OAC/BmE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,IAAIgB,EACJ,MAAMQ,EAAS,IAAIyE,EAAYhG,YAC/B,IAAK,MAAMmJ,KAASrO,KAAKwM,KAAK8D,OAC1B,GAAmB,QAAfjC,EAAMkC,KACDjR,EAAO9E,KAAKsC,UAAU4Q,EAAM7P,QAC7BoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAU,UACVD,SAAU,QACVvB,QAASuN,EAAMvN,UAEnB2F,EAAOH,cAGV,GAAmB,QAAf+H,EAAMkC,KAAgB,EACVlC,EAAM5K,UACjBiK,EAAM7P,KAAOwQ,EAAMzT,MACnB8S,EAAM7P,MAAQwQ,EAAMzT,SAEtBqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS2K,EAAMzT,MACf2I,KAAM,SACNE,UAAW4K,EAAM5K,UACjBD,OAAO,EACP1C,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,MACI,GAAmB,QAAf+H,EAAMkC,KAAgB,EACZlC,EAAM5K,UACfiK,EAAM7P,KAAOwQ,EAAMzT,MACnB8S,EAAM7P,MAAQwQ,EAAMzT,SAEtBqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAASyK,EAAMzT,MACf2I,KAAM,SACNE,UAAW4K,EAAM5K,UACjBD,OAAO,EACP1C,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,KACuB,eAAf+H,EAAMkC,KACyC,IAAhDqC,EAAmBlF,EAAM7P,KAAMwQ,EAAMzT,SACrCqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAasE,gBAC9BC,WAAYqK,EAAMzT,MAClBkG,QAASuN,EAAMvN,UAEnB2F,EAAOH,SAGS,WAAf+H,EAAMkC,KACNxT,OAAOC,SAAS0Q,EAAM7P,QACvBoI,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAawE,WAC9BnD,QAASuN,EAAMvN,UAEnB2F,EAAOH,SAIXhH,EAAO9E,KAAKY,YAAYiT,GAGhC,MAAO,CAAE5H,OAAQA,EAAO7L,MAAOA,MAAO8S,EAAM7P,KAC/C,CACDyV,IAAI1Y,EAAOkG,GACP,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAMqQ,EAAY3D,UAAU7J,SAASqD,GAC3E,CACD2S,GAAG7Y,EAAOkG,GACN,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAOqQ,EAAY3D,UAAU7J,SAASqD,GAC5E,CACDyS,IAAI3Y,EAAOkG,GACP,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAMqQ,EAAY3D,UAAU7J,SAASqD,GAC3E,CACD4S,GAAG9Y,EAAOkG,GACN,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAOqQ,EAAY3D,UAAU7J,SAASqD,GAC5E,CACD0S,SAASjD,EAAM3V,EAAO6I,EAAW3C,GAC7B,OAAO,IAAIuS,EAAU,IACdrT,KAAKwM,KACR8D,OAAQ,IACDtQ,KAAKwM,KAAK8D,OACb,CACIC,OACA3V,QACA6I,YACA3C,QAASmK,EAAY3D,UAAU7J,SAASqD,MAIvD,CACDuQ,UAAUhD,GACN,OAAO,IAAIgF,EAAU,IACdrT,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQjC,IAErC,CACDsF,IAAI7S,GACA,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACNzP,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACD8S,SAAS9S,GACL,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO,EACP6I,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACD+S,SAAS/S,GACL,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO,EACP6I,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDgT,YAAYhT,GACR,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO,EACP6I,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDiT,YAAYjT,GACR,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO,EACP6I,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDkD,WAAWpJ,EAAOkG,GACd,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,aACN3V,MAAOA,EACPkG,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDkT,OAAOlT,GACH,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,SACNzP,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDmT,KAAKnT,GACD,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN9M,WAAW,EACX7I,MAAOmC,OAAOmX,iBACdpT,QAASmK,EAAY3D,UAAU7J,SAASqD,KACzCuQ,UAAU,CACTd,KAAM,MACN9M,WAAW,EACX7I,MAAOmC,OAAOoX,iBACdrT,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACGsT,eACA,IAAIhE,EAAM,KACV,IAAK,MAAM+B,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARH,GAAgB+B,EAAGvX,MAAQwV,KAC3BA,EAAM+B,EAAGvX,OAGrB,OAAOwV,CACV,CACGiE,eACA,IAAItC,EAAM,KACV,IAAK,MAAMI,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARwB,GAAgBI,EAAGvX,MAAQmX,KAC3BA,EAAMI,EAAGvX,OAGrB,OAAOmX,CACV,CACGuC,YACA,QAAStU,KAAKwM,KAAK8D,OAAO3T,MAAMwV,GAAmB,QAAZA,EAAG5B,MACzB,eAAZ4B,EAAG5B,MAAyBjR,EAAO9E,KAAKsC,UAAUqV,EAAGvX,QAC7D,CACGoC,eACA,IAAI+U,EAAM,KAAM3B,EAAM,KACtB,IAAK,MAAM+B,KAAMnS,KAAKwM,KAAK8D,OAAQ,CAC/B,GAAgB,WAAZ6B,EAAG5B,MACS,QAAZ4B,EAAG5B,MACS,eAAZ4B,EAAG5B,KACH,OAAO,EAEU,QAAZ4B,EAAG5B,MACI,OAARH,GAAgB+B,EAAGvX,MAAQwV,KAC3BA,EAAM+B,EAAGvX,OAEI,QAAZuX,EAAG5B,OACI,OAARwB,GAAgBI,EAAGvX,MAAQmX,KAC3BA,EAAMI,EAAGvX,MAEpB,CACD,OAAOmC,OAAOC,SAASoT,IAAQrT,OAAOC,SAAS+U,EAClD,EAELpX,EAAA0Y,UAAoBA,EACpBA,EAAUtR,OAAUwD,GACT,IAAI8N,EAAU,CACjB/C,OAAQ,GACR3B,SAAUjH,GAAsB2L,UAChC5L,QAASlC,aAAuC,EAASA,EAAOkC,UAAW,KACxEsE,EAAoBxG,KAG/B,MAAMoE,UAAkBE,EACpBhK,cACIE,SAASgQ,WACT/P,KAAKoQ,IAAMpQ,KAAKsT,IAChBtT,KAAK+R,IAAM/R,KAAKuT,GACnB,CACDxF,OAAOL,GACC1N,KAAKwM,KAAK/E,SACViG,EAAM7P,KAAO0W,OAAO7G,EAAM7P,OAG9B,GADmBmC,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcuD,OAAQ,CAC5C,MAAM4H,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcuD,OAC/BgE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,IAAIgB,EACJ,MAAMQ,EAAS,IAAIyE,EAAYhG,YAC/B,IAAK,MAAMmJ,KAASrO,KAAKwM,KAAK8D,OAC1B,GAAmB,QAAfjC,EAAMkC,KAAgB,EACLlC,EAAM5K,UACjBiK,EAAM7P,KAAOwQ,EAAMzT,MACnB8S,EAAM7P,MAAQwQ,EAAMzT,SAEtBqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BC,KAAM,SACNG,QAAS2K,EAAMzT,MACf6I,UAAW4K,EAAM5K,UACjB3C,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,MACI,GAAmB,QAAf+H,EAAMkC,KAAgB,EACZlC,EAAM5K,UACfiK,EAAM7P,KAAOwQ,EAAMzT,MACnB8S,EAAM7P,MAAQwQ,EAAMzT,SAEtBqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BJ,KAAM,SACNK,QAASyK,EAAMzT,MACf6I,UAAW4K,EAAM5K,UACjB3C,QAASuN,EAAMvN,UAEnB2F,EAAOH,QAEd,KACuB,eAAf+H,EAAMkC,KACP7C,EAAM7P,KAAOwQ,EAAMzT,QAAU2Z,OAAO,KACpCtO,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAasE,gBAC9BC,WAAYqK,EAAMzT,MAClBkG,QAASuN,EAAMvN,UAEnB2F,EAAOH,SAIXhH,EAAO9E,KAAKY,YAAYiT,GAGhC,MAAO,CAAE5H,OAAQA,EAAO7L,MAAOA,MAAO8S,EAAM7P,KAC/C,CACDyV,IAAI1Y,EAAOkG,GACP,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAMqQ,EAAY3D,UAAU7J,SAASqD,GAC3E,CACD2S,GAAG7Y,EAAOkG,GACN,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAOqQ,EAAY3D,UAAU7J,SAASqD,GAC5E,CACDyS,IAAI3Y,EAAOkG,GACP,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAMqQ,EAAY3D,UAAU7J,SAASqD,GAC3E,CACD4S,GAAG9Y,EAAOkG,GACN,OAAOd,KAAKwT,SAAS,MAAO5Y,GAAO,EAAOqQ,EAAY3D,UAAU7J,SAASqD,GAC5E,CACD0S,SAASjD,EAAM3V,EAAO6I,EAAW3C,GAC7B,OAAO,IAAI6I,EAAU,IACd3J,KAAKwM,KACR8D,OAAQ,IACDtQ,KAAKwM,KAAK8D,OACb,CACIC,OACA3V,QACA6I,YACA3C,QAASmK,EAAY3D,UAAU7J,SAASqD,MAIvD,CACDuQ,UAAUhD,GACN,OAAO,IAAI1E,EAAU,IACd3J,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQjC,IAErC,CACDuF,SAAS9S,GACL,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO2Z,OAAO,GACd9Q,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACD+S,SAAS/S,GACL,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO2Z,OAAO,GACd9Q,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDgT,YAAYhT,GACR,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO2Z,OAAO,GACd9Q,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDiT,YAAYjT,GACR,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO2Z,OAAO,GACd9Q,WAAW,EACX3C,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDkD,WAAWpJ,EAAOkG,GACd,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,aACN3V,QACAkG,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACGsT,eACA,IAAIhE,EAAM,KACV,IAAK,MAAM+B,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARH,GAAgB+B,EAAGvX,MAAQwV,KAC3BA,EAAM+B,EAAGvX,OAGrB,OAAOwV,CACV,CACGiE,eACA,IAAItC,EAAM,KACV,IAAK,MAAMI,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARwB,GAAgBI,EAAGvX,MAAQmX,KAC3BA,EAAMI,EAAGvX,OAGrB,OAAOmX,CACV,EAELpX,EAAAgP,UAAoBA,EACpBA,EAAU5H,OAAUwD,IAChB,IAAI2I,EACJ,OAAO,IAAIvE,EAAU,CACjB2G,OAAQ,GACR3B,SAAUjH,GAAsBiC,UAChClC,OAAiF,QAAxEyG,EAAK3I,aAAuC,EAASA,EAAOkC,cAA2B,IAAPyG,GAAgBA,KACtGnC,EAAoBxG,IACzB,EAEN,MAAMmE,UAAmBG,EACrBkE,OAAOL,GACC1N,KAAKwM,KAAK/E,SACViG,EAAM7P,KAAO2W,QAAQ9G,EAAM7P,OAG/B,GADmBmC,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcqD,QAAS,CAC7C,MAAM8H,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcqD,QAC/BkE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAA+O,WAAqBA,EACrBA,EAAW3H,OAAUwD,GACV,IAAImE,EAAW,CAClBiF,SAAUjH,GAAsBgC,WAChCjC,QAASlC,aAAuC,EAASA,EAAOkC,UAAW,KACxEsE,EAAoBxG,KAG/B,MAAMkE,UAAgBI,EAClBkE,OAAOL,GACC1N,KAAKwM,KAAK/E,SACViG,EAAM7P,KAAO,IAAImB,KAAK0O,EAAM7P,OAGhC,GADmBmC,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcmE,KAAM,CAC1C,MAAMgH,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcmE,KAC/BoD,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,GAAIjH,MAAM0P,EAAM7P,KAAK4W,WAAY,CAC7B,MAAMxO,EAAMjG,KAAK2N,gBAAgBD,GAIjC,OAHA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAasD,eAE3BmI,EAAYjG,OACtB,CACD,MAAMwB,EAAS,IAAIyE,EAAYhG,YAC/B,IAAIe,EACJ,IAAK,MAAMoI,KAASrO,KAAKwM,KAAK8D,OACP,QAAfjC,EAAMkC,KACF7C,EAAM7P,KAAK4W,UAAYpG,EAAMzT,QAC7BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BxC,QAASuN,EAAMvN,QACf2C,WAAW,EACXD,OAAO,EACPE,QAAS2K,EAAMzT,MACf2I,KAAM,SAEVkD,EAAOH,SAGS,QAAf+H,EAAMkC,KACP7C,EAAM7P,KAAK4W,UAAYpG,EAAMzT,QAC7BqL,EAAMjG,KAAK2N,gBAAgBD,EAAOzH,IAClC,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9B7C,QAASuN,EAAMvN,QACf2C,WAAW,EACXD,OAAO,EACPI,QAASyK,EAAMzT,MACf2I,KAAM,SAEVkD,EAAOH,SAIXhH,EAAO9E,KAAKY,YAAYiT,GAGhC,MAAO,CACH5H,OAAQA,EAAO7L,MACfA,MAAO,IAAIoE,KAAK0O,EAAM7P,KAAK4W,WAElC,CACDpD,UAAUhD,GACN,OAAO,IAAI5E,EAAQ,IACZzJ,KAAKwM,KACR8D,OAAQ,IAAItQ,KAAKwM,KAAK8D,OAAQjC,IAErC,CACD+B,IAAIsE,EAAS5T,GACT,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO8Z,EAAQD,UACf3T,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACDiR,IAAI4C,EAAS7T,GACT,OAAOd,KAAKqR,UAAU,CAClBd,KAAM,MACN3V,MAAO+Z,EAAQF,UACf3T,QAASmK,EAAY3D,UAAU7J,SAASqD,IAE/C,CACG4T,cACA,IAAItE,EAAM,KACV,IAAK,MAAM+B,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARH,GAAgB+B,EAAGvX,MAAQwV,KAC3BA,EAAM+B,EAAGvX,OAGrB,OAAc,MAAPwV,EAAc,IAAIpR,KAAKoR,GAAO,IACxC,CACGuE,cACA,IAAI5C,EAAM,KACV,IAAK,MAAMI,KAAMnS,KAAKwM,KAAK8D,OACP,QAAZ6B,EAAG5B,OACS,OAARwB,GAAgBI,EAAGvX,MAAQmX,KAC3BA,EAAMI,EAAGvX,OAGrB,OAAc,MAAPmX,EAAc,IAAI/S,KAAK+S,GAAO,IACxC,EAELpX,EAAA8O,QAAkBA,EAClBA,EAAQ1H,OAAUwD,GACP,IAAIkE,EAAQ,CACf6G,OAAQ,GACR7I,QAASlC,aAAuC,EAASA,EAAOkC,UAAW,EAC3EkH,SAAUjH,GAAsB+B,WAC7BsC,EAAoBxG,KAG/B,MAAMiE,UAAkBK,EACpBkE,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcwD,OAAQ,CAC5C,MAAM2H,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcwD,OAC/B+D,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAA6O,UAAoBA,EACpBA,EAAUzH,OAAUwD,GACT,IAAIiE,EAAU,CACjBmF,SAAUjH,GAAsB8B,aAC7BuC,EAAoBxG,KAG/B,MAAMgE,UAAqBM,EACvBkE,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcgD,UAAW,CAC/C,MAAMmI,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcgD,UAC/BuE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAA4O,aAAuBA,EACvBA,EAAaxH,OAAUwD,GACZ,IAAIgE,EAAa,CACpBoF,SAAUjH,GAAsB6B,gBAC7BwC,EAAoBxG,KAG/B,MAAM+D,UAAgBO,EAClBkE,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAc2D,KAAM,CAC1C,MAAMwH,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAc2D,KAC/B4D,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAA2O,QAAkBA,EAClBA,EAAQvH,OAAUwD,GACP,IAAI+D,EAAQ,CACfqF,SAAUjH,GAAsB4B,WAC7ByC,EAAoBxG,KAG/B,MAAMqP,UAAe/K,EACjBhK,cACIE,SAASgQ,WACT/P,KAAK6U,MAAO,CACf,CACD9G,OAAOL,GACH,OAAO,EAAIxC,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAAia,OAAiBA,EACjBA,EAAO7S,OAAUwD,GACN,IAAIqP,EAAO,CACdjG,SAAUjH,GAAsBkN,UAC7B7I,EAAoBxG,KAG/B,MAAM8D,UAAmBQ,EACrBhK,cACIE,SAASgQ,WACT/P,KAAK8U,UAAW,CACnB,CACD/G,OAAOL,GACH,OAAO,EAAIxC,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAA0O,WAAqBA,EACrBA,EAAWtH,OAAUwD,GACV,IAAI8D,EAAW,CAClBsF,SAAUjH,GAAsB2B,cAC7B0C,EAAoBxG,KAG/B,MAAM6D,UAAiBS,EACnBkE,OAAOL,GACH,MAAMzH,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAc4P,MAC/BrI,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,EAELtK,EAAAyO,SAAmBA,EACnBA,EAASrH,OAAUwD,GACR,IAAI6D,EAAS,CAChBuF,SAAUjH,GAAsB0B,YAC7B2C,EAAoBxG,KAG/B,MAAM4D,UAAgBU,EAClBkE,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcgD,UAAW,CAC/C,MAAMmI,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAciP,KAC/B1H,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,EAELlD,EAAAwO,QAAkBA,EAClBA,EAAQpH,OAAUwD,GACP,IAAI4D,EAAQ,CACfwF,SAAUjH,GAAsByB,WAC7B4C,EAAoBxG,KAG/B,MAAM2D,UAAiBW,EACnBkE,OAAOL,GACH,MAAMzH,IAAEA,EAAGQ,OAAEA,GAAWzG,KAAK6N,oBAAoBH,GAC3CrB,EAAMrM,KAAKwM,KACjB,GAAIvG,EAAI2H,aAAetO,EAAOxE,cAAcsC,MAMxC,OALA,EAAI8N,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcsC,MAC/BiF,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,GAAwB,OAApBoH,EAAI0I,YAAsB,CAC1B,MAAMpE,EAAS1K,EAAIpI,KAAK2D,OAAS6K,EAAI0I,YAAYna,MAC3CgW,EAAW3K,EAAIpI,KAAK2D,OAAS6K,EAAI0I,YAAYna,OAC/C+V,GAAUC,MACV,EAAI1F,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMwP,EAASxR,EAAWM,aAAakE,QAAUxE,EAAWM,aAAa6D,UACzEI,QAAUkN,EAAWvE,EAAI0I,YAAYna,WAAQkD,EAC7C8F,QAAU+M,EAAStE,EAAI0I,YAAYna,WAAQkD,EAC3CyF,KAAM,QACNE,WAAW,EACXD,OAAO,EACP1C,QAASuL,EAAI0I,YAAYjU,UAE7B2F,EAAOH,QAEd,CA2BD,GA1BsB,OAAlB+F,EAAIyF,WACA7L,EAAIpI,KAAK2D,OAAS6K,EAAIyF,UAAUlX,SAChC,EAAIsQ,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS2I,EAAIyF,UAAUlX,MACvB2I,KAAM,QACNE,WAAW,EACXD,OAAO,EACP1C,QAASuL,EAAIyF,UAAUhR,UAE3B2F,EAAOH,SAGO,OAAlB+F,EAAI2F,WACA/L,EAAIpI,KAAK2D,OAAS6K,EAAI2F,UAAUpX,SAChC,EAAIsQ,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAASyI,EAAI2F,UAAUpX,MACvB2I,KAAM,QACNE,WAAW,EACXD,OAAO,EACP1C,QAASuL,EAAI2F,UAAUlR,UAE3B2F,EAAOH,SAGXL,EAAIC,OAAOiI,MACX,OAAO/G,QAAQ4N,IAAI,IAAI/O,EAAIpI,MAAM3B,KAAI,CAACR,EAAMgG,IACjC2K,EAAI9I,KAAKyK,YAAY,IAAI1C,EAAmBrF,EAAKvK,EAAMuK,EAAI1E,KAAMG,OACxEhD,MAAMkN,GACCV,EAAYhG,YAAY+P,WAAWxO,EAAQmF,KAG1D,MAAMA,EAAS,IAAI3F,EAAIpI,MAAM3B,KAAI,CAACR,EAAMgG,IAC7B2K,EAAI9I,KAAKuK,WAAW,IAAIxC,EAAmBrF,EAAKvK,EAAMuK,EAAI1E,KAAMG,MAE3E,OAAOwJ,EAAYhG,YAAY+P,WAAWxO,EAAQmF,EACrD,CACGsJ,cACA,OAAOlV,KAAKwM,KAAKjJ,IACpB,CACD6M,IAAI0B,EAAWhR,GACX,OAAO,IAAIoI,EAAS,IACblJ,KAAKwM,KACRsF,UAAW,CAAElX,MAAOkX,EAAWhR,QAASmK,EAAY3D,UAAU7J,SAASqD,KAE9E,CACDiR,IAAIC,EAAWlR,GACX,OAAO,IAAIoI,EAAS,IACblJ,KAAKwM,KACRwF,UAAW,CAAEpX,MAAOoX,EAAWlR,QAASmK,EAAY3D,UAAU7J,SAASqD,KAE9E,CACDU,OAAOyQ,EAAKnR,GACR,OAAO,IAAIoI,EAAS,IACblJ,KAAKwM,KACRuI,YAAa,CAAEna,MAAOqX,EAAKnR,QAASmK,EAAY3D,UAAU7J,SAASqD,KAE1E,CACDqP,SAASrP,GACL,OAAOd,KAAKoQ,IAAI,EAAGtP,EACtB,EAaL,SAASqU,EAAezG,GACpB,GAAIA,aAAkBzF,EAAW,CAC7B,MAAMmM,EAAW,CAAA,EACjB,IAAK,MAAM9Y,KAAOoS,EAAO2G,MAAO,CAC5B,MAAMC,EAAc5G,EAAO2G,MAAM/Y,GACjC8Y,EAAS9Y,GAAOsS,GAAY7M,OAAOoT,EAAeG,GACrD,CACD,OAAO,IAAIrM,EAAU,IACdyF,EAAOlC,KACV6I,MAAO,IAAMD,GAEpB,CACI,OAAI1G,aAAkBxF,EAChB,IAAIA,EAAS,IACbwF,EAAOlC,KACVjJ,KAAM4R,EAAezG,EAAOwG,WAG3BxG,aAAkBE,GAChBA,GAAY7M,OAAOoT,EAAezG,EAAO6G,WAE3C7G,aAAkBvG,GAChBA,GAAYpG,OAAOoT,EAAezG,EAAO6G,WAE3C7G,aAAkB5F,EAChBA,EAAS/G,OAAO2M,EAAOlT,MAAMU,KAAKR,GAASyZ,EAAezZ,MAG1DgT,CAEd,CAzCD/T,EAAAuO,SAAmBA,EACnBA,EAASnH,OAAS,CAAC2M,EAAQnJ,IAChB,IAAI2D,EAAS,CAChB3F,KAAMmL,EACNoD,UAAW,KACXE,UAAW,KACX+C,YAAa,KACbpG,SAAUjH,GAAsBwB,YAC7B6C,EAAoBxG,KAkC/B,MAAM0D,UAAkBY,EACpBhK,cACIE,SAASgQ,WACT/P,KAAKwV,QAAU,KACfxV,KAAKyV,UAAYzV,KAAK0V,YACtB1V,KAAK2V,QAAU3V,KAAK4V,MACvB,CACDC,aACI,GAAqB,OAAjB7V,KAAKwV,QACL,OAAOxV,KAAKwV,QAChB,MAAMH,EAAQrV,KAAKwM,KAAK6I,QAClBjZ,EAAOkD,EAAO9E,KAAKqB,WAAWwZ,GACpC,OAAQrV,KAAKwV,QAAU,CAAEH,QAAOjZ,OACnC,CACD2R,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcuB,OAAQ,CAC5C,MAAM4J,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcuB,OAC/BgG,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,MAAMwB,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,IAC3C2H,MAAEA,EAAOjZ,KAAM0Z,GAAc9V,KAAK6V,aAClCE,EAAY,GAClB,KAAM/V,KAAKwM,KAAKwJ,oBAAoB5M,GACN,UAA1BpJ,KAAKwM,KAAKyJ,aACV,IAAK,MAAM3Z,KAAO2J,EAAIpI,KACbiY,EAAU5S,SAAS5G,IACpByZ,EAAUrZ,KAAKJ,GAI3B,MAAMuK,EAAQ,GACd,IAAK,MAAMvK,KAAOwZ,EAAW,CACzB,MAAMI,EAAeb,EAAM/Y,GACrB1B,EAAQqL,EAAIpI,KAAKvB,GACvBuK,EAAMnK,KAAK,CACPJ,IAAK,CAAEmK,OAAQ,QAAS7L,MAAO0B,GAC/B1B,MAAOsb,EAAanI,OAAO,IAAIzC,EAAmBrF,EAAKrL,EAAOqL,EAAI1E,KAAMjF,IACxE4K,UAAW5K,KAAO2J,EAAIpI,MAE7B,CACD,GAAImC,KAAKwM,KAAKwJ,oBAAoB5M,EAAU,CACxC,MAAM6M,EAAcjW,KAAKwM,KAAKyJ,YAC9B,GAAoB,gBAAhBA,EACA,IAAK,MAAM3Z,KAAOyZ,EACdlP,EAAMnK,KAAK,CACPJ,IAAK,CAAEmK,OAAQ,QAAS7L,MAAO0B,GAC/B1B,MAAO,CAAE6L,OAAQ,QAAS7L,MAAOqL,EAAIpI,KAAKvB,WAIjD,GAAoB,WAAhB2Z,EACDF,EAAUvU,OAAS,KACnB,EAAI0J,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa+C,kBAC9BpG,KAAM2Z,IAEVtP,EAAOH,cAGV,GAAoB,UAAhB2P,EAGL,MAAM,IAAI3a,MAAM,uDAEvB,KACI,CACD,MAAM0a,EAAWhW,KAAKwM,KAAKwJ,SAC3B,IAAK,MAAM1Z,KAAOyZ,EAAW,CACzB,MAAMnb,EAAQqL,EAAIpI,KAAKvB,GACvBuK,EAAMnK,KAAK,CACPJ,IAAK,CAAEmK,OAAQ,QAAS7L,MAAO0B,GAC/B1B,MAAOob,EAASjI,OAAO,IAAIzC,EAAmBrF,EAAKrL,EAAOqL,EAAI1E,KAAMjF,IACpE4K,UAAW5K,KAAO2J,EAAIpI,MAE7B,CACJ,CACD,OAAIoI,EAAIC,OAAOiI,MACJ/G,QAAQ6G,UACVvP,MAAKyP,UACN,MAAMrH,EAAY,GAClB,IAAK,MAAMC,KAAQF,EAAO,CACtB,MAAMvK,QAAYyK,EAAKzK,IACvBwK,EAAUpK,KAAK,CACXJ,MACA1B,YAAamM,EAAKnM,MAClBsM,UAAWH,EAAKG,WAEvB,CACD,OAAOJ,CAAS,IAEfpI,MAAMoI,GACAoE,EAAYhG,YAAY8B,gBAAgBP,EAAQK,KAIpDoE,EAAYhG,YAAY8B,gBAAgBP,EAAQI,EAE9D,CACGwO,YACA,OAAOrV,KAAKwM,KAAK6I,OACpB,CACDc,OAAOrV,GAEH,OADAmK,EAAY3D,UAAUC,SACf,IAAI0B,EAAU,IACdjJ,KAAKwM,KACRyJ,YAAa,iBACGnY,IAAZgD,EACE,CACEkL,SAAU,CAACnL,EAAOoF,KACd,IAAIiI,EAAIkI,EAAIC,EAAIC,EAChB,MAAMpS,EAAgI,QAAhHmS,EAA0C,QAApCD,GAAMlI,EAAKlO,KAAKwM,MAAMR,gBAA6B,IAAPoK,OAAgB,EAASA,EAAG3Z,KAAKyR,EAAIrN,EAAOoF,GAAKnF,eAA4B,IAAPuV,EAAgBA,EAAKpQ,EAAI/B,aACvK,MAAmB,sBAAfrD,EAAMM,KACC,CACHL,QAAoE,QAA1DwV,EAAKrL,EAAY3D,UAAUC,SAASzG,GAASA,eAA4B,IAAPwV,EAAgBA,EAAKpS,GAElG,CACHpD,QAASoD,EACZ,GAGP,IAEb,CACDqS,QACI,OAAO,IAAItN,EAAU,IACdjJ,KAAKwM,KACRyJ,YAAa,SAEpB,CACDP,cACI,OAAO,IAAIzM,EAAU,IACdjJ,KAAKwM,KACRyJ,YAAa,eAEpB,CACDL,OAAOY,GACH,OAAO,IAAIvN,EAAU,IACdjJ,KAAKwM,KACR6I,MAAO,KAAO,IACPrV,KAAKwM,KAAK6I,WACVmB,KAGd,CACDC,MAAMC,GAUF,OATe,IAAIzN,EAAU,CACzBgN,YAAaS,EAAQlK,KAAKyJ,YAC1BD,SAAUU,EAAQlK,KAAKwJ,SACvBX,MAAO,KAAO,IACPrV,KAAKwM,KAAK6I,WACVqB,EAAQlK,KAAK6I,UAEpB1G,SAAUjH,GAAsBuB,WAGvC,CACD0N,OAAOra,EAAKoS,GACR,OAAO1O,KAAK2V,QAAQ,CAAErZ,CAACA,GAAMoS,GAChC,CACDsH,SAASY,GACL,OAAO,IAAI3N,EAAU,IACdjJ,KAAKwM,KACRwJ,SAAUY,GAEjB,CACDC,KAAKC,GACD,MAAMzB,EAAQ,CAAA,EAMd,OALA/V,EAAO9E,KAAKqB,WAAWib,GAAMC,SAASza,IAC9Bwa,EAAKxa,IAAQ0D,KAAKqV,MAAM/Y,KACxB+Y,EAAM/Y,GAAO0D,KAAKqV,MAAM/Y,GAC3B,IAEE,IAAI2M,EAAU,IACdjJ,KAAKwM,KACR6I,MAAO,IAAMA,GAEpB,CACD2B,KAAKF,GACD,MAAMzB,EAAQ,CAAA,EAMd,OALA/V,EAAO9E,KAAKqB,WAAWmE,KAAKqV,OAAO0B,SAASza,IACnCwa,EAAKxa,KACN+Y,EAAM/Y,GAAO0D,KAAKqV,MAAM/Y,GAC3B,IAEE,IAAI2M,EAAU,IACdjJ,KAAKwM,KACR6I,MAAO,IAAMA,GAEpB,CACD4B,cACI,OAAO9B,EAAenV,KACzB,CACDkX,QAAQJ,GACJ,MAAM1B,EAAW,CAAA,EAUjB,OATA9V,EAAO9E,KAAKqB,WAAWmE,KAAKqV,OAAO0B,SAASza,IACxC,MAAMgZ,EAActV,KAAKqV,MAAM/Y,GAC3Bwa,IAASA,EAAKxa,GACd8Y,EAAS9Y,GAAOgZ,EAGhBF,EAAS9Y,GAAOgZ,EAAYhL,UAC/B,IAEE,IAAIrB,EAAU,IACdjJ,KAAKwM,KACR6I,MAAO,IAAMD,GAEpB,CACD+B,SAASL,GACL,MAAM1B,EAAW,CAAA,EAcjB,OAbA9V,EAAO9E,KAAKqB,WAAWmE,KAAKqV,OAAO0B,SAASza,IACxC,GAAIwa,IAASA,EAAKxa,GACd8Y,EAAS9Y,GAAO0D,KAAKqV,MAAM/Y,OAE1B,CAED,IAAI8a,EADgBpX,KAAKqV,MAAM/Y,GAE/B,KAAO8a,aAAoBxI,IACvBwI,EAAWA,EAAS5K,KAAKwC,UAE7BoG,EAAS9Y,GAAO8a,CACnB,KAEE,IAAInO,EAAU,IACdjJ,KAAKwM,KACR6I,MAAO,IAAMD,GAEpB,CACDiC,QACI,OAAOC,EAAchY,EAAO9E,KAAKqB,WAAWmE,KAAKqV,OACpD,EAEL1a,EAAAsO,UAAoBA,EACpBA,EAAUlH,OAAS,CAACsT,EAAO9P,IAChB,IAAI0D,EAAU,CACjBoM,MAAO,IAAMA,EACbY,YAAa,QACbD,SAAU5M,EAASrH,SACnB4M,SAAUjH,GAAsBuB,aAC7B8C,EAAoBxG,KAG/B0D,EAAUsO,aAAe,CAAClC,EAAO9P,IACtB,IAAI0D,EAAU,CACjBoM,MAAO,IAAMA,EACbY,YAAa,SACbD,SAAU5M,EAASrH,SACnB4M,SAAUjH,GAAsBuB,aAC7B8C,EAAoBxG,KAG/B0D,EAAUuO,WAAa,CAACnC,EAAO9P,IACpB,IAAI0D,EAAU,CACjBoM,QACAY,YAAa,QACbD,SAAU5M,EAASrH,SACnB4M,SAAUjH,GAAsBuB,aAC7B8C,EAAoBxG,KAG/B,MAAMyD,UAAiBa,EACnBkE,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACnC/K,EAAU3C,KAAKwM,KAAK7J,QAoB1B,GAAIsD,EAAIC,OAAOiI,MACX,OAAO/G,QAAQ4N,IAAIrS,EAAQzG,KAAIiS,MAAOU,IAClC,MAAM4I,EAAW,IACVxR,EACHC,OAAQ,IACDD,EAAIC,OACPpG,OAAQ,IAEZyL,OAAQ,MAEZ,MAAO,CACHK,aAAciD,EAAOb,YAAY,CAC7BnQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQkM,IAEZxR,IAAKwR,EACR,KACD/Y,MArCR,SAAuBgI,GACnB,IAAK,MAAMkF,KAAUlF,EACjB,GAA6B,UAAzBkF,EAAOA,OAAOnF,OACd,OAAOmF,EAAOA,OAGtB,IAAK,MAAMA,KAAUlF,EACjB,GAA6B,UAAzBkF,EAAOA,OAAOnF,OAEd,OADAR,EAAIC,OAAOpG,OAAOpD,QAAQkP,EAAO3F,IAAIC,OAAOpG,QACrC8L,EAAOA,OAGtB,MAAMxK,EAAcsF,EAAQxK,KAAK0P,GAAW,IAAIzM,EAAWC,SAASwM,EAAO3F,IAAIC,OAAOpG,UAKtF,OAJA,EAAIoL,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAagD,cAC9BrB,gBAEG8J,EAAYjG,OACtB,IAqBI,CACD,IAAIqB,EACJ,MAAMxG,EAAS,GACf,IAAK,MAAM+O,KAAUlM,EAAS,CAC1B,MAAM8U,EAAW,IACVxR,EACHC,OAAQ,IACDD,EAAIC,OACPpG,OAAQ,IAEZyL,OAAQ,MAENK,EAASiD,EAAOf,WAAW,CAC7BjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQkM,IAEZ,GAAsB,UAAlB7L,EAAOnF,OACP,OAAOmF,EAEgB,UAAlBA,EAAOnF,QAAuBH,IACnCA,EAAQ,CAAEsF,SAAQ3F,IAAKwR,IAEvBA,EAASvR,OAAOpG,OAAO0B,QACvB1B,EAAOpD,KAAK+a,EAASvR,OAAOpG,OAEnC,CACD,GAAIwG,EAEA,OADAL,EAAIC,OAAOpG,OAAOpD,QAAQ4J,EAAML,IAAIC,OAAOpG,QACpCwG,EAAMsF,OAEjB,MAAMxK,EAActB,EAAO5D,KAAK4D,GAAW,IAAIX,EAAWC,SAASU,KAKnE,OAJA,EAAIoL,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAagD,cAC9BrB,gBAEG8J,EAAYjG,OACtB,CACJ,CACGtC,cACA,OAAO3C,KAAKwM,KAAK7J,OACpB,EAELhI,EAAAqO,SAAmBA,EACnBA,EAASjH,OAAS,CAAC2V,EAAOnS,IACf,IAAIyD,EAAS,CAChBrG,QAAS+U,EACT/I,SAAUjH,GAAsBsB,YAC7B+C,EAAoBxG,KAG/B,MAAMoS,EAAoBpU,GAClBA,aAAgBqU,EACTD,EAAiBpU,EAAKmL,QAExBnL,aAAgB8E,GACdsP,EAAiBpU,EAAKyL,aAExBzL,aAAgBkF,EACd,CAAClF,EAAK3I,OAER2I,aAAgBiF,EACdjF,EAAKZ,QAEPY,aAAgBgF,EACd9N,OAAO2B,KAAKmH,EAAKwH,MAEnBxH,aAAgB2E,GACdyP,EAAiBpU,EAAKiJ,KAAKwC,WAE7BzL,aAAgBgG,EACd,MAACzL,GAEHyF,aAAgB+F,EACd,CAAC,MAGD,KAGf,MAAMuO,UAA8BhO,EAChCkE,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACzC,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAcuB,OAMxC,OALA,EAAI6O,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcuB,OAC/BgG,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,MAAM6S,EAAgB9X,KAAK8X,cACrBC,EAAqB9R,EAAIpI,KAAKia,GAC9BjJ,EAAS7O,KAAKgY,WAAWC,IAAIF,GACnC,OAAKlJ,EAQD5I,EAAIC,OAAOiI,MACJU,EAAOb,YAAY,CACtBnQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAIL4I,EAAOf,WAAW,CACrBjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,MAlBZ,EAAIiF,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAaiD,4BAC9BC,QAASpE,MAAM2Z,KAAKlY,KAAKgY,WAAW5b,QACpCmF,KAAM,CAACuW,KAEJ5M,EAAYjG,QAgB1B,CACG6S,oBACA,OAAO9X,KAAKwM,KAAKsL,aACpB,CACGnV,cACA,OAAO3C,KAAKwM,KAAK7J,OACpB,CACGqV,iBACA,OAAOhY,KAAKwM,KAAKwL,UACpB,CACDxR,cAAcsR,EAAenV,EAAS4C,GAClC,MAAMyS,EAAa,IAAInZ,IACvB,IAAK,MAAM0E,KAAQZ,EAAS,CACxB,MAAMwV,EAAsBR,EAAiBpU,EAAK8R,MAAMyC,IACxD,IAAKK,EACD,MAAM,IAAI7c,MAAM,mCAAmCwc,sDAEvD,IAAK,MAAMld,KAASud,EAAqB,CACrC,GAAIH,EAAWI,IAAIxd,GACf,MAAM,IAAIU,MAAM,0BAA0BoV,OAAOoH,0BAAsCpH,OAAO9V,MAElGod,EAAWjZ,IAAInE,EAAO2I,EACzB,CACJ,CACD,OAAO,IAAIsU,EAAsB,CAC7BlJ,SAAUjH,GAAsBmQ,sBAChCC,gBACAnV,UACAqV,gBACGjM,EAAoBxG,IAE9B,EAGL,SAAS8S,EAAYC,EAAGC,GACpB,MAAMC,GAAQ,EAAIlZ,EAAOzE,eAAeyd,GAClCG,GAAQ,EAAInZ,EAAOzE,eAAe0d,GACxC,GAAID,IAAMC,EACN,MAAO,CAAEG,OAAO,EAAM7a,KAAMya,GAE3B,GAAIE,IAAUlZ,EAAOxE,cAAcuB,QAAUoc,IAAUnZ,EAAOxE,cAAcuB,OAAQ,CACrF,MAAMsc,EAAQrZ,EAAO9E,KAAKqB,WAAW0c,GAC/BK,EAAatZ,EAAO9E,KACrBqB,WAAWyc,GACXxc,QAAQQ,IAAgC,IAAxBqc,EAAME,QAAQvc,KAC7Bwc,EAAS,IAAKR,KAAMC,GAC1B,IAAK,MAAMjc,KAAOsc,EAAY,CAC1B,MAAMG,EAAcV,EAAYC,EAAEhc,GAAMic,EAAEjc,IAC1C,IAAKyc,EAAYL,MACb,MAAO,CAAEA,OAAO,GAEpBI,EAAOxc,GAAOyc,EAAYlb,IAC7B,CACD,MAAO,CAAE6a,OAAO,EAAM7a,KAAMib,EAC/B,CACI,GAAIN,IAAUlZ,EAAOxE,cAAcsC,OAASqb,IAAUnZ,EAAOxE,cAAcsC,MAAO,CACnF,GAAIkb,EAAE9W,SAAW+W,EAAE/W,OACf,MAAO,CAAEkX,OAAO,GAEpB,MAAMM,EAAW,GACjB,IAAK,IAAIpC,EAAQ,EAAGA,EAAQ0B,EAAE9W,OAAQoV,IAAS,CAC3C,MAEMmC,EAAcV,EAFNC,EAAE1B,GACF2B,EAAE3B,IAEhB,IAAKmC,EAAYL,MACb,MAAO,CAAEA,OAAO,GAEpBM,EAAStc,KAAKqc,EAAYlb,KAC7B,CACD,MAAO,CAAE6a,OAAO,EAAM7a,KAAMmb,EAC/B,CACI,OAAIR,IAAUlZ,EAAOxE,cAAcmE,MACpCwZ,IAAUnZ,EAAOxE,cAAcmE,OAC9BqZ,IAAOC,EACD,CAAEG,OAAO,EAAM7a,KAAMya,GAGrB,CAAEI,OAAO,EAEvB,CA9CD/d,EAAAkd,sBAAgCA,EA+ChC,MAAM9O,UAAwBc,EAC1BkE,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GAC3CuL,EAAe,CAACC,EAAYC,KAC9B,IAAI,EAAIjO,EAAYpG,WAAWoU,KAAe,EAAIhO,EAAYpG,WAAWqU,GACrE,OAAOjO,EAAYjG,QAEvB,MAAMmU,EAASf,EAAYa,EAAWte,MAAOue,EAAYve,OACzD,OAAKwe,EAAOV,SAMR,EAAIxN,EAAYrG,SAASqU,KAAe,EAAIhO,EAAYrG,SAASsU,KACjE1S,EAAOH,QAEJ,CAAEG,OAAQA,EAAO7L,MAAOA,MAAOwe,EAAOvb,SARzC,EAAIqN,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAaqE,6BAE3BoH,EAAYjG,QAK4B,EAEvD,OAAIgB,EAAIC,OAAOiI,MACJ/G,QAAQ4N,IAAI,CACfhV,KAAKwM,KAAK6M,KAAKrL,YAAY,CACvBnQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAEZjG,KAAKwM,KAAK8M,MAAMtL,YAAY,CACxBnQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,MAEbvH,MAAK,EAAE2a,EAAMC,KAAWL,EAAaI,EAAMC,KAGvCL,EAAajZ,KAAKwM,KAAK6M,KAAKvL,WAAW,CAC1CjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IACRjG,KAAKwM,KAAK8M,MAAMxL,WAAW,CAC3BjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAGnB,EAELtL,EAAAoO,gBAA0BA,EAC1BA,EAAgBhH,OAAS,CAACsX,EAAMC,EAAO/T,IAC5B,IAAIwD,EAAgB,CACvBsQ,KAAMA,EACNC,MAAOA,EACP3K,SAAUjH,GAAsBqB,mBAC7BgD,EAAoBxG,KAG/B,MAAMuD,UAAiBe,EACnBkE,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACjD,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAcsC,MAMxC,OALA,EAAI8N,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcsC,MAC/BiF,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,GAAIgB,EAAIpI,KAAK2D,OAASxB,KAAKwM,KAAKhR,MAAMgG,OAQlC,OAPA,EAAI0J,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS1D,KAAKwM,KAAKhR,MAAMgG,OACzBiC,WAAW,EACXD,OAAO,EACPD,KAAM,UAEH2H,EAAYjG,SAEVjF,KAAKwM,KAAK+M,MACVtT,EAAIpI,KAAK2D,OAASxB,KAAKwM,KAAKhR,MAAMgG,UAC3C,EAAI0J,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAAS5D,KAAKwM,KAAKhR,MAAMgG,OACzBiC,WAAW,EACXD,OAAO,EACPD,KAAM,UAEVkD,EAAOH,SAEX,MAAM9K,EAAQ,IAAIyK,EAAIpI,MACjB3B,KAAI,CAACR,EAAM8d,KACZ,MAAM9K,EAAS1O,KAAKwM,KAAKhR,MAAMge,IAAcxZ,KAAKwM,KAAK+M,KACvD,OAAK7K,EAEEA,EAAOX,OAAO,IAAIzC,EAAmBrF,EAAKvK,EAAMuK,EAAI1E,KAAMiY,IADtD,IACiE,IAE3E1d,QAAQuK,KAAQA,IACrB,OAAIJ,EAAIC,OAAOiI,MACJ/G,QAAQ4N,IAAIxZ,GAAOkD,MAAMgI,GACrBwE,EAAYhG,YAAY+P,WAAWxO,EAAQC,KAI/CwE,EAAYhG,YAAY+P,WAAWxO,EAAQjL,EAEzD,CACGA,YACA,OAAOwE,KAAKwM,KAAKhR,KACpB,CACD+d,KAAKA,GACD,OAAO,IAAIzQ,EAAS,IACb9I,KAAKwM,KACR+M,QAEP,EAEL5e,EAAAmO,SAAmBA,EACnBA,EAAS/G,OAAS,CAAC0X,EAASlU,KACxB,IAAKhH,MAAMC,QAAQib,GACf,MAAM,IAAIne,MAAM,yDAEpB,OAAO,IAAIwN,EAAS,CAChBtN,MAAOie,EACP9K,SAAUjH,GAAsBoB,SAChCyQ,KAAM,QACHxN,EAAoBxG,IACzB,EAEN,MAAMsD,UAAkBgB,EAChB6P,gBACA,OAAO1Z,KAAKwM,KAAKmN,OACpB,CACGC,kBACA,OAAO5Z,KAAKwM,KAAKqN,SACpB,CACD9L,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACjD,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAcuB,OAMxC,OALA,EAAI6O,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcuB,OAC/BgG,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,MAAM4B,EAAQ,GACR8S,EAAU3Z,KAAKwM,KAAKmN,QACpBE,EAAY7Z,KAAKwM,KAAKqN,UAC5B,IAAK,MAAMvd,KAAO2J,EAAIpI,KAClBgJ,EAAMnK,KAAK,CACPJ,IAAKqd,EAAQ5L,OAAO,IAAIzC,EAAmBrF,EAAK3J,EAAK2J,EAAI1E,KAAMjF,IAC/D1B,MAAOif,EAAU9L,OAAO,IAAIzC,EAAmBrF,EAAKA,EAAIpI,KAAKvB,GAAM2J,EAAI1E,KAAMjF,MAGrF,OAAI2J,EAAIC,OAAOiI,MACJjD,EAAYhG,YAAY4U,iBAAiBrT,EAAQI,GAGjDqE,EAAYhG,YAAY8B,gBAAgBP,EAAQI,EAE9D,CACGqO,cACA,OAAOlV,KAAKwM,KAAKqN,SACpB,CACDrT,cAAc7I,EAAOC,EAAQmc,GACzB,OACW,IAAIlR,EADXjL,aAAkBiM,EACG,CACjB8P,QAAShc,EACTkc,UAAWjc,EACX+Q,SAAUjH,GAAsBmB,aAC7BkD,EAAoBgO,IAGV,CACjBJ,QAAS/P,EAAU7H,SACnB8X,UAAWlc,EACXgR,SAAUjH,GAAsBmB,aAC7BkD,EAAoBnO,IAE9B,EAELjD,EAAAkO,UAAoBA,EACpB,MAAMD,UAAeiB,EACjBkE,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACjD,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAcoB,IAMxC,OALA,EAAIgP,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcoB,IAC/BmG,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,MAAM0U,EAAU3Z,KAAKwM,KAAKmN,QACpBE,EAAY7Z,KAAKwM,KAAKqN,UACtBhT,EAAQ,IAAIZ,EAAIpI,KAAKmc,WAAW9d,KAAI,EAAEI,EAAK1B,GAAQgc,KAC9C,CACHta,IAAKqd,EAAQ5L,OAAO,IAAIzC,EAAmBrF,EAAK3J,EAAK2J,EAAI1E,KAAM,CAACqV,EAAO,SACvEhc,MAAOif,EAAU9L,OAAO,IAAIzC,EAAmBrF,EAAKrL,EAAOqL,EAAI1E,KAAM,CAACqV,EAAO,eAGrF,GAAI3Q,EAAIC,OAAOiI,MAAO,CAClB,MAAM8L,EAAW,IAAIpb,IACrB,OAAOuI,QAAQ6G,UAAUvP,MAAKyP,UAC1B,IAAK,MAAMpH,KAAQF,EAAO,CACtB,MAAMvK,QAAYyK,EAAKzK,IACjB1B,QAAcmM,EAAKnM,MACzB,GAAmB,YAAf0B,EAAImK,QAAyC,YAAjB7L,EAAM6L,OAClC,OAAOyE,EAAYjG,QAEJ,UAAf3I,EAAImK,QAAuC,UAAjB7L,EAAM6L,QAChCA,EAAOH,QAEX2T,EAASlb,IAAIzC,EAAI1B,MAAOA,EAAMA,MACjC,CACD,MAAO,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAOqf,EAAU,GAEvD,CACI,CACD,MAAMA,EAAW,IAAIpb,IACrB,IAAK,MAAMkI,KAAQF,EAAO,CACtB,MAAMvK,EAAMyK,EAAKzK,IACX1B,EAAQmM,EAAKnM,MACnB,GAAmB,YAAf0B,EAAImK,QAAyC,YAAjB7L,EAAM6L,OAClC,OAAOyE,EAAYjG,QAEJ,UAAf3I,EAAImK,QAAuC,UAAjB7L,EAAM6L,QAChCA,EAAOH,QAEX2T,EAASlb,IAAIzC,EAAI1B,MAAOA,EAAMA,MACjC,CACD,MAAO,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAOqf,EACzC,CACJ,EAELtf,EAAAiO,OAAiBA,EACjBA,EAAO7G,OAAS,CAAC4X,EAASE,EAAWtU,IAC1B,IAAIqD,EAAO,CACdiR,YACAF,UACAhL,SAAUjH,GAAsBkB,UAC7BmD,EAAoBxG,KAG/B,MAAMoD,UAAekB,EACjBkE,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACjD,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAciE,IAMxC,OALA,EAAImM,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAciE,IAC/BsD,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,MAAMoH,EAAMrM,KAAKwM,KACG,OAAhBH,EAAI6N,SACAjU,EAAIpI,KAAKsc,KAAO9N,EAAI6N,QAAQtf,SAC5B,EAAIsQ,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa6D,UAC9BI,QAAS2I,EAAI6N,QAAQtf,MACrB2I,KAAM,MACNE,WAAW,EACXD,OAAO,EACP1C,QAASuL,EAAI6N,QAAQpZ,UAEzB2F,EAAOH,SAGK,OAAhB+F,EAAI+N,SACAnU,EAAIpI,KAAKsc,KAAO9N,EAAI+N,QAAQxf,SAC5B,EAAIsQ,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAakE,QAC9BC,QAASyI,EAAI+N,QAAQxf,MACrB2I,KAAM,MACNE,WAAW,EACXD,OAAO,EACP1C,QAASuL,EAAI+N,QAAQtZ,UAEzB2F,EAAOH,SAGf,MAAMuT,EAAY7Z,KAAKwM,KAAKqN,UAC5B,SAASQ,EAAYC,GACjB,MAAMC,EAAY,IAAIzb,IACtB,IAAK,MAAMoW,KAAWoF,EAAU,CAC5B,GAAuB,YAAnBpF,EAAQzO,OACR,OAAOyE,EAAYjG,QACA,UAAnBiQ,EAAQzO,QACRA,EAAOH,QACXiU,EAAUC,IAAItF,EAAQta,MACzB,CACD,MAAO,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAO2f,EACzC,CACD,MAAMD,EAAW,IAAIrU,EAAIpI,KAAK4c,UAAUve,KAAI,CAACR,EAAMgG,IAAMmY,EAAU9L,OAAO,IAAIzC,EAAmBrF,EAAKvK,EAAMuK,EAAI1E,KAAMG,MACtH,OAAIuE,EAAIC,OAAOiI,MACJ/G,QAAQ4N,IAAIsF,GAAU5b,MAAM4b,GAAaD,EAAYC,KAGrDD,EAAYC,EAE1B,CACDlK,IAAI8J,EAASpZ,GACT,OAAO,IAAI6H,EAAO,IACX3I,KAAKwM,KACR0N,QAAS,CAAEtf,MAAOsf,EAASpZ,QAASmK,EAAY3D,UAAU7J,SAASqD,KAE1E,CACDiR,IAAIqI,EAAStZ,GACT,OAAO,IAAI6H,EAAO,IACX3I,KAAKwM,KACR4N,QAAS,CAAExf,MAAOwf,EAAStZ,QAASmK,EAAY3D,UAAU7J,SAASqD,KAE1E,CACDqZ,KAAKA,EAAMrZ,GACP,OAAOd,KAAKoQ,IAAI+J,EAAMrZ,GAASiR,IAAIoI,EAAMrZ,EAC5C,CACDqP,SAASrP,GACL,OAAOd,KAAKoQ,IAAI,EAAGtP,EACtB,EAELnG,EAAAgO,OAAiBA,EACjBA,EAAO5G,OAAS,CAAC8X,EAAWtU,IACjB,IAAIoD,EAAO,CACdkR,YACAK,QAAS,KACTE,QAAS,KACTzL,SAAUjH,GAAsBiB,UAC7BoD,EAAoBxG,KAG/B,MAAMmD,UAAoBmB,EACtBhK,cACIE,SAASgQ,WACT/P,KAAK0a,SAAW1a,KAAK2a,SACxB,CACD5M,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACzC,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAcsD,SAMxC,OALA,EAAI8M,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcsD,SAC/BiE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,SAAS2V,EAAc7J,EAAM7P,GACzB,OAAO,EAAIgK,EAAY7F,WAAW,CAC9BxH,KAAMkT,EACNxP,KAAM0E,EAAI1E,KACViE,UAAW,CACPS,EAAIC,OAAOC,mBACXF,EAAIG,gBACJ,EAAId,EAAShB,eACbgB,EAASb,iBACX3I,QAAQuK,KAAQA,IAClBZ,UAAW,CACPtE,KAAMhC,EAAWM,aAAaoD,kBAC9BvB,eAAgBJ,IAG3B,CACD,SAAS2Z,EAAiBC,EAAS5Z,GAC/B,OAAO,EAAIgK,EAAY7F,WAAW,CAC9BxH,KAAMid,EACNvZ,KAAM0E,EAAI1E,KACViE,UAAW,CACPS,EAAIC,OAAOC,mBACXF,EAAIG,gBACJ,EAAId,EAAShB,eACbgB,EAASb,iBACX3I,QAAQuK,KAAQA,IAClBZ,UAAW,CACPtE,KAAMhC,EAAWM,aAAaqD,oBAC9BzB,gBAAiBH,IAG5B,CACD,MAAMqE,EAAS,CAAEyG,SAAU/F,EAAIC,OAAOC,oBAChC4U,EAAK9U,EAAIpI,KACf,OAAImC,KAAKwM,KAAKsO,mBAAmBxS,IACtB,EAAI4C,EAAYnG,KAAIoJ,SAAU4C,KACjC,MAAM7P,EAAQ,IAAI/B,EAAWC,SAAS,IAChC4b,QAAmBhb,KAAKwM,KAAKuE,KAC9BnE,WAAWmE,EAAMxL,GACjB5G,OAAOxC,IAER,MADA+E,EAAMjB,SAAS2a,EAAc7J,EAAM5U,IAC7B+E,CAAK,IAET0K,QAAemP,KAAMC,GAO3B,aAN4Bhb,KAAKwM,KAAKsO,QAAQtO,KAAKjJ,KAC9CqJ,WAAWhB,EAAQrG,GACnB5G,OAAOxC,IAER,MADA+E,EAAMjB,SAAS4a,EAAiBjP,EAAQzP,IAClC+E,CAAK,GAEK,KAIjB,EAAIgK,EAAYnG,KAAI,IAAIgM,KAC3B,MAAMiK,EAAahb,KAAKwM,KAAKuE,KAAKpE,UAAUoE,EAAMxL,GAClD,IAAKyV,EAAWnP,QACZ,MAAM,IAAI1M,EAAWC,SAAS,CAACwb,EAAc7J,EAAMiK,EAAW9Z,SAElE,MAAM0K,EAASmP,KAAMC,EAAWnd,MAC1Bod,EAAgBjb,KAAKwM,KAAKsO,QAAQnO,UAAUf,EAAQrG,GAC1D,IAAK0V,EAAcpP,QACf,MAAM,IAAI1M,EAAWC,SAAS,CAACyb,EAAiBjP,EAAQqP,EAAc/Z,SAE1E,OAAO+Z,EAAcpd,IAAI,GAGpC,CACDqd,aACI,OAAOlb,KAAKwM,KAAKuE,IACpB,CACDoK,aACI,OAAOnb,KAAKwM,KAAKsO,OACpB,CACD/J,QAAQvV,GACJ,OAAO,IAAIkN,EAAY,IAChB1I,KAAKwM,KACRuE,KAAMjI,EAAS/G,OAAOvG,GAAO+d,KAAKlQ,EAAWtH,WAEpD,CACD+Y,QAAQK,GACJ,OAAO,IAAIzS,EAAY,IAChB1I,KAAKwM,KACRsO,QAASK,GAEhB,CACDR,UAAUS,GAEN,OADsBpb,KAAKyM,MAAM2O,EAEpC,CACDC,gBAAgBD,GAEZ,OADsBpb,KAAKyM,MAAM2O,EAEpC,CACD5U,cAAcuK,EAAM+J,EAASvV,GACzB,OAAO,IAAImD,EAAY,CACnBqI,KAAOA,GAEDjI,EAAS/G,OAAO,IAAIwX,KAAKlQ,EAAWtH,UAC1C+Y,QAASA,GAAWzR,EAAWtH,SAC/B4M,SAAUjH,GAAsBgB,eAC7BqD,EAAoBxG,IAE9B,EAEL5K,EAAA+N,YAAsBA,EACtB,MAAMkP,UAAgB/N,EACd6E,aACA,OAAO1O,KAAKwM,KAAK8O,QACpB,CACDvN,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GAEzC,OADmB1N,KAAKwM,KAAK8O,SACXvN,OAAO,CAAElQ,KAAMoI,EAAIpI,KAAM0D,KAAM0E,EAAI1E,KAAMgK,OAAQtF,GACtE,EAELtL,EAAAid,QAAkBA,EAClBA,EAAQ7V,OAAS,CAACuZ,EAAQ/V,IACf,IAAIqS,EAAQ,CACf0D,OAAQA,EACR3M,SAAUjH,GAAsBkQ,WAC7B7L,EAAoBxG,KAG/B,MAAMkD,UAAmBoB,EACrBkE,OAAOL,GACH,GAAIA,EAAM7P,OAASmC,KAAKwM,KAAK5R,MAAO,CAChC,MAAMqL,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC5D,SAAU4D,EAAIpI,KACdsD,KAAMhC,EAAWM,aAAa8C,gBAC9BD,SAAUtC,KAAKwM,KAAK5R,QAEjBsQ,EAAYjG,OACtB,CACD,MAAO,CAAEwB,OAAQ,QAAS7L,MAAO8S,EAAM7P,KAC1C,CACGjD,YACA,OAAOoF,KAAKwM,KAAK5R,KACpB,EAUL,SAAS0c,EAAcmD,EAAQlV,GAC3B,OAAO,IAAIiD,EAAQ,CACfiS,OAAQA,EACR9L,SAAUjH,GAAsBc,WAC7BuD,EAAoBxG,IAE9B,CAdD5K,EAAA8N,WAAqBA,EACrBA,EAAW1G,OAAS,CAACnH,EAAO2K,IACjB,IAAIkD,EAAW,CAClB7N,MAAOA,EACP+T,SAAUjH,GAAsBe,cAC7BsD,EAAoBxG,KAU/B,MAAMiD,UAAgBqB,EAClBkE,OAAOL,GACH,GAA0B,iBAAfA,EAAM7P,KAAmB,CAChC,MAAMoI,EAAMjG,KAAK2N,gBAAgBD,GAC3B6N,EAAiBvb,KAAKwM,KAAKiO,OAMjC,OALA,EAAIvP,EAAY/F,mBAAmBc,EAAK,CACpC3D,SAAUhD,EAAO9E,KAAK2C,WAAWoe,GACjClZ,SAAU4D,EAAI2H,WACdzM,KAAMhC,EAAWM,aAAa2C,eAE3B8I,EAAYjG,OACtB,CACD,IAA8C,IAA1CjF,KAAKwM,KAAKiO,OAAO5B,QAAQnL,EAAM7P,MAAc,CAC7C,MAAMoI,EAAMjG,KAAK2N,gBAAgBD,GAC3B6N,EAAiBvb,KAAKwM,KAAKiO,OAMjC,OALA,EAAIvP,EAAY/F,mBAAmBc,EAAK,CACpC5D,SAAU4D,EAAIpI,KACdsD,KAAMhC,EAAWM,aAAamD,mBAC9BD,QAAS4Y,IAENrQ,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,CACG8E,cACA,OAAO3C,KAAKwM,KAAKiO,MACpB,CACG1P,WACA,MAAMyQ,EAAa,CAAA,EACnB,IAAK,MAAMvgB,KAAO+E,KAAKwM,KAAKiO,OACxBe,EAAWvgB,GAAOA,EAEtB,OAAOugB,CACV,CACGC,aACA,MAAMD,EAAa,CAAA,EACnB,IAAK,MAAMvgB,KAAO+E,KAAKwM,KAAKiO,OACxBe,EAAWvgB,GAAOA,EAEtB,OAAOugB,CACV,CACGE,WACA,MAAMF,EAAa,CAAA,EACnB,IAAK,MAAMvgB,KAAO+E,KAAKwM,KAAKiO,OACxBe,EAAWvgB,GAAOA,EAEtB,OAAOugB,CACV,CACDG,QAAQlB,GACJ,OAAOjS,EAAQzG,OAAO0Y,EACzB,CACDmB,QAAQnB,GACJ,OAAOjS,EAAQzG,OAAO/B,KAAK2C,QAAQ7G,QAAQ+f,IAASpB,EAAOvX,SAAS2Y,KACvE,EAELlhB,EAAA6N,QAAkBA,EAClBA,EAAQzG,OAASuV,EACjB,MAAM/O,UAAsBsB,EACxBkE,OAAOL,GACH,MAAMoO,EAAmBxc,EAAO9E,KAAKmB,mBAAmBqE,KAAKwM,KAAKiO,QAC5DxU,EAAMjG,KAAK2N,gBAAgBD,GACjC,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAciD,QACxCkI,EAAI2H,aAAetO,EAAOxE,cAAcoD,OAAQ,CAChD,MAAMqd,EAAiBjc,EAAO9E,KAAKyB,aAAa6f,GAMhD,OALA,EAAI5Q,EAAY/F,mBAAmBc,EAAK,CACpC3D,SAAUhD,EAAO9E,KAAK2C,WAAWoe,GACjClZ,SAAU4D,EAAI2H,WACdzM,KAAMhC,EAAWM,aAAa2C,eAE3B8I,EAAYjG,OACtB,CACD,IAA8C,IAA1C6W,EAAiBjD,QAAQnL,EAAM7P,MAAc,CAC7C,MAAM0d,EAAiBjc,EAAO9E,KAAKyB,aAAa6f,GAMhD,OALA,EAAI5Q,EAAY/F,mBAAmBc,EAAK,CACpC5D,SAAU4D,EAAIpI,KACdsD,KAAMhC,EAAWM,aAAamD,mBAC9BD,QAAS4Y,IAENrQ,EAAYjG,OACtB,CACD,OAAO,EAAIiG,EAAYnG,IAAI2I,EAAM7P,KACpC,CACGkN,WACA,OAAO/K,KAAKwM,KAAKiO,MACpB,EAEL9f,EAAA4N,cAAwBA,EACxBA,EAAcxG,OAAS,CAAC0Y,EAAQlV,IACrB,IAAIgD,EAAc,CACrBkS,OAAQA,EACR9L,SAAUjH,GAAsBa,iBAC7BwD,EAAoBxG,KAG/B,MAAM+C,WAAmBuB,EACrB0L,SACI,OAAOvV,KAAKwM,KAAKjJ,IACpB,CACDwK,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACzC,GAAIzH,EAAI2H,aAAetO,EAAOxE,cAAc8D,UACnB,IAArBqH,EAAIC,OAAOiI,MAMX,OALA,EAAIjD,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAc8D,QAC/ByD,SAAU4D,EAAI2H,aAEX1C,EAAYjG,QAEvB,MAAM8W,EAAc9V,EAAI2H,aAAetO,EAAOxE,cAAc8D,QACtDqH,EAAIpI,KACJuJ,QAAQ6G,QAAQhI,EAAIpI,MAC1B,OAAO,EAAIqN,EAAYnG,IAAIgX,EAAYrd,MAAMb,GAClCmC,KAAKwM,KAAKjJ,KAAKqJ,WAAW/O,EAAM,CACnC0D,KAAM0E,EAAI1E,KACVyK,SAAU/F,EAAIC,OAAOC,uBAGhC,EAELxL,EAAA2N,WAAqBA,GACrBA,GAAWvG,OAAS,CAAC2M,EAAQnJ,IAClB,IAAI+C,GAAW,CAClB/E,KAAMmL,EACNC,SAAUjH,GAAsBY,cAC7ByD,EAAoBxG,KAG/B,MAAM8C,WAAmBwB,EACrBmF,YACI,OAAOhP,KAAKwM,KAAKkC,MACpB,CACDsN,aACI,OAAOhc,KAAKwM,KAAKkC,OAAOlC,KAAKmC,WAAajH,GAAsBW,WAC1DrI,KAAKwM,KAAKkC,OAAOsN,aACjBhc,KAAKwM,KAAKkC,MACnB,CACDX,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GAC3C1C,EAAShL,KAAKwM,KAAKxB,QAAU,KACnC,GAAoB,eAAhBA,EAAOzH,KAAuB,CAC9B,MAAM0Y,EAAYjR,EAAOmC,UAAUlH,EAAIpI,MACvC,OAAIoI,EAAIC,OAAOiI,MACJ/G,QAAQ6G,QAAQgO,GAAWvd,MAAMud,GAC7Bjc,KAAKwM,KAAKkC,OAAOV,YAAY,CAChCnQ,KAAMoe,EACN1a,KAAM0E,EAAI1E,KACVgK,OAAQtF,MAKTjG,KAAKwM,KAAKkC,OAAOZ,WAAW,CAC/BjQ,KAAMoe,EACN1a,KAAM0E,EAAI1E,KACVgK,OAAQtF,GAGnB,CACD,MAAMiW,EAAW,CACbjc,SAAWkc,KACP,EAAIjR,EAAY/F,mBAAmBc,EAAKkW,GACpCA,EAAIC,MACJ3V,EAAOF,QAGPE,EAAOH,OACV,EAED/E,WACA,OAAO0E,EAAI1E,IACd,GAGL,GADA2a,EAASjc,SAAWic,EAASjc,SAASyM,KAAKwP,GACvB,eAAhBlR,EAAOzH,KAAuB,CAC9B,MAAM8Y,EAAqBC,IACvB,MAAM1Q,EAASZ,EAAO8B,WAAWwP,EAAKJ,GACtC,GAAIjW,EAAIC,OAAOiI,MACX,OAAO/G,QAAQ6G,QAAQrC,GAE3B,GAAIA,aAAkBxE,QAClB,MAAM,IAAI9L,MAAM,6FAEpB,OAAOghB,CAAG,EAEd,IAAyB,IAArBrW,EAAIC,OAAOiI,MAAiB,CAC5B,MAAMoO,EAAQvc,KAAKwM,KAAKkC,OAAOZ,WAAW,CACtCjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAEZ,MAAqB,YAAjBsW,EAAM9V,OACCyE,EAAYjG,SACF,UAAjBsX,EAAM9V,QACNA,EAAOH,QACX+V,EAAkBE,EAAM3hB,OACjB,CAAE6L,OAAQA,EAAO7L,MAAOA,MAAO2hB,EAAM3hB,OAC/C,CAEG,OAAOoF,KAAKwM,KAAKkC,OACZV,YAAY,CAAEnQ,KAAMoI,EAAIpI,KAAM0D,KAAM0E,EAAI1E,KAAMgK,OAAQtF,IACtDvH,MAAM6d,GACc,YAAjBA,EAAM9V,OACCyE,EAAYjG,SACF,UAAjBsX,EAAM9V,QACNA,EAAOH,QACJ+V,EAAkBE,EAAM3hB,OAAO8D,MAAK,KAChC,CAAE+H,OAAQA,EAAO7L,MAAOA,MAAO2hB,EAAM3hB,YAI3D,CACD,GAAoB,cAAhBoQ,EAAOzH,KAAsB,CAC7B,IAAyB,IAArB0C,EAAIC,OAAOiI,MAAiB,CAC5B,MAAMqO,EAAOxc,KAAKwM,KAAKkC,OAAOZ,WAAW,CACrCjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAEZ,KAAK,EAAIiF,EAAYtG,SAAS4X,GAC1B,OAAOA,EACX,MAAM5Q,EAASZ,EAAOmC,UAAUqP,EAAK5hB,MAAOshB,GAC5C,GAAItQ,aAAkBxE,QAClB,MAAM,IAAI9L,MAAM,mGAEpB,MAAO,CAAEmL,OAAQA,EAAO7L,MAAOA,MAAOgR,EACzC,CAEG,OAAO5L,KAAKwM,KAAKkC,OACZV,YAAY,CAAEnQ,KAAMoI,EAAIpI,KAAM0D,KAAM0E,EAAI1E,KAAMgK,OAAQtF,IACtDvH,MAAM8d,IACF,EAAItR,EAAYtG,SAAS4X,GAEvBpV,QAAQ6G,QAAQjD,EAAOmC,UAAUqP,EAAK5hB,MAAOshB,IAAWxd,MAAMkN,IAAM,CAAQnF,OAAQA,EAAO7L,MAAOA,MAAOgR,MADrG4Q,GAItB,CACDld,EAAO9E,KAAKY,YAAY4P,EAC3B,EAELrQ,EAAA0N,WAAqBA,GACrB1N,EAAAyN,eAAyBC,GACzBA,GAAWtG,OAAS,CAAC2M,EAAQ1D,EAAQzF,IAC1B,IAAI8C,GAAW,CAClBqG,SACAC,SAAUjH,GAAsBW,WAChC2C,YACGe,EAAoBxG,KAG/B8C,GAAWoU,qBAAuB,CAACrS,EAAYsE,EAAQnJ,IAC5C,IAAI8C,GAAW,CAClBqG,SACA1D,OAAQ,CAAEzH,KAAM,aAAc4J,UAAW/C,GACzCuE,SAAUjH,GAAsBW,cAC7B0D,EAAoBxG,KAG/B,MAAMqJ,WAAoB/E,EACtBkE,OAAOL,GAEH,OADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcgD,WAC7B,EAAIoN,EAAYnG,SAAIjH,GAExBkC,KAAKwM,KAAKwC,UAAUjB,OAAOL,EACrC,CACD6H,SACI,OAAOvV,KAAKwM,KAAKwC,SACpB,EAELrU,EAAAiU,YAAsBA,GACtBA,GAAY7M,OAAS,CAACwB,EAAMgC,IACjB,IAAIqJ,GAAY,CACnBI,UAAWzL,EACXoL,SAAUjH,GAAsBkH,eAC7B7C,EAAoBxG,KAG/B,MAAM4C,WAAoB0B,EACtBkE,OAAOL,GAEH,OADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAc2D,MAC7B,EAAIyM,EAAYnG,IAAI,MAExB/E,KAAKwM,KAAKwC,UAAUjB,OAAOL,EACrC,CACD6H,SACI,OAAOvV,KAAKwM,KAAKwC,SACpB,EAELrU,EAAAwN,YAAsBA,GACtBA,GAAYpG,OAAS,CAACwB,EAAMgC,IACjB,IAAI4C,GAAY,CACnB6G,UAAWzL,EACXoL,SAAUjH,GAAsBS,eAC7B4D,EAAoBxG,KAG/B,MAAM2C,WAAmB2B,EACrBkE,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACzC,IAAI7P,EAAOoI,EAAIpI,KAIf,OAHIoI,EAAI2H,aAAetO,EAAOxE,cAAcgD,YACxCD,EAAOmC,KAAKwM,KAAKyC,gBAEdjP,KAAKwM,KAAKwC,UAAUjB,OAAO,CAC9BlQ,OACA0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,GAEf,CACDyW,gBACI,OAAO1c,KAAKwM,KAAKwC,SACpB,EAELrU,EAAAuN,WAAqBA,GACrBA,GAAWnG,OAAS,CAACwB,EAAMgC,IAChB,IAAI2C,GAAW,CAClB8G,UAAWzL,EACXoL,SAAUjH,GAAsBQ,WAChC+G,aAAwC,mBAAnB1J,EAAOrD,QACtBqD,EAAOrD,QACP,IAAMqD,EAAOrD,WAChB6J,EAAoBxG,KAG/B,MAAM0C,WAAiB4B,EACnBkE,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACnCiP,EAAS,IACR1W,EACHC,OAAQ,IACDD,EAAIC,OACPpG,OAAQ,KAGV8L,EAAS5L,KAAKwM,KAAKwC,UAAUjB,OAAO,CACtClQ,KAAM8e,EAAO9e,KACb0D,KAAMob,EAAOpb,KACbgK,OAAQ,IACDoR,KAGX,OAAI,EAAIzR,EAAYvG,SAASiH,GAClBA,EAAOlN,MAAMkN,IACT,CACHnF,OAAQ,QACR7L,MAAyB,UAAlBgR,EAAOnF,OACRmF,EAAOhR,MACPoF,KAAKwM,KAAK2C,WAAW,CACfjO,YACA,OAAO,IAAI/B,EAAWC,SAASud,EAAOzW,OAAOpG,OAChD,EACD4N,MAAOiP,EAAO9e,WAMvB,CACH4I,OAAQ,QACR7L,MAAyB,UAAlBgR,EAAOnF,OACRmF,EAAOhR,MACPoF,KAAKwM,KAAK2C,WAAW,CACfjO,YACA,OAAO,IAAI/B,EAAWC,SAASud,EAAOzW,OAAOpG,OAChD,EACD4N,MAAOiP,EAAO9e,OAIjC,CACD+e,cACI,OAAO5c,KAAKwM,KAAKwC,SACpB,EAELrU,EAAAsN,SAAmBA,GACnBA,GAASlG,OAAS,CAACwB,EAAMgC,IACd,IAAI0C,GAAS,CAChB+G,UAAWzL,EACXoL,SAAUjH,GAAsBO,SAChCkH,WAAoC,mBAAjB5J,EAAO5G,MAAuB4G,EAAO5G,MAAQ,IAAM4G,EAAO5G,SAC1EoN,EAAoBxG,KAG/B,MAAMyC,WAAe6B,EACjBkE,OAAOL,GAEH,GADmB1N,KAAKyN,SAASC,KACdpO,EAAOxE,cAAcmD,IAAK,CACzC,MAAMgI,EAAMjG,KAAK2N,gBAAgBD,GAMjC,OALA,EAAIxC,EAAY/F,mBAAmBc,EAAK,CACpC9E,KAAMhC,EAAWM,aAAa2C,aAC9BE,SAAUhD,EAAOxE,cAAcmD,IAC/BoE,SAAU4D,EAAI2H,aAEX1C,EAAYjG,OACtB,CACD,MAAO,CAAEwB,OAAQ,QAAS7L,MAAO8S,EAAM7P,KAC1C,EAELlD,EAAAqN,OAAiBA,GACjBA,GAAOjG,OAAUwD,GACN,IAAIyC,GAAO,CACd2G,SAAUjH,GAAsBM,UAC7B+D,EAAoBxG,KAG/B5K,EAAAoN,MAAgB8U,OAAO,aACvB,MAAM/U,WAAmB+B,EACrBkE,OAAOL,GACH,MAAMzH,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACnC7P,EAAOoI,EAAIpI,KACjB,OAAOmC,KAAKwM,KAAKjJ,KAAKwK,OAAO,CACzBlQ,OACA0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,GAEf,CACDsP,SACI,OAAOvV,KAAKwM,KAAKjJ,IACpB,EAEL5I,EAAAmN,WAAqBA,GACrB,MAAMwH,WAAoBzF,EACtBkE,OAAOL,GACH,MAAMjH,OAAEA,EAAMR,IAAEA,GAAQjG,KAAK6N,oBAAoBH,GACjD,GAAIzH,EAAIC,OAAOiI,MAAO,CAqBlB,MApBoBA,WAChB,MAAM2O,QAAiB9c,KAAKwM,KAAKuQ,GAAG/O,YAAY,CAC5CnQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAEZ,MAAwB,YAApB6W,EAASrW,OACFyE,EAAYjG,QACC,UAApB6X,EAASrW,QACTA,EAAOH,SACA,EAAI4E,EAAYlG,OAAO8X,EAASliB,QAGhCoF,KAAKwM,KAAKwQ,IAAIhP,YAAY,CAC7BnQ,KAAMif,EAASliB,MACf2G,KAAM0E,EAAI1E,KACVgK,OAAQtF,GAEf,EAEEgX,EACV,CACI,CACD,MAAMH,EAAW9c,KAAKwM,KAAKuQ,GAAGjP,WAAW,CACrCjQ,KAAMoI,EAAIpI,KACV0D,KAAM0E,EAAI1E,KACVgK,OAAQtF,IAEZ,MAAwB,YAApB6W,EAASrW,OACFyE,EAAYjG,QACC,UAApB6X,EAASrW,QACTA,EAAOH,QACA,CACHG,OAAQ,QACR7L,MAAOkiB,EAASliB,QAIboF,KAAKwM,KAAKwQ,IAAIlP,WAAW,CAC5BjQ,KAAMif,EAASliB,MACf2G,KAAM0E,EAAI1E,KACVgK,OAAQtF,GAGnB,CACJ,CACDO,cAAc8R,EAAGC,GACb,OAAO,IAAIjJ,GAAY,CACnByN,GAAIzE,EACJ0E,IAAKzE,EACL5J,SAAUjH,GAAsB4H,aAEvC,EAEL3U,EAAA2U,YAAsBA,GAsBtB,IAAI5H,GAJJ/M,EAAAkJ,OAjBe,CAACwK,EAAO9I,EAAS,CAAA,EAAI6W,IAC5B/N,EACOuG,EAAO7S,SAASgL,aAAY,CAAClP,EAAMoI,KACtC,IAAIiI,EAAIkI,EACR,IAAK/H,EAAMxQ,GAAO,CACd,MAAMqf,EAAsB,mBAAX3X,EACXA,EAAO1H,GACW,iBAAX0H,EACH,CAAEzE,QAASyE,GACXA,EACJ4X,EAA0E,QAAhE/G,EAAwB,QAAlBlI,EAAKgP,EAAEd,aAA0B,IAAPlO,EAAgBA,EAAKkO,SAA0B,IAAPhG,GAAgBA,EAClGgH,EAAkB,iBAANF,EAAiB,CAAEpc,QAASoc,GAAMA,EACpDjX,EAAIhG,SAAS,CAAEkB,KAAM,YAAaic,EAAIhB,MAAOe,GAChD,KAEFvI,EAAO7S,SAGlBpH,EAAegN,KAAA,CACXtL,OAAQ4M,EAAUuO,YAGtB,SAAW9P,GACPA,EAAiC,UAAI,YACrCA,EAAiC,UAAI,YACrCA,EAA8B,OAAI,SAClCA,EAAiC,UAAI,YACrCA,EAAkC,WAAI,aACtCA,EAA+B,QAAI,UACnCA,EAAiC,UAAI,YACrCA,EAAoC,aAAI,eACxCA,EAA+B,QAAI,UACnCA,EAA8B,OAAI,SAClCA,EAAkC,WAAI,aACtCA,EAAgC,SAAI,WACpCA,EAA+B,QAAI,UACnCA,EAAgC,SAAI,WACpCA,EAAiC,UAAI,YACrCA,EAAgC,SAAI,WACpCA,EAA6C,sBAAI,wBACjDA,EAAuC,gBAAI,kBAC3CA,EAAgC,SAAI,WACpCA,EAAiC,UAAI,YACrCA,EAA8B,OAAI,SAClCA,EAA8B,OAAI,SAClCA,EAAmC,YAAI,cACvCA,EAA+B,QAAI,UACnCA,EAAkC,WAAI,aACtCA,EAA+B,QAAI,UACnCA,EAAkC,WAAI,aACtCA,EAAqC,cAAI,gBACzCA,EAAmC,YAAI,cACvCA,EAAmC,YAAI,cACvCA,EAAkC,WAAI,aACtCA,EAAgC,SAAI,WACpCA,EAAkC,WAAI,aACtCA,EAAkC,WAAI,aACtCA,EAAmC,YAAI,aAC1C,CApCD,CAoCGA,GAAwB/M,EAAQ+M,wBAA0B/M,EAAgC+M,sBAAA,CAAE,IAO/F/M,EAAA0iB,WAHuB,CAACC,EAAK/X,EAAS,CAClCzE,QAAS,yBAAyBwc,EAAI9c,WACpC,EAAI7F,EAAQkJ,SAAShG,GAASA,aAAgByf,GAAK/X,GAEzD,MAAMgY,GAAa3T,EAAU7H,OAC7BpH,EAAAoD,OAAiBwf,GACjB,MAAMC,GAAanK,EAAUtR,OAC7BpH,EAAAuD,OAAiBsf,GACjB,MAAMC,GAAUzV,GAAOjG,OACvBpH,EAAAsD,IAAcwf,GACd,MAAMC,GAAa/T,EAAU5H,OAC7BpH,EAAA0D,OAAiBqf,GACjB,MAAMC,GAAcjU,EAAW3H,OAC/BpH,EAAAwD,QAAkBwf,GAClB,MAAMC,GAAWnU,EAAQ1H,OACzBpH,EAAAsE,KAAe2e,GACf,MAAMC,GAAarU,EAAUzH,OAC7BpH,EAAA2D,OAAiBuf,GACjB,MAAMC,GAAgBvU,EAAaxH,OACnCpH,EAAAmD,UAAoBggB,GACpB,MAAMC,GAAWzU,EAAQvH,OACzBpH,EAAA8D,KAAesf,GACf,MAAMC,GAAUpJ,EAAO7S,OACvBpH,EAAAsjB,IAAcD,GACd,MAAME,GAAc7U,EAAWtH,OAC/BpH,EAAAuE,QAAkBgf,GAClB,MAAMC,GAAY/U,EAASrH,OAC3BpH,EAAA+P,MAAgByT,GAChB,MAAMC,GAAWjV,EAAQpH,OACzBpH,EAAAoP,KAAeqU,GACf,MAAMC,GAAYnV,EAASnH,OAC3BpH,EAAAyC,MAAgBihB,GAChB,MAAMC,GAAarV,EAAUlH,OAC7BpH,EAAA0B,OAAiBiiB,GACjB,MAAMC,GAAmBtV,EAAUsO,aACnC5c,EAAAuP,aAAuBqU,GACvB,MAAMC,GAAYxV,EAASjH,OAC3BpH,EAAAqP,MAAgBwU,GAChB,MAAMC,GAAyB5G,EAAsB9V,OACrDpH,EAAA6M,mBAA6BiX,GAC7B,MAAMC,GAAmB3V,EAAgBhH,OACzCpH,EAAAmQ,aAAuB4T,GACvB,MAAMC,GAAY7V,EAAS/G,OAC3BpH,EAAAikB,MAAgBD,GAChB,MAAME,GAAahW,EAAU9G,OAC7BpH,EAAAwP,OAAiB0U,GACjB,MAAMC,GAAUlW,EAAO7G,OACvBpH,EAAAuB,IAAc4iB,GACd,MAAMC,GAAUpW,EAAO5G,OACvBpH,EAAAoE,IAAcggB,GACd,MAAMC,GAAetW,EAAY3G,OACjCpH,EAAAyD,SAAmB4gB,GACnB,MAAMC,GAAWrH,EAAQ7V,OACzBpH,EAAAkQ,KAAeoU,GACf,MAAMC,GAAczW,EAAW1G,OAC/BpH,EAAAiQ,QAAkBsU,GAClB,MAAMC,GAAW3W,EAAQzG,OACzBpH,EAAAoQ,KAAeoU,GACf,MAAMC,GAAiB7W,EAAcxG,OACrCpH,EAAAgQ,WAAqByU,GACrB,MAAMC,GAAc/W,GAAWvG,OAC/BpH,EAAAiE,QAAkBygB,GAClB,MAAMC,GAAcjX,GAAWtG,OAC/BpH,EAAAqQ,OAAiBsU,GACjB3kB,EAAAsP,YAAsBqV,GACtB,MAAMC,GAAe3Q,GAAY7M,OACjCpH,EAAA2P,SAAmBiV,GACnB,MAAMC,GAAerX,GAAYpG,OACjCpH,EAAA8P,SAAmB+U,GACnB,MAAMC,GAAiBpX,GAAWoU,qBAClC9hB,EAAAyP,WAAqBqV,GACrB,MAAMC,GAAepQ,GAAYvN,OACjCpH,EAAA0P,SAAmBqV,GAEnB/kB,EAAAglB,QADgB,IAAMpC,KAAajT,WAGnC3P,EAAA4P,QADgB,IAAMiT,KAAalT,WAGnC3P,EAAA6P,SADiB,IAAMmT,KAAcrT,WAErC3P,EAAiB8M,OAAA,CACb1J,OAAUoe,GAAQvS,EAAU7H,OAAO,IAAKoa,EAAK1U,QAAQ,IACrDvJ,OAAUie,GAAQ9I,EAAUtR,OAAO,IAAKoa,EAAK1U,QAAQ,IACrDtJ,QAAWge,GAAQzS,EAAW3H,OAAO,IAC9Boa,EACH1U,QAAQ,IAEZpJ,OAAU8d,GAAQxS,EAAU5H,OAAO,IAAKoa,EAAK1U,QAAQ,IACrDxI,KAAQkd,GAAQ1S,EAAQ1H,OAAO,IAAKoa,EAAK1U,QAAQ,KAErD9M,EAAgBmP,MAAAoB,EAAYjG,wBClrG5B,IAAI2a,EAAmB5f,GAAQA,EAAK4f,kBAAqBnlB,OAAOsH,OAAM,SAAa8d,EAAG/Z,EAAG/J,EAAG+jB,QAC7EhiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3BtB,OAAOC,eAAemlB,EAAGC,EAAI,CAAEC,YAAY,EAAM9H,IAAK,WAAa,OAAOnS,EAAE/J,EAAG,GAClF,EAAA,SAAc8jB,EAAG/Z,EAAG/J,EAAG+jB,QACThiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3B8jB,EAAEC,GAAMha,EAAE/J,EACb,GACGikB,EAAgBhgB,GAAQA,EAAKggB,cAAiB,SAASla,EAAGnL,GAC1D,IAAK,IAAIuiB,KAAKpX,EAAa,YAANoX,GAAoBziB,OAAO8B,UAAUC,eAAeC,KAAK9B,EAASuiB,IAAI0C,EAAgBjlB,EAASmL,EAAGoX,EAC3H,EACAziB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDolB,EAAazgB,EAAqB5E,GAClCqlB,EAAa/d,EAAgCtH,GAC7CqlB,EAAa7U,EAAkCxQ,GAC/CqlB,EAAa5U,EAA2BzQ,GACxCqlB,EAAa3U,EAAoB1Q,GACjCqlB,EAAaC,EAAuBtlB,mBChBpC,IAAIilB,EAAmB5f,GAAQA,EAAK4f,kBAAqBnlB,OAAOsH,OAAM,SAAa8d,EAAG/Z,EAAG/J,EAAG+jB,QAC7EhiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3BtB,OAAOC,eAAemlB,EAAGC,EAAI,CAAEC,YAAY,EAAM9H,IAAK,WAAa,OAAOnS,EAAE/J,EAAG,GAClF,EAAA,SAAc8jB,EAAG/Z,EAAG/J,EAAG+jB,QACThiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3B8jB,EAAEC,GAAMha,EAAE/J,EACb,GACGmkB,EAAsBlgB,GAAQA,EAAKkgB,qBAAwBzlB,OAAOsH,OAAM,SAAa8d,EAAGM,GACxF1lB,OAAOC,eAAemlB,EAAG,UAAW,CAAEE,YAAY,EAAMnlB,MAAOulB,GAClE,EAAI,SAASN,EAAGM,GACbN,EAAW,QAAIM,CACnB,GACIC,EAAgBpgB,GAAQA,EAAKogB,cAAiB,SAAUhc,GACxD,GAAIA,GAAOA,EAAIC,WAAY,OAAOD,EAClC,IAAIwH,EAAS,CAAA,EACb,GAAW,MAAPxH,EAAa,IAAK,IAAIrI,KAAKqI,EAAe,YAANrI,GAAmBtB,OAAO8B,UAAUC,eAAeC,KAAK2H,EAAKrI,IAAI6jB,EAAgBhU,EAAQxH,EAAKrI,GAEtI,OADAmkB,EAAmBtU,EAAQxH,GACpBwH,CACX,EACIoU,EAAgBhgB,GAAQA,EAAKggB,cAAiB,SAASla,EAAGnL,GAC1D,IAAK,IAAIuiB,KAAKpX,EAAa,YAANoX,GAAoBziB,OAAO8B,UAAUC,eAAeC,KAAK9B,EAASuiB,IAAI0C,EAAgBjlB,EAASmL,EAAGoX,EAC3H,EACAziB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAY0lB,OAAA,EACZ,MAAMA,EAAID,EAAa7gB,GACvB5E,EAAA0lB,EAAYA,EACZL,EAAazgB,EAAuB5E,GACpCA,EAAAuH,QAAkBme,4BCkGlB,IAAWC,EASAC,EAEIC,EAUJC,EAQAC,EAUAC,EApJXlmB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAAgmB,IAAchmB,UAAkBA,EAAc8lB,IAAA9lB,EAAA4lB,gBAA0B5lB,SAAiBA,EAAkBimB,aAAA,EAE3G,SAAWA,GAEP,IAAIC,GACJ,SAAWA,GAEPA,EAA2B,gBAAI,mBAC/BA,EAA4B,iBAAI,qBAChCA,EAAuB,YAAI,gBAC3BA,EAAuB,YAAI,gBAC3BA,EAAwB,aAAI,iBAC5BA,EAAsB,WAAI,eAC1BA,EAAwB,aAAI,iBAC5BA,EAA6B,kBAAI,sBACjCA,EAA0B,eAAI,kBAC9BA,EAAwB,aAAI,gBAC5BA,EAAgC,qBAAI,uBAEvC,CAdD,CAcGA,EAAYD,EAAQC,YAAcD,EAAQC,UAAY,CAAE,IAC3D,MAAMC,EACF5f,MACAJ,QACAigB,WACAlhB,YAAYqB,EAAOJ,EAASigB,GACxB/gB,KAAKkB,MAAQA,EACblB,KAAKc,QAAUA,EACfd,KAAK+gB,WAAaA,CACrB,CACDC,gBAAgBC,GACZ,MAAO,CACHC,GAAID,EACJ/f,MAAOlB,KAAKkB,MACZJ,QAASd,KAAKc,QACdigB,WAAY/gB,KAAK+gB,WAExB,EAELH,EAAQE,cAAgBA,EAMxBF,EAAQO,yBALR,cAAuCL,EACnCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUO,gBAAiBtgB,EAASigB,EAC7C,GAQLH,EAAQS,sBALR,cAAoCP,EAChCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUS,aAAcxgB,EAASigB,EAC1C,GAQLH,EAAQW,0BALR,cAAwCT,EACpCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUW,iBAAkB1gB,EAASigB,EAC9C,GAQLH,EAAQa,qBALR,cAAmCX,EAC/BjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUa,YAAa5gB,EAASigB,EACzC,GAQLH,EAAQe,qBALR,cAAmCb,EAC/BjhB,YAAYiB,GACRf,MAAM8gB,EAAUe,YAAa9gB,EAChC,GAQL8f,EAAQiB,oBALR,cAAkCf,EAC9BjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUiB,WAAYhhB,EAASigB,EACxC,GAQLH,EAAQmB,sBALR,cAAoCjB,EAChCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUmB,aAAclhB,EAASigB,EAC1C,GAQLH,EAAQqB,2BALR,cAAyCnB,EACrCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUqB,kBAAmBphB,EAASigB,EAC/C,GAQLH,EAAQuB,wBALR,cAAsCrB,EAClCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUuB,eAAgBthB,EAASigB,EAC5C,GAQLH,EAAQyB,sBALR,cAAoCvB,EAChCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAUyB,aAAcxhB,EAASigB,EAC1C,GAQLH,EAAQ2B,8BALR,cAA4CzB,EACxCjhB,YAAYiB,EAASigB,GACjBhhB,MAAM8gB,EAAU2B,qBAAsB1hB,EAASigB,EAClD,EAGR,CAvGD,CAuGapmB,EAAQimB,UAAYjmB,EAAkBimB,QAAA,CAAA,MAGxCN,EAMC3lB,EAAQ2lB,SAAW3lB,EAAiB2lB,OAAA,CAAA,IAFrBE,aAAeF,EAAOE,WAAa,CAAA,IAD7B,aAAI,iBAEjCF,EAAOmC,UAAY,SAIZlC,EASU5lB,EAAQ4lB,kBAAoB5lB,EAA0B4lB,gBAAA,CAAA,IAP5DC,EAKKD,EAAgBC,aAAeD,EAAgBC,WAAa,CAAA,IAJlD,UAAI,uBAC1BA,EAAkC,sBAAI,mCACtCA,EAAgC,oBAAI,iCACpCA,EAAkC,sBAAI,mCAE1CD,EAAgBkC,UAAY,mBAIrBhC,EAMF9lB,EAAQ8lB,MAAQ9lB,EAAc8lB,IAAA,CAAA,IAL/BgC,UAAY,MAEhB,SAAWjC,GACPA,EAA+B,mBAAI,gBACtC,CAFD,CAEgBC,EAAID,aAAeC,EAAID,WAAa,CAAA,KAG7CE,EAQE/lB,EAAQ+lB,UAAY/lB,EAAkB+lB,QAAA,CAAA,IAPvC+B,UAAY,UAEpB,SAAWjC,GACPA,EAAmC,uBAAI,4BACvCA,EAAmC,uBAAI,4BACvCA,EAA4B,gBAAI,oBACnC,CAJD,CAIgBE,EAAQF,aAAeE,EAAQF,WAAa,CAAA,KAGrDG,EAMFhmB,EAAQgmB,MAAQhmB,EAAcgmB,IAAA,CAAA,IAL/B8B,UAAY,MAEhB,SAAWjC,GACPA,EAA+B,mBAAI,mBACtC,CAFD,CAEgBG,EAAIH,aAAeG,EAAIH,WAAa,CAAA,oBCzJxD/lB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAA+nB,QAAkB/nB,MAAcA,EAA0B4lB,gBAAA5lB,EAAA2lB,OAAiB3lB,kBAA0BA,EAAsBgoB,iBAAA,EAK3H,MAAMC,EAAQrjB,EACRsjB,EAAgB5gB,EAEtB,SAAS0gB,EAAYlnB,EAAKiT,GACtB,MAAMoU,EAAcpU,EAAO/B,UAAUlR,GACrC,GAAIqnB,EAAYjX,QACZ,OAAOiX,EAAYjlB,KAEvB,MAAM+H,EAAekd,EAAY5hB,MAAMT,OAClCvE,KAAKC,GAAM,GAAGA,EAAE2E,cACd3E,EAAEoF,KAAKrF,KAAKghB,GAAMxd,KAAKC,UAAUud,KAAI5f,KAAK,UAC5CA,KAAK,KACV,MAAM,IAAIulB,EAAcjC,QAAQO,yBAAyBvb,EAC5D,CAED,IAAImd,EADJpoB,EAAAgoB,YAAsBA,EAEtB,SAAWI,GACPA,EAAgBC,sBAAwBJ,EAAMvC,EAAEhkB,OAAO,CACnD4mB,SAAUL,EAAMvC,EAAEtiB,SAASqS,IAAI,KAEnC2S,EAAgBG,sBAAwBN,EAAMvC,EAAEhkB,OAAO,CACnD8mB,OAAQP,EAAMvC,EAAEtiB,SAASqS,IAAI,KAKjC,MAAMgT,EAAuBR,EAAMvC,EAAEhkB,OAAO,CAAEkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,eAI9DyY,EAAkBT,EAAMvC,EAAEhkB,OAAO,CAAEkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UAKzD0Y,EAAoBV,EAAMvC,EAAEhkB,OAAO,CACrCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UACtBhQ,MAAOgoB,EAAMvC,EAAEtiB,WAGbwlB,EAAsBX,EAAMvC,EAAEtV,KAAK,CAAC,MAAO,KAAM,WAAY,cAK7DyY,EAAoBZ,EAAMvC,EAAEhkB,OAAO,CACrCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UACtBhQ,MAAOgoB,EAAMvC,EAAErW,MAAM,CAACuZ,EAAqBX,EAAMvC,EAAEniB,aAMjDulB,EAAqBb,EAAMvC,EAAEhkB,OAAO,CACtCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,WACtBhQ,MAAOgoB,EAAMvC,EAAEliB,YAMbulB,EAAoBd,EAAMvC,EAAEhkB,OAAO,CACrCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UACtBhQ,MAAOgoB,EAAMvC,EAAEtiB,WAEb4lB,EAA+Bf,EAAMvC,EAAErW,MAAM,CAC/CoZ,EACAC,EACAC,EACAE,EACAC,EACAC,IAEJX,EAAgBa,iBAAmBhB,EAAMvC,EAAExV,MAAK,IAAM+X,EAAMvC,EAAErW,MAAM,CAChE2Z,EACAE,EACAC,EACAC,EACAC,EACAC,EACAC,MAIJ,MAAMC,EAA2BvB,EAAMvC,EAAErW,MAAM,CAC3C+Y,EAAgBC,sBAChBD,EAAgBG,sBAChBH,EAAgBa,mBAGdQ,EAAuBxB,EAAMvC,EAAEjjB,MAAM+mB,GAKrCN,EAAwBjB,EAAMvC,EAAExV,MAAK,IAAM+X,EAAMvC,EAAEhkB,OAAO,CAC5DkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,SACtBhQ,MAAOwpB,MAMLN,EAAuBlB,EAAMvC,EAAEhkB,OAAO,CACxCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,QACtBhQ,MAAOgoB,EAAMvC,EAAEtiB,SAASqS,IAAI,KAG1BiU,EAA0BzB,EAAMvC,EAAExV,MAAK,IAAM+X,EAAMvC,EAAEzB,MAAM,CAC7DgE,EAAMvC,EAAErW,MAAM,CAAC4Y,EAAMvC,EAAEtiB,SAAUomB,IACjCA,MAMEJ,EAAsBnB,EAAMvC,EAAEhkB,OAAO,CACvCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,OACtBhQ,MAAOgoB,EAAMvC,EAAEjjB,MAAMinB,KAMnBL,EAAyBpB,EAAMvC,EAAEhkB,OAAO,CAC1CkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UACtBhQ,MAAOgoB,EAAMvC,EAAEjjB,MAAMinB,KAMnBJ,EAAyBrB,EAAMvC,EAAExV,MAAK,IAAM+X,EAAMvC,EAAEhkB,OAAO,CAC7DkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,UACtBhQ,MAAOgoB,EAAMvC,EAAEhkB,OAAO,CAClBioB,QAAS1B,EAAMvC,EAAEtiB,SACjBwmB,MAAO3B,EAAMvC,EAAEtiB,SAASuM,iBAO1B4Z,EAAsBtB,EAAMvC,EAAExV,MAAK,IAAM+X,EAAMvC,EAAEhkB,OAAO,CAC1DkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,OACtBhQ,MAAOwpB,MAGXrB,EAAgByB,sBAAwB5B,EAAMvC,EAAEtiB,SAChDglB,EAAgB0B,eAAiB7B,EAAMvC,EAAEniB,SAASyV,MAAMI,cAAchC,IAnJ1D,iBAoJf,CAtID,CAsIGgR,EAAkBpoB,EAAQooB,kBAAoBpoB,EAA0BooB,gBAAA,CAAE,IAG7E,SAAWzC,GACP,MAAMoE,EAAkB9B,EAAMvC,EAAEtV,KAAK,CACjC,SACA,mBACA,gBACA,iBACA,SACA,gBACA,gBACA,YAEJuV,EAAOqE,0BAA4B/B,EAAMvC,EAAEhkB,OAAO,CAC9CuoB,QAAS7B,EAAgByB,sBAAsBla,WAC/C/G,KAAMmhB,EAAgBpa,aAK1BgW,EAAOuE,qBAHP,SAA8Btf,GAC1B,OAAOod,EAAYpd,EAAQ+a,EAAOqE,0BACrC,EAMD,MAAMG,EAAsBlC,EAAMvC,EAAEhkB,OAAO,CACvCuoB,QAAS7B,EAAgByB,sBACzBO,QAASnC,EAAMvC,EAAEtiB,SAASuM,aAGxB0a,EAAoBpC,EAAMvC,EAAEhkB,OAAO,CACrC4oB,MAAOrC,EAAMvC,EAAEtiB,SAASqS,IAAI,KAQ1B8U,EAAetC,EAAMvC,EAAErW,MAAM,CAACgb,EAAmBF,IAEjDK,EAAwBvC,EAAMvC,EAAEtV,KAAK,CAAC,OAAQ,SAO9Cqa,EAA2BxC,EAAMvC,EAAEhkB,OAAO,CAC5CgpB,WAAYzC,EAAMvC,EAAEtiB,SACpBunB,aAAc1C,EAAMvC,EAAEliB,UACtBkR,OAAQ6V,EACRK,gBAAiBJ,EAAsB7a,aAK3CgW,EAAOkF,oBAHP,SAA6BjgB,GACzB,OAAOod,EAAYpd,EAAQ6f,EAC9B,EAMD,MAAMK,EAAyB7C,EAAMvC,EAAEhkB,OAAO,CAC1CgT,OAAQ6V,EACRQ,QAAS9C,EAAMvC,EAAEjjB,MAAMwlB,EAAMvC,EAAEtiB,YAKnCuiB,EAAOqF,kBAHP,SAA2BpgB,GACvB,OAAOod,EAAYpd,EAAQkgB,EAC9B,EAEDnF,EAAOsF,oBAAsBhD,EAAMvC,EAAEtiB,SACrCuiB,EAAOuF,iCAAmCjD,EAAMvC,EAAEhkB,OAAO,CACrDgpB,WAAYzC,EAAMvC,EAAEtiB,SACpBgnB,QAASnC,EAAMvC,EAAEtiB,SAASuM,WAC1Bsa,QAAS7B,EAAgByB,sBAAsBla,aAKnDgW,EAAOwF,4BAHP,SAAqCvgB,GACjC,OAAOod,EAAYpd,EAAQ+a,EAAOuF,iCACrC,EAEDvF,EAAOyF,oCAAsCnD,EAAMvC,EAAEhkB,OAAO,CACxD2pB,OAAQ1F,EAAOsF,sBAKnBtF,EAAO2F,+BAHP,SAAwC1gB,GACpC,OAAOod,EAAYpd,EAAQ+a,EAAOyF,oCACrC,EAED,MAAMG,EAAkBtD,EAAMvC,EAAEtiB,SAC1BooB,EAA0BvD,EAAMvC,EAAEhkB,OAAO,CAC3C+pB,QAASF,EAGTG,SAAUzD,EAAMvC,EAAEniB,SAASyV,MAAMvD,IAAI,GAAG2B,IAAI,GAAGzH,WAC/Cgc,UAAWnB,EAAsB7a,aAErCgW,EAAOiG,cAAgB3D,EAAMvC,EAAEhkB,OAAO,CAClCkH,KAAMqf,EAAMvC,EAAEzV,QAAQ,WACtBhQ,MAAOurB,IAOX,MAAMK,EAAsB5D,EAAMvC,EAAErW,MAAM,CACtC+Y,EAAgBG,sBAChBH,EAAgBC,sBAChBD,EAAgBa,iBAChBtD,EAAOiG,gBAULE,EAA+B7D,EAAMvC,EAAEhkB,OAAO,CAChDqqB,oBAAqB9D,EAAMvC,EAAEtiB,SAC7BsR,OAAQ6V,EACRnV,UAAW6S,EAAMvC,EAAEjjB,MAAMopB,GAAqBlc,WAC9CtK,KAAMwmB,EAAoBlc,WAC1Bgb,aAAc1C,EAAMvC,EAAEliB,UACtBonB,gBAAiBJ,EAAsB7a,aAK3CgW,EAAOqG,wBAHP,SAAiCphB,GAC7B,OAAOod,EAAYpd,EAAQkhB,EAC9B,CAEJ,CAhID,CAgIY9rB,EAAQ2lB,SAAW3lB,EAAiB2lB,OAAA,CAAA,IAGhD,SAAWC,GAKP,MAAMqG,EAA0BhE,EAAMvC,EAAEhkB,OAAO,CAC3CgqB,SAAUtD,EAAgB0B,eAAena,WACzCuc,KAAM9D,EAAgByB,sBAAsBla,aAKhDiW,EAAgBuG,mBAHhB,SAA4BvhB,GACxB,OAAOod,EAAYpd,EAAQqhB,EAC9B,EAGD,MAAMG,EAAuBnE,EAAMvC,EAAEtV,KAAK,CAAC,OAAQ,cAAe,aAO5Dic,EAA2BpE,EAAMvC,EAAEhkB,OAAO,CAC5CuoB,QAAS7B,EAAgByB,sBACzBjT,IAAKqR,EAAMvC,EAAEtiB,SAASwT,MACtB0V,KAAMF,EAAqBzc,aAK/BiW,EAAgB2G,oBAHhB,SAA6B3hB,GACzB,OAAOod,EAAYpd,EAAQyhB,EAC9B,EAMD,MAAMG,EAAyBvE,EAAMvC,EAAEhkB,OAAO,CAC1CkH,KAAMqf,EAAMvC,EAAEtV,KAAK,CAAC,MAAO,WAC3Bqc,iBAAkBrE,EAAgByB,sBAAsBla,aAK5DiW,EAAgB8G,kBAHhB,SAA2B9hB,GACvB,OAAOod,EAAYpd,EAAQ4hB,EAC9B,EAKD,MAAMG,EAAwB1E,EAAMvC,EAAEhkB,OAAO,CACzCuoB,QAAS7B,EAAgByB,wBAK7BjE,EAAgBgH,iBAHhB,SAA0BhiB,GACtB,OAAOod,EAAYpd,EAAQ+hB,EAC9B,EAKD,MAAME,EAAoC5E,EAAMvC,EAAEhkB,OAAO,CACrDuoB,QAAS7B,EAAgByB,wBAK7BjE,EAAgBkH,6BAHhB,SAAsCliB,GAClC,OAAOod,EAAYpd,EAAQiiB,EAC9B,EAOD,MAAME,EAA4B9E,EAAMvC,EAAEhkB,OAAO,CAC7CsrB,OAAQ/E,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,OAAOoI,WACjDsd,MAAOhF,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,OAAOoI,aAS9Cud,EAA8BjF,EAAMvC,EAAEhkB,OAAO,CAC/CyrB,OAAQlF,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,GAAKoI,WAC/C+O,KAAMuJ,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,GAAKoI,WAC7CgP,MAAOsJ,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,GAAKoI,WAC9Cyd,IAAKnF,EAAMvC,EAAEniB,SAASkS,IAAI,GAAKlO,QAAQ,GAAKoI,aAG1C0d,EAAwBpF,EAAMvC,EAC/BjjB,MAAMwlB,EAAMvC,EAAErW,MAAM,CAAC4Y,EAAMvC,EAAEtiB,SAASqS,IAAI,GAAIwS,EAAMvC,EAAEniB,SAASyV,MAAMI,iBACrElH,QAAQob,GACFA,EAAWC,OAAOC,IACrB,MAAMC,EAAQ1X,OAAOyX,GAAWC,MAEhC,wEAEMC,MAAEA,EAAKC,IAAEA,GAAQF,GAAOG,QAAU,CAAA,EACxC,QAAIF,GAASC,GAAOvrB,OAAOsrB,GAAStrB,OAAOurB,KAGpCF,CAAK,MAadI,EAAwB5F,EAAMvC,EAAEhkB,OAAO,CACzCuoB,QAAS7B,EAAgByB,sBACzBiE,WAAY7F,EAAMvC,EAAEliB,UAAU+D,SAAQ,GAAOoI,WAC7Coe,OAAQb,EAA4Bvd,WACpCqe,YAAa/F,EAAMvC,EACdtV,KAAK,CAAC,WAAY,cAClB7I,QAAQ,YACRoI,WACLse,KAAMlB,EAA0Bpd,WAChC2d,WAAYD,EAAsB9lB,QAAQ,IAAIoI,WAC9Cue,MAAOjG,EAAMvC,EAAEniB,SAASkS,IAAI,IAAK2B,IAAI,GAAK7P,QAAQ,GAAKoI,WACvDwe,YAAalG,EAAMvC,EAAEliB,UAAU+D,SAAQ,GAAMoI,aAKjDiW,EAAgBwI,iBAHhB,SAA0BxjB,GACtB,OAAOod,EAAYpd,EAAQijB,EAC9B,CAEJ,CA/HD,CA+HqB7tB,EAAQ4lB,kBAAoB5lB,EAA0B4lB,gBAAA,CAAA,IAE3E,SAAWI,GACP,MAAMqI,EAA0BpG,EAAMvC,EAAEhkB,OAAO,CAE3C4sB,UAAWrG,EAAMvC,EAAEtiB,SAGnBmrB,UAAWtG,EAAMvC,EAAEhkB,OAAO,CAAE,GAAEqZ,cAC9ByT,WAAYvG,EAAMvC,EAAEtiB,SAASuM,aAKjCqW,EAAIyI,uBAHJ,SAAgC7jB,GAC5B,OAAOod,EAAYpd,EAAQyjB,EAC9B,EAED,MAAMK,EAAyBzG,EAAMvC,EAAEhkB,OAAO,CAC1CuoB,QAAS7B,EAAgByB,wBAK7B7D,EAAI2I,sBAHJ,SAA+B/jB,GAC3B,OAAOod,EAAYpd,EAAQ8jB,EAC9B,CAEJ,CApBD,CAoBS1uB,EAAQgmB,MAAQhmB,EAAcgmB,IAAA,CAAA,IAGvC,SAAW+B,GACP,MAAM6G,EAA4C3G,EAAMvC,EAAEtV,KAAK,CAC3D8X,EAActC,gBAAgBkC,aAC3BhoB,OAAOggB,OAAOoI,EAActC,gBAAgBC,YAC/CqC,EAAcpC,IAAIgC,aACfhoB,OAAOggB,OAAOoI,EAAcpC,IAAID,YACnCqC,EAAclC,IAAI8B,aACfhoB,OAAOggB,OAAOoI,EAAclC,IAAIH,YACnCqC,EAAcnC,QAAQ+B,aACnBhoB,OAAOggB,OAAOoI,EAAcnC,QAAQF,YACvCqC,EAAcvC,OAAOmC,aAClBhoB,OAAOggB,OAAOoI,EAAcvC,OAAOE,cAMpCgJ,EAAsC5G,EAAMvC,EAAEhkB,OAAO,CACvDotB,OAAQ7G,EAAMvC,EAAEjjB,MAAMmsB,GACtBG,SAAU9G,EAAMvC,EAAEjjB,MAAM2lB,EAAgByB,uBAAuBla,aAKnEoY,EAAQiH,qBAHR,SAA8BpkB,GAC1B,OAAOod,EAAYpd,EAAQikB,EAC9B,CAEJ,CAzBD,CAyBa7uB,EAAQ+nB,UAAY/nB,EAAkB+nB,QAAA,CAAA,sBCne/Cve,EAAmBnE,GAAQA,EAAKmE,iBAAoB,SAAUC,GAC9D,OAAQA,GAAOA,EAAIC,WAAcD,EAAM,CAAElC,QAAWkC,EACxD,EACA3J,OAAOC,eAAekvB,EAAS,aAAc,CAAEhvB,OAAO,IAClCgvB,EAAAC,kBAAG,EAiBvB,MAAMC,EAAS3lB,GCtBA,SAAS4lB,GAAG,MAAM,CAAC/U,IAAI+U,EAAEA,GAAG,IAAIlrB,IAAImrB,GAAG,SAAS7tB,EAAE8tB,GAAG,IAAIvoB,EAAEqoB,EAAE9R,IAAI9b,GAAGuF,EAAEA,EAAEhF,KAAKutB,GAAGF,EAAEhrB,IAAI5C,EAAE,CAAC8tB,GAAG,EAAEC,IAAI,SAAS/tB,EAAE8tB,GAAG,IAAIvoB,EAAEqoB,EAAE9R,IAAI9b,GAAGuF,IAAIuoB,EAAEvoB,EAAEyoB,OAAOzoB,EAAEmX,QAAQoR,KAAK,EAAE,GAAGF,EAAEhrB,IAAI5C,EAAE,IAAI,EAAEiuB,KAAK,SAASjuB,EAAE8tB,GAAG,IAAIvoB,EAAEqoB,EAAE9R,IAAI9b,GAAGuF,GAAGA,EAAEqE,QAAQ7J,KAAI,SAAS6tB,GAAGA,EAAEE,EAAE,KAAIvoB,EAAEqoB,EAAE9R,IAAI,OAAOvW,EAAEqE,QAAQ7J,KAAI,SAAS6tB,GAAGA,EAAE5tB,EAAE8tB,EAAE,GAAE,EAAE,IDyDrSI,EAAAR,aAlCpB,MACIS,IAAW,EAAIR,EAAO5nB,WACtB8nB,GAAGzmB,EAAMgnB,GAEL,OADAvqB,MAAKsqB,EAASN,GAAGzmB,EAAMgnB,GAChBvqB,IACV,CAODwqB,KAAKC,EAAOF,GACR,MAAMG,EAAeC,IACjBJ,EAAQI,GACR3qB,KAAKkqB,IAAIO,EAAOC,EAAY,EAEhC,OAAO1qB,KAAKgqB,GAAGS,EAAOC,EACzB,CACDR,IAAI3mB,EAAMgnB,GAEN,OADAvqB,MAAKsqB,EAASJ,IAAI3mB,EAAMgnB,GACjBvqB,IACV,CAQDoqB,KAAKK,EAAOE,GACR3qB,MAAKsqB,EAASF,KAAKK,EAAOE,EAC7B,8BEnCL,IAAWC,EAHXnwB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAkBiwB,aAAA,GAEPA,EAOEjwB,EAAQiwB,UAAYjwB,EAAkBiwB,QAAA,CAAA,IALjC,KAAI,gBAClBA,EAA0B,iBAAI,oBAC9BA,EAAa,IAAI,MACjBA,EAAgB,OAAI,aCRxBnwB,OAAOC,eAAemwB,EAAS,aAAc,CAAEjwB,OAAO,IAC/BiwB,EAAAC,qBAAG,EAC1B,MAAMC,EAAWxrB,EAqCMsrB,EAAAC,gBApCvB,MACInsB,GACAqsB,GACAC,GACAC,GAAS,GAETC,IAAgB,EAChBtrB,YAAYorB,EAAWG,EAAS,KAAMhkB,QAAQ6G,WAAW+c,GACrDhrB,MAAKrB,EAASysB,EACdprB,MAAKirB,EAAaA,EAClBjrB,MAAKgrB,EAAUA,CAClB,CACDxQ,IAAI6Q,GACArrB,MAAKkrB,EAAOxuB,KAAK2uB,GAEZrrB,MAAKsrB,GACb,CACDnd,UACI,IAAInO,MAAKmrB,EAAT,CAIA,IADAnrB,MAAKmrB,GAAgB,EACdnrB,MAAKkrB,EAAO1pB,OAAS,GAAG,CAC3B,MAAM+pB,EAAevrB,MAAKkrB,EAAOM,aACZ1tB,IAAjBytB,SACMA,EACD7sB,MAAM2sB,GAAUrrB,MAAKirB,EAAWI,KAChC1sB,OAAOxC,IACR6D,MAAKgrB,IAAUD,EAASH,QAAQa,OAAQ,2BAA4BtvB,GACpE6D,MAAKrB,EAAOxC,EAAE,GAGzB,CACD6D,MAAKmrB,GAAgB,CAbpB,CAcJ,2BCrCL1wB,OAAOC,eAAegxB,EAAS,aAAc,CAAE9wB,OAAO,IAClC8wB,EAAAC,kBAAG,EAKHD,EAAAC,aAHpB,SAAsBC,GAClB,OAAOA,EAAK,IAChB,WCLAnxB,OAAOC,eAAemxB,EAAS,aAAc,CAAEjxB,OAAO,IACtCixB,EAAAC,cAAG,EACnB,MAAMA,EACFC,IAAc,EACdntB,GACAqP,GAAW,OACX+d,GAAU,OACND,iBACA,OAAO/rB,MAAK+rB,CACf,CACDlsB,cACIG,MAAKpB,EAAW,IAAIwI,SAAQ,CAAC6G,EAAS+d,KAClChsB,MAAKiO,EAAWA,EAChBjO,MAAKgsB,EAAUA,CAAM,IAIzBhsB,MAAKpB,EAASD,OAAM,QACvB,CACDD,KAAKutB,EAAaC,GACd,OAAOlsB,MAAKpB,EAASF,KAAKutB,EAAaC,EAC1C,CACDvtB,MAAMutB,GACF,OAAOlsB,MAAKpB,EAASD,MAAMutB,EAC9B,CACDje,QAAQrT,GACJoF,MAAK+rB,GAAc,EACnB/rB,MAAKiO,EAASrT,EACjB,CACDoxB,OAAOG,GACHnsB,MAAK+rB,GAAc,EACnB/rB,MAAKgsB,EAAQG,EAChB,CACDC,QAAQC,GACJ,OAAOrsB,MAAKpB,EAASwtB,QAAQC,EAChC,CACD,CAACxP,OAAOyP,aAAe,UAEXT,EAAAC,SAAGA,6BCtDnBrxB,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAA0B4xB,gBAAA5xB,EAAA6xB,uBAA4B,EACtD,MAAM3J,EAAgBtjB,EAKtB5E,EAAA6xB,kBAA4B,YAC5B,MAAMD,EACFE,GACA5sB,YAAY4sB,GACRzsB,MAAKysB,EAAgBA,CACxB,CAQDjmB,6BAA6BkmB,EAAWzH,GAUpC,aAT8BA,EAAM0H,UAAUC,YAAY,yBAA0B,CAChFlG,oBAAqBhW,QAAQjV,GAClBiV,OAAOjV,KAElB6pB,cAAc,EACdvV,UAAW,CAAC2c,GACZG,eAAe,EACfC,mBAAoB7H,EAAM6H,sBAEPlhB,OAAOhR,KACjC,CAQDuT,yBAAyB4e,EAAiBxH,EAAiBN,GACvD,MAAM9I,EAAMoQ,GAAgBS,EAA+BD,GACrDE,QAA0BhI,EAAM0H,UAAUC,YAAY,yBAA0B,CAClFlG,oBAAqBhW,QAAQjV,GAAQA,IACrC6pB,cAAc,EACdvV,UAAW,CAACoM,GACZ+Q,wBAAwB,EACxBJ,mBAAoB7H,EAAM6H,qBAE9B,OAAO7H,EAAMkI,eAAeF,EAAmB1H,EAClD,CACDpX,qBAAqB8W,EAAOI,EAAYC,EAAcC,GAClD,MAAM6H,QAA0BnI,EAAM0H,UAAUC,YAAY,mBAAoB,CAC5ES,UAAWpI,EAAM6H,mBACjBzH,aACAC,eACA4H,wBAAwB,IAE5B,OAAIE,EAAkBE,iBAEX,CACHA,uBAAwBttB,MAAKutB,EAA8BH,EAAkBE,iBAtDrD,EAsDwG/H,EAAiBN,GACjJ1hB,KAAM,YACN0hB,MAAOA,EAAMuI,SAGd,CACHjqB,KAAM,UACNqI,OAAQqZ,EAAMkI,eAAeC,EAAmB7H,GAChDN,MAAOA,EAAMuI,QAEpB,CACDrf,mBAAmB8W,EAAOyB,EAAqB+G,EAAOC,EAAYpI,EAAcC,GAC5E,MAAMoI,EAAiC,wCAAwCjH,iOAMzEkH,EAAuB,OACnB5tB,MAAK6tB,EAAqBJ,EAAOxI,IAK3C,IAAI6I,EAHJF,EAAqBlxB,cAAe0K,QAAQ4N,IAAI0Y,EAAWxxB,KAAIiS,MAAOmK,GAC3DtY,MAAK6tB,EAAqBvV,EAAG2M,OAGxC,IACI6I,QAA8B7I,EAAM0H,UAAUC,YAAY,yBAA0B,CAChFlG,oBAAqBiH,EACrBrI,eACAvV,UAAW6d,EACXV,wBAAwB,EACxBJ,mBAAoB7H,EAAM6H,oBAgBjC,CAbD,MAAO3wB,GAIH,IAAgB,OAAZA,EAAEgF,MACF,CACI,sCACA,uEACA,4BACF+B,SAAS/G,EAAE2E,SACb,MAAM,IAAI+hB,EAAcjC,QAAQS,sBAAsB,yBAE1D,MAAMllB,CACT,CACD,OAAI2xB,EAAsBR,iBAEf,CACHA,uBAAwBttB,MAAKutB,EAA8BO,EAAsBR,iBA1GpD,EA0G4G/H,EAAiBN,GAC1J1hB,KAAM,YACN0hB,MAAOA,EAAMuI,SAGd,CACHjqB,KAAM,UACNqI,OAAQqZ,EAAMkI,eAAeW,EAAuBvI,GACpDN,MAAOA,EAAMuI,QAEpB,CACDhnB,SAAsCumB,GAClC,YAAiCjvB,IAA7BivB,EAAgBgB,SACT,CAAEA,SAAUhB,EAAgBgB,eAEKjwB,IAAxCivB,EAAgBiB,oBACT,CAAEA,oBAAqBjB,EAAgBiB,qBAE3C,CAAEpzB,MAAOmyB,EAAgBnyB,MACnC,CACDuT,QAA2B8f,EAAehJ,GACtC,GAAI,aAAcgJ,EAAe,CAC7B,MAAOC,EAAaC,GAAoBF,EAAchL,SAASlQ,MAAMpY,EAAQ6xB,mBACvE4B,EAAgBlb,SAASib,GAAoB,IACnD,GAAInwB,MAAMowB,SACYtwB,IAAlBswB,QACgBtwB,IAAhBowB,EACA,MAAM,IAAIrL,EAAcjC,QAAQO,yBAAyB,aAAa8M,EAAchL,8CAA8CtoB,EAAQ6xB,sCAE9I,GAAIvH,EAAMiJ,cAAgBA,EACtB,MAAM,IAAIrL,EAAcjC,QAAQiB,oBAAoB,aAAaoM,EAAchL,gEAAgEgC,EAAMiJ,gBAEzJ,IAMI,MAAO,CAAEH,gBALS9I,EAAM0H,UAAUC,YAAY,kBAAmB,CAC7DwB,gBACAtB,mBAAoB7H,EAAM6H,sBAGPzwB,OAAO0xB,SASjC,CAPD,MAAO5xB,GAGH,IAAgB,OAAZA,EAAEgF,MAAiC,gCAAdhF,EAAE2E,QACvB,MAAM,IAAI+hB,EAAcjC,QAAQiB,oBAAoB,aAAaoM,EAAchL,4BAEnF,MAAM9mB,CACT,CACJ,CACD,GAAI,WAAY8xB,EACZ,MAAO,CAAEF,SAAUE,EAAc9K,QAErC,OAAQ8K,EAAc1qB,MAGlB,IAAK,YACD,MAAO,CAAEyqB,oBAAqB,aAClC,IAAK,OACD,MAAO,CAAEA,oBAAqB,QAClC,IAAK,SACD,MAAO,CAAEpzB,MAAOqzB,EAAcrzB,OAClC,IAAK,SACD,MAA4B,QAAxBqzB,EAAcrzB,MACP,CAAEozB,oBAAqB,OAED,OAAxBC,EAAcrzB,MACZ,CAAEozB,oBAAqB,MAED,aAAxBC,EAAcrzB,MACZ,CAAEozB,oBAAqB,YAED,cAAxBC,EAAcrzB,MACZ,CAAEozB,oBAAqB,aAE3B,CACHpzB,MAAOqzB,EAAcrzB,OAE7B,IAAK,UACD,MAAO,CAAEA,MAAO4Z,QAAQyZ,EAAcrzB,QAC1C,IAAK,SACD,MAAO,CACHozB,oBAAqB,UAAUtuB,KAAKC,UAAUsuB,EAAcrzB,WAEpE,IAAK,OACD,MAAO,CACHozB,oBAAqB,uBAAuBtuB,KAAKC,UAAUsuB,EAAcrzB,YAEjF,IAAK,SACD,MAAO,CACHozB,oBAAqB,cAActuB,KAAKC,UAAUsuB,EAAcrzB,MAAM0pB,aAAa5kB,KAAKC,UAAUsuB,EAAcrzB,MAAM2pB,WAE9H,IAAK,MAAO,CAGR,MAAM8J,QAAsBruB,MAAKsuB,EAAsBL,EAAcrzB,MAAOqqB,GAe5E,MAAO,CAAE8I,gBAdmB9I,EAAM0H,UAAUC,YAAY,yBAA0B,CAC9ElG,oBAAqBhW,QAAO,IAAIK,KAC5B,MAAMnF,EAAS,IAAI/M,IACnB,IAAK,IAAI6C,EAAI,EAAGA,EAAIqP,EAAKvP,OAAQE,GAAK,EAClCkK,EAAO7M,IAAIgS,EAAKrP,GAAIqP,EAAKrP,EAAI,IAEjC,OAAOkK,CAAM,IAEjB0Z,cAAc,EACdvV,UAAWse,EACXxB,eAAe,EACfC,mBAAoB7H,EAAM6H,sBAGGlhB,OAAOmiB,SAC3C,CACD,IAAK,SAAU,CAGX,MAAMM,QAAsBruB,MAAKsuB,EAAsBL,EAAcrzB,MAAOqqB,GAiB5E,MAAO,CAAE8I,gBAhBmB9I,EAAM0H,UAAUC,YAAY,yBAA0B,CAC9ElG,oBAAqBhW,QAAO,IAAIK,KAC5B,MAAMnF,EAAS,CAAA,EACf,IAAK,IAAIlK,EAAI,EAAGA,EAAIqP,EAAKvP,OAAQE,GAAK,EAAG,CAGrCkK,EADYmF,EAAKrP,IACHqP,EAAKrP,EAAI,EAC1B,CACD,OAAOkK,CAAM,IAEjB0Z,cAAc,EACdvV,UAAWse,EACXxB,eAAe,EACfC,mBAAoB7H,EAAM6H,sBAGGlhB,OAAOmiB,SAC3C,CACD,IAAK,QAAS,CAGV,MAAMhd,QAAa/Q,MAAKuuB,EAAkBN,EAAcrzB,MAAOqqB,GAW/D,MAAO,CAAE8I,gBAVmB9I,EAAM0H,UAAUC,YAAY,yBAA0B,CAC9ElG,oBAAqBhW,QAAO,IAAIK,IACrBA,IAEXuU,cAAc,EACdvV,UAAWgB,EACX8b,eAAe,EACfC,mBAAoB7H,EAAM6H,sBAGGlhB,OAAOmiB,SAC3C,CACD,IAAK,MAAO,CAGR,MAAMhd,QAAa/Q,MAAKuuB,EAAkBN,EAAcrzB,MAAOqqB,GAW/D,MAAO,CAAE8I,gBAVmB9I,EAAM0H,UAAUC,YAAY,yBAA0B,CAC9ElG,oBAAqBhW,QAAO,IAAIK,IACrB,IAAIjS,IAAIiS,KAEnBuU,cAAc,EACdvV,UAAWgB,EACX8b,eAAe,EACfC,mBAAoB7H,EAAM6H,sBAGGlhB,OAAOmiB,SAC3C,CACD,IAAK,UAAW,CACZ,MAmCMS,SAnCkCvJ,EAAM0H,UAAUC,YAAY,yBAA0B,CAC1FlG,oBAAqBhW,QAAO,KACxB,MAAMwa,EAAQ,GACd,IAAIuD,EAAwB,KAC5B,MAAO,CAKHtgB,mBACI,MAAMugB,EAAYxD,EAAM1pB,OAAS,EAC3B4F,QAAQ6G,UACR,IAAI7G,SAAS6G,IACXwgB,EAAwBxgB,CAAO,IAGvC,aADMygB,EACCxD,EAAMM,OAChB,EAKDmD,YAAY7tB,GACRoqB,EAAMxuB,KAAKoE,GACmB,OAA1B2tB,IACAA,IACAA,EAAwB,KAE/B,EACJ,IAEL5B,eAAe,EACfC,mBAAoB7H,EAAM6H,mBAC1BI,wBAAwB,KAEoBthB,OAAOmiB,SAElD/tB,MAAK4uB,EAAqBX,EAAeO,EAAevJ,GAc7D,MAAO,CAAE8I,gBAb0B9I,EAAM0H,UAAUC,YAAY,yBAA0B,CACrFlG,oBAAqBhW,QAAQ8d,GAClBA,EAAcG,cAEzB5e,UAAW,CACP,CACIge,SAAUS,IAGlB3B,eAAe,EACfC,mBAAoB7H,EAAM6H,mBAC1BI,wBAAwB,KAEYthB,OAAOmiB,SAClD,CAED,QACI,MAAM,IAAIzyB,MAAM,SAASoE,KAAKC,UAAUsuB,6BAEnD,CACD9f,QAA4B0gB,EAAS5J,GACjC,MAAMoJ,EAAgB,GACtB,IAAK,MAAO/xB,EAAK1B,KAAUi0B,EAAS,CAChC,IAAIC,EAGAA,EAFe,iBAARxyB,EAEE,CAAE1B,MAAO0B,SAIH0D,MAAK6tB,EAAqBvxB,EAAK2oB,GAElD,MAAM8J,QAAiB/uB,MAAK6tB,EAAqBjzB,EAAOqqB,GACxDoJ,EAAc3xB,KAAKoyB,GACnBT,EAAc3xB,KAAKqyB,EACtB,CACD,OAAOV,CACV,CACDlgB,QAAwB6gB,EAAM/J,GAC1B,OAAO7d,QAAQ4N,IAAIga,EAAK9yB,KAAKtB,GAAUoF,MAAK6tB,EAAqBjzB,EAAOqqB,KAC3E,CACD9W,QAA2BiY,EAASoI,EAAevJ,GAC/C,MAAMgK,EAAY7I,EAAQxrB,MAAMwrB,QAGhC,OAAS,CACL,MAAMtlB,QAAgBmkB,EAAM0H,UAAUC,YAAY,yBAA0B,CACxElG,oBAAqBhW,QAAOvC,MAAOqgB,GAAkBA,EAAcU,eACnEnf,UAAW,CACP,CACIge,SAAUS,IAGlBlJ,cAAc,EACdwH,mBAAoB7H,EAAM6H,mBAC1BI,wBAAwB,IAE5BltB,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQvM,EAAcvC,OAAOE,WAAW6O,aACxC9pB,OAAQ,CACJ6gB,QAAS6I,EACTpxB,KAAMonB,EAAMkI,eAAersB,EAASslB,EAAQxrB,MAAM0rB,WAAa,QAC/DgJ,OAAQ,CACJrK,MAAOA,EAAMuI,QACb5I,QAASK,EAAMsK,qBAGxBtK,EAAMsK,kBACZ,CACJ,CACDphB,QAAoCqhB,EAAqBC,EAAYlK,EAAiBN,GAClF,MAAMyK,EAAaF,EAAoBG,YAAYD,WAAWxzB,KAAK0zB,IAAW,CAC1Ere,IAAKqe,EAAMre,IACXse,aAAcD,EAAMC,aAGpBC,WAAYF,EAAME,WAAaL,EAC/BM,aAAcH,EAAMG,iBAElBC,QAAkBhwB,KAAKiwB,mBAE7BT,EAAoBQ,UAAWzK,EAAiBN,GAC1CiL,QAAa3D,EAAgB4D,gBAAgBX,EAAoBQ,UAAW/K,GAClF,MAAO,CACH+K,YACAD,aAAcP,EAAoBO,aAGlCD,WAAYN,EAAoBM,WAAaL,EAC7CE,WAAY,CACRD,WAAYA,GAAc,IAE9BQ,KAAMA,GAAQV,EAAoBU,KAEzC,EAELv1B,EAAA4xB,gBAA0BA,MC3Y1B9xB,OAAOC,eAAeuqB,EAAS,aAAc,CAAErqB,OAAO,IACzCqqB,EAAAmL,WAAG,EAChB,MAAMC,EAAuB9wB,EA4KhB0lB,EAAAmL,MA3Kb,MACIE,GACAC,GACA/C,GACA+B,GACAzC,GACA0D,GACAjtB,GACAopB,GACAF,GACAgE,GACA1L,QACA2L,aACA7wB,YAAYywB,EAAcC,EAAwB/C,EAAS+B,EAAmBzC,EAAoB0D,EAAQjtB,EAAMwhB,EAAS2L,EAAc/D,EAAWF,GAC9IzsB,MAAKwtB,EAAWA,EAChBxtB,MAAKuvB,EAAqBA,EAC1BvvB,MAAK8sB,EAAsBA,EAC3B9sB,KAAK+kB,QAAUA,EACf/kB,MAAKwwB,EAAUA,EACfxwB,MAAKuD,EAAQA,EACbvD,KAAK0wB,aAAeA,EACpB1wB,MAAK2sB,EAAaA,EAClB3sB,MAAKswB,EAAgBA,EACrBtwB,MAAKuwB,EAA0BA,EAC/BvwB,MAAKysB,EAAgBA,EACrBzsB,MAAKywB,EAAmB,IAAIJ,EAAqB9D,gBAAgBvsB,MAAKysB,GACtEzsB,MAAKswB,EAAcK,SAAS5xB,IAAIiB,MAAKwtB,EAAUxtB,KAClD,CACDmO,aAAagV,GAET,GAAInjB,MAAKswB,EAAcM,oBAAoB3Y,IAAIkL,KAAYnjB,KAAKwtB,QAAhE,CAGA,UACUxtB,KAAK2sB,UAAUC,YAAY,wBAAyB,CACtDmB,SAAU5K,GASjB,CAND,MAAOhnB,GAGH,IAAkB,OAAZA,EAAEgF,MAAiC,6BAAdhF,EAAE2E,QACzB,MAAM3E,CAEb,CACD6D,MAAKswB,EAAcM,oBAAoBC,OAAO1N,EAb7C,CAcJ,CACDgK,eAAe2D,EAAUvL,GACrB,MAAM0H,EAAoB6D,EAASllB,OAAOmlB,eACpCC,EAAYhxB,KAAKixB,qBAAqBhE,GAC5C,GAAI6D,EAASllB,OAAOmiB,SAAU,CAC1B,MAAMA,EAAW+C,EAASllB,OAAOmiB,SACT,SAApBxI,GAGAyL,EAAU7N,OAAS4K,EAEnB/tB,MAAKswB,EAAcM,oBAAoB7xB,IAAIgvB,EAAU/tB,KAAKwtB,UAIrDxtB,KAAK2sB,UAAUC,YAAY,wBAAyB,CAAEmB,YAElE,CACD,OAAOiD,CACV,CACDC,qBAAqBF,GAGjB,MAAMnlB,EAASmlB,EAGf,GAAoB,mBAAhBnlB,EAAOrI,KACP,MAAO,CAAEA,KAAM,UAEnB,MAAMytB,EAAYplB,EAAOhR,MACzB,QAAkBkD,IAAdkzB,EACA,OAAOplB,EAEX,GAAoB,SAAhBA,EAAOrI,OACH9I,OAAOy2B,OAAOF,EAAW,mBAEzBA,EAAU/N,SAAW,GAAGjjB,KAAKkuB,cAAcmC,EAAqB7D,oBAAoBwE,EAAU5C,uBACvF4C,EAAyB,eAEhCv2B,OAAOy2B,OAAOF,EAAW,aACzB,IAAK,MAAMtvB,KAAKsvB,EAAUG,SACtBH,EAAUG,SAASzvB,GAAK1B,KAAKixB,qBAAqBD,EAAUG,SAASzvB,IAKjF,GAAI,CAAC,QAAS,OAAOwB,SAAS6tB,EAAextB,MACzC,IAAK,MAAM7B,KAAKsvB,EACZA,EAAUtvB,GAAK1B,KAAKixB,qBAAqBD,EAAUtvB,IAG3D,GAAI,CAAC,SAAU,OAAOwB,SAAS6tB,EAAextB,MAC1C,IAAK,MAAM7B,KAAKsvB,EACZA,EAAUtvB,GAAK,CACX1B,KAAKixB,qBAAqBD,EAAUtvB,GAAG,IACvC1B,KAAKixB,qBAAqBD,EAAUtvB,GAAG,KAInD,OAAOkK,CACV,CACDwlB,SACI,MAAO,CACHnM,MAAOjlB,KAAKwtB,QACZgD,OAAQxwB,KAAKwwB,OACbjtB,KAAMvD,KAAKuD,KACXqhB,QAAS5kB,KAAKuvB,0BACOzxB,IAAjBkC,KAAK+kB,QAAwB,CAAA,EAAK,CAAEA,QAAS/kB,KAAK+kB,SAE7D,CACGyI,cACA,OAAOxtB,MAAKwtB,CACf,CACGU,kBACA,OAAQluB,MAAKuwB,EAAwBc,YAAYrxB,MAAKuvB,IAChDrB,aAAe,SACxB,CACGqB,wBACA,OAAOvvB,MAAKuvB,CACf,CACGzC,yBACA,OAAO9sB,MAAK8sB,CACf,CACG0D,aACA,OAAOxwB,MAAKwwB,CACf,CACGjtB,WACA,OAAOvD,MAAKuD,CACf,CACGopB,gBACA,OAAO3sB,MAAK2sB,CACf,CACDxe,mBAAmBuY,EAAqB+G,EAAOC,EAAYpI,EAAcC,GACrE,MAAMX,EAAU5kB,MAAKuwB,EAAwBe,WAAWtxB,KAAKuvB,mBAE7D,aADM3K,EAAQ2M,iBACP,CACH3lB,aAAc5L,MAAKywB,EAAiBe,aAAaxxB,KAAM0mB,EAAqB+G,EAAOC,EAAYpI,EAAcC,GAEpH,CACDpX,qBAAqBkX,EAAYC,EAAcC,GAC3C,MAAMX,EAAU5kB,MAAKuwB,EAAwBe,WAAWtxB,KAAKuvB,mBAE7D,aADM3K,EAAQ2M,iBACP,CACH3lB,aAAc5L,MAAKywB,EAAiBgB,eAAezxB,KAAMqlB,EAAYC,EAAcC,GAE1F,CAODpX,yBAAyBue,EAAWnH,GAChC,OAAOvlB,MAAKywB,EAAiBR,mBAAmBvD,EAAWnH,EAAiBvlB,KAC/E,CAODmO,sBAAsBue,GAClB,OAAO2D,EAAqB9D,gBAAgB4D,gBAAgBzD,EAAW1sB,KAC1E,GC5KLvF,OAAOC,eAAeg3B,EAAS,aAAc,CAAE92B,OAAO,IAC3B82B,EAAAC,yBAAG,EAC9B,MAAMC,EAAuBryB,EACvBsjB,EAAgB5gB,EAChB8oB,EAAW5f,EACX0mB,EAAgBzmB,EAChB0mB,EAAazmB,EACnB,MAAMsmB,EAEFtE,GAKA0E,GAKAZ,GAAY,IAAItyB,IAChB0xB,GACAyB,GAAU,CACNC,oBAAqB,IAAIJ,EAAc/F,SACvCoG,KAAM,CACFC,wBAAyB,IAAIN,EAAc/F,SAC3CsG,eAAgB,CACZC,iBAAkB,IAAIR,EAAc/F,SACpCwG,KAAM,IAAIT,EAAc/F,YAIpCva,GAAO,cACPkb,GACA6D,GACAiC,GAAY,KACZC,GACAC,GACAzH,GACAnrB,YAAY2yB,EAAWlC,EAAcjD,EAAW0E,EAAUtF,EAAc8D,EAAwBvF,GAC5FhrB,MAAKwyB,EAAaA,EAClBxyB,MAAKswB,EAAgBA,EACrBtwB,MAAKqtB,EAAaA,EAClBrtB,MAAK+xB,EAAYA,EACjB/xB,MAAKysB,EAAgBA,EACrBzsB,MAAKuwB,EAA0BA,EAC/BvwB,MAAKgrB,EAAUA,EACfhrB,MAAK0yB,GACR,CACDlsB,cAAcgsB,EAAWlC,EAAcjD,EAAW0E,EAAUtF,EAAc8D,EAAwBvF,GAC9F,MAAMpG,EAAU,IAAI+M,EAAoBa,EAAWlC,EAAcjD,EAAW0E,EAAUtF,EAAc8D,EAAwBvF,GAM5H,OALAuF,EAAuBoC,WAAW/N,GAClC6H,EAAa0C,cAAc,CACvBC,OAAQvM,EAActC,gBAAgBC,WAAWoS,oBACjDrtB,OAAQqf,EAAQiO,wBACjBjO,EAAQyI,WACJzI,CACV,CAIGsJ,kBACA,OAAOluB,MAAKuyB,CACf,CACD1B,SAMI,GALA7wB,MAAK8yB,IACL9yB,MAAKswB,EAAcyC,aAAa,CAC5BxD,kBAAmBvvB,KAAKqtB,YAGN,OAAlBrtB,KAAK+xB,SAAmB,CACT/xB,MAAKuwB,EAAwBe,WAAWtxB,KAAK+xB,WACrDZ,EAAUN,OAAO7wB,KAAKqtB,UAChC,CACDrtB,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQvM,EAActC,gBAAgBC,WAAWwS,sBACjDztB,OAAQvF,KAAK6yB,wBACd7yB,KAAKqtB,WACRrtB,MAAKuwB,EAAwB0C,cAAcjzB,KAAKqtB,UACnD,CAEGA,gBACA,OAAOrtB,MAAKqtB,CACf,CAEG0E,eACA,OAAO/xB,MAAK+xB,CACf,CAEGZ,eACA,OAAO5yB,MAAM2Z,KAAKlY,MAAKmxB,EAAU1W,SACpC,CAKDyY,oBACI,OAA0B,OAAnBlzB,MAAK+xB,CACf,CACDoB,SAASC,GACLpzB,MAAKmxB,EAAUpyB,IAAIq0B,EAAM/F,UAAW+F,EACvC,CACDN,KACI9yB,KAAKmxB,SAASj1B,KAAKk3B,GAAUA,EAAMvC,UACtC,CACGwC,QACA,QAAgCv1B,IAA5BkC,MAAKyyB,EACL,MAAM,IAAIn3B,MAAM,yCAAyC0E,MAAKqtB,KAElE,OAAOrtB,MAAKyyB,CACf,CACGD,gBACA,OAAOxyB,MAAKwyB,CACf,CACDc,gBAAgBd,GACZxyB,MAAKwyB,EAAaA,EAClBxyB,MAAK0yB,GACR,CACGnhB,UACA,OAAOvR,MAAKuR,CACf,CACDpD,0BACUnO,MAAKgyB,EAAQE,KAAKE,eAAeE,IAC1C,CACDf,iBACI,OAAOvxB,MAAKwyB,EAAWe,eAC1B,CACDplB,yBAAyB4W,GACrB,QAAgBjnB,IAAZinB,GAAqC,KAAZA,EACzB,OAAO/kB,MAAKqzB,EAEhB,IAAIG,EAAiBxzB,MAAKswB,EAAcmD,WAAW,CAC/ClE,kBAAmBvvB,KAAKqtB,UACxBtI,YAcJ,GAZ8B,IAA1ByO,EAAehyB,eACTxB,MAAKwyB,EAAW7F,UAAUC,YAAY,2BAA4B,CACpE8G,QAAS1zB,KAAKqtB,UACdsG,UAAW5O,IAIfyO,EAAiBxzB,MAAKswB,EAAcmD,WAAW,CAC3ClE,kBAAmBvvB,KAAKqtB,UACxBtI,aAGsB,IAA1ByO,EAAehyB,OACf,MAAMlG,MAAM,WAAWypB,qBAE3B,OAAOyO,EAAe,EACzB,CACDX,qBAAqBxM,EAAW,EAAGuN,GAAiB,GAChD,MAAO,CACHhP,QAAS5kB,MAAKqtB,EACd9b,IAAKvR,KAAKuR,IACV4f,SAAU9K,EAAW,EACfrmB,KAAKmxB,SAASj1B,KAAK23B,GAAMA,EAAEhB,qBAAqBxM,EAAW,GAAG,KAC9D,QACFuN,EAAiB,CAAEroB,OAAQvL,MAAK+xB,GAAc,CAAA,EAEzD,CACDW,KACI1yB,MAAKwyB,EAAW7F,UAAU3C,GAAG,4BAA6BzkB,IAClDvF,KAAKqtB,YAAc9nB,EAAOuuB,WAAWC,WAGzC/zB,MAAKuR,EAAOhM,EAAOuuB,WAAWviB,IAAG,IAErCvR,MAAKwyB,EAAW7F,UAAU3C,GAAG,uBAAwBzkB,IAC7CvF,KAAKqtB,YAAc9nB,EAAOqqB,MAAM1O,KAGpClhB,MAAKuR,EAAOhM,EAAOqqB,MAAMre,KAAOhM,EAAOqqB,MAAMoE,aAAe,IAI5Dh0B,MAAK8yB,IAAiB,IAE1B9yB,MAAKwyB,EAAW7F,UAAU3C,GAAG,gCAAiCzkB,IACtDvF,KAAKqtB,YAAc9nB,EAAOmuB,UAG9B1zB,MAAKuR,EAAOhM,EAAOgM,IACnBvR,MAAKgyB,EAAQE,KAAKC,wBAAwBlkB,QAAQ1I,GAAO,IAE7DvF,MAAKwyB,EAAW7F,UAAU3C,GAAG,uBAAwBzkB,IACjD,GAAIvF,KAAKqtB,YAAc9nB,EAAOmuB,QAC1B,OAMJ,MAAMO,GAAY,IAAIj1B,MAAOyV,UAK7B,GAJoB,SAAhBlP,EAAO/E,OACPR,MAAKk0B,EAAiB3uB,EAAOgtB,UAC7BvyB,MAAKgyB,EAAQC,oBAAoBhkB,WAEjB,WAAhB1I,EAAO/E,MAIX,GAAI+E,EAAOgtB,WAAavyB,MAAKuyB,EAG7B,OAAQhtB,EAAO/E,MACX,IAAK,mBACDR,MAAKgyB,EAAQE,KAAKE,eAAeC,iBAAiBpkB,QAAQ1I,GAC1DvF,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQvM,EAActC,gBAAgBC,WAAW2T,sBACjD5uB,OAAQ,CACJqf,QAAS5kB,KAAKqtB,UACd+G,WAAYp0B,MAAKuyB,EACjB0B,YACA1iB,IAAKvR,MAAKuR,IAEfvR,KAAKqtB,WACR,MACJ,IAAK,OACDrtB,MAAKgyB,EAAQE,KAAKE,eAAeE,KAAKrkB,QAAQ1I,GAC9CvF,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQvM,EAActC,gBAAgBC,WAAW6T,UACjD9uB,OAAQ,CACJqf,QAAS5kB,KAAKqtB,UACd+G,WAAYp0B,MAAKuyB,EACjB0B,YACA1iB,IAAKvR,MAAKuR,IAEfvR,KAAKqtB,iBA7BZrtB,MAAKuyB,EAAYhtB,EAAOgtB,QA+B3B,IAELvyB,MAAKwyB,EAAW7F,UAAU3C,GAAG,mCAAoCzkB,IAC7D,GAAIA,EAAOqf,QAAQ0P,QAAQZ,UAAY1zB,KAAKqtB,UACxC,OAGJ,IAAK,CAAC,UAAW,YAAYnqB,SAASqC,EAAOqf,QAAQ0P,QAAQ/wB,MACzD,OAEJ,MAAM0hB,EAAQ,IAAI6M,EAAW1B,MAAMpwB,MAAKswB,EAAetwB,MAAKuwB,EAAyBhrB,EAAOqf,QAAQ2P,SAAUv0B,KAAKqtB,UAAW9nB,EAAOqf,QAAQ1D,GAAIlhB,MAAKw0B,EAAWjvB,GAEjK,SAEgC,aAAhCA,EAAOqf,QAAQ0P,QAAQ/wB,KACjBgC,EAAOqf,QAAQpkB,UACf1C,EAAWkC,MAAKwyB,EAAW9B,aAAc1wB,MAAKwyB,EAAW7F,UAAW3sB,MAAKysB,GAC3ElnB,EAAOqf,QAAQ0P,QAAQG,YACvBz0B,MAAKyyB,EAAqBxN,EAC7B,IAELjlB,MAAKwyB,EAAW7F,UAAU3C,GAAG,qCAAsCzkB,IAC/DvF,MAAKswB,EAAcyC,aAAa,CAC5BrC,aAAc1wB,MAAKwyB,EAAW9B,aAC9B5D,mBAAoBvnB,EAAOunB,oBAC7B,IAEN9sB,MAAKwyB,EAAW7F,UAAU3C,GAAG,oCAAoC,KAC7DhqB,MAAKswB,EAAcyC,aAAa,CAC5BrC,aAAc1wB,MAAKwyB,EAAW9B,cAChC,GAET,CACD8D,GAAWjvB,GACP,MAAoC,aAAhCA,EAAOqf,QAAQ0P,QAAQ/wB,KAGhBvD,MAAKqzB,EAAc7C,OAGvB,CAAC,MAAO,IAAIttB,SAASqC,EAAOqf,QAAQ4L,QACrC,OACAjrB,EAAOqf,QAAQ4L,MACxB,CACD0D,GAAiB3B,QAEIz0B,IAAby0B,GAA0BvyB,MAAKuyB,IAAcA,GAO7CvyB,MAAKgyB,EAAQC,oBAAoBlG,WACjC/rB,MAAKgyB,EAAQC,oBAAsB,IAAIJ,EAAc/F,SAGrD9rB,MAAKgrB,IAAUD,EAASH,QAAQ8J,iBAAkB,oBAElD10B,MAAKgyB,EAAQE,KAAKE,eAAeC,iBAAiBtG,WAClD/rB,MAAKgyB,EAAQE,KAAKE,eAAeC,iBAC7B,IAAIR,EAAc/F,SAGtB9rB,MAAKgrB,IAAUD,EAASH,QAAQ8J,iBAAkB,oBAElD10B,MAAKgyB,EAAQE,KAAKE,eAAeE,KAAKvG,WACtC/rB,MAAKgyB,EAAQE,KAAKE,eAAeE,KAC7B,IAAIT,EAAc/F,SAGtB9rB,MAAKgrB,IAAUD,EAASH,QAAQ8J,iBAAkB,oBAEtD10B,MAAKuyB,EAAYA,GA1BTvyB,MAAKgyB,EAAQE,KAAKC,wBAAwBpG,aAC1C/rB,MAAKgyB,EAAQE,KAAKC,wBACd,IAAIN,EAAc/F,SAyBjC,CACD3d,eAAeoD,EAAK0V,SACVjnB,KAAKuxB,iBAEX,MAAMoD,QAA0B30B,MAAKwyB,EAAW7F,UAAUC,YAAY,gBAAiB,CACnFrb,MACAmiB,QAAS1zB,KAAKqtB,YAElB,GAAIsH,EAAkBC,UAClB,MAAM,IAAI/R,EAAcjC,QAAQyB,sBAAsBsS,EAAkBC,WAI5E,OAFA50B,MAAKk0B,EAAiBS,EAAkBpC,UAEhCtL,GACJ,IAAK,OACD,MACJ,IAAK,mBAEkCnpB,IAA/B62B,EAAkBpC,eACZvyB,MAAKgyB,EAAQE,KAAKC,8BAGlBnyB,MAAKgyB,EAAQE,KAAKE,eAAeC,iBAE3C,MACJ,IAAK,gBAEkCv0B,IAA/B62B,EAAkBpC,eACZvyB,MAAKgyB,EAAQE,KAAKC,8BAGlBnyB,MAAKgyB,EAAQE,KAAKE,eAAeE,KAInD,MAAO,CACH1mB,OAAQ,CACJwoB,WAAYO,EAAkBpC,UAAY,KAC1ChhB,OAGX,CACDpD,0BACI,OAASvC,SAAgBxE,QAAQ4N,IAAI,CAIjChV,MAAKwyB,EAAW7F,UAAUC,YAAY,qBACtC5sB,MAAKwyB,EAAW7F,UAAUC,YAAY,yBAA0B,CAAA,KAEpE,MAAO,CACHhhB,OAAQ,CACJ/N,KAAM+N,EAAO/N,MAGxB,CACDsQ,YAAY5I,GACR,MAAMsvB,EAAsB,CACxBC,gBAAiBvvB,EAAOkjB,WACxBsM,UAAkC,cAAvBxvB,EAAOojB,YAClBV,WAAY1iB,EAAO0iB,YAAY3qB,KAAK,MAAQ,GAC5CurB,MAAOtjB,EAAOsjB,MACdmM,mBAAoBzvB,EAAOujB,aAE3BvjB,EAAOmjB,QAAQZ,SACf+M,EAAoBI,cAAe,EAAIrD,EAAqBjG,cAAcpmB,EAAOmjB,OAAOZ,SAExFviB,EAAOmjB,QAAQrP,OACfwb,EAAoBK,YAAa,EAAItD,EAAqBjG,cAAcpmB,EAAOmjB,OAAOrP,OAEtF9T,EAAOmjB,QAAQpP,QACfub,EAAoBM,aAAc,EAAIvD,EAAqBjG,cAAcpmB,EAAOmjB,OAAOpP,QAEvF/T,EAAOmjB,QAAQX,MACf8M,EAAoBO,WAAY,EAAIxD,EAAqBjG,cAAcpmB,EAAOmjB,OAAOX,MAErFxiB,EAAOqjB,MAAMjB,SACbkN,EAAoBQ,aAAc,EAAIzD,EAAqBjG,cAAcpmB,EAAOqjB,KAAKjB,SAErFpiB,EAAOqjB,MAAMhB,QACbiN,EAAoBS,YAAa,EAAI1D,EAAqBjG,cAAcpmB,EAAOqjB,KAAKhB,QAGxF,MAAO,CACHhc,OAAQ,CACJ/N,YAHamC,MAAKwyB,EAAW7F,UAAUC,YAAY,kBAAmBiI,IAGzDh3B,MAGxB,CACDsQ,uBAAuB5I,GAMnB,MAAO,CACHqG,OAAQ,CACJoa,cAPahmB,MAAKwyB,EAAW7F,UAAUC,YAAY,wCAAyC,CAEhG0C,OAAQ,IAAI/pB,EAAO8f,iBACnBsO,UAAWpuB,EAAOwf,WAICwQ,YAG1B,EAEsB7D,EAAAC,oBAAGA,qBCtZ9Bl3B,OAAOC,eAAe86B,EAAS,aAAc,CAAE56B,OAAO,IACtD46B,EAAAC,oBAA8BD,EAAAE,yBAA8B,EAC5D,MAAMC,EAAa,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MACxD,SAASC,EAAmBC,GACxB,OAAOF,EAAWG,MAAMC,GAASF,EAAI3yB,SAAS6yB,IAClD,CAKA,SAASL,EAAoB3kB,GACzB,IAAIilB,EAAS,GACb,MAAMC,EAAYllB,EAAK,GAAGnW,MAAM6C,WAC1By4B,EAAYnlB,EAAKhL,MAAM,OAAGjI,GAC1Bq4B,EAASF,EAAUljB,MAAM,IAAI7B,OAAOykB,EAAWz5B,KAAK65B,GAAS,IAAIA,OAASz4B,KAAK,KAAM,MAC3F,IAAK,MAAM84B,KAASD,EAChB,QAAcr4B,IAAVs4B,GAAiC,KAAVA,EAG3B,GAAIR,EAAmBQ,GAAQ,CAC3B,MAAMja,EAAM+Z,EAAU1K,QAEtB,QAAY1tB,IAARqe,EACA,MAAM,IAAI7gB,MAAM,4BAA4Bm6B,EAAoB1kB,GAAM,OAE5D,OAAVqlB,EACAJ,GAAUK,EAAcla,GAET,OAAVia,GAA4B,OAAVA,EACN,WAAbja,EAAI5Y,MACS,WAAb4Y,EAAI5Y,MACS,WAAb4Y,EAAI5Y,KACJyyB,GAAU9iB,SAASiJ,EAAIvhB,MAAM6C,WAAY,IAGzCu4B,GAAU,MAGC,OAAVI,EACY,WAAbja,EAAI5Y,MACS,WAAb4Y,EAAI5Y,MACS,WAAb4Y,EAAI5Y,KACJyyB,GAAUM,WAAWna,EAAIvhB,MAAM6C,YAG/Bu4B,GAAU,MAKdA,GAAUO,EAAOpa,EAExB,MAEG6Z,GAAUI,EAIlB,GAAIF,EAAU10B,OAAS,EACnB,MAAM,IAAIlG,MAAM,4BAA4Bm6B,EAAoB1kB,GAAM,OAE1E,OAAOilB,CACX,CAkBA,SAASO,EAAOpa,GAEZ,GAAiB,UAAbA,EAAI5Y,MACS,WAAb4Y,EAAI5Y,MACS,SAAb4Y,EAAI5Y,MACS,WAAb4Y,EAAI5Y,MACS,WAAb4Y,EAAI5Y,MACS,WAAb4Y,EAAI5Y,KACJ,OAAO8yB,EAAcla,GAEzB,GAAiB,WAAbA,EAAI5Y,KACJ,MAAO,GAAG4Y,EAAIvhB,MAAM6C,cAExB,GAAiB,WAAb0e,EAAI5Y,KACJ,OAAO4Y,EAAIvhB,MAAM6C,WAErB,GAAI,CAAC,OAAQ,UAAUyF,SAASiZ,EAAI5Y,MAChC,OAAO7D,KAAKC,UAAUwc,EAAIvhB,OAE9B,GAAiB,WAAbuhB,EAAI5Y,KACJ,MAAO,IAAI4Y,EAAIvhB,MACVsB,KAAK6K,GACC,GAAGrH,KAAKC,UAAUoH,EAAK,OAAOwvB,EAAOxvB,EAAK,QAEhDzJ,KAAK,QAEd,GAAiB,UAAb6e,EAAI5Y,KACJ,MAAO,IAAI4Y,EAAIvhB,OAAOsB,KAAKjB,GAAQs7B,EAAOt7B,KAAMqC,KAAK,MAAQ,MAEjE,MAAMhC,MAAM,uBAAuB6gB,EAAI1e,aAC3C,CACA,SAAS44B,EAAcla,GACnB,IAAK1hB,OAAOy2B,OAAO/U,EAAK,SACpB,OAAOA,EAAI5Y,KAEf,OAAQ4Y,EAAI5Y,MACR,IAAK,SACL,IAAK,SACL,IAAK,UACL,IAAK,SACD,OAAOmN,OAAOyL,EAAIvhB,OACtB,IAAK,SACD,MAAO,IAAIuhB,EAAIvhB,MAAM0pB,WAAWnI,EAAIvhB,MAAM2pB,OAAS,KACvD,IAAK,OACD,OAAO,IAAIvlB,KAAKmd,EAAIvhB,OAAO6C,WAC/B,IAAK,SACD,MAAO,UAAU0e,EAAIvhB,OAAO4G,QAAU,MAC1C,IAAK,QACD,MAAO,SAAS2a,EAAIvhB,OAAO4G,QAAU,MACzC,IAAK,MACD,MAAO,OAAO2a,EAAIvhB,MAAM4G,UAC5B,IAAK,MACD,MAAO,OAAO2a,EAAIvhB,MAAM4G,UAC5B,IAAK,OACD,MAAO,OACX,QACI,OAAO2a,EAAI5Y,KAEvB,CACA,SAASkyB,EAAoB1kB,EAAMylB,GAC/B,MAAMra,EAAMpL,EAAK,GACjB,OAAKoL,EAIY,WAAbA,EAAI5Y,MACJqyB,EAAmBzZ,EAAIvhB,MAAM6C,aAC7B+4B,EACOd,EAAoB3kB,GAGxBA,EACF7U,KAAKigB,GACCka,EAAcla,KAEpB7e,KAAK,KAbC,EAcf,CA7F2Bk4B,EAAAE,oBAAGA,EA8FHF,EAAAC,oBAAGA,EC7K9Bh7B,OAAOC,eAAe+7B,EAAS,aAAc,CAAE77B,OAAO,IACpC67B,EAAAC,gBAAG,EACrB,MAAM7T,EAAgBtjB,EAChBo3B,GAAiB10B,EAEvB,SAAS20B,GAAkBC,GACvB,MAAMC,EAAcD,GAAenH,WAAWxzB,KAAK66B,IACxC,CACHhH,aAAcgH,EAAUhH,aACxBF,aAAckH,EAAUlH,aACxBC,WAAYiH,EAAUjH,WACtBve,IAAKwlB,EAAUxlB,QAGvB,OAAOulB,EAAc,CAAEpH,WAAYoH,QAAgBh5B,CACvD,CAaA,MAAM44B,GACFjK,GACA6D,GACAkC,GACA3yB,YAAY2yB,EAAWlC,EAAc7D,GACjCzsB,MAAKwyB,EAAaA,EAClBxyB,MAAKswB,EAAgBA,EACrBtwB,MAAKysB,EAAgBA,CACxB,CACDjmB,cAAcgsB,EAAWlC,EAAc7D,GACnC,MAAMgK,EAAa,IAAIC,GAAWlE,EAAWlC,EAAc7D,GAE3D,OADAgK,GAAWO,IACJP,CACV,CACDO,KACIh3B,MAAKi3B,GACR,CACDA,KACIj3B,MAAKwyB,EAAW7F,UAAU3C,GAAG,4BAA6BzkB,IAGtD,MAAM0f,EAAQjlB,MAAKswB,EAAc4G,UAAU,CACvCxG,aAAc1wB,MAAKwyB,EAAW9B,aAC9B5D,mBAAoBvnB,EAAOunB,qBAEzBqK,OAAwBr5B,IAAVmnB,EACd7d,QAAQ6G,QAAQ1I,EAAOwL,MAErB3J,QAAQ4N,IAAIzP,EAAOwL,KAAK7U,KAAKigB,GAClB8I,EAAMgL,mBAAmB9T,EAAK,WAEjDnc,MAAKysB,EAAc2K,qBAAqBD,EAAYz4B,MAAMqS,IAAU,OAChEqe,OAAQvM,EAAcpC,IAAID,WAAW6W,mBACrC9xB,OAAQ,CACJ+xB,OA9CCC,EA8CkBhyB,EAAOhC,KA7CtC,CAAC,SAAU,SAASL,SAASq0B,GACtB,QAEP,CAAC,QAAS,SAASr0B,SAASq0B,GACrB,QAEP,CAAC,OAAQ,WAAWr0B,SAASq0B,GACtB,OAEJ,QAqCSjI,OAAQ,CACJrK,MAAOA,GAAOuI,SAAW,UACzB5I,QAASK,GAAOsK,mBAAqB,WAEzCW,MAAM,EAAIyG,GAAelB,qBAAqB1kB,GAAM,GACpDkjB,UAAWh3B,KAAKu6B,MAAMjyB,EAAO0uB,WAC7BtE,WAAYiH,GAAkBrxB,EAAOoqB,YACrCpsB,KAAM,UAEN6rB,OAAwB,YAAhB7pB,EAAOhC,KAAqB,OAASgC,EAAOhC,KACpDwN,SAzDpB,IAAqBwmB,CA2DP,IAAGtS,GAAOsK,mBAAqB,UAAW1M,EAAcpC,IAAID,WAAW6W,mBAAmB,IAEhGr3B,MAAKwyB,EAAW7F,UAAU3C,GAAG,2BAA4BzkB,IAGrD,MAAM0f,EAAQjlB,MAAKswB,EAAc4G,UAAU,CACvCxG,aAAc1wB,MAAKwyB,EAAW9B,aAC9B5D,mBAAoBvnB,EAAO+nB,iBAAiBR,qBAG1C2K,EAAc,UACXlyB,EAAO+nB,iBAAiB0C,eAGflyB,IAAVmnB,EACOvlB,KAAKC,UAAU4F,EAAO+nB,iBAAiB0C,WAE3C/K,EAAMkL,gBAAgB5qB,EAAO+nB,iBAAiB0C,WAL1CzqB,EAAO+nB,iBAAiB4C,KAFnB,GASpBlwB,MAAKysB,EAAc2K,qBAAqBK,EAAY/4B,MAAMwxB,IAAU,CAChEd,OAAQvM,EAAcpC,IAAID,WAAW6W,mBACrC9xB,OAAQ,CACJ+xB,MAAO,QACPhI,OAAQ,CACJrK,MAAOA,GAAOuI,SAAW,UACzB5I,QAASK,GAAOsK,mBAAqB,WAEzCW,OACA+D,UAAWh3B,KAAKu6B,MAAMjyB,EAAO0uB,WAC7BtE,WAAYiH,GAAkBrxB,EAAO+nB,iBAAiBqC,YACtDpsB,KAAM,kBAET0hB,GAAOsK,mBAAqB,UAAW1M,EAAcpC,IAAID,WAAW6W,mBAAmB,GAEnG,EAEaZ,EAAAC,WAAGA,mBC/FrBj8B,OAAOC,eAAeg9B,GAAS,aAAc,CAAE98B,OAAO,IACpC88B,GAAAC,gBAAG,EAMrB,MAAMA,WAAmB94B,IAErB+4B,GACA/3B,YAAY+3B,EAAiB5d,GACzBja,MAAMia,GACNha,MAAK43B,EAAmBA,CAC3B,CACD3f,IAAI3b,GAIA,OAHK0D,KAAKoY,IAAI9b,IACV0D,KAAKjB,IAAIzC,EAAK0D,MAAK43B,EAAiBt7B,IAEjCyD,MAAMkY,IAAI3b,EACpB,EAEau7B,GAAAF,WAAGA,aCpBrBl9B,OAAOC,eAAeo9B,GAAS,aAAc,CAAEl9B,OAAO,IAChCk9B,GAAAC,oBAAG,EACzB,MAAMC,GAAaz4B,EACb04B,GAAah2B,EACnB,MAAM81B,GACFvxB,SAAkB,UAClB0xB,UACAzL,GACA0L,GACAC,GACAC,GACAC,GACAC,GACAC,GACA34B,YAAYq4B,EAAWzL,GACnBzsB,KAAKk4B,UAAYA,EACjBl4B,MAAKysB,EAAgBA,EACrBzsB,MAAKu4B,EAA6B,IAAIP,GAAWlM,SACjD9rB,MAAKw4B,EAA4B,IAAIR,GAAWlM,QACnD,CACD2M,yBAAyBN,GACrB,QAAqCr6B,IAAjCkC,MAAKm4B,EACL,MAAM,IAAI78B,MAAM,yCAEpB0E,MAAKm4B,EAA0BA,OACer6B,IAA1CkC,MAAKo4B,GACLp4B,MAAKu4B,EAA2BtqB,UAEpCjO,MAAK04B,GACR,CACDC,kCAAkCP,GAC9B,QAA8Ct6B,IAA1CkC,MAAKo4B,EACL,MAAM,IAAI98B,MAAM,kDAEpB0E,MAAKo4B,EAAmCA,OACHt6B,IAAjCkC,MAAKm4B,GACLn4B,MAAKu4B,EAA2BtqB,SAEvC,CACD2qB,wBAAwBP,GACpB,QAAoCv6B,IAAhCkC,MAAKq4B,EACL,MAAM,IAAI/8B,MAAM,wCAEpB0E,MAAKq4B,EAAyBA,OACev6B,IAAzCkC,MAAKs4B,GACLt4B,MAAKw4B,EAA0BvqB,UAEnCjO,MAAK64B,GACR,CACDC,iCAAiCR,GAC7B,QAA6Cx6B,IAAzCkC,MAAKs4B,EACL,MAAM,IAAIh9B,MAAM,iDAEpB0E,MAAKs4B,EAAkCA,OACHx6B,IAAhCkC,MAAKq4B,GACLr4B,MAAKw4B,EAA0BvqB,SAEtC,CACD8qB,qBAAqBC,GACjBh5B,MAAKu4B,EAA2BtqB,UAChCjO,MAAKw4B,EAA0BxM,OAAOgN,GACtC,MAAMzzB,EAAS,IACRvF,MAAKi5B,IACRrE,UAAWoE,EAAmBpE,WAElC50B,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQ6I,GAAWvX,QAAQF,WAAW0Y,gBACtC3zB,UACDvF,MAAKm4B,GAAyBzE,SAAW,KAC/C,CACDgF,KACS14B,MAAKm5B,KACNn5B,MAAKysB,EAAc2K,qBAAqBp3B,MAAKu4B,EAA2B75B,MAAK,IAAMsB,MAAKo5B,OAA2Bp5B,MAAKm4B,GAAyBzE,SAAW,KAAMuE,GAAWvX,QAAQF,WAAW6Y,uBAEvM,CACDD,MACI,QAAqCt7B,IAAjCkC,MAAKm4B,EACL,MAAM,IAAI78B,MAAM,qCAEpB,MAAMiK,EAAS,IACRvF,MAAKi5B,IACRK,UAAW,CAAE/1B,KAAMvD,MAAKu5B,OAE5B,MAAO,CACHnK,OAAQ6I,GAAWvX,QAAQF,WAAW6Y,uBACtC9zB,SAEP,CACD0zB,KACI,MAAO,CACHrU,QAAS5kB,MAAKm4B,GAAyBzE,SAAW,KAClDU,WAAYp0B,MAAKm4B,GAAyB5F,UAAY,KAEtDiH,cAAe,EACfC,QAASz5B,MAAK05B,KAEdzF,UAAWh3B,KAAKu6B,MAAsD,KAA/Cx3B,MAAKm4B,GAAyBwB,UAAY,IAExE,CACDD,MACI,MAAME,OAAoD97B,IAA1CkC,MAAKo4B,EACf,GACAL,IAAe8B,GAAY75B,MAAKo4B,EAAiC0B,mBACvE,MAAO,CACHL,QAASz5B,MAAKm4B,GAAyBD,WAAaH,IAAe74B,EACnEqS,IAAKvR,MAAKm4B,GAAyBsB,QAAQloB,KAAOwmB,IAAe74B,EACjEkwB,OAAQpvB,MAAKm4B,GAAyBsB,QAAQrK,QAAU2I,IAAe74B,EACvE66B,QAASt/B,OAAO2B,KAAK4D,MAAKm4B,GAAyBsB,QAAQM,SAAW,IAAI79B,KAAKI,IAAS,CACpFkE,KAAMlE,EACN1B,MAAOoF,MAAKm4B,GAAyBsB,QAAQM,QAAQz9B,OAEzDs9B,UAEAI,aAAc,EAEdC,SAAU,EACVC,QAAS,CAELC,WAAY,EAEZC,YAAa,EAEbC,cAAe,EAEfC,YAAa,EAEbC,WAAY,EAEZC,SAAU,EAEVC,OAAQ,EAERC,aAAc,EAEdC,WAAY,EAEZC,SAAU,EAEVC,OAAQ,EAERC,aAAc,EAEdC,cAAe,EAEfC,YAAa,GAGxB,CACDzB,MACI,OAAQv5B,MAAKm4B,GAAyBmB,UAAU/1B,MAC5C,IAAK,SACL,IAAK,SACL,IAAK,YACD,OAAOvD,MAAKm4B,EAAwBmB,UAAU/1B,KAClD,QACI,MAAO,QAElB,CACDiD,UAA2By0B,GACvB,OAAQA,GACJ,IAAK,SACD,MAAO,SACX,IAAK,MACD,MAAO,MACX,QACI,MAAO,OAElB,CACDz0B,UAAmBszB,GACf,OAAOA,EAAkB59B,KAAKg/B,IACnB,CACH16B,KAAM06B,EAAWC,OAAO36B,KACxB5F,MAAOsgC,EAAWC,OAAOvgC,MACzBwgC,OAAQF,EAAWC,OAAOC,OAC1B75B,KAAM25B,EAAWC,OAAO55B,KACxB85B,QAASH,EAAWC,OAAOE,QAC3BlhB,KAAM+gB,EAAWC,OAAOhhB,KACxBmhB,SAAUJ,EAAWC,OAAOG,SAC5BC,OAAQL,EAAWC,OAAOI,OAC1BC,SAAUzD,IAAe0D,GAAoBP,EAAWC,OAAOK,aAG1E,CACD3C,KACS74B,MAAKm5B,KAENn5B,MAAKysB,EAAc2K,qBAAqBp3B,MAAKw4B,EAA0B95B,MAAK,IAAMsB,MAAK07B,OAA8B17B,MAAKq4B,GAAwB3E,SAAW,KAAMuE,GAAWvX,QAAQF,WAAWmb,uBAExM,CACDD,MACI,QAAoC59B,IAAhCkC,MAAKq4B,EACL,MAAM,IAAI/8B,MAAM,oCAEpB,QAAqCwC,IAAjCkC,MAAKm4B,EACL,MAAM,IAAI78B,MAAM,qCAEpB,MAAO,CACH8zB,OAAQ6I,GAAWvX,QAAQF,WAAWmb,uBACtCp2B,OAAQ,IACDvF,MAAKi5B,IACR2C,SAAU,CACNrqB,IAAKvR,MAAKq4B,EAAuBuD,SAASrqB,IAC1CsqB,SAAU77B,MAAKq4B,EAAuBuD,SAASC,SAC/Cp1B,OAAQzG,MAAKq4B,EAAuBuD,SAASn1B,OAC7Cq1B,WAAY97B,MAAKq4B,EAAuBuD,SAASE,WAEjDC,UAAW/7B,MAAKq4B,EAAuBuD,SAASI,eAC5Ch8B,MAAKq4B,EAAuBuD,SAASK,kBAEzClC,QAAS/5B,MAAKk8B,GAAYl8B,MAAKq4B,EAAuBuD,SAAS7B,SAC/DoC,SAAUn8B,MAAKq4B,EAAuBuD,SAASO,SAC/CC,cAAep8B,MAAKq4B,EAAuBuD,SAASS,kBACpDrC,YAAah6B,MAAKs4B,GAAiCgE,aAAa96B,SAAW,EAE3Ey4B,UAAW,EACXsC,QAAS,CAELpiB,MAAO,KAK1B,CACD+hB,IAAYnC,GACR,OAAOt/B,OAAO2B,KAAK29B,GAAS79B,KAAKI,IAAS,CACtCkE,KAAMlE,EACN1B,MAAOm/B,EAAQz9B,MAEtB,CACD68B,KACI,OAAQn5B,MAAKm4B,GAAyBsB,QAAQloB,IAAIlO,SAAS,kBACvD,CACP,EAEiBy0B,GAAAC,eAAGA,GC3OzBt9B,OAAOC,eAAe8hC,GAAS,aAAc,CAAE5hC,OAAO,IAC9B4hC,GAAAC,sBAAG,EAC3B,MAAM/E,GAAen4B,GACfm9B,GAAmBz6B,GACzB,MAAMw6B,GACFhQ,GAKAkQ,IACA98B,YAAY4sB,GACRzsB,MAAKysB,EAAgBA,EACrBzsB,MAAK28B,GAAc,IAAIjF,GAAaC,YAAYO,GAAc,IAAIwE,GAAiB3E,eAAeG,EAAWl4B,MAAKysB,IACrH,CACDjmB,oBAAoBmmB,EAAWF,GAC3B,MAAM+P,EAAmB,IAAIC,GAAiBhQ,GA2B9C,OA1BAE,EAAU3C,GAAG,6BAA8BzkB,IACvCi3B,GACKI,GAA2Br3B,EAAO2yB,WAClCO,yBAAyBlzB,EAAO,IAEzConB,EAAU3C,GAAG,sCAAuCzkB,IAChDi3B,GACKI,GAA2Br3B,EAAO2yB,WAClCS,kCAAkCpzB,EAAO,IAElDonB,EAAU3C,GAAG,4BAA6BzkB,IACtCi3B,GACKI,GAA2Br3B,EAAO2yB,WAClCU,wBAAwBrzB,EAAO,IAExConB,EAAU3C,GAAG,qCAAsCzkB,IAC/Ci3B,GACKI,GAA2Br3B,EAAO2yB,WAClCY,iCAAiCvzB,EAAO,IAEjDonB,EAAU3C,GAAG,yBAA0BzkB,IACnCi3B,GACKI,GAA2Br3B,EAAO2yB,WAClCa,qBAAqBxzB,EAAO,UAE/BonB,EAAUC,YAAY,kBACrB4P,CACV,CACDI,IAA2B1E,GACvB,OAAOl4B,MAAK28B,GAAY1kB,IAAIigB,EAC/B,EAEmBsE,GAAAC,iBAAGA,GChD3BhiC,OAAOC,eAAe83B,EAAS,aAAc,CAAE53B,OAAO,IACrC43B,EAAAqK,eAAG,EACpB,MAAMC,GAAev9B,EACf04B,GAAah2B,EACb+1B,GAAa7sB,EACb4xB,GAAqB3xB,GAC3B,MAAMyxB,GACFtJ,IACAQ,IACApH,GACAF,GACAiE,IACAsM,IACAx2B,cAAcutB,EAAUpH,EAAW+D,EAAcJ,EAAc7D,GAC3D,MAAM+F,EAAY,IAAIqK,GAAU9I,EAAUpH,EAAW+D,EAAcjE,GAKnE,OAJAqQ,GAAapG,WAAW30B,OAAOywB,EAAWlC,EAAc7D,GACxD+F,GAAUyK,KAELzK,GAAU0K,KACR1K,CACV,CACD3yB,YAAYk0B,EAAUpH,EAAW+D,EAAcjE,GAC3CzsB,MAAK+zB,GAAYA,EACjB/zB,MAAK2sB,EAAaA,EAClB3sB,MAAK0wB,GAAgBA,EACrB1wB,MAAKysB,EAAgBA,EACrBzsB,MAAKg9B,IAA0B,EAC/Bh9B,MAAKuzB,GAAmB,IAAIyE,GAAWlM,QAC1C,CAIGyH,sBACA,OAAOvzB,MAAKuzB,EACf,CACGQ,eACA,OAAO/zB,MAAK+zB,EACf,CACGpH,gBACA,OAAO3sB,MAAK2sB,CACf,CAIG+D,mBACA,OAAO1wB,MAAK0wB,EACf,CAIDviB,WAGQnO,MAAKysB,EAAc0Q,8BACbn9B,KAAKo9B,4BAETp9B,MAAK2sB,EAAWC,YAAY,wBAC5B5sB,MAAK2sB,EAAWC,YAAY,qBAC5B5sB,MAAK2sB,EAAWC,YAAY,iCAAkC,CAChEyQ,SAAS,UAEPr9B,MAAK2sB,EAAWC,YAAY,uBAAwB,CACtD0Q,YAAY,EACZC,wBAAwB,EACxB17B,SAAS,UAEP7B,MAAK2sB,EAAWC,YAAY,mCAClC5sB,MAAKuzB,GAAiBtlB,SACzB,CAKDE,4BACSnO,MAAKg9B,KACNh9B,MAAKg9B,IAA0B,QACzBD,GAAmBN,iBAAiB16B,OAAO/B,KAAK2sB,UAAW3sB,MAAKysB,GAE7E,CACDwQ,MACIj9B,MAAK2sB,EAAW3C,GAAG,KAAK,CAACoF,EAAQ7pB,KAC7BvF,MAAKysB,EAAc0C,cAAc,CAC7BC,OAAQ6I,GAAWtX,IAAIH,WAAWgd,mBAClCj4B,OAAQ,CACJ0jB,UAAWmG,EACXlG,UAAW3jB,GAAU,CAAE,EACvB4jB,WAAYnpB,MAAK0wB,KAEtB,KAAK,GAEf,EAEY8B,EAAAqK,UAAGA,GC7GpBpiC,OAAOC,eAAe+iC,EAAS,aAAc,CAAE7iC,OAAO,IACtB6iC,EAAAC,8BAAG,EACnC,MAAM7a,GAAgBtjB,EAChBwrB,GAAW9oB,EACX07B,GAA2BxyB,EAC3ByyB,GAAiBxyB,EAoQSqyB,EAAAC,yBAnQhC,MACInN,GACAsN,IACApR,GACAzB,GACAsF,GACAwN,IACAj+B,YAAYywB,EAAcuN,EAAeC,EAAcrR,EAAc8D,EAAwBvF,GACzFhrB,MAAKuwB,EAA0BA,EAC/BvwB,MAAK69B,GAAiBA,EACtB79B,MAAKysB,EAAgBA,EACrBzsB,MAAKgrB,EAAUA,EACfhrB,MAAKswB,EAAgBA,EACrBtwB,MAAK89B,GAAgBA,EACrB99B,MAAKi9B,GAAmBj9B,MAAK69B,GAAeE,gBAC/C,CAKDd,IAAmBtQ,GACfA,EAAU3C,GAAG,2BAA4BzkB,IACrCvF,MAAKg+B,GAA6Bz4B,EAAQonB,EAAU,IAExDA,EAAU3C,GAAG,6BAA8BzkB,IACvCvF,MAAKi+B,GAA+B14B,EAAO,IAE/ConB,EAAU3C,GAAG,sBAAuBzkB,IAChCvF,MAAKk+B,GAA0B34B,EAAO,IAE1ConB,EAAU3C,GAAG,sBAAuBzkB,IAChCvF,MAAKm+B,GAA0B54B,EAAO,GAE7C,CAKD24B,IAA0B34B,GACtB,MAAM64B,EAAwBp+B,MAAKuwB,EAAwBc,YAAY9rB,EAAO84B,oBAChDvgC,IAA1BsgC,GACAT,GAAyBhM,oBAAoB5vB,OAAOq8B,EAAsB5L,UAAWxyB,MAAKswB,EAAe/qB,EAAOmuB,QAASnuB,EAAO84B,cAAer+B,MAAKysB,EAAezsB,MAAKuwB,EAAyBvwB,MAAKgrB,EAE7M,CAKDmT,IAA0B54B,GAEA,SAAlBA,EAAO4mB,QAGXnsB,MAAKuwB,EAAwBc,YAAY9rB,EAAOmuB,UAAU7C,QAC7D,CAaDmN,IAA6Bz4B,EAAQ+4B,GACjC,MAAMC,UAAEA,EAASzK,WAAEA,GAAevuB,EAC5Bi5B,EAAkBx+B,MAAK69B,GAAeY,aAAaF,GACzD,IAAKv+B,MAAK0+B,GAAe5K,GAMrB,YAHK0K,EACA5R,YAAY,mCACZluB,MAAK,IAAM4/B,EAAuB1R,YAAY,0BAA2BrnB,KAGlFvF,MAAKgrB,IAAUD,GAASH,QAAQ8J,iBAAkB,mCAAoCh1B,KAAKC,UAAU4F,EAAQ,KAAM,IACnHvF,MAAKi9B,GAAmBuB,GACxB,MAAMhM,EAAYoL,GAAef,UAAU96B,OAAO+xB,EAAWC,SAAUyK,EAAiBD,EAAWv+B,MAAKswB,EAAetwB,MAAKysB,GACxHzsB,MAAKuwB,EAAwBoO,WAAW7K,EAAWC,UAEnD/zB,MAAKuwB,EACAe,WAAWwC,EAAWC,UACtBT,gBAAgBd,GAGrBmL,GAAyBhM,oBAAoB5vB,OAAOywB,EAAWxyB,MAAKswB,EAAewD,EAAWC,SAAU,KAAM/zB,MAAKysB,EAAezsB,MAAKuwB,EAAyBvwB,MAAKgrB,EAE5K,CAKDiT,IAA+B14B,GAI3B,MAAM8nB,EAAY9nB,EAAOwuB,SACzB/zB,MAAKuwB,EAAwBc,YAAYhE,IAAYwD,QACxD,CACD1iB,SAAgBkB,GACZ,GAAI,UAAWA,EACX,OAAOrP,MAAKswB,EAAcsO,SAAS,CAC/BpR,QAASne,EAAO4V,QAIxB,OADgBjlB,MAAKuwB,EAAwBe,WAAWjiB,EAAOuV,SAChDia,mBAAmBxvB,EAAO0V,QAC5C,CACD+Z,gCAAgCv5B,GAI5B,MAAO,CACHqG,OAAQ,CACJ8d,eAL+B5rB,IAAhByH,EAAOshB,KACxB7mB,MAAKuwB,EAAwBwO,sBAC7B,CAAC/+B,MAAKuwB,EAAwBe,WAAW/rB,EAAOshB,QAGrB3qB,KAAK23B,GAAMA,EAAEhB,qBAAqBttB,EAAO8gB,UAAYtpB,OAAOiiC,cAGhG,CACD7wB,qCAAqC5I,GACjC,MAAM05B,EAAmBj/B,MAAK69B,GAAeE,gBAC7C,IAAI3W,EACJ,QAAgCtpB,IAA5ByH,EAAO6hB,mBACPA,EAAmBpnB,MAAKuwB,EAAwBe,WAAW/rB,EAAO6hB,mBAC7DA,EAAiB8L,qBAClB,MAAM,IAAIrQ,GAAcjC,QAAQO,yBAAyB,kDAGjE,MASMkM,SATe4R,EAAiBrS,YAAY,sBAAuB,CACrErb,IAAK,cACL2tB,UAA2B,WAAhB35B,EAAOhC,QAOGwwB,SACnBnP,EAAU5kB,MAAKuwB,EAAwBe,WAAWjE,GAExD,aADMzI,EAAQua,cACP,CACHvzB,OAAQgZ,EAAQiO,qBAAqB,GAE5C,CACDuM,iCAAiC75B,GAE7B,OADgBvF,MAAKuwB,EAAwBe,WAAW/rB,EAAOqf,SAChDya,SAAS95B,EAAOgM,SAAqBzT,IAAhByH,EAAO0hB,KAAqB,OAAS1hB,EAAO0hB,KACnF,CACD9Y,gDAAgD5I,GAE5C,OADgBvF,MAAKuwB,EAAwBe,WAAW/rB,EAAOqf,SAChD0a,mBAClB,CACDnxB,oCAAoC5I,GAEhC,OADgBvF,MAAKuwB,EAAwBe,WAAW/rB,EAAOqf,SAChD2a,MAAMh6B,EACxB,CACD4I,sCAAsC5I,GAClC,MAAMmkB,EAAW,GACX8V,EAAU,GAahB,OAZIj6B,EAAOqf,QAGP8E,EAAShtB,KAAKsD,MAAKuwB,EAAwBe,WAAW/rB,EAAOqf,UAK7D8E,EAAShtB,QAAQsD,MAAKuwB,EAAwBkP,kBAElDD,EAAQ9iC,cAAe0K,QAAQ4N,IAAI0U,EAASxtB,KAAK0oB,GAAYA,EAAQ8a,iBAAiBn6B,OAE/Ei6B,EAAQ,EAClB,CAEDrxB,yCAAyCwxB,GACrC,MAAM,IAAI9c,GAAcjC,QAAQyB,sBAAsB,mBAEzD,CACDlU,8BAA8B5I,GAE1B,aADoBvF,MAAK4+B,GAAUr5B,EAAO8J,SAC7BoiB,eAAelsB,EAAO8f,WAAY9f,EAAO+f,aAAc/f,EAAOggB,iBAAmB,OACjG,CACDqa,yBAAyBr6B,QACEzH,IAAnByH,EAAOqf,SAEP5kB,MAAKuwB,EAAwBe,WAAW/rB,EAAOqf,SAEnD,MAAMib,EAAS7/B,MAAKswB,EACfmD,WAAW,CACZlE,kBAAmBhqB,EAAOqf,QAC1BrhB,KAAMgC,EAAOhC,OAEZrH,KAAK+oB,GAAUA,EAAMmM,WAC1B,MAAO,CAAExlB,OAAQ,CAAEi0B,UACtB,CACD1xB,kCAAkC5I,GAE9B,aADoBvF,MAAK4+B,GAAUr5B,EAAO8J,SAC7BmiB,aAAajsB,EAAOmhB,oBAAqBnhB,EAAOvF,MAAQ,CACjEuD,KAAM,aAEVgC,EAAOwK,WAAa,GACpBxK,EAAO+f,aAAc/f,EAAOggB,iBAAmB,OAClD,CACDpX,4BAA4B5I,GACxB,MAAM0f,QAAcjlB,MAAK4+B,GAAUr5B,EAAO8J,QAE1C,aADMjI,QAAQ4N,IAAIzP,EAAOmgB,QAAQxpB,KAAIiS,MAAO2xB,GAAM7a,EAAM8a,OAAOD,MACxD,CAAEl0B,OAAQ,CAAA,EACpB,CACDuC,oCAAoC6xB,GAChC,MAAMf,EAAmBj/B,MAAK69B,GAAeE,gBAE7C,IADgB/9B,MAAKuwB,EAAwBe,WAAW0O,EAAcpb,SACzDsO,oBACT,MAAM,IAAIrQ,GAAcjC,QAAQO,yBAAyB,kDAE7D,MAAM8e,EAA4B,IAAI74B,SAAS6G,IAC3C,MAAMiyB,EAAsBC,IACpBA,EAAYpM,WAAaiM,EAAcpb,UACvCqa,EAAiB/U,IAAI,4BAA6BgW,GAClDjyB,IACH,EAELgxB,EAAiBjV,GAAG,4BAA6BkW,EAAmB,IASxE,aAPMjB,EAAiBrS,YAAY,qBAAsB,CACrDmH,SAAUiM,EAAcpb,gBAKtBqb,EACC,CAAEr0B,OAAQ,CAAA,EACpB,CACD8yB,IAAervB,GACX,OAAIA,EAAO0kB,WAAa/zB,MAAK89B,IAGtB,CAAC,OAAQ,UAAU56B,SAASmM,EAAO9L,KAC7C,CACD4K,8BAA8B5I,GAC1B,MAAM66B,EAAS76B,EAAO4jB,WAChBnpB,MAAK69B,GAAeY,aAAal5B,EAAO4jB,YACxCnpB,MAAK69B,GAAeE,gBAE1B,MAAO,CACHnyB,aAF+Bw0B,EAAOxT,YAAYrnB,EAAO0jB,UAAW1jB,EAAO2jB,WAG3EC,WAAY5jB,EAAO4jB,WAE1B,CACDkX,uBAAuB96B,GACnB,MAAMqf,EAAUrf,EAAOqf,QACjB2Z,EAAYv+B,MAAKuwB,EAAwBe,WAAW1M,GAAS4N,UAAU9B,aAC7E,YAAkB5yB,IAAdygC,EACO,CAAE3yB,OAAQ,CAAEud,WAAY,OAE5B,CAAEvd,OAAQ,CAAEud,WAAYoV,GAClC,aCvPL9jC,OAAOC,eAAe4lC,GAAS,aAAc,CAAE1lC,OAAO,IAC3B0lC,GAAAC,yBAAG,EAC9B,MAAMA,GACFz/B,IACAslB,IACAvmB,YAAYiB,EAASslB,GACjBpmB,MAAKc,GAAWA,EAChBd,MAAKomB,GAAWA,CACnB,CACD5f,+BAA+Bg6B,EAAgBpa,GAC3C,OAAOoa,EAAe9hC,MAAMoC,GAAY,IAAIy/B,GAAoBz/B,EAASslB,IAC5E,CACD5f,sBAAsB1F,EAASslB,GAC3B,OAAOhf,QAAQ6G,QAAQ,IAAIsyB,GAAoBz/B,EAASslB,GAC3D,CACGtlB,cACA,OAAOd,MAAKc,EACf,CACGslB,cACA,OAAOpmB,MAAKomB,EACf,EAEsBqa,GAAAF,oBAAGA,GCtB9B9lC,OAAOC,eAAegmC,EAAS,aAAc,CAAE9lC,OAAO,IAC9B8lC,EAAAC,sBAAG,EAC3B,MAAM9d,GAAgBtjB,EAChBwrB,GAAW9oB,EACX2+B,GAAoBz1B,EACpB01B,GAAgCz1B,EAChC01B,GAA2Bz1B,GACjC,MAAM01B,GACFjb,4BAA4BvgB,GACxB,OAAOA,CACV,CACD0gB,+BAA+B1gB,GAC3B,OAAOA,CACV,CACDsf,qBAAqBtf,GACjB,OAAOA,CACV,CACDohB,wBAAwBphB,GACpB,OAAOA,CACV,CACDigB,oBAAoBjgB,GAChB,OAAOA,CACV,CACDogB,kBAAkBpgB,GACd,OAAOA,CACV,CACD6jB,uBAAuB7jB,GACnB,OAAOA,CACV,CACD+jB,sBAAsB/jB,GAClB,OAAOA,CACV,CACDokB,qBAAqBpkB,GACjB,OAAOA,CACV,CACD2hB,oBAAoB3hB,GAChB,OAAOA,CACV,CACDuhB,mBAAmBvhB,GACf,OAAOA,CACV,CACD8hB,kBAAkB9hB,GACd,OAAOA,CACV,CACDgiB,iBAAiBhiB,GACb,OAAOA,CACV,CACDkiB,6BAA6BliB,GACzB,OAAOA,CACV,CACDwjB,iBAAiBxjB,GACb,OAAOA,CACV,EAEL,MAAMo7B,WAAyBC,GAAkB/W,aAC7CmX,IACAvU,GACAwU,IACAjW,GACAnrB,YAAYywB,EAAcuN,EAAepR,EAAcqR,EAAcmD,EAAS,IAAIF,GAAkBxQ,EAAwBvF,GACxHjrB,QACAC,MAAKysB,EAAgBA,EACrBzsB,MAAKgrB,EAAUA,EACfhrB,MAAKghC,GAAoB,IAAIH,GAA8BnD,yBAAyBpN,EAAcuN,EAAeC,EAAcrR,EAAc8D,EAAwBvF,GACrKhrB,MAAKihC,GAAUA,CAClB,CACDz6B,YACI,MAAO,CAAEoF,OAAQ,CAAEs1B,OAAO,EAAOpgC,QAAS,qBAC7C,CACDqN,SAAiC5I,EAAQ6gB,GAErC,aADMpmB,MAAKysB,EAAc0U,UAAU57B,EAAOkkB,OAAQlkB,EAAOmkB,UAAY,CAAC,MAAOtD,GACtE,CAAExa,OAAQ,CAAA,EACpB,CACDuC,SAAmC5I,EAAQ6gB,GAEvC,aADMpmB,MAAKysB,EAAc2U,YAAY77B,EAAOkkB,OAAQlkB,EAAOmkB,UAAY,CAAC,MAAOtD,GACxE,CAAExa,OAAQ,CAAA,EACpB,CACDuC,SAAsBkzB,GAClB,OAAQA,EAAYjS,QAChB,IAAK,iBACD,OAAOuR,IAAiBW,KAC5B,IAAK,oBACD,OAAOthC,MAAKuhC,GAA2BvhC,MAAKihC,GAAQtX,qBAAqB0X,EAAY97B,QAAS87B,EAAYjb,SAAW,MACzH,IAAK,sBACD,OAAOpmB,MAAKwhC,GAA6BxhC,MAAKihC,GAAQtX,qBAAqB0X,EAAY97B,QAAS87B,EAAYjb,SAAW,MAC3H,IAAK,yBACD,OAAOpmB,MAAKghC,GAAkBS,+BAA+BzhC,MAAKihC,GAAQ5Z,kBAAkBga,EAAY97B,SAC5G,IAAK,wBACD,OAAOvF,MAAKghC,GAAkBU,8BAA8B1hC,MAAKihC,GAAQ1Z,iBAAiB8Z,EAAY97B,SAC1G,IAAK,0BACD,OAAOvF,MAAKghC,GAAkBlC,gCAAgC9+B,MAAKihC,GAAQna,mBAAmBua,EAAY97B,SAC9G,IAAK,2BACD,OAAOvF,MAAKghC,GAAkB5B,iCAAiCp/B,MAAKihC,GAAQ/Z,oBAAoBma,EAAY97B,SAChH,IAAK,oCACD,OAAOvF,MAAKghC,GAAkBW,0CAA0C3hC,MAAKihC,GAAQxZ,6BAA6B4Z,EAAY97B,SAClI,IAAK,wBACD,OAAOvF,MAAKghC,GAAkBY,8BAA8B5hC,MAAKihC,GAAQlY,iBAAiBsY,EAAY97B,SAC1G,IAAK,0BACD,OAAOvF,MAAKghC,GAAkBa,gCAAgC7hC,MAAKihC,GAAQnb,4BAA4Bub,EAAY97B,SACvH,IAAK,6BACD,OAAOvF,MAAKghC,GAAkBc,mCAAmC9hC,MAAKihC,GAAQhb,+BAA+Bob,EAAY97B,SAC7H,IAAK,mBACD,OAAOvF,MAAKghC,GAAkBpB,yBAAyB5/B,MAAKihC,GAAQpc,qBAAqBwc,EAAY97B,SACzG,IAAK,sBACD,OAAOvF,MAAKghC,GAAkBe,4BAA4B/hC,MAAKihC,GAAQta,wBAAwB0a,EAAY97B,SAC/G,IAAK,kBACD,OAAOvF,MAAKghC,GAAkBgB,wBAAwBhiC,MAAKihC,GAAQzb,oBAAoB6b,EAAY97B,SACvG,IAAK,gBACD,OAAOvF,MAAKghC,GAAkBiB,sBAAsBjiC,MAAKihC,GAAQtb,kBAAkB0b,EAAY97B,SACnG,IAAK,kBACD,OAAOvF,MAAKghC,GAAkBkB,wBAAwBliC,MAAKihC,GAAQ7X,uBAAuBiY,EAAY97B,SAC1G,IAAK,iBACD,OAAOvF,MAAKghC,GAAkBX,uBAAuBrgC,MAAKihC,GAAQ3X,sBAAsB+X,EAAY97B,SACxG,QACI,MAAM,IAAIsd,GAAcjC,QAAQuB,wBAAwB,oBAAoBkf,EAAYjS,YAEnG,CACDjhB,qBAAqBg0B,GACjB,IACI,MAAMv2B,QAAe5L,MAAKoiC,GAAgBD,GACpCvG,EAAW,CACb1a,GAAIihB,EAAQjhB,MACTtV,GAEP5L,KAAKoqB,KAAK,WAAY0W,GAAyBP,oBAAoB8B,eAAezG,EAAUuG,EAAQ/b,SAAW,MAYlH,CAVD,MAAOjqB,GACH,GAAIA,aAAa0mB,GAAcjC,QAAQE,cAAe,CAClD,MAAMwhB,EAAgBnmC,EACtB6D,KAAKoqB,KAAK,WAAY0W,GAAyBP,oBAAoB8B,eAAeC,EAActhB,gBAAgBmhB,EAAQjhB,IAAKihB,EAAQ/b,SAAW,MACnJ,KACI,CACD,MAAMllB,EAAQ/E,EACd6D,MAAKgrB,IAAUD,GAASH,QAAQ2X,KAAMrhC,GACtClB,KAAKoqB,KAAK,WAAY0W,GAAyBP,oBAAoB8B,eAAe,IAAIxf,GAAcjC,QAAQE,cAAc+B,GAAcjC,QAAQC,UAAUyB,aAAcphB,EAAMJ,SAASkgB,gBAAgBmhB,EAAQjhB,IAAKihB,EAAQ/b,SAAW,MAC1O,CACJ,CACJ,EAEmBoc,EAAA7B,iBAAGA,aC3I3BlmC,OAAOC,eAAe61B,GAAS,aAAc,CAAE31B,OAAO,IACxB21B,GAAAkS,4BAAG,EACjC,MAAM5f,GAAgBtjB,EAqDQgxB,GAAAkS,uBAnD9B,MAEI/Y,IAAY,IAAI7qB,IAEhBkgC,sBACI,OAAO/+B,KAAKy/B,iBAAiB3jC,QAAQ+3B,GAAMA,EAAEX,qBAChD,CAEDuM,iBACI,OAAOlhC,MAAM2Z,KAAKlY,MAAK0pB,GAAUjP,SACpC,CAEDwY,cAAc5F,GACVrtB,MAAK0pB,GAAUmH,OAAOxD,EACzB,CAEDsF,WAAW/N,GACP5kB,MAAK0pB,GAAU3qB,IAAI6lB,EAAQyI,UAAWzI,GACjCA,EAAQsO,qBACTlzB,KAAKsxB,WAAW1M,EAAQmN,UAAUoB,SAASvO,EAElD,CAED+Z,WAAWtR,GACP,OAAOrtB,MAAK0pB,GAAUtR,IAAIiV,EAC7B,CAEDgE,YAAYhE,GACR,OAAOrtB,MAAK0pB,GAAUzR,IAAIoV,EAC7B,CAEDqV,sBAAsBrV,GAClB,GAAkB,OAAdA,EACA,OAAO,KAEX,MACM0E,EADe/xB,KAAKqxB,YAAYhE,IACP0E,UAAY,KAC3C,OAAiB,OAAbA,EACO1E,EAEJrtB,KAAK0iC,sBAAsB3Q,EACrC,CAEDT,WAAWjE,GACP,MAAMzhB,EAAS5L,KAAKqxB,YAAYhE,GAChC,QAAevvB,IAAX8N,EACA,MAAM,IAAIiX,GAAcjC,QAAQe,qBAAqB,WAAW0L,eAEpE,OAAOzhB,CACV,mBCrDLnR,OAAOC,eAAeioC,GAAS,aAAc,CAAE/nC,OAAO,IACxC+nC,GAAAC,YAAG,EA6BHD,GAAAC,OAzBd,MACIC,IACA7oB,IAAW,GACX8oB,IAKAjjC,YAAYgjC,EAAUC,EAAgB,UAClC9iC,MAAK6iC,GAAYA,EACjB7iC,MAAK8iC,GAAiBA,CACzB,CACD7qB,MACI,OAAOjY,MAAKga,EACf,CACDQ,IAAI5f,GAEA,IADAoF,MAAKga,GAAStd,KAAK9B,GACZoF,MAAKga,GAASxY,OAASxB,MAAK6iC,IAAW,CAC1C,MAAMnnC,EAAOsE,MAAKga,GAASwR,aACd1tB,IAATpC,GACAsE,MAAK8iC,GAAepnC,EAE3B,CACJ,aC5BLjB,OAAOC,eAAeqoC,GAAS,aAAc,CAAEnoC,OAAO,IACrCmoC,GAAAC,eAAG,EAIpB,MAAMA,GACFx8B,UAAkB,EAClB0a,IACArhB,cACIG,MAAKkhB,KAAQ8hB,IAAUC,EAC1B,CACG/hB,SACA,OAAOlhB,MAAKkhB,EACf,EAEY6hB,GAAAC,UAAGA,aCfpBvoC,OAAOC,eAAewoC,GAAS,aAAc,CAAEtoC,OAAO,IACtDsoC,GAAAC,oBAAkDD,GAAAE,sCAA8B,EAChF,MAAMvgB,GAAgBtjB,EAOtB,SAAS8jC,MAAoB/qB,GACzB,OAAOA,EAAEgrB,QAAO,CAAChrB,EAAGC,IAAMD,EAAEirB,SAASC,GAAMjrB,EAAErc,KAAKC,GAAM,CAACqnC,EAAGrnC,GAAGsnC,YACnE,CAGA,SAASL,GAAa3Z,GAClB,MAAMia,EAAY,GAClB,IAAK,MAAMjZ,KAAShB,EAChB,OAAQgB,GACJ,KAAK5H,GAActC,gBAAgBkC,UAC/BihB,EAAUhnC,QAAQjC,OAAOggB,OAAOoI,GAActC,gBAAgBC,aAC9D,MACJ,KAAKqC,GAAclC,IAAI8B,UACnBihB,EAAUhnC,QAAQjC,OAAOggB,OAAOoI,GAAclC,IAAIH,aAClD,MACJ,KAAKqC,GAAcpC,IAAIgC,UACnBihB,EAAUhnC,QAAQjC,OAAOggB,OAAOoI,GAAcpC,IAAID,aAClD,MACJ,KAAKqC,GAAcnC,QAAQ+B,UACvBihB,EAAUhnC,QAAQjC,OAAOggB,OAAOoI,GAAcnC,QAAQF,aACtD,MACJ,KAAKqC,GAAcvC,OAAOmC,UACtBihB,EAAUhnC,QAAQjC,OAAOggB,OAAOoI,GAAcvC,OAAOE,aACrD,MACJ,QACIkjB,EAAUhnC,KAAK+tB,GAG3B,OAAOiZ,CACX,CA1BwBC,GAAAN,iBAAGA,GA2BPM,GAAAP,aAAGA,GAiIIO,GAAAR,oBAhI3B,MACIS,IAAwB,EAIxBC,IAA8B,IAAIhlC,IAClC0xB,GACA1wB,YAAY0wB,GACRvwB,MAAKuwB,EAA0BA,CAClC,CACDuT,6BAA6BC,EAAa1W,GAQtC,OAP8B9uB,MAAM2Z,KAAKlY,MAAK6jC,GAA4BznC,QACrEF,KAAKkqB,IAAa,CACnB4d,SAAUhkC,MAAKikC,GAAwCF,EAAa1W,EAAWjH,GAC/EA,cAECtqB,QAAO,EAAGkoC,cAA4B,OAAbA,IAGzBE,MAAK,CAAC5rB,EAAGC,IAAMD,EAAE0rB,SAAWzrB,EAAEyrB,WAC9B9nC,KAAI,EAAGkqB,aAAcA,GAC7B,CACD6d,IAAwCF,EAAa1W,EAAWjH,GAC5D,MAAM+d,EAAoBnkC,MAAK6jC,GAA4B5rB,IAAImO,GAC/D,QAA0BtoB,IAAtBqmC,EACA,OAAO,KAEX,MAAMC,EAAyBpkC,MAAKuwB,EAAwBmS,sBAAsBrV,GAI5EgX,EAFmB,IAAI,IAAIvlC,IAAI,CAAC,KAAMslC,KAGvCloC,KAAK23B,GAAMsQ,EAAkBlsB,IAAI4b,IAAI5b,IAAI8rB,KACzCjoC,QAAQohB,QAAYpf,IAANof,IACnB,OAA0B,IAAtBmnB,EAAW7iC,OAEJ,KAGJvE,KAAKmT,OAAOi0B,EACtB,CACDlD,UAAU1W,EAAO4C,EAAWjH,GAGxB,GADAiH,EAAYrtB,MAAKuwB,EAAwBmS,sBAAsBrV,GAC3D5C,IAAU5H,GAActC,gBAAgBkC,UAExC,YADAhoB,OAAOggB,OAAOoI,GAActC,gBAAgBC,YAAYtkB,KAAKooC,GAAkBtkC,KAAKmhC,UAAUmD,EAAejX,EAAWjH,KAG5H,GAAIqE,IAAU5H,GAAclC,IAAI8B,UAE5B,YADAhoB,OAAOggB,OAAOoI,GAAclC,IAAIH,YAAYtkB,KAAKooC,GAAkBtkC,KAAKmhC,UAAUmD,EAAejX,EAAWjH,KAGhH,GAAIqE,IAAU5H,GAAcpC,IAAIgC,UAE5B,YADAhoB,OAAOggB,OAAOoI,GAAcpC,IAAID,YAAYtkB,KAAKooC,GAAkBtkC,KAAKmhC,UAAUmD,EAAejX,EAAWjH,KAGhH,GAAIqE,IAAU5H,GAAcnC,QAAQ+B,UAEhC,YADAhoB,OAAOggB,OAAOoI,GAAcnC,QAAQF,YAAYtkB,KAAKooC,GAAkBtkC,KAAKmhC,UAAUmD,EAAejX,EAAWjH,KAGpH,GAAIqE,IAAU5H,GAAcvC,OAAOmC,UAE/B,YADAhoB,OAAOggB,OAAOoI,GAAcvC,OAAOE,YAAYtkB,KAAKooC,GAAkBtkC,KAAKmhC,UAAUmD,EAAejX,EAAWjH,KAG9GpmB,MAAK6jC,GAA4BzrB,IAAIgO,IACtCpmB,MAAK6jC,GAA4B9kC,IAAIqnB,EAAS,IAAIvnB,KAEtD,MAAMslC,EAAoBnkC,MAAK6jC,GAA4B5rB,IAAImO,GAC1D+d,EAAkB/rB,IAAIiV,IACvB8W,EAAkBplC,IAAIsuB,EAAW,IAAIxuB,KAEzC,MAAM0lC,EAAWJ,EAAkBlsB,IAAIoV,GAEnCkX,EAASnsB,IAAIqS,IAGjB8Z,EAASxlC,IAAI0rB,EAAOzqB,MAAK4jC,KAC5B,CAIDY,eAAe/a,EAAQgb,EAAYre,GAE/B,IAAK,MAAMiH,KAAaoX,EACF,OAAdpX,GACArtB,MAAKuwB,EAAwBe,WAAWjE,GAGtBgW,GAAiBD,GAAa3Z,GAASgb,GAI5DvoC,KAAI,EAAEuuB,EAAO4C,KAAertB,MAAK0kC,GAAkBja,EAAO4C,EAAWjH,KACrErP,SAASqqB,GAAgBA,KACjC,CAKDA,YAAYuD,EAAWtX,EAAWjH,GAC9BpmB,KAAKwkC,eAAe,CAACG,GAAY,CAACtX,GAAYjH,EACjD,CACDse,IAAkBja,EAAO4C,EAAWjH,GAGhC,GADAiH,EAAYrtB,MAAKuwB,EAAwBmS,sBAAsBrV,IAC1DrtB,MAAK6jC,GAA4BzrB,IAAIgO,GACtC,MAAM,IAAIvD,GAAcjC,QAAQO,yBAAyB,2BAA2BsJ,MAAwB,OAAd4C,EAAqB,OAASA,6BAEhI,MAAM8W,EAAoBnkC,MAAK6jC,GAA4B5rB,IAAImO,GAC/D,IAAK+d,EAAkB/rB,IAAIiV,GACvB,MAAM,IAAIxK,GAAcjC,QAAQO,yBAAyB,2BAA2BsJ,MAAwB,OAAd4C,EAAqB,OAASA,6BAEhI,MAAMkX,EAAWJ,EAAkBlsB,IAAIoV,GACvC,IAAKkX,EAASnsB,IAAIqS,GACd,MAAM,IAAI5H,GAAcjC,QAAQO,yBAAyB,2BAA2BsJ,MAAwB,OAAd4C,EAAqB,OAASA,6BAEhI,MAAO,KACHkX,EAAS1T,OAAOpG,GAEM,IAAlB8Z,EAASpqB,MACTgqB,EAAkBtT,OAAOpG,GAEE,IAA3B0Z,EAAkBhqB,MAClBna,MAAK6jC,GAA4BhT,OAAOzK,EAC3C,CAER,GCtKL3rB,OAAOC,eAAekqC,GAAS,aAAc,CAAEhqC,OAAO,IAClCgqC,GAAAC,kBAAG,EACvB,MAAMhiB,GAAgBtjB,EAChBulC,GAAc7iC,GACd8iC,GAAiB55B,GACjB21B,GAA2B11B,GAC3B45B,GAAkB35B,GAClB45B,GAA2BhlB,GACjC,MAAMilB,GACFnC,IACA1V,GACA5C,IACA5qB,YAAY4qB,EAAO4C,GACfrtB,MAAK+iC,GAAa,IAAIgC,GAAe/B,UACrChjC,MAAKqtB,EAAaA,EAClBrtB,MAAKyqB,GAASA,CACjB,CACGvJ,SACA,OAAOlhB,MAAK+iC,GAAW7hB,EAC1B,CACGmM,gBACA,OAAOrtB,MAAKqtB,CACf,CACG5C,YACA,OAAOzqB,MAAKyqB,EACf,EAKL,MAAM0a,GAAoB,IAAItmC,IAAI,CAC9B,CAACgkB,GAAcpC,IAAID,WAAW6W,mBAAoB,OAEtD,MAAMwN,GACFr+B,UAAgC,UAMhC4+B,IAAsB,IAAIJ,GAAgBrN,YAAW,IAAM,IAAI74B,MAK/DumC,IAAgB,IAAIxmC,IAMpBymC,IAAmB,IAAIzmC,IACvB0mC,IACAC,IACArI,IACAt9B,YAAY2lC,GACRxlC,MAAKwlC,GAAcA,EACnBxlC,MAAKulC,GAAuB,IAAIN,GAAyB9B,oBAAoBqC,EAAWC,6BACxFzlC,MAAKm9B,IAA0B,CAClC,CACGA,6BACA,OAAOn9B,MAAKm9B,EACf,CAID32B,UAAkBm+B,EAAWe,EAAiBtf,GAC1C,OAAO1mB,KAAKC,UAAU,CAAEglC,YAAWe,kBAAiBtf,WACvD,CACD+I,cAAc1E,EAAO4C,GACjBrtB,KAAKo3B,qBAAqBhwB,QAAQ6G,QAAQwc,GAAQ4C,EAAW5C,EAAM2E,OACtE,CACDgI,qBAAqB3M,EAAO4C,EAAWsX,GACnC,MAAMgB,EAAe,IAAIT,GAAaza,EAAO4C,GACvCuY,EAAiB5lC,MAAKulC,GAAqBzB,6BAA6Ba,EAAWtX,GACzFrtB,MAAK6lC,GAAaF,EAAchB,GAEhC,IAAK,MAAMve,KAAWwf,EAClB5lC,MAAKwlC,GAAYM,oBAAoBhF,GAAyBP,oBAAoBwF,kBAAkBtb,EAAOrE,IAC3GpmB,MAAKgmC,GAAeL,EAAcvf,EAASue,EAElD,CACDx2B,gBAAgB83B,EAAYxB,EAAYre,GAEpC,IAAK,MAAMiH,KAAaoX,EACF,OAAdpX,GAEArtB,MAAKwlC,GAAYC,4BAA4BnU,WAAWjE,GAGhE,IAAK,MAAMsX,KAAasB,EACpB,IAAK,MAAM5Y,KAAaoX,EAAY,OAC1BzkC,MAAKkmC,GAAevB,EAAWtX,GACrCrtB,MAAKulC,GAAqBpE,UAAUwD,EAAWtX,EAAWjH,GAC1D,IAAK,MAAMuf,KAAgB3lC,MAAKmmC,GAAmBxB,EAAWtX,EAAWjH,GAErEpmB,MAAKwlC,GAAYM,oBAAoBhF,GAAyBP,oBAAoBwF,kBAAkBJ,EAAalb,MAAOrE,IACxHpmB,MAAKgmC,GAAeL,EAAcvf,EAASue,EAElD,CAER,CAKDx2B,SAAqBw2B,EAAWtX,GAExBsX,EAAUvhC,WAAWyhC,IAAauB,MAEhB,OAAd/Y,GACArtB,MAAKm9B,IAA0B,QACzB/1B,QAAQ4N,IAAIhV,MAAKwlC,GAClBC,4BACAhG,iBACAvjC,KAAK0oB,GAAYA,EAAQ4N,UAAU4K,gCAGlCp9B,MAAKwlC,GACNC,4BACAnU,WAAWjE,GACXmF,UAAU4K,sBAG1B,CACDgE,YAAY6E,EAAYxB,EAAYre,GAChCpmB,MAAKulC,GAAqBf,eAAeyB,EAAYxB,EAAYre,EACpE,CAIDyf,IAAaF,EAAchB,GACvB,IAAKQ,GAAkB/sB,IAAIusB,GAEvB,OAEJ,MAAM0B,EAAexB,IAAayB,GAAW3B,EAAWgB,EAAatY,WAChErtB,MAAKqlC,GAAcjtB,IAAIiuB,IACxBrmC,MAAKqlC,GAActmC,IAAIsnC,EAAc,IAAIvB,GAAYlC,OAAOuC,GAAkBltB,IAAI0sB,KAEtF3kC,MAAKqlC,GAAcptB,IAAIouB,GAAc7rB,IAAImrB,GAEzC3lC,MAAKolC,GAAoBntB,IAAI0sB,GAAWnqB,IAAImrB,EAAatY,UAC5D,CAID2Y,IAAeL,EAAcvf,EAASue,GAClC,IAAKQ,GAAkB/sB,IAAIusB,GAEvB,OAEJ,MAAM4B,EAAiB1B,IAAayB,GAAW3B,EAAWgB,EAAatY,UAAWjH,GAClFpmB,MAAKslC,GAAiBvmC,IAAIwnC,EAAgBtpC,KAAK8U,IAAI/R,MAAKslC,GAAiBrtB,IAAIsuB,IAAmB,EAAGZ,EAAazkB,IACnH,CAIDilB,IAAmBxB,EAAWtX,EAAWjH,GACrC,MAAMigB,EAAexB,IAAayB,GAAW3B,EAAWtX,GAClDkZ,EAAiB1B,IAAayB,GAAW3B,EAAWtX,EAAWjH,GAC/DogB,EAAoBxmC,MAAKslC,GAAiBrtB,IAAIsuB,KAAoBE,IAClE76B,EAAS5L,MAAKqlC,GACfptB,IAAIouB,IACHpuB,MACDnc,QAAQ4qC,GAAYA,EAAQxlB,GAAKslB,KAAsB,GAY5D,OAXkB,OAAdnZ,GAEA9uB,MAAM2Z,KAAKlY,MAAKolC,GAAoBntB,IAAI0sB,GAAWvoC,QAC9CN,QAAQ6qC,GAEE,OAAfA,GAEI3mC,MAAKwlC,GAAYC,4BAA4B9G,WAAWgI,KACvDzqC,KAAKyqC,GAAe3mC,MAAKmmC,GAAmBxB,EAAWgC,EAAYvgB,KACnErP,SAAS0S,GAAW7d,EAAOlP,QAAQ+sB,KAErC7d,EAAOs4B,MAAK,CAAC0C,EAAIC,IAAOD,EAAG1lB,GAAK2lB,EAAG3lB,IAC7C,EAEe4lB,GAAAjC,aAAGA,aCpMvBpqC,OAAOC,eAAe41B,GAAS,aAAc,CAAE11B,OAAO,IAClC01B,GAAAyW,kBAAG,EACvB,MAAMlkB,GAAgBtjB,EAoEF+wB,GAAAyW,aAnEpB,MAEInW,IAAuB,IAAI/xB,IAE3B8xB,IAAY,IAAI9xB,IACZ+xB,0BACA,OAAO5wB,MAAK4wB,EACf,CACGD,eACA,OAAO3wB,MAAK2wB,EACf,CACD8C,WAAW33B,GACP,OAAOyC,MAAM2Z,KAAKlY,MAAK2wB,GAAUlW,UAAU3e,QAAQmpB,SACxBnnB,IAAnBhC,EAAO0xB,SAAyB1xB,EAAO0xB,UAAYvI,EAAMuI,iBAG5B1vB,IAA7BhC,EAAOyzB,mBACPzzB,EAAOyzB,oBAAsBtK,EAAMsK,2BAGZzxB,IAAvBhC,EAAOoyB,aACPpyB,EAAOoyB,cAAgBjJ,EAAMiJ,qBAGCpwB,IAA9BhC,EAAOgxB,oBACPhxB,EAAOgxB,qBAAuB7H,EAAM6H,4BAGlBhvB,IAAlBhC,EAAO00B,QAAwB10B,EAAO00B,SAAWvL,EAAMuL,gBAGvC1yB,IAAhBhC,EAAOyH,MAAsBzH,EAAOyH,OAAS0hB,EAAM1hB,cAGhCzF,IAAnBhC,EAAOipB,SAAyBjpB,EAAOipB,UAAYE,EAAMF,gBAGjCjnB,IAAxBhC,EAAO40B,cACP50B,EAAO40B,eAAiBzL,EAAMyL,qBAKzC,CACDwG,UAAUp7B,GACN,MAAMkrC,EAAchnC,KAAKyzB,WAAW33B,GACpC,GAA2B,IAAvBkrC,EAAYxlC,OAGhB,OAAOwlC,EAAY,EACtB,CACDpI,SAAS9iC,GACL,MAAMmrC,EAAajnC,KAAKk3B,UAAUp7B,GAClC,QAAmBgC,IAAfmpC,EACA,MAAM,IAAIpkB,GAAcjC,QAAQe,qBAAqB,SAASjiB,KAAKC,UAAU7D,gBAEjF,OAAOmrC,CACV,CACDlU,aAAaj3B,GACTkE,KAAKyzB,WAAW33B,GAAQI,KAAK+oB,IACzBjlB,MAAK2wB,GAAUE,OAAO5L,EAAMuI,SAC5BjvB,MAAM2Z,KAAKlY,MAAK4wB,GAAqB5W,WAChCle,QAAO,EAAC,CAAGorC,KAAOA,IAAMjiB,EAAMuI,UAC9BtxB,KAAI,EAAE4jC,KAAO9/B,MAAK4wB,GAAqBC,OAAOiP,IAAG,GAE7D,GCpDLrlC,OAAOC,eAAeysC,EAAS,aAAc,CAAEvsC,OAAO,IACpCusC,EAAAC,gBAAG,EACrB,MAAMxG,GAAoBrhC,EACpB8nC,GAAuBplC,EACvBqlC,GAAwBn8B,EACxBo8B,GAA8Bn8B,GAC9Bo8B,GAAoBn8B,GACpBo8B,GAAoBxnB,GAC1B,MAAMmnB,WAAmBxG,GAAkB/W,aACvC6d,IACAC,IACAC,IACArX,GACAD,GACAtF,GACA6c,IAA0B/mC,IACtBd,MAAK4nC,GAAkBxF,eAAethC,EAAQ,EAElDgnC,IAA0B35B,MAAO45B,IAC7B,MAAMjnC,EAAUinC,EAAajnC,QACA,OAAzBinC,EAAa3hB,UACbtlB,EAAiB,QAAIinC,EAAa3hB,eAEhCpmB,MAAK2nC,GAAWhZ,YAAY7tB,EAAQ,EAE9CjB,YAAYmoC,EAAenK,EAAeC,EAAcmD,EAAQjW,GAC5DjrB,QACAC,MAAKgrB,EAAUA,EACfhrB,MAAKuwB,EAA0B,IAAIgX,GAA4B9E,uBAC/DziC,MAAKswB,EAAgB,IAAImX,GAAkBV,aAC3C/mC,MAAK0nC,GAAgB,IAAIL,GAAqBvc,gBAAgB9qB,MAAK8nC,IAAyB,IAAM1gC,QAAQ6G,WAAWjO,MAAKgrB,GAC1HhrB,MAAK2nC,GAAaK,EAClBhoC,MAAK2nC,GAAWM,aAAajoC,MAAK6nC,IAClC7nC,MAAK4nC,GAAoB,IAAIN,GAAsB3G,iBAAiB3gC,MAAKswB,EAAeuN,EAAe,IAAI2J,GAAkB3C,aAAa7kC,MAAO89B,EAAcmD,EAAQjhC,MAAKuwB,EAAyBvwB,MAAKgrB,GAC1MhrB,MAAK4nC,GAAkB5d,GAAG,YAAa4R,IACnC57B,KAAK8lC,oBAAoBlK,EAAS,GAEzC,CACDp1B,4BAA4BwhC,EAAenK,EAAeC,EAAcmD,EAAQjW,GAC5E,MAAMkd,EAAS,IAAId,GAAWY,EAAenK,EAAeC,EAAcmD,EAAQjW,GAC5E2B,EAAYkR,EAAcE,gBAUhC,aARMpR,EAAUC,YAAY,4BAA6B,CAAEub,UAAU,UAE/Dxb,EAAUC,YAAY,uBAAwB,CAChD0Q,YAAY,EACZC,wBAAwB,EACxB17B,SAAS,UAEPqmC,EAAOE,yBACNF,CACV,CACD/5B,qCACU/G,QAAQ4N,IAAIhV,MAAKuwB,EAClBwO,sBACA7iC,KAAK23B,GAAMA,EAAEsL,gBACrB,CAID2G,oBAAoBiC,GAChB/nC,MAAK0nC,GAAcltB,IAAIutB,EAC1B,CACDM,QACIroC,MAAK2nC,GAAWU,OACnB,CACD5C,4BACI,OAAOzlC,MAAKuwB,CACf,EAEa+X,EAAAlB,WAAGA,mBCtErB3sC,OAAOC,eAAeiyB,GAAS,aAAc,CAAE/xB,OAAO,IACrC+xB,GAAA4b,eAAG,EACpB,MAAM3H,GAAoBrhC,EAC1B,MAAMgpC,WAAkB3H,GAAkB/W,aACtCgU,IACAU,IACA1+B,YAAYg+B,EAAeU,GACvBx+B,QACAC,MAAK69B,GAAiBA,EACtB79B,MAAKu+B,GAAaA,CACrB,CAOD/3B,cAAcq3B,EAAeU,GACzB,OAAO,IAAIgK,GAAU1K,EAAeU,EACvC,CAMD3R,YAAYwC,KAAW7pB,GACnB,MAAMijC,EAAQjjC,EAAO,GACrB,OAAOvF,MAAK69B,GAAejR,YAAYwC,EAAQoZ,EAAOxoC,MAAKu+B,GAC9D,EAEY5R,GAAA4b,UAAGA,aC9CpB9tC,OAAOC,eAAemjC,GAAS,aAAc,CAAEjjC,OAAO,IACjCijC,GAAA4K,mBAAG,EACxB,MAAMC,GAAiBnpC,GAqGFs+B,GAAA4K,cAhGrB,MACId,IACA1I,IAEA0J,IAAqB,IAAI9pC,IACzB+pC,IAAoB,IAAI/pC,IACxBgqC,IACAC,IAAU,EACVjpC,YAAY8nC,EAAWkB,EAAM,UACzB7oC,MAAK2nC,GAAaA,EAClB3nC,MAAK6oC,GAAOA,EACZ7oC,MAAK2nC,GAAWM,aAAajoC,MAAK0uB,IAClC1uB,MAAKi/B,GAAoByJ,GAAeH,UAAUxmC,OAAO/B,KAAM,KAClE,CAIDqoC,QACIroC,MAAK2nC,GAAWU,QAChB,IAAK,MAAM,EAAGrc,OAAEA,MAAahsB,MAAK4oC,GAC9B5c,EAAO,IAAI1wB,MAAM,iBAErB0E,MAAK4oC,GAAkBG,QACvB/oC,MAAK2oC,GAAmBI,OAC3B,CAIDhL,gBACI,OAAO/9B,MAAKi/B,EACf,CAMDR,aAAaF,GACT,MAAM5R,EAAY3sB,MAAK2oC,GAAmB1wB,IAAIsmB,GAC9C,IAAK5R,EACD,MAAM,IAAIrxB,MAAM,0BAEpB,OAAOqxB,CACV,CACDC,YAAYwC,EAAQ7pB,EAAQg5B,GACxB,OAAO,IAAIn3B,SAAQ,CAAC6G,EAAS+d,KACzB,MAAM9K,EAAKlhB,MAAK8oC,KAChB9oC,MAAK4oC,GAAkB7pC,IAAImiB,EAAI,CAAEjT,UAAS+d,WAC1C,MAAMgd,EAAa,CAAE9nB,KAAIkO,SAAQ7pB,UAC7Bg5B,IACAyK,EAAWzK,UAAYA,GAE3B,MAAM0K,EAAavpC,KAAKC,UAAUqpC,GAC5BE,EAAgBxpC,KAAKC,UAAUqpC,EAAY,KAAM,GACvDhpC,MAAK2nC,GAAWhZ,YAAYsa,GAC5BjpC,MAAK6oC,GAAK,SAAUK,EAAc,GAEzC,CACDxa,IAAc5tB,IACV,MAAMqoC,EAASzpC,KAAK+M,MAAM3L,GACpBooC,EAAgBxpC,KAAKC,UAAUwpC,EAAQ,KAAM,GAInD,GAHAnpC,MAAK6oC,GAAK,aAAcK,GAGF,4BAAlBC,EAAO/Z,OAAsC,CAC7C,MAAMmP,UAAEA,GAAc4K,EAAO5jC,OAC7BvF,MAAK2oC,GAAmB5pC,IAAIw/B,EAAWmK,GAAeH,UAAUxmC,OAAO/B,KAAMu+B,GAChF,MACI,GAAsB,8BAAlB4K,EAAO/Z,OAAwC,CACpD,MAAMmP,UAAEA,GAAc4K,EAAO5jC,OACdvF,MAAK2oC,GAAmB1wB,IAAIsmB,IAEvCv+B,MAAK2oC,GAAmB9X,OAAO0N,EAEtC,CACD,QAAkBzgC,IAAdqrC,EAAOjoB,GAAkB,CAEzB,MAAMkoB,EAAYppC,MAAK4oC,GAAkB3wB,IAAIkxB,EAAOjoB,IAChDkoB,IACID,EAAOv9B,OACPw9B,EAAUn7B,QAAQk7B,EAAOv9B,QAEpBu9B,EAAOjoC,OACZkoC,EAAUpd,OAAOmd,EAAOjoC,OAGnC,MACI,GAAIioC,EAAO/Z,OAAQ,CACpB,MAAMgR,EAAS+I,EAAO5K,UAChBv+B,MAAK2oC,GAAmB1wB,IAAIkxB,EAAO5K,WACnCv+B,MAAKi/B,GACPmB,GACAA,EAAOhW,KAAK+e,EAAO/Z,OAAQ+Z,EAAO5jC,QAAU,CAAA,EAEnD,cCpFT9K,OAAOC,eAAe2uC,GAAS,aAAc,CAAEzuC,OAAO,IAC5ByuC,GAAAC,wBAAG,EAqBHD,GAAAC,mBApB1B,MACI5a,IAAa,KACb6a,IACA1pC,YAAY0pC,GACRvpC,MAAKupC,GAAaA,EAClBvpC,MAAKupC,GAAWvf,GAAG,WAAYlpB,IAC3Bd,MAAK0uB,KAAa5tB,EAAQ,GAEjC,CACDmnC,aAAavZ,GACT1uB,MAAK0uB,GAAaA,CACrB,CACDC,YAAY7tB,GACRd,MAAKupC,GAAWC,KAAK1oC,EACxB,CACDunC,QACIroC,MAAK0uB,GAAa,KAClB1uB,MAAKupC,GAAWlB,OACnB,eCpBL5tC,OAAOC,eAAcC,EAAU,aAAc,CAAEC,OAAO,IACtDD,EAAA2uC,mBAA6B3uC,EAAwB8tC,cAAA9tC,EAAA4tC,eAAoB,EACzE,IAAIG,EAAiBnpC,GACrB9E,OAAOC,eAAeC,EAAS,YAAa,CAAEolB,YAAY,EAAM9H,IAAK,WAAc,OAAOywB,EAAeH,SAAU,IACnH,IAAIkB,EAAqBxnC,GACzBxH,OAAOC,eAAeC,EAAS,gBAAiB,CAAEolB,YAAY,EAAM9H,IAAK,WAAc,OAAOwxB,EAAmBhB,aAAc,IAC/H,IAAIiB,EAA0Bv+B,GAC9B1Q,OAAOC,eAAeC,EAAS,qBAAsB,CAAEolB,YAAY,EAAM9H,IAAK,WAAc,OAAOyxB,EAAwBJ,kBAAmB,mBCvB9I7uC,OAAOC,eAAeivC,GAAS,aAAc,CAAE/uC,OAAO,IACtD+uC,GAAAd,IAAcc,GAAAC,kBAAuB,EAiBrC,MAAM7e,GAAWxrB,EAWjB,SAASsqC,GAA6BC,GAClC,MAAMC,EAAc,GAAGD,QACjBE,EAAoBC,SAASC,eAAeH,GAClD,GAAIC,EACA,OAAOA,EAEX,MAAMG,EAAeF,SAASC,eAAe,WACvCE,EAAUH,SAASI,cAAc,OACvCD,EAAQE,UAAY,UACpBH,EAAaI,YAAYH,GACzB,MAAMI,EAAWP,SAASI,cAAc,OAIxC,OAHAG,EAASF,UAAY,OACrBE,EAASC,UAAY,OAAOX,kBAAwBC,wBACpDI,EAAaI,YAAYC,GAClBP,SAASC,eAAeH,EACnC,CAaoBJ,GAAAC,aAZpB,WAESc,WAAWT,SAASU,kBAGzBD,WAAWT,SAASU,gBAAgBF,UA9Bf,y8BAgCrBZ,GAA6B9e,GAASH,QAAQa,QAC9Coe,GAA6B9e,GAASH,QAAQ2X,MAC9CsH,GAA6B9e,GAASH,QAAQ8J,kBAC9CmV,GAA6B9e,GAASH,QAAQggB,KAClD,EAiBWjB,GAAAd,IAfX,SAAaiB,KAAYe,GAErB,IAAKH,WAAWT,SAASU,gBACrB,OAGJG,EAAOC,QAAQC,mBAAmBtrC,KAAKC,UAAU,CAAEmqC,UAASe,cAC5D,MAAMI,EAAmBpB,GAA6BC,GAGhDoB,EAAcjB,SAASI,cAAc,OAC3Ca,EAAYZ,UAAY,MACxBY,EAAYC,YAAcN,EAASvtC,KAAK,KACxC2tC,EAAiBV,YAAYW,EACjC;;;;;;;;;;;;;;;;;;;ACtDA,IAAItrB,GAAmB5f,GAAQA,EAAK4f,kBAAqBnlB,OAAOsH,OAAM,SAAa8d,EAAG/Z,EAAG/J,EAAG+jB,QAC7EhiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3B,IAAIqvC,EAAO3wC,OAAO4wC,yBAAyBvlC,EAAG/J,GACzCqvC,KAAS,QAASA,GAAQtlC,EAAEzB,WAAa+mC,EAAKE,UAAYF,EAAKG,gBAClEH,EAAO,CAAErrB,YAAY,EAAM9H,IAAK,WAAa,OAAOnS,EAAE/J,EAAG,IAE3DtB,OAAOC,eAAemlB,EAAGC,EAAIsrB,EAChC,EAAA,SAAcvrB,EAAG/Z,EAAG/J,EAAG+jB,QACThiB,IAAPgiB,IAAkBA,EAAK/jB,GAC3B8jB,EAAEC,GAAMha,EAAE/J,EACb,GACGmkB,GAAsBlgB,GAAQA,EAAKkgB,qBAAwBzlB,OAAOsH,OAAM,SAAa8d,EAAGM,GACxF1lB,OAAOC,eAAemlB,EAAG,UAAW,CAAEE,YAAY,EAAMnlB,MAAOulB,GAClE,EAAI,SAASN,EAAGM,GACbN,EAAW,QAAIM,CACnB,GACIC,GAAgBpgB,GAAQA,EAAKogB,cAAiB,SAAUhc,GACxD,GAAIA,GAAOA,EAAIC,WAAY,OAAOD,EAClC,IAAIwH,EAAS,CAAA,EACb,GAAW,MAAPxH,EAAa,IAAK,IAAIrI,KAAKqI,EAAe,YAANrI,GAAmBtB,OAAO8B,UAAUC,eAAeC,KAAK2H,EAAKrI,IAAI6jB,GAAgBhU,EAAQxH,EAAKrI,GAEtI,OADAmkB,GAAmBtU,EAAQxH,GACpBwH,CACX,EACAnR,OAAOC,eAAe8wC,EAAS,aAAc,CAAE5wC,OAAO,IACtD,MAAM6wC,GAASrrB,GAAa7gB,GACtB04B,GAAah2B,EACbypC,GAAkBvgC,EAClBwgC,GAAavgC,GACb2f,GAAW1f,EACXy1B,GAA2B7gB,GAC3B2rB,GAAqBC,GAErBC,GAuMN39B,iBACI,OAAO,IAAI/G,SAAS6G,IAChB88B,OAAOgB,gBAAmBhY,KACtB,EAAI6X,GAAmB/C,KAAK9d,GAASH,QAAQa,OAAQ,qBAAsBsI,GAC3E9lB,EAAQ8lB,EAAS,CACpB,GAET,CA9MgCiY,GAC3B,YACD,EAAIJ,GAAmBhC,gBAEvB,MAAM9L,QAAqBgO,GACrBtG,QA+BV,SAA0B1H,GACtB,MAAMmO,EACFvd,IAAa,KACb7uB,cACIkrC,OAAOmB,cAAiBjD,IAEpB,IAAIkD,GADJ,EAAIP,GAAmB/C,KAAK9d,GAASH,QAAQ2X,KAAM,aAAc0G,GAEjE,IACIkD,EAAgBF,GAAoBG,GAAkBnD,EAMzD,CAJD,MAAO9sC,GAGH,YADA6D,MAAKqsC,GAAkBpD,EAAYhR,GAAWrX,QAAQC,UAAUO,gBAAiBjlB,EAAE2E,QAAS,KAE/F,CACDd,MAAK0uB,IAAYjyB,KAAK,KAAM0vC,EAAc,CAEjD,CACDlE,aAAavZ,GACT1uB,MAAK0uB,GAAaA,CACrB,CACDC,YAAY7tB,GACR,MAAMmoC,EAAavpC,KAAKC,UAAUmB,GAClCiqC,OAAOuB,iBAAiBrD,IACxB,EAAI2C,GAAmB/C,KAAK9d,GAASH,QAAQ2X,KAAM,SAAU0G,EAChE,CACDZ,QACIroC,MAAK0uB,GAAa,KAClBqc,OAAOmB,cAAgB,IAC1B,CACDG,IAAkBE,EAAkBC,EAAW5mC,EAAcwgB,GACzD,MAAMkc,EAAgB2J,GAAoBQ,GAAkBF,EAAkBC,EAAW5mC,GACrFwgB,EAEApmB,KAAK2uB,YAAY,IACV2T,EACHlc,YAIJpmB,KAAK2uB,YAAY2T,EAExB,CACD97B,UAAoB5L,GAChB,OAAc,OAAVA,EACO,OAEP2D,MAAMC,QAAQ5D,GACP,eAEGA,CACjB,CACD4L,UAAyByiC,EAAYuD,EAAW5mC,GAG5C,IAAI8mC,EACJ,IACI,MAAM1D,EAAatpC,KAAK+M,MAAMw8B,GACuB,WAAjDgD,GAAoBU,GAAa3D,IACjC,OAAQA,IACR0D,EAAY1D,EAAW9nB,GAGtB,CAAT,MAAS,CACT,MAAO,CACHA,GAAIwrB,EACJxrC,MAAOsrC,EACP1rC,QAAS8E,EAGhB,CACDY,UAAyByiC,GACrB,IAAIkD,EACJ,IACIA,EAAgBzsC,KAAK+M,MAAMw8B,EAI9B,CAFD,MACI,MAAM,IAAI3tC,MAAM,4BACnB,CACD,MAAMsS,EAAaq+B,GAAoBU,GAAaR,GACpD,GAAmB,WAAfv+B,EACA,MAAM,IAAItS,MAAM,gCAAgCsS,KAGpD,MAAMsT,GAAEA,EAAEkO,OAAEA,EAAM7pB,OAAEA,GAAW4mC,EACzBS,EAASX,GAAoBU,GAAazrB,GAChD,GAAe,WAAX0rB,IAAwB7vC,OAAOD,UAAUokB,IAAOA,EAAK,EAGrD,MAAM,IAAI5lB,MAAM,qCAAqCsxC,KAEzD,MAAMC,EAAaZ,GAAoBU,GAAavd,GACpD,GAAmB,WAAfyd,EACA,MAAM,IAAIvxC,MAAM,kCAAkCuxC,KAEtD,MAAMC,EAAab,GAAoBU,GAAapnC,GACpD,GAAmB,WAAfunC,EACA,MAAM,IAAIxxC,MAAM,kCAAkCwxC,KAEtD,IAAI1mB,EAAU+lB,EAAc/lB,QAC5B,QAAgBtoB,IAAZsoB,EAAuB,CACvB,MAAM2mB,EAAcd,GAAoBU,GAAavmB,GACrD,GAAoB,WAAhB2mB,EACA,MAAM,IAAIzxC,MAAM,mCAAmCyxC,KAGvC,KAAZ3mB,IACAA,OAAUtoB,EAEjB,CACD,MAAO,CAAEojB,KAAIkO,SAAQ7pB,SAAQ6gB,UAChC,EAEL,OAAOslB,GAAgBtE,WAAW4F,eAAe,IAAIf,EA5IzD,WAKI,MAAMgB,EACFve,IAAa,KACb7uB,cACIkrC,OAAOH,IAAIsC,UAAapsC,IACpBd,MAAK0uB,IAAYjyB,KAAK,KAAMqE,EAAQ,CAE3C,CACDmnC,aAAavZ,GACT1uB,MAAK0uB,GAAaA,CACrB,CACDC,YAAY7tB,GACRiqC,OAAOH,IAAIpB,KAAK1oC,EACnB,CACDunC,QACIroC,MAAK0uB,GAAa,KAClBqc,OAAOH,IAAIsC,UAAY,IAC1B,EAEL,OAAO,IAAIvB,GAAWlD,cAAc,IAAIwE,GAAsB,IAAIpC,MAC9D,EAAIe,GAAmB/C,KAAK9d,GAASH,QAAQggB,OAAQC,EAAS,GAEtE,CAkHgFsC,GAAuBrP,EAAc,IAAIsP,GAAkBxB,GAAmB/C,IAC9J,CAjJ6BwE,CAAiBvP,IAC1C,EAAI8N,GAAmB/C,KAAK9d,GAASH,QAAQa,OAAQ,YACrD+Z,EAAWM,oBAAoBhF,GAAyBP,oBAAoB8B,eAAe,CAAEiL,UAAU,GAAQ,MAClH,EAPI,GAsJL,MAAMF,GACFtnB,4BAA4BvgB,GACxB,OAAOkmC,GAAOnrB,OAAOwF,4BAA4BvgB,EACpD,CACD0gB,+BAA+B1gB,GAC3B,OAAOkmC,GAAOnrB,OAAO2F,+BAA+B1gB,EACvD,CACDsf,qBAAqBtf,GACjB,OAAOkmC,GAAOnrB,OAAOuE,qBAAqBtf,EAC7C,CACDohB,wBAAwBphB,GACpB,OAAOkmC,GAAOnrB,OAAOqG,wBAAwBphB,EAChD,CACDigB,oBAAoBjgB,GAChB,OAAOkmC,GAAOnrB,OAAOkF,oBAAoBjgB,EAC5C,CACDogB,kBAAkBpgB,GACd,OAAOkmC,GAAOnrB,OAAOqF,kBAAkBpgB,EAC1C,CACD6jB,uBAAuB7jB,GACnB,OAAOkmC,GAAO9qB,IAAIyI,uBAAuB7jB,EAC5C,CACD+jB,sBAAsB/jB,GAClB,OAAOkmC,GAAO9qB,IAAI2I,sBAAsB/jB,EAC3C,CACDokB,qBAAqBpkB,GACjB,OAAOkmC,GAAO/oB,QAAQiH,qBAAqBpkB,EAC9C,CACD2hB,oBAAoB3hB,GAChB,OAAOkmC,GAAOlrB,gBAAgB2G,oBAAoB3hB,EACrD,CACDuhB,mBAAmBvhB,GACf,OAAOkmC,GAAOlrB,gBAAgBuG,mBAAmBvhB,EACpD,CACD8hB,kBAAkB9hB,GACd,OAAOkmC,GAAOlrB,gBAAgB8G,kBAAkB9hB,EACnD,CACDgiB,iBAAiBhiB,GACb,OAAOkmC,GAAOlrB,gBAAgBgH,iBAAiBhiB,EAClD,CACDkiB,6BAA6BliB,GACzB,OAAOkmC,GAAOlrB,gBAAgBkH,6BAA6BliB,EAC9D,CACDwjB,iBAAiBxjB,GACb,OAAOkmC,GAAOlrB,gBAAgBwI,iBAAiBxjB,EAClD"}