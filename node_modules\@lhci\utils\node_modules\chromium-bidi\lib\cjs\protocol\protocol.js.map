{"version": 3, "file": "protocol.js", "sourceRoot": "", "sources": ["../../../src/protocol/protocol.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAyCH,IAAiB,OAAO,CAoJvB;AApJD,WAAiB,OAAO;IA0CtB,mBAAmB;IAEnB,IAAY,SAcX;IAdD,WAAY,SAAS;QACnB,oBAAoB;QACpB,iDAAoC,CAAA;QACpC,oDAAuC,CAAA;QACvC,0CAA6B,CAAA;QAC7B,0CAA6B,CAAA;QAC7B,4CAA+B,CAAA;QAC/B,wCAA2B,CAAA;QAC3B,4CAA+B,CAAA;QAC/B,sDAAyC,CAAA;QACzC,+CAAkC,CAAA;QAClC,2CAA8B,CAAA;QAC9B,2DAA8C,CAAA;QAC9C,kBAAkB;IACpB,CAAC,EAdW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAcpB;IAQD,MAAa,aAAa;QAEf;QACA;QACA;QAHT,YACS,KAAwB,EACxB,OAAe,EACf,UAAmB;YAFnB,UAAK,GAAL,KAAK,CAAmB;YACxB,YAAO,GAAP,OAAO,CAAQ;YACf,eAAU,GAAV,UAAU,CAAS;QACzB,CAAC;QAEJ,eAAe,CAAC,SAAiB;YAC/B,OAAO;gBACL,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;QACJ,CAAC;KACF;IAfY,qBAAa,gBAezB,CAAA;IAED,MAAa,wBAAyB,SAAQ,aAAa;QACzD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACxD,CAAC;KACF;IAJY,gCAAwB,2BAIpC,CAAA;IAED,MAAa,qBAAsB,SAAQ,aAAa;QACtD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrD,CAAC;KACF;IAJY,6BAAqB,wBAIjC,CAAA;IAED,MAAa,yBAA0B,SAAQ,aAAa;QAC1D,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;KACF;IAJY,iCAAyB,4BAIrC,CAAA;IAED,MAAa,oBAAqB,SAAQ,aAAa;QACrD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;KACF;IAJY,4BAAoB,uBAIhC,CAAA;IAED,MAAa,oBAAqB,SAAQ,aAAa;QACrD,YAAY,OAAe;YACzB,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;KACF;IAJY,4BAAoB,uBAIhC,CAAA;IAED,MAAa,mBAAoB,SAAQ,aAAa;QACpD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;KACF;IAJY,2BAAmB,sBAI/B,CAAA;IAED,MAAa,qBAAsB,SAAQ,aAAa;QACtD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrD,CAAC;KACF;IAJY,6BAAqB,wBAIjC,CAAA;IAED,MAAa,0BAA2B,SAAQ,aAAa;QAC3D,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1D,CAAC;KACF;IAJY,kCAA0B,6BAItC,CAAA;IAED,MAAa,uBAAwB,SAAQ,aAAa;QACxD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACvD,CAAC;KACF;IAJY,+BAAuB,0BAInC,CAAA;IAED,MAAa,qBAAsB,SAAQ,aAAa;QACtD,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrD,CAAC;KACF;IAJY,6BAAqB,wBAIjC,CAAA;IAED,MAAa,6BAA8B,SAAQ,aAAa;QAC9D,YAAY,OAAe,EAAE,UAAmB;YAC9C,KAAK,CAAC,SAAS,CAAC,oBAAoB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC7D,CAAC;KACF;IAJY,qCAA6B,gCAIzC,CAAA;AACH,CAAC,EApJgB,OAAO,GAAP,eAAO,KAAP,eAAO,QAoJvB;AAoRD,+DAA+D;AAC/D,IAAiB,MAAM,CAoRtB;AApRD,WAAiB,MAAM;IA+QrB,IAAY,UAEX;IAFD,WAAY,UAAU;QACpB,6CAA+B,CAAA;IACjC,CAAC,EAFW,UAAU,GAAV,iBAAU,KAAV,iBAAU,QAErB;IAEY,gBAAS,GAAG,QAAQ,CAAC;AACpC,CAAC,EApRgB,MAAM,GAAN,cAAM,KAAN,cAAM,QAoRtB;AAED,+DAA+D;AAC/D,IAAiB,eAAe,CA6L/B;AA7LD,WAAiB,eAAe;IAqL9B,IAAY,UAKX;IALD,WAAY,UAAU;QACpB,gDAAkC,CAAA;QAClC,wEAA0D,CAAA;QAC1D,oEAAsD,CAAA;QACtD,wEAA0D,CAAA;IAC5D,CAAC,EALW,UAAU,GAAV,0BAAU,KAAV,0BAAU,QAKrB;IAEY,yBAAS,GAAG,iBAAiB,CAAC;AAC7C,CAAC,EA7LgB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QA6L/B;AAED,4DAA4D;AAC5D,IAAiB,GAAG,CAqCnB;AArCD,WAAiB,GAAG;IAgCL,aAAS,GAAG,KAAK,CAAC;IAE/B,IAAY,UAEX;IAFD,WAAY,UAAU;QACpB,mDAAqC,CAAA;IACvC,CAAC,EAFW,UAAU,GAAV,cAAU,KAAV,cAAU,QAErB;AACH,CAAC,EArCgB,GAAG,GAAH,WAAG,KAAH,WAAG,QAqCnB;AAED,IAAiB,OAAO,CAyHvB;AAzHD,WAAiB,OAAO;IAkHT,iBAAS,GAAG,SAAS,CAAC;IAEnC,IAAY,UAIX;IAJD,WAAY,UAAU;QACpB,kEAAoD,CAAA;QACpD,kEAAoD,CAAA;QACpD,oDAAsC,CAAA;IACxC,CAAC,EAJW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAIrB;AACH,CAAC,EAzHgB,OAAO,GAAP,eAAO,KAAP,eAAO,QAyHvB;AAED,IAAiB,GAAG,CA6CnB;AA7CD,WAAiB,GAAG;IAwCL,aAAS,GAAG,KAAK,CAAC;IAE/B,IAAY,UAEX;IAFD,WAAY,UAAU;QACpB,sDAAwC,CAAA;IAC1C,CAAC,EAFW,UAAU,GAAV,cAAU,KAAV,cAAU,QAErB;AACH,CAAC,EA7CgB,GAAG,GAAH,WAAG,KAAH,WAAG,QA6CnB"}