{"core/audits/accessibility/accesskeys.js | description": {"message": "Tastele de acces permit utilizatorilor să focalizeze rapid o parte a paginii. Pentru o navigare corectă, fiecare tastă de acces trebuie să fie unică. [Află mai multe despre tastele de acces](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Valorile `[accesskey]` nu sunt unice"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Valorile `[accesskey]`sunt unice"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON>re `role` ARIA acceptă un anumit subset de atribute `aria-*`. Nepotrivirea acestora anulează atributele `aria-*`. [Află cum să asociezi atributele ARIA cu rolurile respective](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributele `[aria-*]` nu se potrivesc cu rolurile"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributele `[aria-*]` se potrivesc cu rolurile"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Când un element nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află cum să faci elementele de comandă mai accesibile](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementele `button`, `link` și `menuitem` nu au nume accesibile."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementele `button`, `link` și `menuitem` au nume accesibile"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Tehnologiile de asistare, precum cititoarele de ecran, funcționează inconsecvent atunci când `aria-hidden=\"true\"` este setat pentru documentul `<body>`. [Află cum `aria-hidden` afectează corpul documentului](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` există în documentul `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` nu există în documentul `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Descendenții focalizabili dintr-un element `[aria-hidden=\"true\"]` împiedică disponibilitatea elementelor interactive respective pentru utilizatorii tehnologiilor de asistare, cum ar fi cititoarele de ecran. [Află cum `aria-hidden` afectează elementele focalizabile](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementele `[aria-hidden=\"true\"]` con<PERSON>in descendenți focalizabili"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elementele `[aria-hidden=\"true\"]` nu conțin descendenți focalizabili"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Când un câmp de introducere a textului nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făcându-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află mai multe despre etichetele câmpurilor de introducere a textului](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "<PERSON>â<PERSON><PERSON><PERSON> de introducere pentru ARIA nu au nume accesibile"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Câmp<PERSON><PERSON> de introducere pentru ARIA au nume accesibile"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Când un element contor nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află cum să denumești elemente `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Elementele ARIA `meter` nu au nume accesibile."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Elementele ARIA `meter` au nume accesibile"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Când un element `progressbar` nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află cum să etichetezi elementele `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Elementele ARIA `progressbar` nu au nume accesibile."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Elementele ARIA `progressbar` au nume accesibile"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Unele roluri ARIA au atribute obligatorii care descriu starea elementului cititoarelor de ecrane. [Află mai multe despre rolurile și atributele obligatorii](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` nu au toate atributele `[aria-*]` necesare"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` au toate atributele `[aria-*]` obligatorii"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Unele roluri ARIA principale trebuie să conțină roluri secundare specifice pentru a-și îndeplini funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe despre rolurile și elementele secundare obligatorii](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Din elementele unui rol ARIA `[role]` care impun ca elementele secundare să conțină un anumit element `[role]` lipsește unul sau toate elementele secundare necesare respective."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementele unui rol ARIA `[role]` care impun ca elementele secundare să conțină un anumit element `[role]` includ toate elementele secundare necesare."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Unele roluri ARIA secundare trebuie să fie conținute de roluri principale specifice pentru a-și îndeplini corect funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe despre rolurile ARIA și elementul părinte obligatoriu](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` nu sunt conținute de elementul părinte impus"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` sunt conținute de elementul părinte impus"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Rolurile ARIA trebuie să aibă valori valide pentru a-și îndeplini funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe despre rolurile ARIA valide](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` nu sunt valide"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` sunt valide"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Când un câmp de comutare nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află mai multe despre câmpurile de comutare](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Câmpurile de comutare pentru ARIA nu au nume accesibile"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Câmpurile de comutare pentru ARIA au nume accesibile"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Când un element balon explicativ nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făcându-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află cum să denumești elemente `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Elementele ARIA `tooltip` nu au nume accesibile."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Elementele ARIA `tooltip` au nume accesibile"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Când un element `treeitem` nu are un nume accesibil, cititoarele de ecran îl anunță cu o denumire generică, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află mai multe despre etichetarea elementelor `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Elementele ARIA `treeitem` nu au nume accesibile."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Elementele ARIA `treeitem` au nume accesibile"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Tehnologiile de asistare, precum cititoarele de ecran, nu pot interpreta atributele ARIA cu valori nevalide. [Află mai multe despre valorile valide pentru atributele ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributele `[aria-*]` nu au valori valide"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributele `[aria-*]` au valori valide"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Tehnologiile de asistare, precum cititoarele de ecran, nu pot interpreta atributele ARIA cu nume nevalide. [Află mai multe despre atributele ARIA valide](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributele `[aria-*]` sunt nevalide sau scrise greșit"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributele `[aria-*]` sunt valide și nu sunt scrise greșit"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elemente cu probleme"}, "core/audits/accessibility/button-name.js | description": {"message": "Când un buton nu are un nume accesibil, cititoarele de ecran îl anunță ca „buton”, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află cum să faci butoanele mai accesibile](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Butoanele nu au un nume accesibil"}, "core/audits/accessibility/button-name.js | title": {"message": "Butoanele au un nume accesibil"}, "core/audits/accessibility/bypass.js | description": {"message": "Dacă adaugi modalități de a ocoli conținutul repetitiv, utilizatorii tastaturii vor naviga pe pagină mai eficient. [Află mai multe despre blocările ocolirii](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Această pagină nu conține un titlu, un link de închidere sau o regiune de reper"}, "core/audits/accessibility/bypass.js | title": {"message": "Pagina conține un titlu, un link de închidere sau o regiune de reper"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Textul cu un contrast redus este dificil sau imposibil de citit pentru mulți utilizatori. [Află cum să asiguri un contrast suficient al culorilor](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Culorile din fundal și din prim-plan nu au un raport de contrast suficient."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Culorile din fundal și din prim-plan au un raport de contrast suficient"}, "core/audits/accessibility/definition-list.js | description": {"message": "Când listele de definiții nu sunt marcate corect, cititoarele de ecran pot produce un rezultat derutant sau inexact. [Află cum să structurezi corect listele de definiții](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` nu conțin doar grup<PERSON>le `<dt>` și `<dd>` ordonate corect, `<script>`, `<template>` sau elementele `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` con<PERSON>in doar grup<PERSON>le `<dt>` și `<dd>` ordonate corect, `<script>`, `<template>` sau elemente `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Elementele din lista de definiții (`<dt>` și `<dd>`) trebuie grupate într-un element principal `<dl>` pentru a se asigura că cititoarele de ecran le pot anunța corect. [Află cum să structurezi corect listele de definiții](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementele din lista de definiții nu sunt incluse în elementele `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Elementele din lista de definiții sunt incluse în elementele `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Titlul le oferă utilizatorilor de cititoare de ecran o prezentare generală a paginii, iar utilizatorii de motoare de căutare îl folosesc intensiv pentru a stabili dacă o pagină este relevantă pentru căutarea lor. [Află mai multe despre titlurile documentelor](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Documentul nu are un element `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Documentul are un element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Toate elementele focalizabile trebuie să aibă un `id` unic ca să fie vizibile pentru tehnologiile de asistare. [Află cum să remediezi dublurile de `id`](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atributele `[id]` ale elementelor focalizabile active nu sunt unice"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atributele `[id]` ale elementelor focalizabile active sunt unice"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Valoarea unui ID ARIA trebuie să fie unică pentru a preveni omiterea altor instanțe de tehnologiile de asistare. [Află cum să remediezi ID-urile ARIA dublură](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ID-urile ARIA nu sunt unice"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ID-urile ARIA sunt unice"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Câmpurile de formular cu etichete multiple pot fi anunțate în mod derutant de tehnologiile de asistare, precum cititoarele de ecran care folosesc prima, ultima sau toate etichetele. [Află cum să folosești etichetele de formular](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Câmpurile de formular au etichete multiple"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Niciun câmp de formular nu are etichete multiple"}, "core/audits/accessibility/frame-title.js | description": {"message": "Utilizatorii cititoarelor de ecran se bazează pe titlurile cadrelor pentru a descrie conținutul cadrelor. [Află mai multe despre titlurile cadrelor](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementele `<frame>` sau `<iframe>` nu au un titlu"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementele `<frame>` sau `<iframe>` au un titlu"}, "core/audits/accessibility/heading-order.js | description": {"message": "Titlurile ordonate corect care nu omit niveluri preiau structura semantică a paginii, simplificând navigarea și înțelegerea atunci când se folosesc tehnologii de asistare. [Află mai multe despre ordinea titlurilor](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elementele titlului nu apar în ordine descrescătoare succesivă"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elementele titlului apar în ordine descrescătoare succesivă"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Dacă o pagină nu specifică un atribut `lang`, cititorul de ecran presupune că pagina este în limba prestabilită pe care utilizatorul a ales-o când a configurat cititorul de ecran. Dacă pagina nu este în limba prestabilită, cititorul de ecran poate citi greșit textul paginii. [Află mai multe despre atributul `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elementul `<html>` nu are un atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Elementul `<html>` are un atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Indicarea unei etichete de [limbă BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide ajută cititoarele de ecran să anunțe corect textul. [Află cum să folosești atributul `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elementul `<html>` nu are o valoare validă pentru atributul `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Elementul `<html>` are o valoare validă pentru atributul `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Elementele informative ar trebui să conțină texte alternative descriptive scurte. Elementele decorative pot fi ignorate cu un atribut Alt gol. [Află mai multe despre atributul `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementele imaginii nu au atribute `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elementele imagine au atribute `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "C<PERSON>d o imagine este folosită ca buton `<input>`, furnizarea unui text alternativ poate ajuta utilizatorii cititorului de ecran să înțeleagă scopul butonului. [Află despre textul alternativ pentru imaginea input](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementele `<input type=\"image\">` nu au text `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementele `<input type=\"image\">` au text `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Etichetele asigură că opțiunile formularelor sunt anunțate corect de tehnologiile de asistare, precum cititoarele de ecran. [Află mai multe despre etichetele elementelor de formular](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Elementele formularului nu au etichete asociate"}, "core/audits/accessibility/label.js | title": {"message": "Elementele formularului au etichete asociate"}, "core/audits/accessibility/link-name.js | description": {"message": "Textul linkurilor (și textul alternativ pentru imagini, când sunt folosite ca linkuri) care se poate distinge, unic și pe care se poate focaliza, îmbunătățește navigarea pentru utilizatorii de cititoare de ecran. [Află cum să creezi linkuri accesibile](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nu au un nume care se poate distinge"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON> au un nume care se poate distinge"}, "core/audits/accessibility/list.js | description": {"message": "Cititoarele de ecran au un anumit mod de anunțare a listelor. Asigurarea unei structuri corecte a listei îmbunătățește rezultatele cititorului de ecran. [Află mai multe despre structura corectă a listelor](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listele nu conțin doar elemente `<li>` și elemente pe care se bazează scriptul (`<script>` și `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "<PERSON>ele conțin doar elemente `<li>` și elemente pe care se bazează scriptul (`<script>` și `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Cititoarele de ecran necesită ca elementele din listă (`<li>`) să fie conținute într-un element principal `<ul>`, `<ol>` sau `<menu>` pentru a fi anunțate corect. [Află mai multe despre structura corectă a listelor](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementele din listă (`<li>`) nu sunt incluse în elementele principale `<ul>`, `<ol>` sau `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Elementele din listă (`<li>`) sunt conținute în elementele principale `<ul>`, `<ol>` sau `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Utilizatorii nu se așteaptă ca o pagină să se actualizeze automat, iar dacă se întâmplă aceasta, focalizarea se va muta din nou în partea de sus a paginii. Acest lucru poate crea frustrare sau confuzie. [Află mai multe despre metaeticheta de actualizare](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Documentul folosește `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Documentul nu folosește `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Dezactivarea măririi sau micșorării este o problemă pentru utilizatorii cu vedere slabă, care se bazează pe mărirea ecranului pentru a vedea bine conținutul unei pagini web. [Află mai multe despre metaeticheta pentru spațiul vizibil](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` este folosit în elementul `<meta name=\"viewport\">` sau atributul `[maximum-scale]` este mai mic decât 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` nu se folosește în elementul `<meta name=\"viewport\">` și atributul `[maximum-scale]` nu este mai mic decât 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Cititoarele de ecran nu pot traduce conținut în alt format decât text. Adăugarea de text alternativ la elementele `<object>` ajută cititoarele de ecran să le transmită utilizatorilor înțelesul. [Află mai multe despre textul alternativ pentru elementele `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementele `<object>` nu au text alternativ"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementele `<object>` au text alternativ"}, "core/audits/accessibility/tabindex.js | description": {"message": "O valoare mai mare decât 0 implică o ordine explicită de navigare. Deși valid din punct de vedere tehnic, acest lucru creează adesea frustrări utilizatorilor care se bazează pe tehnologii de asistare. [Află mai multe despre atributul `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Unele elemente au o valoare `[tabindex]` mai mare decât 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Niciun element nu are o valoare `[tabindex]` mai mare decât 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Cititoarele de ecran au funcții care facilitează navigarea în tabele. Dacă se asigură că celulele `<td>` care folosesc atributul `[headers]` se referă doar la alte celule din același tabel, se poate îmbunătăți experiența pentru utilizatorii de cititoare de ecran. [Află mai multe despre atributul `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>le dintr-un element `<table>` care folosesc atributul `[headers]` se referă la un element `id` care nu se găsește în același tabel."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON>le dintr-un element `<table>` care folosesc atributul `[headers]` se referă la celule ale aceluiași tabel."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Cititoarele de ecran au funcții care facilitează navigarea în tabele. Dacă se asigură că antetele tabelelor se referă întotdeauna la unele seturi de celule, se poate îmbunătăți experiența pentru utilizatorii de cititoare de ecran. [Află mai multe despre antetele tabelelor](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementele `<th>` și elementele cu `[role=\"columnheader\"/\"rowheader\"]` nu au celule de date pe care să le descrie."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementele `<th>` și elementele cu `[role=\"columnheader\"/\"rowheader\"]` au celule de date pe care le descriu."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Indicarea unei etichete de [limbă BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide la elemente ajută la pronunțarea corectă a textului de un cititor de ecran. [Află cum să folosești atributul `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributele `[lang]` nu au o valoare validă"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributele `[lang]` au o valoare validă"}, "core/audits/accessibility/video-caption.js | description": {"message": "Când un videoclip are subtitrare, este mai simplu pentru utilizatorii surzi și cu dizabilități de auz să îi acceseze informațiile. [Află mai multe despre subtitrările videoclipurilor](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementele `<video>` nu conțin un element `<track>` cu `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementele `<video>` conțin un element `<track>` cu `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Valoare actuală"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Indicativ sugerat"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` ajută utilizatorii să trimită mai repede formulare. Ca să reduci efortul utilizatorilor, activează funcția setând atributul `autocomplete` la o valoare validă. [Află mai multe despre `autocomplete` în formulare](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` elemente nu au atributele`autocomplete` corecte"}, "core/audits/autocomplete.js | manualReview": {"message": "Necesită examinare manuală"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Verifică ordinea indicativelor"}, "core/audits/autocomplete.js | title": {"message": "`<input>` elemente folosesc corect `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Indicative de `autocomplete`: „{token}” nu este valid în {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Verifică ordinea indicativelor: „{tokens}” în {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Se poate acționa"}, "core/audits/bf-cache.js | description": {"message": "Multe navigări se realizează revenind la o pagină anterioară sau redirecționând din nou. Memoria cache înainte/înapoi (bfcache) poate accelera navigările de retur. [Află mai multe despre bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{Un motiv al erorii}few{# motive ale erorii}other{# de motive ale erorii}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivul erorii"}, "core/audits/bf-cache.js | failureTitle": {"message": "Pagina a împiedicat restabilirea memoriei cache înainte-înapoi"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON> de er<PERSON>e"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Nu se poate acționa"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "În așteptarea compatibilității cu browserul"}, "core/audits/bf-cache.js | title": {"message": "Pagina nu a împiedicat restabilirea memoriei cache înainte-înapoi"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Extensiile Chrome au afectat performanța de încărcare a acestei pagini. Încearcă să auditezi pagina în modul incognito sau dintr-un profil Chrome fără extensii."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Evaluarea scripturilor"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON> scripturilor"}, "core/audits/bootup-time.js | columnTotal": {"message": "Timp CPU total"}, "core/audits/bootup-time.js | description": {"message": "Poți reduce timpul petrecut cu analizarea, compilarea și executarea JS. Livrarea unor sarcini JS mai mici poate ajuta în acest sens. [Află cum să reduci timpul de executare JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Redu timpul de execuție JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Timpul de executare JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Elimină modulele JavaScript dublate și mari din grupuri pentru a reduce consumul inutil de byți prin activitatea din rețea. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Elimină modulele dublură din grupurile JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF-urile mari nu sunt eficiente pentru difuzarea conținutului animat. Folosește videoclipuri MPEG4 / WebM pentru animații și PNG / WebP pentru imagini statice în locul GIF-urilor ca să economisești date în rețea. [Află mai multe despre formatele eficiente ale videoclipurilor](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Folosește formate video pentru conținut animat"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfillurile și transformările fac posibilă folosirea noilor funcții JavaScript în browserele vechi. Însă pentru browserele moderne, majoritatea nu sunt necesare. Pentru grupul tău JavaScript, adoptă o strategie modernă de implementare a scriptului, care folosește detectarea funcțiilor module / nomodule, pentru a reduce volumul de cod transferat în browserele moderne și a menține totodată compatibilitatea cu browserele vechi. [Află cum să folosești JavaScript modern](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Nu folosi o versiune veche de JavaScript în browsere moderne"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formatele de imagine ca WebP și AVIF oferă adesea o comprimare mai bună decât PNG sau JPEG, ceea ce înseamnă descărcări mai rapide și mai puțin consum de date. [Află mai multe despre formatele de imagine moderne](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Difuzează imagini în formate moderne"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Poți încărca asincron imaginile ascunse sau pe cele din afara ecranului după ce toate resursele esențiale s-au încărcat, pentru a micșora valoarea Time to Interactive. [Află cum să amâni imaginile din afara ecranului](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Amână imaginile din afara ecranului"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursele blochează prima reprezentare vizuală a paginii. Poți să livrezi conținutul JS / CSS esențial inline și să amâni toate elementele JS / stilurile neesențiale. [Află cum să elimini resursele care blochează redarea](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimină resursele care blochează redarea"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Sarcinile mari de rețea îi costă pe utilizatori și sunt corelate cu timpi de încărcare îndelungați. [Află cum să reduci dimensiunea sarcinilor](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Dimensiunea totală a fost de {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evită sarcinile uriașe de rețea"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evită sarcinile uriașe de rețea"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minimizarea fișierelor CSS poate reduce dimensiunea sarcinilor de rețea. [Află cum să minimizezi fișierele CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Comprimă codul CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minimizarea fișierelor JavaScript poate reduce dimensiunea sarcinilor și timpul de analizare a scripturilor. [Află cum să minimizezi fișierele JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Comprimă codul JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Redu regulile nefolosite din foile de stil și amână codul CSS nefolosit pentru conținutul din partea superioară a paginii ca să reduci numărul de byți consumați de activitatea în rețea. [Află cum să reduci codul CSS nefolosit](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Redu conținutul CSS nefolosit"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Redu codul JavaScript nefolosit și amână încărcarea scripturilor până când sunt necesare ca să reduci numărul de byți consumați de activitatea în rețea. [Află cum să reduci codul JavaScript nefolosit](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Redu codul JavaScript nefolosit"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "O durată lungă a memoriei cache poate grăbi accesările repetate ale paginii. [Află mai multe despre politicile eficiente privind memoria cache](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{o resursă găsită}few{# resurse găsite}other{# de resurse găsite}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Difuzează elementele statice cu o politică eficientă de stocare în memoria cache"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Folosește o politică eficientă de stocare în memoria cache pentru elementele statice"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Imaginile optimizate se încarcă mai repede și consumă mai puține date mobile. [Află cum să codifici eficient imagini](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifică eficient imaginile"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensiunile reale"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensiunile afișate"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Imaginile erau mai mari decât dimensiunea a<PERSON>ă"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Imaginile erau potrivite pentru dimensiunea a<PERSON>ș<PERSON>ă"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Difuzează imagini de dimensiuni corespunzătoare ca să economisești date mobile și să obții o încărcare mai rapidă. [Află cum să adaptezi dimensiunea imaginilor](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Dimensionează corect imaginile"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Resursele bazate pe text trebuie comprimate (gzip, deflate sau brotli) pentru a minimiza numărul total de byți în rețea. [Află mai multe despre comprimarea textului](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Activează comprimarea textului"}, "core/audits/content-width.js | description": {"message": "Dacă lățimea conținutului aplicației nu se potrivește cu lățimea ariei vizibile, este posibil ca aplicația să nu fie optimizată pentru ecrane mobile. [Află cum să adaptezi dimensiunile conținutului pentru aria vizibilă](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Aria vizibilă de {innerWidth} px nu corespunde cu dimensiunea ferestrei, de {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Conținutul nu este dimensionat corect pentru aria vizibilă"}, "core/audits/content-width.js | title": {"message": "Conținutul este dimensionat corect pentru aria vizibilă"}, "core/audits/critical-request-chains.js | description": {"message": "Lanțurile de solicitări esențiale de mai jos îți arată ce resurse sunt încărcate cu prioritate ridicată. Poți să reduci lungimea lanțurilor, să reduci dimensiunea de descărcare a resurselor sau să amâni descărcarea de resurse inutile pentru a îmbunătăți încărcarea paginilor. [Află cum să eviți legarea solicitărilor critice](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{un lanț găsit}few{# lanțuri găsite}other{# de lanțuri găsite}}"}, "core/audits/critical-request-chains.js | title": {"message": "Evită legarea solicitărilor critice"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directivă"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Severitate"}, "core/audits/csp-xss.js | description": {"message": "O politică eficientă privind securitatea conținutului (CSP) reduce semnificativ riscul de atacuri de tip cross-site scripting (XSS). [Află cum să folosești o CSP pentru a împiedica XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxă"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Pagina conține o politică CSP definită într-o etichetă <meta>. Îți recomandăm să muți CSP într-un antet HTTP sau să definești alt CSP strict într-un antet HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Nu s-a găsit nicio politică CSP în modul de aplicare"}, "core/audits/csp-xss.js | title": {"message": "Asigură-te că politica CSP este eficientă împotriva atacurilor de tip XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Renunțare la dezvoltare/Avertisment"}, "core/audits/deprecations.js | columnLine": {"message": "Rând"}, "core/audits/deprecations.js | description": {"message": "API-urile învechite vor fi eliminate, în final, din browser. [Află mai multe despre API-urile învechite](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{A fost identificat un avertisment}few{Au fost identificate # avertismente}other{Au fost identificate # de avertismente}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Folosește API-uri învechite"}, "core/audits/deprecations.js | title": {"message": "Evită API-urile învechite"}, "core/audits/dobetterweb/charset.js | description": {"message": "Este necesară o declarație privind codificarea caracterelor. Aceasta se poate face folosind o etichetă `<meta>` în primii 1.024 de byți din HTML sau în antetul de răspuns Content-Type din HTTP. [Află mai multe despre declararea codificării caracterelor](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Declarația privind setul de caractere lipsește sau apare prea târziu în HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Setează corespunzător setul de caractere"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Specificarea unui doctype împiedică browserul să treacă la modul caracteristici speciale. [Află mai multe despre declarația privind doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Numele doctype trebuie să fie șirul `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Documentul conține un `doctype` care declanșează `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Documentul trebuie să conțină un doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Se aștepta ca publicId să fie un șir gol"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Se aștepta ca systemId să fie un șir gol"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Documentul conține un `doctype` care declanșează `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Pagina nu are doctype HTML, așadar declanșează modul caracteristici speciale"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Pagina are doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistică"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valoare"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Un DOM mare va crește utilizarea memoriei și va produce [calcule de stil](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) mai lungi și [rearanj<PERSON>ri ale aspectului](https://developers.google.com/speed/articles/reflow) costisitoare. [Află cum să eviți o dimensiune DOM excesivă](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}few{# elemente}other{# de elemente}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evită o dimensiune DOM excesivă"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Adâncimea DOM maximă"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Numărul total de elemente DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Numărul maxim de elemente subordonate"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evită o dimensiune DOM excesivă"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Utilizatorii nu au încredere sau sunt derutați de site-urile care le solicită locația fără context. Asociază solicitarea cu o acțiune a utilizatorilor. [Află mai multe despre permisiunea pentru localizare geografică](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicită permisiunea de localizare geografică la încărcarea paginii"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evită solicitarea permisiunii de localizare geografică la încărcarea paginii"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipul problemei"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problemele înregistrate în panoul `Issues` din Chrome DevTools indică probleme nerezolvate. Acestea pot fi provocate de erorile de solicitare din rețea, măsurile de securitate insuficiente și de alte probleme ale browserului. Deschide panoul Probleme în Chrome DevTools pentru mai multe detalii despre fiecare problemă."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problemele au fost înregistrate în panoul `Issues` din Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blocată de politica privind sursele multiple"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> folosesc intensiv resursele"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Nu există probleme în panoul `Issues` din Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versiune"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Toate bibliotecile JavaScript front-end detectate în pagină. [Află mai multe despre acest audit pentru diagnosticarea și detectarea bibliotecii JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Biblioteci JavaScript detectate"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Pentru utilizatorii cu conexiuni lente, scripturile externe injectate dinamic prin `document.write()` pot întârzia încărcarea paginilor cu zeci de secunde. [Află cum să eviți document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evită `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evită `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Utilizatorii nu au încredere sau sunt derutați de site-urile care solicită să trimită notificări fără context. Asociază solicitarea cu gesturile utilizatorilor. [Află mai multe despre obținerea permisiunii pentru notificări în mod responsabil](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicită permisiunea de notificare la încărcarea paginii"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evită solicitarea permisiunii de notificare la încărcarea paginii"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 oferă multe beneficii față de HTTP/1.1, inclusiv antete binare și anunțuri multiplex. [Află mai multe despre HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{O solicitare nu a fost difuzată prin HTTP/2}few{# solicitări nu au fost difuzate prin HTTP/2}other{# de solicitări nu au fost difuzate prin HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Folosește HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Poți marca funcțiile de procesare a evenimentelor prin atingere sau derulare `passive` pentru a îmbunătăți capacitatea de derulare a paginii. [Află mai multe despre adoptarea funcțiilor de procesare a evenimentelor passive](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nu folosește ascultători pasivi pentru a îmbunătăți performanța la derulare"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Folosește ascultători pasivi pentru a îmbunătăți performanța la derulare"}, "core/audits/errors-in-console.js | description": {"message": "Erorile înregistrate pe consolă indică probleme nerezolvate. Acestea pot fi provocate de erorile de solicitare din rețea și de alte probleme ale browserului. [Află mai multe despre aceste erori în auditul pentru diagnosticarea consolei](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "S-au înregistrat erori de browser pe consolă"}, "core/audits/errors-in-console.js | title": {"message": "Nu s-a înregistrat nicio eroare de browser pe consolă"}, "core/audits/font-display.js | description": {"message": "Folosește funcția CSS `font-display` ca să te asiguri că textul este vizibil pentru utilizatori în timp ce fonturile web se încarcă. [Află mai multe despre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Asigură-te că textul rămâne vizibil în timpul încărcării fonturilor web"}, "core/audits/font-display.js | title": {"message": "Tot textul rămâne vizibil în timpul încărcării fonturilor web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse nu a putut verifica automat valoarea `font-display` pentru originea {fontOrigin}.}few{Lighthouse nu a putut verifica automat valorile `font-display` pentru originea {fontOrigin}.}other{Lighthouse nu a putut verifica automat valorile `font-display` pentru originea {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Raport de dimensiuni (real)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Raport de dimensiuni (afișat)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Dimensiunile de afișare a imaginilor trebuie să se potrivească raportului natural de dimensiuni. [Află mai multe despre raportul de dimensiuni al imaginii](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Afișează imaginile cu rapoarte de dimensiuni incorecte"}, "core/audits/image-aspect-ratio.js | title": {"message": "Afișează imaginile cu rapoarte de dimensiuni corecte"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Dimensiunea reală"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Dimensiunea așteptată"}, "core/audits/image-size-responsive.js | description": {"message": "Dimensiunile naturale ale imaginii trebuie să fie proporționale cu dimensiunea afișajului și cu raportul de pixeli, pentru a maximiza claritatea imaginii. [Află cum să adaugi imagini adaptabile](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Difuzează imagini cu rezoluție mică"}, "core/audits/image-size-responsive.js | title": {"message": "Difuzează imagini cu rezoluția corectă"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplicația este deja instalată"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Nu s-a putut descărca o pictogramă necesară din manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivul erorii"}, "core/audits/installable-manifest.js | description": {"message": "Service worker este tehnologia care îi dă aplicației posibilitatea să folosească mai multe funcții de aplicații web progresive, cum ar fi cele offline, adăugarea în ecranul de pornire și notificările push. În urma implementării corecte a service workerului și a manifestului, browserele le pot solicita proactiv utilizatorilor să adauge aplicația pe ecranele de pornire, lucru care poate duce la o implicare mai mare. [Află mai multe despre cerințele de instalare pentru manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{Un motiv}few{# motive}other{# de motive}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifestul aplicației web sau service workerul nu îndeplinesc cerințele de instalare"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Adresa URL a aplicației din Magazinul Play și codul din Magazinul Play nu corespund"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Pagina este încărcată într-o fereastră incognito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Proprietatea „display” din manifest trebuie să fie „standalone”, „fullscreen” sau „minimal-ui”"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifestul conține câmpul „display_override” și primul mod de afișare acceptat trebuie să fie „standalone”, „fullscreen” sau „minimal-ui”"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestul nu a putut fi preluat, este gol sau nu a putut fi analizat"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Adresa URL a manifestului s-a schimbat în timpul preluării manifestului."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestul nu conține câmpul „name” sau „short_name”"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestul nu conține o pictogramă potrivită. Este necesară o pictogramă în format PNG, SVG sau WebP, de minimum {value0} px, care să aibă atributul „dimensiuni” setat și, dacă are atributul „scop” setat, acesta trebuie să includă „oricare”."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "<PERSON>cio pictogramă adăugată nu întrunește următoarele cerințe: minimum {value0} px, pătrată, în format PNG, SVG sau WebP, cu atributul scop nesetat sau setat la „oricare”."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Pictograma descărcată este goală sau deteriorată"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nu s-a indicat niciun cod din Magazinul Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Pagina nu include ad<PERSON><PERSON> <link> a manifestului"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Nu s-a detectat niciun service worker care corespunde. Poate fi necesar să reîncarci pagina sau să verifici dacă aria de acoperire a service workerului pentru pagina actuală include aria de acoperire și adresa URL inițială din manifest."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Nu s-a putut verifica service workerul fără câmpul „start_url” în manifest"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Codul „{errorId}” al erorii legate de instalare nu este recunoscut"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Pagina nu este difuzată dintr-o sursă sigură"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Pagina nu este încărcată în cadrul principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Pagina nu funcționează offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA a fost dezinstalată și se resetează instalarea."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Platforma aplicației indicată nu este acceptată pe Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifestul are proprietatea prefer_related_applications activată"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Proprietatea prefer_related_applications este acceptată numai în Chrome Beta și pe canale stabile de pe Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse nu a putut stabili dacă este vorba despre un service worker. Încearcă folosind o versiune mai nouă de Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Schema adresei URL a manifestului ({scheme}) nu este acceptată pe Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Adresa URL inițială a manifestului nu este validă"}, "core/audits/installable-manifest.js | title": {"message": "Manifestul aplicației web și service workerul nu îndeplinesc cerințele de instalare"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "O adresă URL din manifest conține un nume de utilizator, o parolă sau un port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Pagina nu funcționează offline. Pagina nu va fi considerată instalabilă după lansarea versiunii stabile Chrome 93, în august 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Permisă"}, "core/audits/is-on-https.js | blocked": {"message": "Blocată"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Adresă URL nesigură"}, "core/audits/is-on-https.js | columnResolution": {"message": "Soluționarea solicitărilor"}, "core/audits/is-on-https.js | description": {"message": "Toate site-urile trebuie protejate cu HTTPS, chiar și cele care nu gestionează date sensibile. Acest lucru presupune evitarea [conținutului mixt](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), pentru care unele resurse se încarcă prin HTTP, deși solicitarea inițială a fost trimisă prin HTTPS. HTTPS împiedică persoanele străine să modifice sau să asculte pasiv comunicațiile dintre aplicația ta și utilizatori, fiind o cerință obligatorie pentru HTTP/2 și multe API-uri noi pentru platformele web. [Află mai multe despre HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{S-a găsit o solicitare nesigură}few{S-au găsit # solicitări nesigure}other{S-au găsit # de solicitări nesigure}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Nu folosește HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Folosește HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Actualizată automat la HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Permisă cu avertisment"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Acesta este cel mai mare element de conținut redat în aria vizibilă. [Află mai multe despre elementul Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elementul cu cea mai mare redare de conținut"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribuția la CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Aceste elemente DOM contribuie cel mai mult la schimbarea cumulată a aspectului paginii. [Află cum să îmbunătățești CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Evită schimbările majore ale aspectului"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Imaginile din partea superioară a paginii care se încarcă asincron sunt redate mai târziu în ciclul de viață al paginii, ceea ce poate întârzia redarea Largest Contentful Paint. [Află mai multe despre încărcarea asincronă optimă](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Imaginea Largest Contentful Paint s-a încărcat asincron"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Imaginea Largest Contentful Paint nu s-a încărcat asincron"}, "core/audits/long-tasks.js | description": {"message": "Afișează cele mai îndelungate activități din firul principal, ceea ce este util pentru identificarea elementelor care contribuie cel mai mult la întârziere. [Află cum să eviți activitățile îndelungate din firul principal](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{A fost identificată # activitate îndelungată}few{Au fost identificate # activități îndelungate}other{Au fost identificate # de activități îndelungate}}"}, "core/audits/long-tasks.js | title": {"message": "Evită activitățile îndelungate în firul principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categorie"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Poți reduce timpul petrecut cu analizarea, compilarea și executarea JS. Livrarea unor sarcini JS mai mici poate ajuta în acest sens. [Află cum să minimizezi procesarea firului principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizează procesarea firului principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizează procesarea firului principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Ca să se adreseze cât mai multor utilizatori, site-urile trebuie să funcționeze în toate browserele importante. [Află despre compatibilitatea cu mai multe browsere](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Site-ul funcționează pe orice browser"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Asigură-te că se pot crea linkuri directe către pagini individuale prin adresa URL și că adresele URL sunt unice, pentru a putea fi trimise în rețelele sociale. [Află mai multe despre adăugarea linkurilor directe](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Fiecare pagină are o adresă URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Tranzițiile trebuie să aibă loc repede atunci când atingi ecranul, chiar și în rețele lente. Această experiență este esențială pentru performanța percepută de utilizator. [Află mai multe despre tranzițiile paginilor](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Tranzițiile de pagini nu par să blocheze rețeaua"}, "core/audits/maskable-icon.js | description": {"message": "O pictogramă cu mască asigură faptul că imaginea umple întreaga formă, fără a fi convertită în format letterbox la instalarea unei aplicații pe un dispozitiv. [Află despre pictogramele de manifest cu mască](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifestul nu are o pictogramă care poate fi mascată"}, "core/audits/maskable-icon.js | title": {"message": "Manifestul are o pictogramă care poate fi mascată"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative Layout Shift măsoară deplasarea elementelor vizibile în aria vizibilă. [Află mai multe despre valoarea Cumulative Layout Shift](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interacțiunea cu următoarea reprezentare măsoară receptivitatea paginii, adică timpul necesar pentru ca pagina să răspundă în mod vizibil la comanda utilizatorului. [Află mai multe despre valoarea Interacțiunea cu următoarea reprezentare](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint arată momentul când se redă primul text sau prima imagine. [Află mai multe despre valoarea First Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint arată momentul când este vizibil conținutul principal al unei pagini. [Află mai multe despre valoarea First Meaningful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Time to Interactive este timpul necesar pentru ca pagina să devină complet interactivă. [Află mai multe despre valoarea Time to Interactive](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint semnalează momentul în care este redat cel mai mare text sau cea mai mare imagine. [Află mai multe despre valoarea Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Valoarea First Input Delay maximă potențială cu care utilizatorii se pot confrunta este durata celei mai lungi activități. [Află mai multe despre valoarea First Input Delay maximă potențială](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Indexul de viteză arată cât de repede se completează vizibil conținutul unei pagini. [Află mai multe despre valoarea Index de viteză](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Suma tuturor intervalelor de timp dintre FCP și Time to Interactive, când durata activității a depășit 50 ms, exprimată în milisecunde. [Află mai multe despre valoarea Timpul total de blocare](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Durata pingului în rețea (RTT) are impact important asupra performanței. Dacă RTT către o origine este mare înseamnă că serverele mai apropiate de utilizator pot îmbunătăți performanța. [Află mai multe despre durata pingului](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Durata pingului în rețea"}, "core/audits/network-server-latency.js | description": {"message": "Latențele serverului pot influența rezultatele pe web. Dacă latența serverului unei origini este mare înseamnă că serverul este supraîncărcat sau are performanțe slabe în backend. [Află mai multe despre timpul de răspuns al serverului](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latențe ale backendului serverului"}, "core/audits/no-unload-listeners.js | description": {"message": "Evenimentul `unload` nu se declanșează într-un mod fiabil și ascultarea lui poate împiedica optimizări de browser cum ar fi memoria cache înainte-înapoi. Folosește evenimente `pagehide` sau `visibilitychange` în loc. [Află mai multe despre funcțiile de procesare a evenimentelor unload](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Înregistrează un listener `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Evită funcțiile de procesare a evenimentelor `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animațiile care nu sunt compuse pot fi dificile și pot spori CLS. [Află cum să eviți animațiile care nu sunt compuse](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{A fost găsit # element animat}few{Au fost găsite # elemente animate}other{Au fost găsite # de elemente animate}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Proprietatea legată de filtre poate muta pixelii"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Ținta are altă animație, care este incompatibilă"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efectul are alt mod compus decât „înlocuiește”"}, "core/audits/non-composited-animations.js | title": {"message": "Evită animațiile necompuse"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Proprietatea legată de transformare depinde de dimensiunea pachetului"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Proprietate CSS neacceptată: {properties}}few{Proprietăți CSS neacceptate: {properties}}other{Proprietăți CSS neacceptate: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efectul are parametri de temporizare neacceptați"}, "core/audits/performance-budget.js | description": {"message": "Păstrează cantitatea și dimensiunea solicitărilor din rețea sub obiectivele stabilite de limita de funcționare solicitată. [Află mai multe despre limitele de funcționare](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{O solicitare}few{# solicitări}other{# de solicitări}}"}, "core/audits/performance-budget.js | title": {"message": "Buget de performanță"}, "core/audits/preload-fonts.js | description": {"message": "Preîncarcă fonturile `optional` pentru ca vizitatorii noi să le poată folosi. [Află mai multe despre preîncărcarea fonturilor](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "Fonturile cu `font-display: optional` nu sunt preîncărcate"}, "core/audits/preload-fonts.js | title": {"message": "Fonturile cu `font-display: optional` sunt preîncărcate"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Dacă elementul LCP este adăugat dinamic în pagină, preîncarcă imaginea pentru a îmbunătăți LCP. [Află mai multe despre preîncărcarea elementelor LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Preîncarcă imaginea Largest Contentful Paint"}, "core/audits/redirects.js | description": {"message": "Redirecționările introduc întârzieri suplimentare înainte ca pagina să se poată încărca. [Află cum să eviți redirecționările paginii](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evită mai multe redirecționări ale paginii"}, "core/audits/resource-summary.js | description": {"message": "Ca să setezi limitele pentru cantitatea și dimensiunea resurselor de pagină, adaugă un fișier budget.json. [Află mai multe despre limitele de funcționare](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{O solicitare • {byteCount, number, bytes} KiB}few{# solicitări • {byteCount, number, bytes} KiB}other{# de solicitări • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Păstrează numărul de solicitări scăzut și dimensiunea transferurilor redusă"}, "core/audits/seo/canonical.js | description": {"message": "Linkurile canonice sugerează care adresă URL să se afișeze în rezultatele căutării. [Află mai multe despre linkurile canonice](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON> multe adrese URL incompatibile ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Adresă URL nevalidă ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Indică spre altă locație `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Nu este o adresă URL absolută ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Direcționează către adresa URL a rădăcinii domeniului (pagina principală), nu către o pagină de conținut echivalentă"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Documentul nu are un `rel=canonical` valid"}, "core/audits/seo/canonical.js | title": {"message": "Documentul are un `rel=canonical` valid"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Linkul nu poate fi accesat cu crawlere"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Motoarele de căutare pot folosi atribute `href` în linkuri pentru a accesa site-uri cu crawlere. Asigură-te că atributul `href` al elementelor ancoră trimite către o destinație corespunzătoare, pentru a descoperi mai multe pagini din site. [Află cum să setezi linkurile astfel încât să poată fi accesate cu crawlere](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON><PERSON>le nu pot fi accesate cu crawlere"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Linkurile pot fi accesate cu crawlere"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Text suplimentar ilizibil"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> fontului"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% din textul paginii"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Dimensiunile fontului mai mici de 12 pixeli sunt prea mici pentru a putea fi citite, iar utilizatorii de dispozitive mobile trebuie să „ciupească pentru zoom” pentru a citi. Încearcă să ai peste 60 % din textul paginii cu font ≥12 px. [Află mai multe despre dimensiunile de fonturi lizibile](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "Text lizibil {decimalProportion, number, extendedPercent}"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Textul nu este lizibil deoarece nu există nicio metaetichetă viewport optimizată pentru ecranele dispozitivelor mobile."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Documentul nu folosește dimensiuni de fonturi lizibile"}, "core/audits/seo/font-size.js | legibleText": {"message": "Text lizibil"}, "core/audits/seo/font-size.js | title": {"message": "Documentul folosește dimensiuni de font lizibile"}, "core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON>le hreflang spun motoarelor de căutare ce versiune a unei pagini să înregistreze în rezultatele căutării pentru o limbă sau regiune dată. [Află mai multe despre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Document nu are un `hreflang` valid"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valoare href relativă"}, "core/audits/seo/hreflang.js | title": {"message": "Documentul are un `hreflang` valid"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Cod de limbă neașteptat"}, "core/audits/seo/http-status-code.js | description": {"message": "Este posibil ca paginile cu coduri de stare HTTP nefuncționale să nu fie indexate corect. [Află mai multe despre codurile de stare HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Pagina are Cod de stare HTTP nefuncțional"}, "core/audits/seo/http-status-code.js | title": {"message": "Pagina are Cod de stare HTTP funcțional"}, "core/audits/seo/is-crawlable.js | description": {"message": "Motoarele de căutare nu pot include paginile în rezultatele căutării dacă nu au permisiunea de a le accesa cu crawlere. [Află mai multe despre directivele privind crawlerele](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indexarea este blocată pentru pagină"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indexarea nu este blocată pentru pagină"}, "core/audits/seo/link-text.js | description": {"message": "Textul descriptiv al linkului ajută motoarele de căutare să înțeleagă conținutul. [Află cum să faci linkurile mai accesibile](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{S-a găsit un link}few{S-au găsit # linkuri}other{S-au găsit # de linkuri}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linkurile nu au text descriptiv"}, "core/audits/seo/link-text.js | title": {"message": "Linkurile au text descriptiv"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Rulează [Instrumentul de testare pentru date structurate](https://search.google.com/structured-data/testing-tool/) și [Structured Data Linter](http://linter.structured-data.org/) pentru a valida datele structurate. [Află mai multe despre datele structurate](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Datele structurate sunt valide"}, "core/audits/seo/meta-description.js | description": {"message": "Metadescrierile pot fi incluse în rezultatele căutării pentru a rezuma conținutul paginii. [Află mai multe despre metadescriere](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Textul de descriere nu este completat."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Documentul nu are o metadescriere"}, "core/audits/seo/meta-description.js | title": {"message": "Documentul are o metadescriere"}, "core/audits/seo/plugins.js | description": {"message": "Motoarele de căutare nu pot indexa conținutul pluginurilor și multe dispozitive restricționează pluginurile sau nu le acceptă. [Află mai multe despre evitarea pluginurilor](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Documentul folosește pluginuri"}, "core/audits/seo/plugins.js | title": {"message": "Documentul evită pluginurile"}, "core/audits/seo/robots-txt.js | description": {"message": "Dacă fișierul robots.txt este deteriorat, este posibil ca aplicațiile crawler să nu poată înțelege cum vrei să fie site-ul tău accesat cu crawlere sau indexat. [Află mai multe despre robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Cererea pentru robots.txt a returnat starea HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{A fost identificată o eroare}few{Au fost identificate # erori}other{Au fost identificate # de erori}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nu a putut să descarce un fișier robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Fișierul robots.txt nu este valid"}, "core/audits/seo/robots-txt.js | title": {"message": "Fișierul robots.txt este valid"}, "core/audits/seo/tap-targets.js | description": {"message": "Elementele interactive, precum butoanele și linkurile, trebuie să fie suficient de mari (48x48 px) și să aibă suficient spațiu în jurul lor, pentru a fi ușor de atins, fără a se suprapune altor elemente. [Află mai multe despre țintele de atingere](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} direcționări ale atingerilor au mărimea corectă"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Direcționările atingerilor sunt prea mici, deoarece nu există o metaetichetă Viewport optimizată pentru ecranele dispozitivelor mobile"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Direcționările atingerilor nu sunt dimensionate corect"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Direcționarea se suprapune"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Direcționarea atingerii"}, "core/audits/seo/tap-targets.js | title": {"message": "Direcționările atingerilor sunt dimensionate corect"}, "core/audits/server-response-time.js | description": {"message": "Menține o durată scurtă de răspuns de la server pentru documentul principal, de<PERSON><PERSON>e toate celelalte solicitări depind de acest lucru. [Află mai multe despre valoarea Timpul până la primul byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Documentul rădăcină a avut nevoie de {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Redu durata inițială de răspuns de la server"}, "core/audits/server-response-time.js | title": {"message": "Durata inițială de răspuns de la server a fost scurtă"}, "core/audits/service-worker.js | description": {"message": "Service worker este tehnologia care îi permite aplicației să folosească mai multe funcții de aplicații web progresive, cum ar fi cele offline, adăugarea în ecranul de pornire și notificările push. [Află mai multe despre elementele service worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Pagina este controlată de un service worker, dar nu s-a g<PERSON><PERSON><PERSON> niciun `start_url`, deoarece manifestul nu s-a putut analiza ca un JSON valid"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Pagina este controlată de un service worker, dar `start_url` ({startUrl}) nu este în aria de acoperire a acestuia ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Pagina este controlată de un service worker, dar nu s-a găsit un `start_url`, deoarece nu s-a preluat niciun manifest."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Această origine are unul sau mai multe elemente service worker, însă pagina ({pageUrl}) nu este în aria de acoperire."}, "core/audits/service-worker.js | failureTitle": {"message": "Nu înregistrează un service worker care să controleze pagina și `start_url`"}, "core/audits/service-worker.js | title": {"message": "Înregistrează un service worker care să controleze pagina și `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Un ecran de întâmpinare tematic asigură o experiență de calitate atunci când utilizatorii lansează aplicația din ecranele de pornire. [Află mai multe despre ecranele de întâmpinare](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Nu este configurat pentru un ecran de întâmpinare personalizat"}, "core/audits/splash-screen.js | title": {"message": "Configurat pentru un ecran de întâmpinare personalizat"}, "core/audits/themed-omnibox.js | description": {"message": "Bara de adrese din browser poate avea o temă ce corespunde cu site-ul tău. [Află mai multe despre personalizarea barei de adrese](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Nu setează o culoare tematică pentru bara de adrese."}, "core/audits/themed-omnibox.js | title": {"message": "Setează o culoare tematică pentru bara de adrese."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Asistență pentru clienți)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Rețele sociale)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produs"}, "core/audits/third-party-facades.js | description": {"message": "Unele încorporări terță parte se pot încărca asincron. Îți recomandăm să le înlocuiești cu o fațadă până când devin necesare. [Află cum să amâni terții folosind o fațadă](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternativă de fațadă disponibilă}few{# alternative de fațadă disponibile}other{# de alternative de fațadă disponibile}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Unele resurse terță parte se pot încărca lent cu ajutorul unei fațade"}, "core/audits/third-party-facades.js | title": {"message": "Înc<PERSON><PERSON><PERSON> lent resursele terță parte folosind fațade"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Terță parte"}, "core/audits/third-party-summary.js | description": {"message": "Codul terță parte poate influența semnificativ performanța de încărcare. Limitează numărul de furnizori terță parte redundanți și încearcă să încarci cod terță parte după ce pagina a terminat încărcarea de bază. [Află cum să minimizezi impactul terților](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Codul terță parte a blocat firul principal pentru {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Redu impactul codului de la terți"}, "core/audits/third-party-summary.js | title": {"message": "Minimalizează folosirea terțelor părți"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Cuantificare"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Valoare"}, "core/audits/timing-budget.js | description": {"message": "Stabilește o limită pentru durată ca să urmărești performanța site-ului. Site-urile performante se încarcă rapid și răspund rapid la evenimentele de intervenție a utilizatorului. [Află mai multe despre limitele de funcționare](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "<PERSON><PERSON><PERSON> pentru durat<PERSON>"}, "core/audits/unsized-images.js | description": {"message": "Setează lățimea și înălțimea explicite pentru elementele imagine, ca să reduci modificările de aspect și să îmbunătățești CLS. [Află cum să setezi dimensiunile imaginii](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Elementele imagine nu au `width` și `height` explicite"}, "core/audits/unsized-images.js | title": {"message": "Elementele imagine au `width` și `height` explicite"}, "core/audits/user-timings.js | columnType": {"message": "Tip"}, "core/audits/user-timings.js | description": {"message": "Îți poți îmbunătăți aplicația cu API-ul User Timing ca să măsori performanța reală a acesteia în timpul experiențelor de utilizare principale. [Află mai multe despre marcajele User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{o durată a utilizării}few{# durate ale utilizărilor}other{# de durate ale utilizărilor}}"}, "core/audits/user-timings.js | title": {"message": "Mărci și dimensiuni pentru Duratele utilizărilor"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "S-a găsit un `<link rel=preconnect>` pentru „{securityOrigin}” dar browserul nu l-a folosit. Verifică dacă folosești corect atributul `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Adaugă indicii de resurse `preconnect` sau `dns-prefetch` pentru a crea conexiunile inițiale la originile terță parte importante. [Află cum să te preconectezi la originile necesare](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Preconectează la originile necesare"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "S-au găsit mai mult de două conexiuni `<link rel=preconnect>`. Acestea trebuie folosite cu măsură și numai pentru cele mai importante origini."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "S-a găsit un `<link rel=preconnect>` pentru „{securityOrigin}” dar browserul nu l-a folosit. Folosește `preconnect` doar pentru originile importante, pe care pagina le va solicita cu siguranță."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "S-a găsit un `<link>` preînc<PERSON><PERSON><PERSON> pentru „{preloadURL}” dar browserul nu l-a folosit. Verifică dacă folosești corect atributul `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "<PERSON><PERSON>i folosi `<link rel=preload>` ca să acorzi prioritate preluării resurselor care sunt momentan solicitate mai târziu la încărcarea paginii. [Află cum să preîncarci solicitările importante](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Preîncarcă solicitările importante"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Adresa URL a hărții"}, "core/audits/valid-source-maps.js | description": {"message": "Hărțile sursă traduc codul minimizat în codul sursă inițial. Astfel, dezvoltatorii pot să remedieze erorile în producție. În plus, Lighthouse poate oferi statistici suplimentare. Implementează hărțile sursă ca să profiți de aceste beneficii. [Află mai multe despre hărțile sursă](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Lipsesc hărțile sursă pentru codul JavaScript primar mare"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Fișierului JavaScript mare îi lipsește o hartă sursă"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Avertisment: lipsește un element din `.sourcesContent`}few{Avertisment: lipsesc # elemente din `.sourcesContent`}other{Avertisment: lipsesc # de elemente din `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Pagina are hărți sursă valide"}, "core/audits/viewport.js | description": {"message": "Pe lângă faptul că `<meta name=\"viewport\">` optimizează aplicația pentru dimensiunile ecranelor de dispozitive mobile, împiedică [o întârziere de 300 milisecunde a intrării de la utilizator](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Află mai multe despre folosirea metaetichetei pentru spațiul vizibil](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Nu s-a gă<PERSON>t nicio etichet<PERSON> `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Nu are o etichetă `<meta name=\"viewport\">` cu `width` sau `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Are o etichetă `<meta name=\"viewport\">` cu `width` sau `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Aceasta este activitatea de blocare a firului care are loc în timpul măsurării valorii Interacțiunea cu următoarea reprezentare. [Află mai multe despre valoarea Interacțiunea cu următoarea reprezentare](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms petrecute cu evenimentul {interactionType}"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Eveniment vizat"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimizează procesarea în timpul interacțiunilor importante"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Întârziere la introducerea textului"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Întârzier<PERSON> prezentării"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Durată de procesare"}, "core/audits/work-during-interaction.js | title": {"message": "Minimizează procesarea în timpul interacțiunilor importante"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți utilizarea ARIA în aplicație, lucru care poate îmbunătăți experiența pentru utilizatorii tehnologiei care asigură asistență, precum un cititor de ecran."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Acestea sunt oportunități de a oferi conținut secundar audio și video. Astfel se poate îmbunătăți experiența utilizatorilor cu deficiențe de auz sau de vedere."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio și video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Aceste elemente evidențiază cele mai bune practici privind accesibilitatea."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON> mai bune practici"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Aceste verificări evidențiază oportunitățile pentru a [îmbunătăți accesibilitatea aplicației web](https://developer.chrome.com/docs/lighthouse/accessibility/). Doar un subset de probleme de accesibilitate pot fi detectate automat, astfel încât se recomandă și testarea manuală."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Aceste elemente se adresează zonelor pe care un instrument de testare automată nu le poate acoperi. Află mai multe din ghidul nostru despre [realizarea unei evaluări a accesibilității](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accesibilitate"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Acestea sunt oportunități de a-ți îmbunătăți lizibilitatea conținutului."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți interpretarea conținutului tău de utilizatori, în diferite coduri locale."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internaționalizare și localizare"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți semantica opțiunilor de control din aplicația ta. Astfel se poate îmbunătăți experiența utilizatorilor de tehnologii care asigură asistență, precum un cititor de ecran."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nume și etichete"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți navigarea pe tastatură în aplicația ta."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigare"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți experiența citirii datelor din tabele sau din liste folosind tehnologia care asigură asistență, cum ar fi un cititor de ecran."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele și liste"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilitatea browserului"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON> mai bune practici"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "General"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Încredere și siguranță"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiența utilizatorului"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Bugetele de performanță stabilesc standarde pentru performanța site-ului."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Bugete"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mai multe informații despre performanța aplicației tale. Numerele nu [influențează direct](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) Scorul de performanță."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnosticare"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Cel mai important aspect al performanței este cât de repede se redau pixelii pe ecran. Valori cheie: prima redare de conținut, prima redare semnificativă"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Îmbunătățirile pentru prima redare"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Aceste sugestii îți pot face pagina să se încarce mai repede. Ele nu [influențează direct](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) scorul de performanță."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunități"}, "core/config/default-config.js | metricGroupTitle": {"message": "Valori"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Îmbunătățește experiența globală de încărcare, astfel ca pagina să se afișeze și să fie gata de utilizare cât mai curând posibil. Valori cheie: timpul până la interactivitate, indexul de viteză"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Îmbunătățiri generale"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performanță"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Aceste verificări validează aspectele unei aplicații web progresive. [Află ce înseamnă o aplicație web progresivă bună](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Aceste verificări sunt impuse de [Lista de verificare PWA](https://web.dev/pwa-checklist/) de referință, dar nu sunt verificate automat de Lighthouse. Ele nu-ți afectează scorul, dar e important să fie făcute manual."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Poate fi instalat"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizat pentru PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Aceste verificări asigură faptul că pagina urmează recomandările de bază privind optimizarea pentru motoarele de căutare. Există mulți alți factori de care Lighthouse nu ține cont care îți pot afecta poziționarea în rețeaua de căutare, inclusiv performanța în [Detaliile de funcționare de bază pentru web](https://web.dev/learn-core-web-vitals/). [Află mai multe despre Noțiuni de bază despre Căutarea Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Rulează acești validatori suplimentari pe site pentru a căuta alte recomandări SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatează codul HTML în așa fel încât crawlerele să înțeleagă mai bine conținutul aplicației."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON> mai bune practici privind conținutul"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Pentru a apărea în rezultatele căutării, crawlerele trebuie să îți acceseze aplicația."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Accesarea cu crawlere și indexarea"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Asigură-te că paginile sunt create pentru dispozitivele mobile, pentru ca utilizatorii să nu trebuiască să ciupească sau să mărească pentru a citi paginile conținutului. [Află cum să faci paginile potrivite pentru dispozitivele mobile](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Pentru dispozitivele mobile"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Se pare că dispozitivul testat are un CPU mai lent decât prevede Lighthouse. Aceasta poate afecta negativ scorul de performanță. Află mai multe despre [calibrarea unui multiplicator corespunzător pentru încetinirea CPU-ului](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Este posibil ca pagina să nu se încarce conform așteptărilor, deoarece adresa URL de testare ({requested}) a fost redirecționată spre {final}. Încearcă să testezi direct a doua adresă URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Pagina s-a <PERSON><PERSON><PERSON><PERSON><PERSON> prea lent pentru a respecta limita de timp. Rezultatele pot fi incomplete."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Timpul de ștergere a memoriei cache a browserului a expirat. Încearcă să auditezi din nou această pagină și să raportezi o eroare dacă problema persistă."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Este posibil să existe date stocate care afectează performanța de încărcare în următoarea locație: {locations}. Verifică pagina într-o fereastră incognito pentru a împiedica resursele să îți afecteze scorurile.}few{Este posibil să existe date stocate care afectează performanța de încărcare în următoarele locații: {locations}. Verifică pagina într-o fereastră incognito pentru a împiedica resursele să îți afecteze scorurile.}other{Este posibil să existe date stocate care afectează performanța de încărcare în următoarele locații: {locations}. Verifică pagina într-o fereastră incognito pentru a împiedica resursele să îți afecteze scorurile.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Ștergerea datelor de origine a expirat. Încearcă să auditezi din nou această pagină și să raportezi o eroare dacă problema persistă."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Doar paginile încărcate prin intermediul unei solicitări GET sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Doar paginile cu codul de stare 2XX pot fi stocate în memoria cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome a detectat o încercare de executare JavaScript în cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Paginile care au solicitat un AppBanner nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Memoria cache înainte/înapoi este dezactivată de funcțiile experimentale. Accesează chrome://flags/#back-forward-cache ca să o activezi local pe acest dispozitiv."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Memoria cache înainte/înapoi este dezactivată de linia de comandă."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza memoriei insuficiente."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Memoria cache înainte/înapoi nu este acceptată de delegat."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Memoria cache înainte/înapoi este dezactivată de programul de redare anticipată."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Pagina nu poate fi stocată în memoria cache deoarece are o instanță BroadcastChannel cu ascultători înregistrați."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Paginile cu antetul cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Memoria cache a fost golită intenționat."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Pagina a fost eliminată din memoria cache pentru a permite stocarea în cache a unei alte pagini."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Paginile care conțin pluginuri nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Paginile care folosesc API-ul FileChooser nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Paginile care folosesc API-ul de acces la sistemul de fișiere nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Paginile care folosesc dispecerul pentru dispozitive media nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Un player media funcționa la ieșire."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Paginile care folosesc API-ul MediaSession și setează o stare de redare nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Paginile care folosesc API-ul MediaSession și setează handlere de acțiune nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Memoria cache înainte-înapoi este dezactivată din cauza cititorului de ecran."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Paginile care folosesc SecurityHandler nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Paginile care folosesc API-ul Serial nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Paginile care folosesc API-ul WebAuthentication nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Paginile care folosesc API-ul WebBluetooth nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Paginile care folosesc API-ul WebUSB nu sunt eligibile pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Paginile care folosesc un lucrător sau un worklet special nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Documentul nu s-a încărcat complet înainte de a ieși."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Bannerul de aplicație era prezent la ieșire."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Managerul de parole Chrome era prezent la ieșire."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Distilarea DOM era în curs în momentul ieșirii."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Vizualizatorul de distilare DOM era prezent la ieșire."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza extensiilor care folosesc API de mesagerie."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Extensiile cu conexiune pe termen lung ar trebui să închidă conexiunea înainte să intre în memoria cache înainte/înapoi"}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Extensiile cu conexiune pe termen lung au încercat să trimită mesaje către cadre din memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza extensiilor."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Un dialog modal, precum retrimiterea formularelor sau caseta de dialog pentru parola http, s-a afișat pentru pagină la ieșire."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Pagina offline s-a afișat la ieșire."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Bara de intervenție Memorie insuficientă era prezentă la ieșire."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Au apărut solicitări de permisiune la ieșire."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Funcția de blocare a ferestrelor pop-up era prezentă la ieșire."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Detaliile Navigării sigure s-au afișat la ieșire."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Navigarea sigură a considerat această pagină abuzivă și a blocat elementul pop-up."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Un service worker s-a activat în timp ce pagina era în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza unei erori de document."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Paginile care folosesc FencedFrames nu pot fi stocate în bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Pagina a fost eliminată din memoria cache pentru a permite stocarea în cache a unei alte pagini."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Paginile care au acordat acces pentru fluxul media nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Paginile care folosesc portaluri nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Paginile care folosesc IdleManager nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Paginile care au o conexiune IndexedDB deschisă nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "S-au folosit API-uri ineligibile."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Paginile în care se injectează JavaScript prin extensii nu sunt eligibile momentan pentru memoria cache înainte-înapoi."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Paginile în care se injectează StyleSheet prin extensii nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON> internă."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Memoria cache înainte-înapoi este dezactivată din cauza unei solicitări keep-alive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Paginile care folosesc blocarea tastaturii nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | loading": {"message": "Pagina nu s-a încărcat complet înainte de a ieși."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Paginile a căror resursă principală are cache-control:no-cache nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Paginile a căror resursă principală are cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigarea s-a anulat înainte să se poată restabili pagina din memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Pagina a fost eliminată din cache pentru că o conexiune la rețea activă primea prea multe date. Chrome limitează volumul de date pe care îl poate primi o pagină în cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Paginile care au fetch() sau XHR în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Pagina a fost eliminată din memoria cache înainte/înapoi deoarece o solicitare pentru o rețea activă implica o redirecționare."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Pagina a fost eliminată din cache pentru că o conexiune la rețea a fost deschisă prea mult. Chrome limitează durata în care o pagină poate primi date în cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Paginile care nu au un antet de răspuns valid nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigarea a avut loc într-un cadru diferit de principalul."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Paginile cu tranzacții DB indexate în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Paginile cu o solicitare de rețea în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Paginile cu o solicitare de rețea de preluare în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Paginile cu o solicitare de rețea în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Paginile cu o solicitare de rețea XHR în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Paginile care folosesc PaymentManager nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Paginile care folosesc modul picture-in-picture nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | portal": {"message": "Paginile care folosesc portaluri nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | printing": {"message": "Paginile care afișează interfața de printare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Pagina s-a deschis folosind `window.open()` și altă filă are o referință la aceasta sau pagina a deschis o fereastră."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Procesul dispozitivului de redare pentru pagina din memoria cache înainte/înapoi s-a blocat."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Procesul dispozitivului de redare pentru pagina din memoria cache înainte/înapoi a fost oprit."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Paginile care au solicitat permisiuni de înregistrare audio nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Paginile care au permisiunile solicitate pentru senzori nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Paginile care au solicitat sincronizarea în fundal sau permisiuni de preluare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Paginile care au solicitat permisiuni MIDI nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Paginile care au solicitat permisiuni de notificare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Paginile care au solicitat acces la spațiul de stocare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Paginile care au solicitat permisiuni de înregistrare video nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Doar paginile a căror schemă URL este HTTP / HTTPS pot fi stocate în memoria cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Pagina a fost revendicată de un service worker în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker a încercat să trimită paginii din memoria cache înainte/înapoi o proprietate `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "S-a anulat înregistrarea ServiceWorker cât timp o pagină era în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Pagina a fost eliminată din memoria cache înainte/înapoi din cauza activării unui service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome a repornit și a șters intrările din memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Paginile care folosesc SharedWorker nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Paginile care folosesc SpeechRecognizer nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Paginile care folosesc SpeechSynthesis nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Un iframe de pe pagină a început o navigare care nu s-a terminat."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Paginile a căror subresursă are cache-control:no-cache nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Paginile a căror subresursă are cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Pagina a depășit timpul maxim în memoria cache înainte/înapoi și a expirat."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "A expirat intrarea paginii în memoria cache înainte/înapoi (probabil din cauza handlerelor pagehide pe termen lung)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Pagina are un handler de descărcare în cadrul principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Pagina are un handler de descărcare într-un subcadru."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Browserul a schimbat antetul de modificare user agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Paginile care au acordat acces pentru a înregistra conținut video sau audio nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Paginile care folosesc WebDatabase nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Paginile care folosesc WebHID nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Paginile care folosesc WebLocks nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Paginile care folosesc WebNfc nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Paginile care folosesc WebOTPService nu sunt eligibile momentan pentru bfcache."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Paginile cu WebRTC nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Paginile care folosesc WebShare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Paginile cu WebSocket nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Paginile cu WebTransport nu pot intra în memoria cache înainte/înapoi."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Paginile care folosesc WebXR nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Îți recomandăm să adaugi scheme de adrese URL https: și http: (ignorate de browserele care acceptă strict-dynamic) pentru retrocompatibilitatea cu browsere mai vechi."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Directiva disown-opener este învechită începând cu versiunea CSP3. Folosește antetul Cross-Origin-Opener-Policy în locul acesteia."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Directiva referrer este învechită începând cu versiunea CSP2. Folosește antetul Referrer-Policy în locul acesteia."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Directiva reflected-xss este învechită începând cu versiunea CSP2. Folosește antetul X-XSS-Protection în locul acesteia."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Lipsa directivei base-uri permite etichetelor <base> injectate să seteze adresa URL de bază pentru toate adresele URL relative (de ex., cu scripturi) ale unui domeniu controlat de atacatori. Îți recomandăm să setezi base-uri la none sau self."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Întrucât object-src <PERSON><PERSON><PERSON><PERSON>, se permite injectarea de pluginuri care execută scripturi nesigure. Îți recomandăm să setezi object-src la „none”, dacă poți."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Directiva script-src <PERSON><PERSON><PERSON><PERSON>. Ast<PERSON>l, se pot executa scripturi nesigure."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Ai uitat să folosești punct și virgulă? {keyword} pare a fi o directivă, nu un cuvânt cheie."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Valorile nonce trebuie să folosească setul de caractere base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Valorile nonce trebuie să aibă minimum 8 caractere."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Evită să folosești scheme simple pentru adresa URL ({keyword}) în această directivă. Schemele simple pentru adresa URL permit ca scripturile să provină dintr-un domeniu nesigur."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Evită să folosești metacaractere ({keyword}) în această directivă. Metacaracterele simple pentru adresa URL permit ca scripturile să provină dintr-un domeniu nesigur."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Destinația de raportare se configurează numai prin directiva report-to. Această directivă este acceptată doar în browserele bazate pe Chromium, prin urmare îți recomandăm să folosești și o directivă report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nicio politică CSP nu configurează o destinație de raportare. De aceea, este dificilă menținerea CSP de-a lungul timpului și monitorizarea eventualelor încălcări."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Listele gazdă cu acces permis pot fi ocolite frecvent. Îți recomandăm să folosești codurile hash sau valorile nonce CSP, împreună cu „strict-dynamic”, dacă este necesar."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Directivă CSP necunoscută."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} pare un cuvânt cheie nevalid."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "unsafe-inline permite executarea de scripturi în pagină și handlere pentru evenimente nesigure. Îți recomandăm să folosești valori nonce sau hash pentru CSP pentru a accepta scripturile individual."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Îți recomandăm să adaugi opțiunea unsafe-inline (ignorată de browserele care acceptă valori nonce/hash) pentru retrocompatibilitatea cu browsere mai vechi."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Autorizarea nu va fi acoperită de simbolul pentru metacaracter (*) în cadrul gestionării `Access-Control-Allow-Headers` prin CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Solicitările de resurse ale căror adrese URL conțineau atât caractere pentru spații `(n|r|t)`, cât și semnul mai mic decât (`<`) sunt blocate. Pentru a încărca resursele, elimină caracterele de rând nou și codifică semnul mai mic decât în locuri cum ar fi valorile atributelor elementelor."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` s-a învechit. Folosește API-ul standardizat Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` s-a învechit. Folosește API-ul standardizat Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` s-a învechit. Folosește API-ul standardizat `nextHopProtocol` din Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "<PERSON>ie-urile care conțin un caracter `(0|r|n)` vor fi respinse în loc de a fi trunchiate."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Relaxarea politicii pentru aceeași origine prin setarea `document.domain` este învechită și va fi dezactivată în mod prestabilit. Acest avertisment privind renunțarea la dezvoltare este asociat unei accesări cu origini diferite, care a fost activată din setarea `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Declanșarea {PH1} din cadre iframe cu origini diferite s-a învechit și va fi eliminată pe viitor."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Pentru a dezactiva integrarea Cast prestabilită, trebuie folosit atributul `disableRemotePlayback` în locul selectorului `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} s-a învechit. Folosește {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Acesta este un exemplu de mesaj tradus privind o problemă legată de renunțarea la dezvoltare."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Relaxarea politicii pentru aceeași origine prin setarea `document.domain` este învechită și va fi dezactivată în mod prestabilit. Pentru a folosi în continuare funcția, renunță la grupurile de agenți pentru care au fost configurate anumite origini, trimițând un antet `Origin-Agent-Cluster: ?0` împreună cu răspunsul HTTP pentru document și cadre. Consultă https://developer.chrome.com/blog/immutable-document-domain/ pentru detalii suplimentare."}, "core/lib/deprecations-strings.js | eventPath": {"message": "API-ul `Event.path` este învechit și va fi eliminat. Folosește `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Antetul `Expect-CT` este învechit și va fi eliminat. Chrome necesită Transparența certificatului pentru toate certificatele de încredere publice emise după 30 aprilie 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Pentru mai multe detalii accesează pagina cu starea funcțiilor."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` și `watchPosition()` nu mai funcționează cu origini nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` și `watchPosition()` sunt învechite pentru originile nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` nu mai funcționează cu origini nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` s-a învechit. Folosește `RTCPeerConnectionIceErrorEvent.address` sau `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Originea comerciantului și datele arbitrare din evenimentul service worker `canmakepayment` sunt învechite și vor fi eliminate: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Site-ul a solicitat o subresursă de la o rețea pe care a putut să o acceseze numai datorită poziției privilegiate în rețea a utilizatorilor săi. Aceste solicitări expun pe internet dispozitive și servere care nu sunt publice, crescând riscul de atacuri prin falsificarea solicitărilor de pe alt site (CSRF) și de scurgeri de informații. Pentru a reduce aceste riscuri, Chrome marchează drept învechite solicitările de la subresurse care nu sunt publice atunci când sunt inițiate din contexte nesigure și va începe să le blocheze."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "Fișierul CSS nu se poate încărca din adrese URL `file:` dacă acestea nu se termină cu extensia de fișier `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Folosirea `SourceBuffer.abort()` pentru a abandona eliminarea intervalului asincron pentru `remove()` este învechită din cauza modificării specificațiilor. Pe viitor, aceasta nu va mai fi acceptată. Ar trebui să asculți evenimentul `updateend`. `abort()` are numai rolul de a abandona atașări media asincrone sau de a reseta starea parserului."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Setarea `MediaSource.duration` sub cel mai mare marcaj temporal de prezentare al oricăror cadre codificate din memoria temporară este învechită din cauza modificării specificațiilor. Eliminarea implicită a elementelor media trunchiate din memoria temporară va fi eliminată pe viitor. În locul acesteia, trebuie să rulezi `remove(newDuration, oldDuration)` pentru toate obiectele `sourceBuffers`, unde `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Această modificare va intra în vigoare cu obiectivul {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI va solicita o permisiune de folosit chiar dacă sysex nu este specificat în `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "API-ul Notification nu mai poate fi folosit din origini nesigure. Îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Este posibil ca permisiunea pentru API-ul Notification să nu mai fie solicitată dintr-un iframe cu origini diferite. Îți recomandăm să soliciți permisiunea dintr-un cadru de nivel superior sau să deschizi o fereastră nouă."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Partenerul tău negociază o versiune (D)TLS învechită. Consultă-te cu partenerul pentru a remedia problema."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL în contexte nesecurizate este învechit și se va elimina în curând. Folosește Spațiul de stocare web sau Baza de date indexată."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Dacă specifici `overflow: visible` pe etichetele imagine, video și pânză, este posibil să se producă conținut vizual în afara limitelor elementului. Accesează https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` s-a învechit. Folosește instalarea la timp pentru handlerele pentru plăți."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Apelul `PaymentRequest` a omis directiva Content-Security-Policy (CSP) `connect-src`. Această opțiune de ocolire este învechită. Adaugă identificatorul metodei de plată din API-ul `PaymentRequest` (în câmpul `supportedMethods`) la directiva CSP `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` s-a învechit. Folosește `navigator.storage` standardizat."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` cu un element părinte `<picture>` nu este valid, deci va fi ignorat. Folosește `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` s-a învechit. Folosește `navigator.storage` standardizat."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Solicitările de subresurse ale căror adrese URL conțin date de conectare încorporate (de ex., `**********************/`) sunt blocate."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Restricția `DtlsSrtpKeyAgreement` a fost eliminată. Ai specificat o valoare `false` pentru această restricție, care este interpretată ca o încercare de a folosi metoda `SDES key negotiation` eliminată. Această funcție a fost eliminată. Folosește un serviciu care acceptă `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Restricția `DtlsSrtpKeyAgreement` a fost eliminată. Ai specificat o valoare `true` pentru această restricție, care nu a avut niciun efect, dar poți elimina restricția pentru un aspect ordonat."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "S-a detectat `Complex Plan B SDP`. Acest dialect pentru `Session Description Protocol` nu mai este acceptat. Folosește `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, care se folosește la construirea unui obiect `RTCPeerConnection` cu `{sdpSemantics:plan-b}`, care este o versiune non-standard veche `Session Description Protocol` care a fost ștearsă definitiv de pe platforma web. Aceasta este disponibilă și când construiești cu `IS_FUCHSIA`, dar intenționăm să o ștergem cât mai curând. Nu te mai baza pe aceasta. Consultă https://crbug.com/1302249 pentru stare."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Opțiunea `rtcpMuxPolicy` este învechită și va fi eliminată."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` va necesita blocarea accesului de la mai multe surse. Consultă https://developer.chrome.com/blog/enabling-shared-array-buffer/ pentru mai multe detalii."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Apelarea `speechSynthesis.speak()` fără activarea utilizatorului este învechită și va fi eliminată."}, "core/lib/deprecations-strings.js | title": {"message": "S-a folosit o funcție învechită"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensiile trebuie să aibă blocarea accesului de la mai multe surse activată pentru a folosi în continuare `SharedArrayBuffer`. Consultă https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "Metoda {PH1} este specifică furnizorului. Folosește metoda standard, {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "Fișierul JSON de răspuns din `XMLHttpRequest` nu acceptă UTF-16"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Obiectul sincron `XMLHttpRequest` din firul principal este învechit din cauza efectelor sale negative asupra experienței utilizatorului final. Pentru mai mult ajutor, consultă https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` s-a învechit. Folosește `isSessionSupported()` și verifică valoarea booleană rezultată."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Timpul de blocare a firului principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL cache"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Des<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elemente cu probleme"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Locație"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nume"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Peste buget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> resursei"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tip de resursă"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Sursă"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Ora de începere"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON> petre<PERSON>"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Dimensiunea transferului"}, "core/lib/i18n/i18n.js | columnURL": {"message": "Adresa URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Economii potențiale"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Economii potențiale"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Poți economisi {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{A fost identificat un element}few{Au fost identificate # elemente}other{Au fost identificate # de elemente}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Poți economisi {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Prima redare semnificativă"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagine"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interacțiunea cu următoarea reprezentare"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Înaltă"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Redusă"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Me<PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Timpul maxim potențial de la prima interacțiune"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Altele"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Alte resurse"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> de stil"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Terță parte"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "A apărut o eroare la înregistrarea urmei de la încărcarea paginii. Rulează din nou Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Timpul a expirat așteptând conexiunea inițială la protocolul Debugger."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome nu a colectat capturi de ecran în timpul încărcării paginii. Asigură-te că există conținut vizibil în pagină și încearcă să rulezi din nou Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Serverele DNS nu au putut rezolva domeniul furnizat."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Colectorul de {artifactName} obligatorii a întâmpinat o eroare: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "A apărut o eroare Chrome internă. Repornește Chrome și încearcă să rulezi din nou Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Colectorul {artifactName} obligatoriu nu a rulat."}, "core/lib/lh-error.js | noFcp": {"message": "Pagina nu redă conținut. Lasă fereastra de browser deschisă în prim-plan în timpul încărcării și încearcă din nou. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Pagina nu afișa conținut care se califică drept Largest Contentful Paint (LCP). Verifică dacă pagina are un element LCP valid, apoi încearcă din nou. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Pagina introdusă nu este HTML (difuzată în format de tip MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Această versiune Chrome este prea veche pentru a fi compatibilă cu „{featureName}”. Folosește o versiune mai nouă pentru a vedea rezultatele complete."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nu a putut încărca în mod sigur adresa URL solicitată, deoarece pagina nu mai răspunde."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Adresa URL furnizată nu are un certificat de securitate valid. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome a împiedicat încărcarea paginii cu un interstițial. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările. (Detalii: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările. (Cod de stare: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Încărcarea paginii a durat prea mult. Urmează recomandările din raport pentru a reduce durata de încărcare a paginii, apoi încearcă din nou să rulezi Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Așteptarea răspunsului la protocolul DevTools a depășit timpul alocat. (Metoda: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Preluarea conținutului resursei a depășit timpul alocat"}, "core/lib/lh-error.js | urlInvalid": {"message": "Adresa URL furnizată pare să fie nevalidă."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Tipul MIME al paginii este XHTML: Lighthouse nu acceptă în mod explicit acest tip de document"}, "core/user-flow.js | defaultFlowName": {"message": "Flux pentru utilizatori ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Raport privind navigarea ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Raport instantaneu ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Raport privind intervalul de timp ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Toate rapoartele"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorii"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accesibilitate"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON> mai bune practici"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performanță"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Aplicație web progresivă"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Computer"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Înțelegerea Raportului privind fluxul Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Înțeleger<PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Folosește Rapoartele privind navigarea pentru..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Folosește Rapoartele privind instantaneele pentru..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Folosește Rapoartele privind perioada pentru..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obține un scor de performanță pentru Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Măsoară valorile de performanță pentru încărcarea paginii, cum ar fi Largest Contentful Paint și indicele de viteză."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Evaluează funcțiile aplicațiilor web progresive."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Identifică problemele legate de accesibilitate în aplicații cu o singură pagină sau formulare complexe."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Evaluează recomandările pentru meniuri și elemente IU din spatele interacțiunilor."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Măsoară modificările de aspect și timpul de execuție JavaScript pentru o serie de interacțiuni."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descoperă oportunități de performanță pentru a îmbunătăți experiența în paginile vechi și aplicațiile cu o singură pagină."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Cel mai mare impact"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} audit informativ}few{{numInformative} audituri informative}other{{numInformative} de audituri informative}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Încărcarea paginii"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Rapoartele privind navigarea analizează încărcarea unei singure pagini, exact ca rapoartele Lighthouse inițiale."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Raport privind navigarea"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} raport privind navigarea}few{{numNavigation} rapoarte privind navigarea}other{{numNavigation} de rapoarte privind navigarea}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} audit care poate fi trecut}few{{numPassableAudits} audituri care pot fi trecute}other{{numPassableAudits} de audituri care pot fi trecute}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audit trecut}few{{numPassed} audituri trecute}other{{numPassed} de audituri trecute}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Me<PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Eroare"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Slabă"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Salvează"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Starea înregistrată a paginii"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Rapoartele privind instantaneele analizează pagina într-o anumită stare, de obicei după interacțiunile cu utilizatorul."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Raport instantaneu"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} raport instantaneu}few{{numSnapshot} rapoarte instantanee}other{{numSnapshot} de rapoarte instantanee}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interacțiunile utilizatorilor"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Rapoartele privind perioada analizează o perioadă arbitrară, care conține de obicei interacțiuni cu utilizatorul."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Raport privind perioada de timp"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} raport privind perioada de timp}few{{numTimespan} rapoarte privind perioada de timp}other{{numTimespan} de rapoarte privind perioada de timp}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Raport privind fluxul pentru utilizatori Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Pentru conținut animat, folo<PERSON><PERSON><PERSON> [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) ca să minimizezi utilizarea CPU în timp ce conținutul rămâne în afara ecranului."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "<PERSON>fișeaz<PERSON> toate componentele [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) în formate WebP și specifică o alternativă adecvată pentru alte browsere. [Află mai multe](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Verifică dacă folo<PERSON>ști [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) pentru ca imaginile să se încarce lent automat. [Află mai multe](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Folosește instrumente precum [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) pentru a [reda aspectele AMP pe server](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consultă [documentația AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) ca să te asiguri că se acceptă toate stilurile."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Componenta [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) acceptă ca atributul [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) să specifice elementele imagine care se vor folosi în funcție de dimensiunea ecranului. [Află mai multe](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Poți derula virtual cu Kitul pentru dezvoltatori de componente (CDK) dacă se redau liste foarte mari. [Află mai multe](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Aplică [scindarea prin cod la nivel de traseu](https://web.dev/route-level-code-splitting-in-angular/) ca să minimizezi dimensiunea grupurilor JavaScript. În plus, poți memora în avans în cache elementele cu [service workerul Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Dacă folosești CLI Angular, verifică dacă versiunile sunt generate în modul de producție. [Află mai multe](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Dacă folosești CLI Angular, include hăr<PERSON><PERSON> sursă în versiunea de producție ca să-ți inspectezi grupurile. [Află mai multe](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Preîncarcă traseele din timp, ca să accelerezi navigarea. [Află mai multe](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Poți folosi utilitarul `BreakpointObserver` din Kitul pentru dezvoltatori de componente (CDK) pentru a gestiona punctele de întrerupere ale imaginilor. [Află mai multe](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Îți recomandăm să încarci GIF-ul într-un serviciu care îl va pune la dispoziție pentru încorporare ca videoclip HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Specifică `@font-display` at<PERSON><PERSON> când definești fonturile personalizate pentru temă."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Configurează [formatele de imagine WebP cu un stil de imagine Convert](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) pe site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instalează [un modul Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) care poate încărca lent imagini. Aceste module oferă opțiunea de a amâna imaginile din afara ecranului pentru a îmbunătăți performanța."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Îți recomandăm să folosești un modul pentru a activa inline elementele CSS și JavaScript critice sau să încarci în mod asincron elemente prin JavaScript, de exemplu, modulul [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Atenție, optimizările oferite de acest modul pot să întrerupă site-ul, astfel că probabil va trebui să modifici codul."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, modulele și specificațiile serverului contribuie toate la timpul de răspuns al serverului. Îți recomandăm să găsești o temă mai optimizată, selectând cu atenție un modul de optimizare și/sau făcând upgrade la server. Serverele de găzduire ar trebui să folosească stocarea în memoria cache opcode a codului PHP pentru a reduce duratele de interogare a bazei de date, de exemplu, Redis sau Memcached, precum și logica optimizată a aplicației pentru a pregăti paginile mai rapid."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Îți recomandăm să folosești [Stilurile de imagini adaptabile](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) pentru a reduce dimensiunea imaginilor încărcate în pagină. Dacă folosești Views pentru a afișa mai multe elemente de conținut într-o pagină, îți recomandăm să implementezi paginarea pentru a limita numărul elementelor de conținut afișate într-o anumită pagină."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Activează opțiunea „Aggregate CSS files” (Agregă fișierele CSS) în pagina „Administration » Configuration » Development” (Administrare » Configurare » Dezvoltare). Poți să configurezi și opțiuni de agregare mai avansate folosind [module suplimentare](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search), ca să accelerezi site-ul prin concatenarea, minimizarea și comprimarea stilurilor CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Activează opțiunea „Aggregate JavaScript files” (Agregă fișierele JavaScript) în pagina „Administration » Configuration » Development” (Administrare » Configurare » Dezvoltare). Poți să configurezi și opțiuni de agregare mai avansate folosind [module suplimentare](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search), ca să accelerezi site-ul prin concatenarea, minimizarea și comprimarea elementelor JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Îți recomandăm să elimini regulile CSS nefolosite și să atașezi numai bibliotecile Drupal necesare la pagina sau componenta relevantă dintr-o pagină. Accesează [linkul spre documentația Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) pentru detalii. Ca să identifici bibliotecile atașate care adaugă conținut CSS neesențial, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica tema/modulul responsabil din adresa URL a foii de stil dacă dezactivezi agregarea CSS pe site-ul Drupal. Caută teme/module care au multe foi de stil în listă cu mult roșu în bara acoperirii codului. O temă/un modul ar trebui să pună în coadă o foaie de stil numai dacă este într-adevăr folosită în pagină."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Îți recomandăm să elimini elementele JavaScript nefolosite și să atașezi numai bibliotecile Drupal necesare la pagina sau componenta relevantă dintr-o pagină. Accesează [linkul spre documentația Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) pentru detalii. Ca să identifici bibliotecile atașate care adaugă conținut JavaScript neesențial, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica tema/modulul responsabil din adresa URL a scriptului dacă dezactivezi agregarea JavaScript pe site-ul Drupal. Caută teme/module care au multe scripturi în listă cu mult roșu în acoperirea codului. O temă/un modul ar trebui să pună în coadă un script numai dacă este într-adevăr folosit în pagină."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Setează „Browser and proxy cache maximum age” (Vechimea maximă a browserului și a memoriei cache de proxy) în pagina „Administration » Configuration » Development” (Administrare » Configurare » Dezvoltare). Citește despre [memoria cache Drupal și optimizarea pentru performanță](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Îți recomandăm să folo<PERSON>ști [un modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) care optimizează și reduce automat dimensiunea imaginilor încărcate pe site, păstrând calitatea. În plus, folosește [Stilurile de imagini adaptabile](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) native oferite în Drupal (disponibile în Drupal versiunea 8 și versiunile ulterioare) pentru toate imaginile redate pe site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Indiciile de resurse „preconnect” sau „dns-prefetch” pot fi adăugate prin instalarea și configurarea [unui modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) care oferă facilități pentru indicii de resurse user agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Folosește [Stilurile de imagini adaptabile](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) native oferite în Drupal (disponibile în Drupal versiunea 8 și versiunile ulterioare). Folosește Stilurile de imagini adaptabile când redai câmpuri de imagine în moduri de afișare, afișări sau imagini încărcate în editorul WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Optimize Fonts` ca să folosești automat funcția CSS `font-display` ca să te asiguri că textul este vizibil pentru utilizatori în timp ce fonturile web se încarcă."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Next-Gen Formats` ca să transformi imaginile în WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Lazy Load Images` ca să amâni încărcarea imaginilor din afara ecranului până când este nevoie de ele."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Critical CSS` și `Script Delay` ca să amâni codul JS/CSS neesențial."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Folosește [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) ca să memorezi în cache conținutul din rețeaua din întreaga lume, îmbunătățind timpul până la primul byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Minify CSS` ca să minimizezi automat codul CSS și să reduci dimensiunea sarcinilor de rețea."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Minify Javascript` ca să minimizezi automat codul JS și să reduci dimensiunea sarcinilor de rețea."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Remove Unused CSS` pentru ajutor cu această problemă. Astfel, vor fi identificate clasele CSS folosite în fiecare pagină din site, iar celelalte vor fi eliminate, pentru a păstra dimensiunea fișierului mică."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Efficient Static Cache Policy` ca să setezi valorile recomandate în antetul de memorare în cache pentru elementele statice."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Next-Gen Formats` ca să transformi imaginile în WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Pre-Connect Origins` ca să adaugi automat indicii pentru resurse `preconnect` și să stabilești conexiuni anticipate către originile terțe importante."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Preload Fonts` și `Preload Background Images` ca să adaugi linkuri `preload` care să acorde prioritate preluării resurselor solicitate momentan în cursul încărcării paginii."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Folosește [Ezoic Leap](https://pubdash.ezoic.com/speed) și activează `Resize Images` ca să redimensionezi imaginile la o dimensiune adecvată pentru dispozitiv, reducând dimensiunile sarcinilor de rețea."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Îți recomandăm să încarci GIF-ul într-un serviciu care îl va pune la dispoziție pentru încorporare ca videoclip HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Îți recomandăm să folosești un [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) sau un serviciu care convertește automat imaginile încărcate în formatele optime."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instalează un [plugin <PERSON><PERSON><PERSON> de încărcare lentă](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) care oferă capacitatea de a amâna imaginile din afara ecranului sau schimbă-ți șablonul cu unul care oferă acea funcție. Începând cu versiunea Joomla 4.0, toate imaginile noi vor prelua [automat](https://github.com/joomla/joomla-cms/pull/30748) atributul `loading` de la nucleu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Există o serie de pluginuri Joomla care te pot ajuta să [activezi inline elementele critice](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) sau să [amâni resurse mai puțin importante](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Atenție, optimizările oferite de aceste pluginuri pot să întrerupă funcțiile șabloanelor sau pluginurilor tale, astfel încât va trebui să le testezi în detaliu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, extensiile și specificațiile serverului contribuie toate la timpul de răspuns al serverului. Îți recomandăm să găsești un șablon mai optimizat, selectând cu atenție o extensie de optimizare și/sau făcând upgrade la server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Îți recomandăm să afișezi extrase în categoriile de articole (de exemplu, în linkul Citește mai mult), reducând numărul de articole afișate pe o anumită pagină, împărțind postările lungi pe mai multe pagini sau folosind un plugin pentru a încărca lent comentariile."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON> mult<PERSON> [extens<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea stilurilor CSS. Există și șabloane care oferă această funcție."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON> mult<PERSON> [extens<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea scripturilor. Există și șabloane care oferă această funcție."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [extensii Joomla](https://extensions.joomla.org/) care încarcă CSS nefolosit în pagină. Ca să identifici extensiile care adaugă conținut CSS neesențial, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica tema/pluginul responsabil din adresa URL a foii de stil. Caută pluginuri care au multe foi de stil în listă cu mult roșu în bara acoperirii codului. Un plugin ar trebui să pună în coadă o foaie de stil numai dacă este într-adevăr folosită în pagină."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [extensii Joomla](https://extensions.joomla.org/) care încarcă JavaScript nefolosit în pagină. Ca să identifici pluginurile care adaugă conținut JS neesențial, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica extensia responsabilă din adresa URL a scriptului. Caută extensii care au multe scripturi în listă cu mult roșu în bara acoperirii codului. O extensie ar trebui să pună în coadă un script numai dacă este într-adevăr folosit în pagină."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Citește despre [stocarea în memoria cache a browserului în Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Îți recomandăm să folosești un [plugin de optimizare a imaginii](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) care comprimă imaginile, păstrând calitatea."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Îți recomandăm să folosești un [plugin pentru imagini adaptabile](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) pentru a utiliza imagini adaptabile în conținut."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Poți activa comprimarea textului activând Comprimarea paginilor Gzip în Joomla: System > Global configuration > Server (Sistem > Configurație globală > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Dacă nu grupezi elementele JavaScript, poți folosi [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Dezactivează [gruparea și minimalizarea JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) încorporate din Magento și folosește [baler](https://github.com/magento/baler/) în loc."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Specifică `@font-display` atunci când [definești fonturile personalizate](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Caută în [Piața Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) o varietate de extensii terță parte pentru a obține formate de imagine mai noi."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Modifică șabloanele de produse și de cataloage ca să folosești funcția de [încărcare lentă](https://web.dev/native-lazy-loading) a platformei web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Folosește [integrarea Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) din Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Activează opțiunea „Minimalizează fișierele CSS” din setările pentru dezvoltatori ale magazinului. [Află mai multe](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Folosește [Terser](https://www.npmjs.com/package/terser) ca să minimizezi toate elementele JavaScript din implementările de conținut static și să dezactivezi funcția de minimizate încorporată."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Dezactivează [gruparea JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) încorporată în Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Caută în [Piața Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) o varietate de extensii terță parte pentru a optimiza imaginile."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Indiciile de resurse preconectare sau dns-prefetch pot fi adăugate prin [modificarea aspectului unei teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Etichetele `<link rel=preload>` se pot adăuga prin [modificarea aspectului unei teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Folosește componenta `next/image` în loc de `<img>` ca să optimizezi automat formatul de imagine. [Află mai multe](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Folosește componenta `next/image` în loc de `<img>` pentru a încărca asincron automat imagini. [Află mai multe](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Folosește componenta `next/image` și setează prioritatea la true ca să preîncarci imaginea LCP. [Află mai multe](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Folosește componenta `next/script` pentru a amâna încărcarea scripturilor terță parte neesențiale. [Află mai multe](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Folosește componenta `next/image` ca să te asiguri că imaginile au întotdeauna dimensiunea corectă. [Află mai multe](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Este recomandat să setezi `PurgeCSS` în configurația `Next.js` pentru a elimina regulile nefolosite din foile de stil. [Află mai multe](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON>ște `Webpack Bundle Analyzer` pentru a detecta cod JavaScript nefolosit. [Află mai multe](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Este recomandat s<PERSON> folo<PERSON> `Next.js Analytics` pentru a măsura performanța reală a aplicației. [Află mai multe](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configurează stocarea în memoria cache a elementelor și a paginilor `Server-side Rendered` (SSR) invariabile. [Află mai multe](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Folosește componenta `next/image` în loc de `<img>` ca să ajustezi calitatea imaginii. [Află mai multe](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Folosește componenta `next/image` pentru a seta `sizes` potrivite. [Află mai multe](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Activează comprimarea pe serverul Next.js. [Află mai multe](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Folosește componenta `nuxt/image` și setea<PERSON> `format=\"webp\"`. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Folosește componenta `nuxt/image` și set<PERSON> `loading=\"lazy\"` pentru imagini in afara ecranului. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Folosește componenta `nuxt/image` și specifică `preload` pentru imaginea LCP. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Folosește componenta `nuxt/image` și specifică explicit `width` și `height`. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Folosește componenta `nuxt/image` și setează `quality` potrivit. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Folosește componenta `nuxt/image` și seteaz<PERSON> `sizes` potrivit. [Află mai multe](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Înlocuiește GIF-urile animate cu videoclipuri](https://web.dev/replace-gifs-with-videos/) pentru încărcarea mai rapidă a paginilor web și folosește formate de fișiere moderne, cum ar fi [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) sau [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), pentru a îmbunătăți eficiența comprimării cu până la 30 % față de cea a codecului video actual de ultimă generație, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Îți recomandăm să folosești un [plugin](https://octobercms.com/plugins?search=image) sau un serviciu care convertește automat imaginile încărcate în formatele optime. [Imaginile WebP fără pierderi](https://developers.google.com/speed/webp) sunt cu 26 % mai mici decât PNG-urile și cu 25 – 34 % mai mici decât imaginile JPEG comparabile la indicele de calitate SSIM echivalent. Un alt format de imagine de ultimă generație recomandat este [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Îți recomandăm să instalezi un [plugin de încărcare lentă a imaginilor](https://octobercms.com/plugins?search=lazy) care oferă capacitatea de a amâna imaginile din afara ecranului sau schimbă-ți tema cu una care oferă această funcție. Îți recomandăm [pluginul AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Există multe pluginuri care ajută la [activarea inline a elementelor critice](https://octobercms.com/plugins?search=css). Acestea pot să întrerupă alte pluginuri, astfel încât trebuie să le testezi în detaliu."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, pluginurile și specificațiile serverului contribuie toate la timpul de răspuns al serverului. Îți recomandăm să găsești o temă mai optimizată, selectând cu atenție un plugin de optimizare și / sau făcând upgrade la server. CMS-ul October le dă dezvoltatorilor posibilitatea de a folosi [`Queues`](https://octobercms.com/docs/services/queues) pentru a amâna procesarea unei activități care durează mult timp, cum ar fi trimiterea unui e-mail. Astfel, solicitările web sunt accelerate semnificativ."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Îți recomandăm să afișezi extrase în listele postărilor (de exemplu, folosind un buton `show more`), să reduci numărul de postări afișate pe o anumită pagină web, să împarți postările lungi pe mai multe pagini web sau să folosești un plugin pentru a încărca lent comentariile."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Există multe [pluginuri](https://octobercms.com/plugins?search=css) care pot accelera un site prin concatenarea, minimizarea și comprimarea stilurilor. Folosirea unui proces de versiune pentru a face minimizarea în avans poate accelera dezvoltarea."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Există multe [pluginuri](https://octobercms.com/plugins?search=javascript) care pot accelera un site prin concatenarea, minimizarea și comprimarea scripturilor. Folosirea unui proces de versiune pentru a face minimizarea în avans poate accelera dezvoltarea."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Îți recomandăm să consulți [pluginurile](https://octobercms.com/plugins) care încarcă CSS nefolosit pe site. Ca să identifici pluginurile care adaugă CSS inutil, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Identifică tema / pluginul responsabil din adresa URL a foii de stil. Caută pluginuri care au multe foi de stil cu mult roșu în bara acoperirii codului. Un plugin ar trebui să adauge o foaie de stil numai dacă este într-adevăr folosită în pagina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Îți recomandăm să consulți [pluginurile](https://octobercms.com/plugins?search=javascript) care încarcă JavaScript nefolosit în pagina web. Ca să identifici pluginurile care adaugă JavaScript inutil, rulează [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Identifică tema / pluginul responsabil din adresa URL a scriptului. Caută pluginuri care au multe scripturi cu mult roșu în bara acoperirii codului. Un plugin ar trebui să adauge un script numai dacă este într-adevăr folosit în pagina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Citește despre [împiedicarea solicitărilor de rețea inutile cu ajutorul memoriei cache HTTP](https://web.dev/http-cache/#caching-checklist). Există multe [pluginuri](https://octobercms.com/plugins?search=Caching) care pot fi folosite pentru a accelera stocarea în memoria cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Îți recomandăm să folosești un [plugin de optimizare a imaginii](https://octobercms.com/plugins?search=image) pentru a comprima imaginile, păstrând calitatea."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Încarcă imaginile direct în managerul de conținut media pentru a te asigura că sunt disponibile dimensiunile de imagine necesare. Îți recomandăm să folosești [filtrul de redimensionare](https://octobercms.com/docs/markup/filter-resize) sau un [plugin de redimensionare a imaginilor](https://octobercms.com/plugins?search=image) pentru a te asigura că se folosesc dimensiunile de imagine optime."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Activează comprimarea textului în configurația serverului web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Folosește o bibliotecă „windowing”, ca `react-window`, pentru a minimaliza numărul de noduri DOM create dacă redai multe elemente repetate pe pagină. [Află mai multe](https://web.dev/virtualize-long-lists-react-window/). În plus, redu redările suplimentare inutile folosind [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) sau [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) și [omite efectele](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) doar până când anumite dependențe s-au schimbat dacă folosești hookul `Effect` ca să îmbunătățești performanța la redare."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Dacă folosești React Router, minimalizează folosirea componentei `<Redirect>` pentru [navigările pe traseu](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Dacă redai componente React pe server, poți folosi `renderToPipeableStream()` sau `renderToStaticNodeStream()` ca să-i permiți clientului să primească și să completeze diferite părți ale codului de markup, nu pe tot deodată. [Află mai multe](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Dacă sistemul de versiuni minimalizează automat fișierele CSS, verifică dacă implementezi versiunea de producție a aplicației. Poți face verificarea cu extensia React Developer Tools. [Află mai multe](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Dacă sistemul de versiuni minimalizează automat fișierele JS, verifică dacă implementezi versiunea de producție a aplicației. Poți face verificarea cu extensia React Developer Tools. [Află mai multe](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Dacă nu redai pe server, [împarte grupurile JavaScript](https://web.dev/code-splitting-suspense/) cu `React.lazy()`. Altfel, scindează prin cod folosind o bibliotecă terță parte, precum [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Folosește React DevTools Profiler, care beneficiază de API-ul Profiler pentru a măsura performanța de redare a componentelor. [Află mai multe.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Îți recomandăm să încarci GIF-ul într-un serviciu care îl va pune la dispoziție pentru încorporare ca videoclip HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Îți recomandăm să folosești pluginul [Performance Lab](https://wordpress.org/plugins/performance-lab/) pentru a face automat conversia imaginilor JPEG încărcate în WebP, dacă este acceptat."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instalează un [plugin WordPress de încărcare lentă](https://wordpress.org/plugins/search/lazy+load/) care oferă capacitatea de a amâna imaginile din afara ecranului sau schimbă-ți tema cu una care oferă acea funcție. Îți recomandăm [pluginul AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Există o serie de pluginuri Wordpress care te pot ajuta să [activezi inline elementele critice](https://wordpress.org/plugins/search/critical+css/) sau să [amâni resurse mai puțin importante](https://wordpress.org/plugins/search/defer+css+javascript/). Atenție, optimizările oferite de aceste pluginuri pot să întrerupă funcțiile temei sau pluginurilor tale, astfel încât este posibil să trebuiască să modifici codul."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, pluginurile și specificațiile serverului contribuie toate la timpul de răspuns al serverului. Îți recomandăm să găsești o temă mai optimizată, selectând cu atenție un plugin de optimizare și/sau făcând upgrade la server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Îți recomandăm să afișezi extrase în listele postărilor tale (de exemplu, prin intermediul filei Mai multe), reducând numărul de postări afișate pe o anumită pagină, împărțind postările lungi pe mai multe pagini sau folosind un plugin pentru a încărca lent comentariile."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON> multe [pluginuri WordPress](https://wordpress.org/plugins/search/minify+css/) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea stilurilor. Ai putea să folosești și un proces de design pentru a face minimizarea în avans, dacă este posibil."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON> multe [pluginuri WordPress](https://wordpress.org/plugins/search/minify+javascript/) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea scripturilor. Ai putea să folosești și un proces de design pentru a face minimizarea în avans, dacă este posibil."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [pluginuri WordPress](https://wordpress.org/plugins/) care încarcă CSS nefolosit în pagină. Ca să identifici pluginurile care adaugă conținut CSS neesențial, încearcă să rulezi [acoperirea codului](https://developer.chrome.com/docs/devtools/coverage/) în Chrome DevTools. Poți identifica tema/pluginul responsabil din adresa URL a foii de stil. Caută pluginuri care au multe foi de stil în listă cu mult roșu în bara acoperirii codului. Un plugin ar trebui să pună în coadă o foaie de stil numai dacă este într-adevăr folosită în pagină."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [pluginuri WordPress](https://wordpress.org/plugins/) care încarcă JavaScript nefolosit în pagină. Ca să identifici pluginurile care adaugă conținut JS neesențial, încearcă să rulezi [acoperirea codului](https://developer.chrome.com/docs/devtools/coverage/) în Chrome DevTools. Poți identifica tema/pluginul responsabil din adresa URL a scriptului. Caută pluginuri care au multe scripturi în listă cu mult roșu în bara acoperirii codului. Un plugin ar trebui să pună în coadă un script numai dacă este într-adevăr folosit în pagină."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Citește despre [stocarea în memoria cache a browserului în WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Îți recomandăm să folosești un [plugin WordPress de optimizare a imaginii](https://wordpress.org/plugins/search/optimize+images/) care comprimă imaginile, păstrând calitatea."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Încarcă imaginile direct prin [biblioteca de media](https://wordpress.org/support/article/media-library-screen/) pentru a te asigura că sunt disponibile dimensiunile de imagine necesare, apoi inserează-le din biblioteca de media sau folosește widgetul de imagine pentru a te asigura că se folosesc dimensiunile de imagine optime (inclusiv cele pentru punctele de întrerupere adaptabile). Evită să folosești imagini `Full Size`, cu excepția cazului în care dimensiunile sunt adecvate pentru utilizare. [Află mai multe](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Poți activa comprimarea textului în configurarea serverului web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Activează Imagify din fila Optimizarea imaginilor din WP Rocket pentru a converti imaginile în WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Activează [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) în WP Rocket pentru a remedia această recomandare. Această funcție întârzie încărcarea imaginilor până când vizitatorul derulează în jos pagina și trebuie să le vadă."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Activează [Elimină CSS nefolosit](https://docs.wp-rocket.me/article/1529-remove-unused-css) și [Încărcare JavaScript amânată](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) în WP Rocket pentru a remedia această recomandare. Aceste funcții vor optimiza fișierele CSS și JavaScript, astfel încât să nu blocheze redarea paginii."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Pentru a remedia această problemă, activează [Minimizează fișierele CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) în WP Rocket. Toate spațiile și comentariile din fișierele CSS ale site-ului se vor elimina pentru ca dimensiunea fișierului să fie mai scurtă și mai rapidă."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Pentru a remedia această problemă, activează [Minimizează fișierele JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) în WP Rocket. Spațiile și comentariile goale se vor elimina din fișierele JavaScript pentru a le face mai mici și mai rapide."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Pentru a remedia această problemă, activează [Elimină CSS nefolosit](https://docs.wp-rocket.me/article/1529-remove-unused-css) din WP Rocket. Aceasta reduce dimensiunea paginii, eliminând tot limbajul CSS și foile de stil care nu sunt folosite, păstrând numai limbajul CSS folosit pentru fiecare pagină."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Pentru a remedia această problemă, activează [Întârzierea executării JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) în WP Rocket. Aceasta va îmbunătăți încărcarea paginii prin întârzierea executării scripturilor până la interacțiunea cu utilizatorul. Dacă site-ul are iframe-uri, poți să folosești și [LazyLoad pentru iframe-uri și videoclipuri](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) de la WP Rocket și [să înlocuiești iframe-ul YouTube cu imaginea de previzualizare](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Activează Imagify din fila Optimizarea imaginilor din WP Rocket și rulează Optimizarea în bloc pentru a comprima imaginile."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Folosește [Preia în avans solicitările DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) în WP Rocket pentru a adăuga dns-preFetch și a accelera conexiunea cu domeniile externe. În plus, WP Rocket adaugă automat preconnect la [domeniul Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) și toate CNAME-urile adăugate prin funcția [Activează CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Pentru a remedia această problemă privind fonturile, activează [Elimină CSS nefolosit](https://docs.wp-rocket.me/article/1529-remove-unused-css) din WP Rocket. Fonturile critice de pe site vor fi preîncărcate cu prioritate."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Accesează calculatorul."}, "report/renderer/report-utils.js | collapseView": {"message": "Restrânge vizualizarea"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navigare inițială"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latența maximă a căii critice:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copiază JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Activează/dezactivează Tema întunecată"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Printează în formă extinsă"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Rezumatul printării"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Salvează ca Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Salvează ca HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Salvează ca JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Deschide în vizualizator"}, "report/renderer/report-utils.js | errorLabel": {"message": "E<PERSON>re!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Eroare de raport: nu există informații de auditare"}, "report/renderer/report-utils.js | expandView": {"message": "Extinde vizualizarea"}, "report/renderer/report-utils.js | footerIssue": {"message": "Raportează o problemă"}, "report/renderer/report-utils.js | hide": {"message": "Ascunde"}, "report/renderer/report-utils.js | labDataTitle": {"message": "<PERSON><PERSON> testului"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) a paginii actuale cu o rețea mobilă simulată. Valorile sunt estimate și pot varia."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Elemente suplimentare de verificat manual"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Nu se aplică"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunitate"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Economii estimate"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tre<PERSON>"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Încărcarea inițială a paginii"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitare personalizată"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Computer simulat"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON> simulare"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Vers<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Putere CPU/memorie nelimitată"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitarea CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispozitiv"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitarea rețelei"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Simularea ecranului"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User Agent (rețea)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "O singură încărcare a paginii"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Datele se preiau dintr-o singură încărcare a paginii, spre deosebire de datele de pe teren care rezumă mai multe sesiuni."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitare 4G lentă"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Necunoscută"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON>ș<PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Vezi audituri relevante pentru:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Restrânge fragmentul"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Extinde fragmentul"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Afișează resursele terță parte"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Oferită de mediu"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Au apărut probleme care au afectat această rulare Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Valorile sunt estimate și pot varia. [Scorul de performanță este calculat](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) direct folosind aceste valori."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "<PERSON><PERSON><PERSON> urma <PERSON>"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Afișează Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> trecute, dar cu avertismente"}, "report/renderer/report-utils.js | warningHeader": {"message": "Avertismente: "}, "treemap/app/src/util.js | allLabel": {"message": "Toate"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Toate scripturile"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Acoperire"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON> dub<PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "<PERSON><PERSON><PERSON> resursă"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nume"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Activează / dezactivează tabelul"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "<PERSON><PERSON><PERSON>fo<PERSON>"}}