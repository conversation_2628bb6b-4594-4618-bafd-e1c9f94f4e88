{"version": 3, "file": "StringUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/StringUtilities.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,yEAAyE;AACzE,6BAA6B;AAE7B,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,WAAmB,EAAE,aAAqB,EAAU,EAAE;IACrF,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QAC9C,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACxD,SAAS,GAAG,IAAI,CAAC;YACjB,MAAM;QACR,CAAC;IACH,CAAC;IAED,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QAC5C,IAAI,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QACD,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,QAAgB,EAAE,WAAmB,EAAU,EAAE;IACtE,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF,yEAAyE;AACzE,uEAAuE;AACvE,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;IAClC,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,KAAK,CAAC;IACb,CAAC,IAAI,EAAE,MAAM,CAAC;IACd,CAAC,IAAI,EAAE,MAAM,CAAC;IACd,CAAC,MAAM,EAAE,UAAU,CAAC;IACpB,CAAC,SAAS,EAAE,aAAa,CAAC;IAC1B,CAAC,UAAU,EAAE,cAAc,CAAC;CAC7B,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,OAAe,EAAU,EAAE;IAC3D,MAAM,gBAAgB,GAAG,yDAAyD,CAAC;IACnF,MAAM,+BAA+B,GAAG,2DAA2D,CAAC;IACpG,MAAM,aAAa,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,WAAmB,EAAE,aAAqB,EAAU,EAAE;QAC3G,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzC,kEAAkE;gBAClE,OAAO,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC;YACD,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAChE,OAAO,KAAK,GAAG,WAAW,CAAC;QAC7B,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnE,OAAO,KAAK,GAAG,YAAY,CAAC;QAC9B,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAChD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,KAAK,GAAG,IAAI,CAAC;QACb,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;SAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAClC,KAAK,GAAG,GAAG,CAAC;QACZ,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;SAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7D,KAAK,GAAG,GAAG,CAAC;QACZ,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,IAAI,CAAC;QACb,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,+BAA+B,EAAE,aAAa,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,GAAG,KAAK,GAAG,cAAc,GAAG,KAAK,EAAE,CAAC;AAC7C,CAAC,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,GAAW,EAAE,GAAG,IAAe,EAAU,EAAE;IACjE,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,MAAM,EAAE,GAAG,oCAAoC,CAAC;IAChD,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAS,EAAE,KAAc,EAAE,SAAkB,EAAE,SAAkB,EAAE,EAAE;QAC9F,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC;QACb,CAAC;QACD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;YACnC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,UAAU,CAAC,2BAA2B,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QACD,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,IAAI,UAAU,CAAC,qBAAqB,QAAQ,GAAG,CAAC,gCAAgC,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC;QACpH,CAAC;QACD,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACxC,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpB,QAAQ,GAAG,CAAC,CAAC;QACf,CAAC;QACD,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;YACtB,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,WAAmB,EAAU,EAAE;IACtD;;;;OAIG;IAEH,SAAS,UAAU,CAAC,CAAS;QAC3B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjG,CAAC;IACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACZ,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,KAAK,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3B,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,CAAC;QACpC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAChB,OAAO,IAAI,MAAM,CAAC,YAAY,CAC1B,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACxG,CAAC,GAAG,CAAC,CAAC;QACR,CAAC;IACH,CAAC;IACD,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QAChB,OAAO,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC/F,CAAC;SAAM,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACrH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,WAAmB,EAAE,YAAoB,EAAY,EAAE;IAC5F,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC1C,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,WAAmB,EAAY,EAAE;IACrE,MAAM,OAAO,GAAG,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAC1D,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,WAAmB,EAAW,EAAE;IAC3D,OAAO,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,GAAW,EAAE,aAAsB,EAAU,EAAE;IACrE,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;IACzD,IAAI,aAAa,EAAE,CAAC;QAClB,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACjE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,WAAmB,EAAU,EAAE;IAChE,OAAO,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,WAAmB,EAAU,EAAE;IACrD,OAAO,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAAG,CAAC,WAAmB,EAAU,EAAE;IACtE,uEAAuE;IACvE,qCAAqC;IACrC,OAAO,WAAW,CAAC,OAAO,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;AAC7E,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,WAAmB,EAAU,EAAE;IAC5D,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACd,KAAK,EAAE,CAAC;QACV,CAAC;aAAM,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;aAAM,IAAI,CAAC,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC9C,+DAA+D;gBAC/D,kBAAkB;gBAClB,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3C,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;oBACrC,2DAA2D;oBAC3D,uBAAuB;oBACvB,KAAK,IAAI,CAAC,CAAC;oBACX,CAAC,EAAE,CAAC;oBACJ,SAAS;gBACX,CAAC;YACH,CAAC;YACD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAU,EAAE;IAC1D,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAM,0BAA0B,GAAG,yDAAyD,CAAC;AAE7F;;;;;;;;;;GAUG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,QAAgB,EAAW,EAAE;IAC/D,OAAO,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAU,EAAE;IACtD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAU,EAAE;IAC5D,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;IACd,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,wBAAwB,GAAG,oBAAoB,CAAC;AAEtD,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,OAAO,wBAAwB,CAAC;AAClC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,UAAS,KAAa;IAC/C,IAAI,WAAW,GAAG,aAAa,CAAC,CAAE,qCAAqC;IACvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC/C,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACf,CAAC;QACD,WAAW,IAAI,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,UAC7B,KAAa,EAAE,aAAsB,EAAE,OAAgB,EAAE,iBAA0B,KAAK;IAC1F,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,IAAI,WAAW,CAAC;IAEhB,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,CAAC;YACH,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,gBAAgB;QAClB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,WAAW,GAAG,0BAA0B,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAC9D,CAAC;IAED,IAAI,cAAc,IAAI,WAAW,EAAE,CAAC;QAClC,WAAW,GAAG,IAAI,MAAM,CAAC,MAAM,WAAW,CAAC,MAAM,KAAK,EAAE,UAAU,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAAG,UAAS,CAAS,EAAE,CAAS;IACpE,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IACpB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IACpB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACZ,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAS,MAAe;IAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CAAC,CAAC;IACX,CAAC;IACD,4FAA4F;IAC5F,wDAAwD;IACxD,uGAAuG;IACvG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,kBAAkB;IAClD,MAAM,CAAC,GAAG,UAAU,CAAC,CAAW,0BAA0B;IAC1D,MAAM,EAAE,GAAG,UAAU,CAAC,CAAU,2BAA2B;IAC3D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,MAAM,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACrC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QACtB,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,CAAS,EAAE,CAAS,EAAU,EAAE;IACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACV,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACV,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,SAAiB,EAAU,EAAE;IACnE,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,QAAQ,GAAG,SAAS,IAAI,CAAC,CAAC;IAC9B,IAAI,SAAS,GAAG,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;IACzC,IAAK,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAY,IAAI,OAAO,EAAE,CAAC;QACvE,EAAE,SAAS,CAAC;QACZ,EAAE,QAAQ,CAAC;IACb,CAAC;IACD,IAAI,QAAQ,GAAG,CAAC,IAAK,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAY,IAAI,OAAO,EAAE,CAAC;QACzE,EAAE,QAAQ,CAAC;IACb,CAAC;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,CAAC;AACvF,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,GAAW,EAAE,SAAiB,EAAU,EAAE;IAC7E,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5C,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAU,EAAE;IACrD,OAAO,gBAAgB,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,CAAS,EAAE,CAAS,EAAU,EAAE;IACrE,MAAM,KAAK,GAAG,WAAW,CAAC;IAC1B,IAAI,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/B,OAAO,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,EAAE,CAAC;YACN,IAAI,CAAC,CAAC,EAAE,CAAC;gBACP,OAAO,CAAC,CAAC;YACX,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,CAAC;gBACN,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC;QACD,MAAM,GAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAc,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,GAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAc,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACrC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACrC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC;QACX,CAAC;QACD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAE,8CAA8C;oBACvF,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBACvC,CAAC;gBACD,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACvC,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;QACD,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,UAAS,OAAoB;IACvD,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACxC,IAAI,EAAE,CAAC;IACT,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QAC9D,IAAI,EAAE,CAAC;IACT,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC;AACjC,MAAM,CAAC,MAAM,YAAY,GAAG,GAAG,CAAC;AAChC,MAAM,SAAS,GAAG,IAAI,CAAC;AAEvB,MAAM,CAAC,MAAM,oBAAoB,GAAG,UAAS,GAAW;IACtD,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,CAAC,EAAE,CAAC;YACJ,SAAS;QACX,CAAC;QACD,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YACnD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;gBAC5B,cAAc,GAAG,EAAE,CAAC;YACtB,CAAC;iBAAM,IAAI,cAAc,KAAK,EAAE,EAAE,CAAC;gBACjC,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,6BAA6B,GAAG,CAAC,GAAW,EAAU,EAAE;IACnE,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;QACnB,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YAC3C,cAAc,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAAG,UAAS,KAAa,EAAE,KAAc;IAC9E,6DAA6D;IAC7D,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACtC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,sBAAsB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC/C,KAAK,IAAI,IAAI,CAAC;QAChB,CAAC;QACD,KAAK,IAAI,CAAC,CAAC;IACb,CAAC;IACD,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,kBAAkB;IACd,kBAAkB,CAAqB;CAChD;AAID,MAAM,CAAC,MAAM,iBAAiB,GAAG,UAAS,KAAa;IACrD,OAAO,KAAK,CAAC,WAAW,EAAqB,CAAC;AAChD,CAAC,CAAC;AAEF,MAAM,IAAI,GAAG,wGAAwG,CAAC;AACtH,mHAAmH;AACnH,wFAAwF;AACxF,sDAAsD;AACtD,oEAAoE;AACpE,qDAAqD;AACrD,wFAAwF;AACxF,uCAAuC;AACvC,4FAA4F;AAC5F,iGAAiG;AACjG,gGAAgG;AAChG,oEAAoE;AACpE,kCAAkC;AAClC,MAAM,CAAC,MAAM,WAAW,GAAG,UAAS,KAAa;IAC/C,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,KAAK,CAC3E,CAAC;AACxB,CAAC,CAAC;AAEF,uDAAuD;AACvD,MAAM,UAAU,eAAe,CAAC,YAE/B;IACC,MAAM,MAAM,GAER,EAAE,CAAC;IACP,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IACnC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,sDAAsD;AAEtD,4FAA4F;AAC5F,MAAM,CAAC,MAAM,WAAW,GAAG,UAAS,KAAa,EAAE,MAAc,EAAE,WAAmB;IACpF,MAAM,qBAAqB,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACxD,IAAI,qBAAqB,KAAK,CAAC,CAAC,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,qBAAqB,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACjH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,SAAS,sBAAsB,CAAC,CAAS,EAAE,SAAS,GAAG,CAAC;IAC5F,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;QACpB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,MAAM,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC1D,OAAO,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;AACxC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,UAAS,GAAW,EAAE,GAAW;IAC3D,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3C,8DAA8D;QAC9D,OAAO,GAAG,GAAG,GAAG,CAAC;IACnB,CAAC;IACD,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtD,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,OAAO,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC", "sourcesContent": ["// Copyright (c) 2020 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const escapeCharacters = (inputString: string, charsToEscape: string): string => {\n  let foundChar = false;\n  for (let i = 0; i < charsToEscape.length; ++i) {\n    if (inputString.indexOf(charsToEscape.charAt(i)) !== -1) {\n      foundChar = true;\n      break;\n    }\n  }\n\n  if (!foundChar) {\n    return String(inputString);\n  }\n\n  let result = '';\n  for (let i = 0; i < inputString.length; ++i) {\n    if (charsToEscape.indexOf(inputString.charAt(i)) !== -1) {\n      result += '\\\\';\n    }\n    result += inputString.charAt(i);\n  }\n\n  return result;\n};\n\nconst toHexadecimal = (charCode: number, padToLength: number): string => {\n  return charCode.toString(16).toUpperCase().padStart(padToLength, '0');\n};\n\n// Remember to update the third group in the regexps patternsToEscape and\n// patternsToEscapePlusSingleQuote when adding new entries in this map.\nconst escapedReplacements = new Map([\n  ['\\b', '\\\\b'],\n  ['\\f', '\\\\f'],\n  ['\\n', '\\\\n'],\n  ['\\r', '\\\\r'],\n  ['\\t', '\\\\t'],\n  ['\\v', '\\\\v'],\n  ['\\'', '\\\\\\''],\n  ['\\\\', '\\\\\\\\'],\n  ['<!--', '\\\\x3C!--'],\n  ['<script', '\\\\x3Cscript'],\n  ['</script', '\\\\x3C/script'],\n]);\n\nexport const formatAsJSLiteral = (content: string): string => {\n  const patternsToEscape = /(\\\\|<(?:!--|\\/?script))|(\\p{Control})|(\\p{Surrogate})/gu;\n  const patternsToEscapePlusSingleQuote = /(\\\\|'|<(?:!--|\\/?script))|(\\p{Control})|(\\p{Surrogate})/gu;\n  const escapePattern = (match: string, pattern: string, controlChar: string, loneSurrogate: string): string => {\n    if (controlChar) {\n      if (escapedReplacements.has(controlChar)) {\n        // @ts-ignore https://github.com/microsoft/TypeScript/issues/13086\n        return escapedReplacements.get(controlChar);\n      }\n      const twoDigitHex = toHexadecimal(controlChar.charCodeAt(0), 2);\n      return '\\\\x' + twoDigitHex;\n    }\n    if (loneSurrogate) {\n      const fourDigitHex = toHexadecimal(loneSurrogate.charCodeAt(0), 4);\n      return '\\\\u' + fourDigitHex;\n    }\n    if (pattern) {\n      return escapedReplacements.get(pattern) || '';\n    }\n    return match;\n  };\n\n  let escapedContent = '';\n  let quote = '';\n  if (!content.includes('\\'')) {\n    quote = '\\'';\n    escapedContent = content.replaceAll(patternsToEscape, escapePattern);\n  } else if (!content.includes('\"')) {\n    quote = '\"';\n    escapedContent = content.replaceAll(patternsToEscape, escapePattern);\n  } else if (!content.includes('`') && !content.includes('${')) {\n    quote = '`';\n    escapedContent = content.replaceAll(patternsToEscape, escapePattern);\n  } else {\n    quote = '\\'';\n    escapedContent = content.replaceAll(patternsToEscapePlusSingleQuote, escapePattern);\n  }\n  return `${quote}${escapedContent}${quote}`;\n};\n\n/**\n * This implements a subset of the sprintf() function described in the Single UNIX\n * Specification. It supports the %s, %f, %d, and %% formatting specifiers, and\n * understands the %m$d notation to select the m-th parameter for this substitution,\n * as well as the optional precision for %s, %f, and %d.\n *\n * @param fmt format string.\n * @param args parameters to the format string.\n * @returns the formatted output string.\n */\nexport const sprintf = (fmt: string, ...args: unknown[]): string => {\n  let argIndex = 0;\n  const RE = /%(?:(\\d+)\\$)?(?:\\.(\\d*))?([%dfs])/g;\n  return fmt.replaceAll(RE, (_: string, index?: string, precision?: string, specifier?: string) => {\n    if (specifier === '%') {\n      return '%';\n    }\n    if (index !== undefined) {\n      argIndex = parseInt(index, 10) - 1;\n      if (argIndex < 0) {\n        throw new RangeError(`Invalid parameter index ${argIndex + 1}`);\n      }\n    }\n    if (argIndex >= args.length) {\n      throw new RangeError(`Expected at least ${argIndex + 1} format parameters, but only ${args.length} where given.`);\n    }\n    if (specifier === 's') {\n      const argValue = String(args[argIndex++]);\n      if (precision !== undefined) {\n        return argValue.substring(0, Number(precision));\n      }\n      return argValue;\n    }\n    let argValue = Number(args[argIndex++]);\n    if (isNaN(argValue)) {\n      argValue = 0;\n    }\n    if (specifier === 'd') {\n      return String(Math.floor(argValue)).padStart(Number(precision), '0');\n    }\n    if (precision !== undefined) {\n      return argValue.toFixed(Number(precision));\n    }\n    return String(argValue);\n  });\n};\n\nexport const toBase64 = (inputString: string): string => {\n  /* note to the reader: we can't use btoa here because we need to\n   * support Unicode correctly. See the test cases for this function and\n   * also\n   * https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_Unicode_Problem\n   */\n\n  function encodeBits(b: number): number {\n    return b < 26 ? b + 65 : b < 52 ? b + 71 : b < 62 ? b - 4 : b === 62 ? 43 : b === 63 ? 47 : 65;\n  }\n  const encoder = new TextEncoder();\n  const data = encoder.encode(inputString.toString());\n  const n = data.length;\n  let encoded = '';\n  if (n === 0) {\n    return encoded;\n  }\n  let shift;\n  let v = 0;\n  for (let i = 0; i < n; i++) {\n    shift = i % 3;\n    v |= data[i] << (16 >>> shift & 24);\n    if (shift === 2) {\n      encoded += String.fromCharCode(\n          encodeBits(v >>> 18 & 63), encodeBits(v >>> 12 & 63), encodeBits(v >>> 6 & 63), encodeBits(v & 63));\n      v = 0;\n    }\n  }\n  if (shift === 0) {\n    encoded += String.fromCharCode(encodeBits(v >>> 18 & 63), encodeBits(v >>> 12 & 63), 61, 61);\n  } else if (shift === 1) {\n    encoded += String.fromCharCode(encodeBits(v >>> 18 & 63), encodeBits(v >>> 12 & 63), encodeBits(v >>> 6 & 63), 61);\n  }\n  return encoded;\n};\n\nexport const findIndexesOfSubString = (inputString: string, searchString: string): number[] => {\n  const matches = [];\n  let i = inputString.indexOf(searchString);\n  while (i !== -1) {\n    matches.push(i);\n    i = inputString.indexOf(searchString, i + searchString.length);\n  }\n  return matches;\n};\n\nexport const findLineEndingIndexes = (inputString: string): number[] => {\n  const endings = findIndexesOfSubString(inputString, '\\n');\n  endings.push(inputString.length);\n  return endings;\n};\n\nexport const isWhitespace = (inputString: string): boolean => {\n  return /^\\s*$/.test(inputString);\n};\n\nexport const trimURL = (url: string, baseURLDomain?: string): string => {\n  let result = url.replace(/^(https|http|file):\\/\\//i, '');\n  if (baseURLDomain) {\n    if (result.toLowerCase().startsWith(baseURLDomain.toLowerCase())) {\n      result = result.substr(baseURLDomain.length);\n    }\n  }\n  return result;\n};\n\nexport const collapseWhitespace = (inputString: string): string => {\n  return inputString.replace(/[\\s\\xA0]+/g, ' ');\n};\n\nexport const reverse = (inputString: string): string => {\n  return inputString.split('').reverse().join('');\n};\n\nexport const replaceControlCharacters = (inputString: string): string => {\n  // Replace C0 and C1 control character sets with replacement character.\n  // Do not replace '\\t', \\n' and '\\r'.\n  return inputString.replace(/[\\0-\\x08\\x0B\\f\\x0E-\\x1F\\x80-\\x9F]/g, '\\uFFFD');\n};\n\nexport const countWtf8Bytes = (inputString: string): number => {\n  let count = 0;\n  for (let i = 0; i < inputString.length; i++) {\n    const c = inputString.charCodeAt(i);\n    if (c <= 0x7F) {\n      count++;\n    } else if (c <= 0x07FF) {\n      count += 2;\n    } else if (c < 0xD800 || 0xDFFF < c) {\n      count += 3;\n    } else {\n      if (c <= 0xDBFF && i + 1 < inputString.length) {\n        // The current character is a leading surrogate, and there is a\n        // next character.\n        const next = inputString.charCodeAt(i + 1);\n        if (0xDC00 <= next && next <= 0xDFFF) {\n          // The next character is a trailing surrogate, meaning this\n          // is a surrogate pair.\n          count += 4;\n          i++;\n          continue;\n        }\n      }\n      count += 3;\n    }\n  }\n  return count;\n};\n\nexport const stripLineBreaks = (inputStr: string): string => {\n  return inputStr.replace(/(\\r)?\\n/g, '');\n};\n\nconst EXTENDED_KEBAB_CASE_REGEXP = /^([a-z0-9]+(?:-[a-z0-9]+)*\\.)*[a-z0-9]+(?:-[a-z0-9]+)*$/;\n\n/**\n * Tests if the `inputStr` is following the extended Kebab Case naming convetion,\n * where words are separated with either a dash (`-`) or a dot (`.`), and all\n * characters must be lower-case alphanumeric.\n *\n * For example, it will yield `true` for `'my.amazing-string.literal'`, but `false`\n * for `'Another.AmazingLiteral'` or '`another_amazing_literal'`.\n *\n * @param inputStr the input string to test.\n * @return `true` if the `inputStr` follows the extended Kebab Case convention.\n */\nexport const isExtendedKebabCase = (inputStr: string): boolean => {\n  return EXTENDED_KEBAB_CASE_REGEXP.test(inputStr);\n};\n\nexport const toTitleCase = (inputStr: string): string => {\n  return inputStr.substring(0, 1).toUpperCase() + inputStr.substring(1);\n};\n\nexport const removeURLFragment = (inputStr: string): string => {\n  const url = new URL(inputStr);\n  url.hash = '';\n  return url.toString();\n};\n\nconst SPECIAL_REGEX_CHARACTERS = '^[]{}()\\\\.^$*+?|-,';\n\nexport const regexSpecialCharacters = function(): string {\n  return SPECIAL_REGEX_CHARACTERS;\n};\n\nexport const filterRegex = function(query: string): RegExp {\n  let regexString = '^(?:.*\\\\0)?';  // Start from beginning or after a \\0\n  for (let i = 0; i < query.length; ++i) {\n    let c = query.charAt(i);\n    if (SPECIAL_REGEX_CHARACTERS.indexOf(c) !== -1) {\n      c = '\\\\' + c;\n    }\n    regexString += '[^\\\\0' + c + ']*' + c;\n  }\n  return new RegExp(regexString, 'i');\n};\n\nexport const createSearchRegex = function(\n    query: string, caseSensitive: boolean, isRegex: boolean, matchWholeWord: boolean = false): RegExp {\n  const regexFlags = caseSensitive ? 'g' : 'gi';\n  let regexObject;\n\n  if (isRegex) {\n    try {\n      regexObject = new RegExp(query, regexFlags);\n    } catch (e) {\n      // Silent catch.\n    }\n  }\n\n  if (!regexObject) {\n    regexObject = createPlainTextSearchRegex(query, regexFlags);\n  }\n\n  if (matchWholeWord && regexObject) {\n    regexObject = new RegExp(`\\\\b${regexObject.source}\\\\b`, regexFlags);\n  }\n\n  return regexObject;\n};\n\nexport const caseInsensetiveComparator = function(a: string, b: string): number {\n  a = a.toUpperCase();\n  b = b.toUpperCase();\n  if (a === b) {\n    return 0;\n  }\n  return a > b ? 1 : -1;\n};\n\nexport const hashCode = function(string?: string): number {\n  if (!string) {\n    return 0;\n  }\n  // Hash algorithm for substrings is described in \"Über die Komplexität der Multiplikation in\n  // eingeschränkten Branchingprogrammmodellen\" by Woelfe.\n  // http://opendatastructures.org/versions/edition-0.1d/ods-java/node33.html#SECTION00832000000000000000\n  const p = ((1 << 30) * 4 - 5);  // prime: 2^32 - 5\n  const z = 0x5033d967;           // 32 bits from random.org\n  const z2 = 0x59d2f15d;          // random odd 32 bit number\n  let s = 0;\n  let zi = 1;\n  for (let i = 0; i < string.length; i++) {\n    const xi = string.charCodeAt(i) * z2;\n    s = (s + zi * xi) % p;\n    zi = (zi * z) % p;\n  }\n  s = (s + zi * (p - 1)) % p;\n  return Math.abs(s | 0);\n};\n\nexport const compare = (a: string, b: string): number => {\n  if (a > b) {\n    return 1;\n  }\n  if (a < b) {\n    return -1;\n  }\n  return 0;\n};\n\nexport const trimMiddle = (str: string, maxLength: number): string => {\n  if (str.length <= maxLength) {\n    return String(str);\n  }\n  let leftHalf = maxLength >> 1;\n  let rightHalf = maxLength - leftHalf - 1;\n  if ((str.codePointAt(str.length - rightHalf - 1) as number) >= 0x10000) {\n    --rightHalf;\n    ++leftHalf;\n  }\n  if (leftHalf > 0 && (str.codePointAt(leftHalf - 1) as number) >= 0x10000) {\n    --leftHalf;\n  }\n  return str.substr(0, leftHalf) + '…' + str.substr(str.length - rightHalf, rightHalf);\n};\n\nexport const trimEndWithMaxLength = (str: string, maxLength: number): string => {\n  if (str.length <= maxLength) {\n    return String(str);\n  }\n  return str.substr(0, maxLength - 1) + '…';\n};\n\nexport const escapeForRegExp = (str: string): string => {\n  return escapeCharacters(str, SPECIAL_REGEX_CHARACTERS);\n};\n\nexport const naturalOrderComparator = (a: string, b: string): number => {\n  const chunk = /^\\d+|^\\D+/;\n  let chunka, chunkb, anum, bnum;\n  while (true) {\n    if (a) {\n      if (!b) {\n        return 1;\n      }\n    } else {\n      if (b) {\n        return -1;\n      }\n      return 0;\n    }\n    chunka = (a.match(chunk) as string[])[0];\n    chunkb = (b.match(chunk) as string[])[0];\n    anum = !Number.isNaN(Number(chunka));\n    bnum = !Number.isNaN(Number(chunkb));\n    if (anum && !bnum) {\n      return -1;\n    }\n    if (bnum && !anum) {\n      return 1;\n    }\n    if (anum && bnum) {\n      const diff = Number(chunka) - Number(chunkb);\n      if (diff) {\n        return diff;\n      }\n      if (chunka.length !== chunkb.length) {\n        if (!Number(chunka) && !Number(chunkb)) {  // chunks are strings of all 0s (special case)\n          return chunka.length - chunkb.length;\n        }\n        return chunkb.length - chunka.length;\n      }\n    } else if (chunka !== chunkb) {\n      return (chunka < chunkb) ? -1 : 1;\n    }\n    a = a.substring(chunka.length);\n    b = b.substring(chunkb.length);\n  }\n};\n\nexport const base64ToSize = function(content: string|null): number {\n  if (!content) {\n    return 0;\n  }\n  let size = content.length * 3 / 4;\n  if (content[content.length - 1] === '=') {\n    size--;\n  }\n  if (content.length > 1 && content[content.length - 2] === '=') {\n    size--;\n  }\n  return size;\n};\n\nexport const SINGLE_QUOTE = '\\'';\nexport const DOUBLE_QUOTE = '\"';\nconst BACKSLASH = '\\\\';\n\nexport const findUnclosedCssQuote = function(str: string): string {\n  let unmatchedQuote = '';\n  for (let i = 0; i < str.length; ++i) {\n    const char = str[i];\n    if (char === BACKSLASH) {\n      i++;\n      continue;\n    }\n    if (char === SINGLE_QUOTE || char === DOUBLE_QUOTE) {\n      if (unmatchedQuote === char) {\n        unmatchedQuote = '';\n      } else if (unmatchedQuote === '') {\n        unmatchedQuote = char;\n      }\n    }\n  }\n  return unmatchedQuote;\n};\n\nexport const countUnmatchedLeftParentheses = (str: string): number => {\n  let unmatchedCount = 0;\n  for (const c of str) {\n    if (c === '(') {\n      unmatchedCount++;\n    } else if (c === ')' && unmatchedCount > 0) {\n      unmatchedCount--;\n    }\n  }\n  return unmatchedCount;\n};\n\nexport const createPlainTextSearchRegex = function(query: string, flags?: string): RegExp {\n  // This should be kept the same as the one in StringUtil.cpp.\n  let regex = '';\n  for (let i = 0; i < query.length; ++i) {\n    const c = query.charAt(i);\n    if (regexSpecialCharacters().indexOf(c) !== -1) {\n      regex += '\\\\';\n    }\n    regex += c;\n  }\n  return new RegExp(regex, flags || '');\n};\n\nclass LowerCaseStringTag {\n  private lowerCaseStringTag: (string|undefined);\n}\n\nexport type LowerCaseString = string&LowerCaseStringTag;\n\nexport const toLowerCaseString = function(input: string): LowerCaseString {\n  return input.toLowerCase() as LowerCaseString;\n};\n\nconst WORD = /[A-Z]{2,}(?=[A-Z0-9][a-z0-9]+|\\b|_)|[A-Za-z][0-9]+[a-z]?|[A-Z]?[a-z]+|[0-9][A-Za-z]+|[A-Z]|[0-9]+|[.]/g;\n//            <---1---><------------2-----------> <---------3--------> <-----4----> <------5-----> <-----6----> <7>\n// 1: two or more consecutive uppercase letters. This is useful for identifying acronyms\n// 2: lookahead assertion that matches a word boundary\n// 3: numeronym: single letter followed by number and another letter\n// 4: word starting with an optional uppercase letter\n// 5: single digit followed by word to handle '3D' or '2px' (this might be controverial)\n// 6: single uppercase letter or number\n// 7: a dot character. We extract it into a separate word and remove dashes around it later.\n//    This is makes more sense conceptually and allows accounting for all possible word variants.\n//    Making dot a part of a word prevent us from handling acronyms or numeronyms after the word\n//    correctly without making the RegExp prohibitively complicated.\n// https://regex101.com/r/FhMVKc/1\nexport const toKebabCase = function(input: string): Lowercase<string> {\n  return (input.match?.(WORD)?.map(w => w.toLowerCase()).join('-').replaceAll('-.-', '.') || input) as\n      Lowercase<string>;\n};\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nexport function toKebabCaseKeys(settingValue: {\n  [x: string]: any,\n}): {[x: string]: any} {\n  const result: {\n    [x: string]: any,\n  } = {};\n  for (const [key, value] of Object.entries(settingValue)) {\n    result[toKebabCase(key)] = value;\n  }\n  return result;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n// Replaces the last ocurrence of parameter `search` with parameter `replacement` in `input`\nexport const replaceLast = function(input: string, search: string, replacement: string): string {\n  const replacementStartIndex = input.lastIndexOf(search);\n  if (replacementStartIndex === -1) {\n    return input;\n  }\n\n  return input.slice(0, replacementStartIndex) + input.slice(replacementStartIndex).replace(search, replacement);\n};\n\nexport const stringifyWithPrecision = function stringifyWithPrecision(s: number, precision = 2): string {\n  if (precision === 0) {\n    return s.toFixed(0);\n  }\n  const string = s.toFixed(precision).replace(/\\.?0*$/, '');\n  return string === '-0' ? '0' : string;\n};\n\n/**\n * Somewhat efficiently concatenates 2 base64 encoded strings.\n */\nexport const concatBase64 = function(lhs: string, rhs: string): string {\n  if (lhs.length === 0 || !lhs.endsWith('=')) {\n    // Empty string or no padding, we can straight-up concatenate.\n    return lhs + rhs;\n  }\n  const lhsLeaveAsIs = lhs.substring(0, lhs.length - 4);\n  const lhsToDecode = lhs.substring(lhs.length - 4);\n  return lhsLeaveAsIs + window.btoa(window.atob(lhsToDecode) + window.atob(rhs));\n};\n"]}