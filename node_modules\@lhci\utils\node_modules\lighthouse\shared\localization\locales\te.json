{"core/audits/accessibility/accesskeys.js | description": {"message": "యాక్సెస్ కీలతో యూజర్‌లు పేజీలోని నిర్దిష్ట భాగంపై వేగంగా ఫోకస్ చేయగలరు. సరైన నావిగేషన్ కోసం, ప్రతి యాక్సెస్ కీ తప్పనిసరిగా విభిన్నంగా ఉండాలి. [యాక్సెస్ కీల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "'`[accesskey]`' విలువలు విశిష్ఠమైనవి కావు"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` విలువలు ప్రత్యేకమైనవి"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "ప్రతి ARIA `role`, `aria-*` లక్షణాలకు సంబంధించిన నిర్దిష్ట సబ్‌సెట్‌కు సపోర్ట్ ఇస్తుంది. ఇవి మ్యాచ్ కాకపోతే, `aria-*` లక్షణాలు చెల్లనివి అయిపోతాయి. [ARIA లక్షణాలను, వాటి రోల్స్‌తో ఎలా మ్యాచ్ చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "``[aria-*]`` లక్షణాలు వాటి పాత్రలతో సరిపోలలేదు"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "'`[aria-*]`' లక్షణాలు వాటి పాత్రలతో సరిపోలాలి"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "ఏదైనా ఎలిమెంట్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని సాధారణ పేరుతో బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [కమాండ్ ఎలిమెంట్‌లను మరింత యాక్సెస్ చేయదగినవిగా ఎలా చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link`, `menuitem` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి లేవు."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link`, `menuitem` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "`<body>` డాక్యుమెంట్‌లో `aria-hidden=\"true\"`‌ను సెట్ చేసినప్పుడు స్క్రీన్ రీడర్‌ల లాంటి సహాయక టెక్నాలజీలు స్థిరంగా పని చేయవు. [డాక్యుమెంట్‌లోని కంటెంట్‌ను `aria-hidden` ఎలా ప్రభావితం చేస్తుందో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`<body>` డాక్యుమెంట్‌లో `[aria-hidden=\"true\"]` ఉంది"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` అనేది '`<body>`' డాక్యుమెంట్‌లో లేదు"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "`[aria-hidden=\"true\"]` ఎలిమెంట్‌లోని ఫోకస్ చేయదగిన సబ్-ఎలిమెంట్‌లు, స్క్రీన్ రీడర్‌ల వంటి సహాయక టెక్నాలజీలను ఉపయోగించే యూజర్‌లకు ఆ ఇంటరాక్టివ్ ఎలిమెంట్‌లు అందుబాటులో ఉండకుండా నిరోధిస్తాయి. [ఫోకస్ చేయదగిన ఎలిమెంట్‌లను `aria-hidden` ఎలా ప్రభావితం చేస్తుందో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` మూలకాలలో దృష్టి కేంద్రీకరించదగిన సంక్రమిత అంశాలు ఉన్నాయి"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` మూలకాలలో దృష్టి కేంద్రీకరించదగిన సంక్రమిత అంశాలు లేవు"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "ఏదైనా ఇన్‌పుట్ ఫీల్డ్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని సాధారణ పేరుతో బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [ఇన్‌పుట్ ఫీల్డ్ లేబుల్స్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA ఇన్‌పుట్ ఫీల్డ్‌లకు యాక్సెస్ చేయదగిన పేర్లు లేవు"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA ఇన్‌పుట్ లేబుళ్లు యాక్సెస్ చేయదగిన పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "ఒక మీటర్ ఎలిమెంట్‌కు యాక్సెస్ చేయగల పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దానిని సాధారణ పేరుతో అనౌన్స్ చేస్తాయి, తద్వారా వాటిని స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు నిరుపయోగమైనవిగా చేస్తాయి. [`meter` ఎలిమెంట్స్‌కు ఎలా పేరు పెట్టాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి లేవు."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "ఏదైనా `progressbar` ఎలిమెంట్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని సాధారణ పేరుతో బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [`progressbar` ఎలిమెంట్‌లను ఎలా లేబుల్ చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి లేవు."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "కొన్ని ARIA రోల్స్‌లో, ఎలిమెంట్ స్టేటస్‌ను స్క్రీన్ రీడర్‌లకు వివరించే ఆవశ్యక లక్షణాలు ఉంటాయి. [రోల్స్ గురించి, అలాగే ఆవశ్యక లక్షణాల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "'`[role]`'లలో అవసరమైన అన్ని '`[aria-*]`' లక్షణాలు లేవు"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "'`[role]`'లకు అన్ని అవసరమైన అన్ని '`[aria-*]`' లక్షణాలు ఉన్నాయి"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "ఏ యాక్సెసిబిలిటీ ఫంక్షన్‌లను చేయడానికి అయితే ARIA పేరెంట్ రోల్స్ ఉద్దేశించబడ్డాయో, ఆ ఫంక్షన్‌లను చేయడానికి కొన్ని ARIA పేరెంట్ రోల్స్‌లో తప్పనిసరిగా నిర్దిష్ట చైల్డ్ రోల్స్ ఉండాలి. [రోల్స్ గురించి, అలాగే అవసరమైన చైల్డ్ ఎలిమెంట్‌ల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "నిర్దిష్టమైన '`[role]`'ను కలిగి ఉండాల్సిన, ఉప మూలకాలు అవసరమైన ARIA '`[role]`' మూలకాలలో అటువంటి ఉప మూలకాలన్నీ లేదా వాటిలో కొన్ని లేకపోవడంతో ఈ సమస్య ఏర్పడింది."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "నిర్దిష్టమైన '`[role]`'ను కలిగి ఉండాల్సిన, ఉప మూలకాలు అవసరమైన ARIA `[role]` గల మూలకాలు అవసరమైన అన్ని ఉప మూలకాలను కలిగి ఉన్నాయి."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "ఏ యాక్సెసిబిలిటీ ఫంక్షన్‌లను చేయడానికి అయితే ARIA చైల్డ్ రోల్స్ ఉద్దేశించబడ్డాయో, ఆ ఫంక్షన్‌లను అవి సక్రమంగా చేయడానికి కొన్ని ARIA చైల్డ్ రోల్స్ తప్పనిసరిగా నిర్దిష్ట పేరెంట్ రోల్స్‌లో ఉండాలి. [ARIA రోల్స్ గురించి, అలాగే అవసరమైన పేరెంట్ ఎలిమెంట్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "``[role]``లు వాటి అవసరమైన మూలాధార మూలకంతో లేవు"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "``[role]``లు వాటికి అవసరమైన మూలాధార మూలకాలలో ఉన్నాయి."}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA రోల్స్‌కు, వాటి ఉద్దేశిత యాక్సెసిబిలిటీ ఫంక్షన్‌లను అమలు చేయడానికి తప్పనిసరిగా చెల్లుబాటయ్యే విలువలు ఉండాలి. [చెల్లుబాటయ్యే ARIA రోల్స్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` విలువలు చెల్లుబాటు అయ్యేవి కావు"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` విలువలు చెల్లుబాటు అయ్యేవి"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "ఏదైనా టోగుల్ ఫీల్డ్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని సాధారణ పేరుతో బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [టోగుల్ ఫీల్డ్‌ల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA టోగుల్ ఫీల్డ్‌లకు యాక్సెస్ చేయదగిన పేర్లు లేవు"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA టోగుల్ ఫీల్డ్‌లకు యాక్సెస్ చేయదగిన పేర్లు ఉన్నాయి"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "ఒక టూల్‌టిప్ ఎలిమెంట్‌కు యాక్సెస్ చేయగల పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దానిని సాధారణ పేరుతో అనౌన్స్ చేస్తాయి, తద్వారా వాటిని స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు నిరుపయోగమైనవిగా చేస్తాయి. [`tooltip` ఎలిమెంట్స్‌కు ఎలా పేరు పెట్టాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి లేవు."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "ఏదైనా `treeitem` ఎలిమెంట్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని సాధారణ పేరుతో బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [`treeitem` ఎలిమెంట్‌లకు లేబుల్ చేయడం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి లేవు."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` ఎలిమెంట్‌లు యాక్సెస్ చేయగల పేర్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "స్క్రీన్ రీడర్‌ల లాంటి సహాయక టెక్నాలజీలు, చెల్లని విలువలు గల ARIA లక్షణాలను అర్థం చేసుకోలేవు. [ARIA లక్షణాలకు సంబంధించి చెల్లుబాటయ్యే విలువల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "'`[aria-*]`' లక్షణాలలో చెల్లుబాటయ్యే విలువలు లేవు"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "'`[aria-*]`' లక్షణాలు చెల్లుబాటయ్యే విలువలను కలిగి ఉన్నాయి"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "స్క్రీన్ రీడర్‌ల లాంటి సహాయక టెక్నాలజీలు, చెల్లని పేర్లు గల ARIA లక్షణాలను అర్థం చేసుకోలేవు. [చెల్లుబాటయ్యే ARIA లక్షణాల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "``[aria-*]`` లక్షణాలు చెల్లుబాటు అయ్యేవి కావు లేదా అక్షరదోషాలు ఉన్నాయి"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "``[aria-*]`` లక్షణాలు చెల్లుబాటు అయ్యేవి, అక్షరదోషాలేవీ లేవు"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "విఫలం అవుతున్న మూలకాలు"}, "core/audits/accessibility/button-name.js | description": {"message": "ఏదైనా బటన్‌కు యాక్సెస్ చేయదగిన పేరు లేనప్పుడు, స్క్రీన్ రీడర్‌లు దాన్ని \"బటన్\"గా బయటకు చదువుతాయి, స్క్రీన్ రీడర్‌లపై ఆధారపడే యూజర్‌లకు దీని వల్ల ఉపయోగం ఉండకుండా పోతుంది. [బటన్‌లను మరింత యాక్సెస్ చేయదగినవిగా ఎలా చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "బటన్‌లకు యాక్సెస్‌కి తగిన పేరు లేదు"}, "core/audits/accessibility/button-name.js | title": {"message": "బటన్‌లు యాక్సెస్ చేయదగిన పేరును కలిగి ఉన్నాయి"}, "core/audits/accessibility/bypass.js | description": {"message": "రిపీట్ అయ్యే కంటెంట్‌ను బైపాస్ చేయడానికి మార్గాలను జోడించడం ద్వారా కీబోర్డ్ యూజర్‌లు పేజీని మరింత సమర్థవంతంగా నావిగేట్ చేయగలరు. [బైపాస్ బ్లాక్‌ల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "పేజీలో ముఖ్యశీర్షిక, దాటివేత లింక్ లేదా ల్యాండ్‌మార్క్ ప్రాంతం లేవు"}, "core/audits/accessibility/bypass.js | title": {"message": "పేజీలో ముఖ్య శీర్షిక, దాటివేత లింక్ లేదా ల్యాండ్‌మార్క్ ప్రాంతం ఉన్నాయి"}, "core/audits/accessibility/color-contrast.js | description": {"message": "తక్కువ కాంట్రాస్ట్ గల టెక్స్ట్ అనేది చాలా మంది యూజర్‌లు కష్టపడి చదవాల్సి వచ్చేలా లేదా అస్సలు చదవలేనిదిగా ఉంటుంది. [సరిపడేంత కలర్ కాంట్రాస్ట్‌ను అందించడం ఎలాగో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "నేపథ్యం, ముందువైపు రంగులు తగినంత వర్ణభేద నిష్పత్తితో లేవు"}, "core/audits/accessibility/color-contrast.js | title": {"message": "నేపథ్యం మరియు ముందువైపు రంగులు తగినంత వర్ణభేద నిష్పత్తితో ఉంటున్నాయి"}, "core/audits/accessibility/definition-list.js | description": {"message": "నిర్వచన లిస్ట్‌లను సరిగ్గా గుర్తు పెట్టనప్పుడు, స్క్రీన్ రీడర్‌లు అయోమయానికి గురి చేసే లేదా సరికాని అవుట్‌పుట్‌ను అందించవచ్చు. [నిర్వచన లిస్ట్‌లను సరిగ్గా ఎలా రూపొందించాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`'లలో కేవలం సక్రమంగా ఆర్డర్ చేసిన `<dt>`, `<dd>` గ్రూప్‌లు, `<script>`, `<template>` లేదా `<div>` మూలకాలు మాత్రమే ఉండకూడదు."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`'లలో కేవలం సక్రమంగా ఆర్డర్ చేసిన `<dt>`, `<dd>` గ్రూప్‌లు, `<script>`, `<template>` లేదా `<div>` మూలకాలు ఉన్నాయి."}, "core/audits/accessibility/dlitem.js | description": {"message": "స్క్రీన్ రీడర్‌లు నిర్వచన లిస్ట్ ఐటెమ్‌లను (`<dt>`, `<dd>`) సక్రమంగా చదివి వినిపించగలవని నిర్ధారించుకోవడానికి, వాటిని తప్పనిసరిగా పేరెంట్ `<dl>` ఎలిమెంట్‌లో సర్దుబాటు చేయాలి. [నిర్వచన లిస్ట్‌లను సరిగ్గా ఎలా రూపొందించాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "నిర్వచన లిస్ట్‌ అంశాలు '`<dl>`' మూలకాలలో సర్దుబాటు చేయబడలేదు"}, "core/audits/accessibility/dlitem.js | title": {"message": "నిర్వచన లిస్ట్‌ అంశాలు '`<dl>`' మూలకాలలో సర్దుబాటు చేయబడ్డాయి"}, "core/audits/accessibility/document-title.js | description": {"message": "టైటిల్ అనేది స్క్రీన్ రీడర్ యూజర్‌లకు పేజీ గురించి ఒక ఓవర్‌వ్యూను ఇస్తుంది, సెర్చ్ ఇంజిన్ యూజర్‌లు, ఏదైనా పేజీ వారి సెర్చ్‌కు సంబంధితమైనదో కాదో గుర్తించడానికి దీనిపై చాలా ఎక్కువగా ఆధారపడుతుంటారు. [డాక్యుమెంట్ టైటిల్స్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "డాక్యుమెంట్‌లో '`<title>`' మూలకం లేదు"}, "core/audits/accessibility/document-title.js | title": {"message": "డాక్యుమెంట్‌లో '`<title>`' మూలకం ఉంది"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "ఫోకస్ చేయదగిన ఎలిమెంట్‌లన్నీ సహాయక టెక్నాలజీలకు కనిపించేలా ఉండటానికి, వాటికి తప్పనిసరిగా విభిన్నమైన `id` ఉండాలి. [డూప్లికేట్ `id`‌లను ఎలా సరి చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "యాక్టివ్‌గా ఉన్న, దృష్టి కేంద్రీకరించదగిన మూలకాలలో `[id]` లక్షణాలు విభిన్న రీతిలో లేవు"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "యాక్టివ్‌గా ఉన్న, దృష్టి కేంద్రీకరించదగిన మూలకాలలో `[id]` లక్షణాలు విభిన్న రీతిలో ఉన్నాయి"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "సహాయక టెక్నాలజీల ద్వారా ఇతర సందర్భాలు విస్మరించబడకుండా నిరోధించడానికి ARIA ID విలువ తప్పనిసరిగా విభిన్నంగా ఉండాలి. [డూప్లికేట్ ARIA IDలను ఎలా సరి చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA IDలు విభిన్నమైనవి కావు"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA IDలు విభిన్నంగా ఉన్నాయి"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "మొదటి, చివరి లేదా అన్ని లేబుల్స్‌ను ఉపయోగించే స్క్రీన్ రీడర్‌ల వంటి సహాయక టెక్నాలజీలు, పలు లేబుల్స్ ఉండే ఫారమ్ ఫీల్డ్‌లను గందరగోళంగా బయటకు చదివే అవకాశం ఉంది. [ఫారమ్ లేబుల్స్‌ను ఎలా ఉపయోగించాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "ఫారమ్ ఫీల్డ్‌లు బహుళ లేబుళ్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "ఫారమ్ ఫీల్డ్‌లు వేటికీ బహుళ లేబుళ్లు లేవు"}, "core/audits/accessibility/frame-title.js | description": {"message": "స్క్రీన్ రీడర్ యూజర్‌లు, ఫ్రేమ్‌ల కంటెంట్‌లను వివరించడానికి ఫ్రేమ్ టైటిల్స్‌పై ఆధారపడతారు. [ఫ్రేమ్ టైటిల్స్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "'`<frame>`' లేదా '`<iframe>`' మూలకాలకు పేరు అందించలేదు"}, "core/audits/accessibility/frame-title.js | title": {"message": "'`<frame>`' లేదా '`<iframe>`' మూలకాలలో శీర్షికలు ఉన్నాయి"}, "core/audits/accessibility/heading-order.js | description": {"message": "లెవెల్స్‌ను స్కిప్ చేయకుండా సరైన క్రమంలో అమర్చబడిన హెడింగ్‌లు, పేజీ నిర్మాణం సరైన విధంగా ఉందని చూపుతాయి, దీని వలన సహాయక టెక్నాలజీలను ఉపయోగిస్తున్నప్పుడు నావిగేట్ చేయడం, ఇంకా అర్థం చేసుకోవడం సులభం అవుతుంది. [హెడింగ్ క్రమం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "హెడింగ్ మూలకాలు శ్రేణీకృతంగా అవరోహణ క్రమంలో లేవు"}, "core/audits/accessibility/heading-order.js | title": {"message": "హెడింగ్ మూలకాలు శ్రేణీకృతంగా అవరోహణ క్రమంలో కనిపిస్తాయి"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "ఏదైనా పేజీ `lang` లక్షణాన్ని పేర్కొనకుంటే, స్క్రీన్ రీడర్‌ను సెటప్ చేసేటప్పుడు యూజర్ ఆటోమేటిక్‌గా ఏ భాషను అయితే ఎంచుకుంటారో, ఆ భాషలోనే పేజీ ఉందని స్క్రీన్ రీడర్ భావిస్తుంది. ఒకవేళ ఆ పేజీ ఆటోమేటిక్‌గా సెట్ చేసిన భాషలో లేకపోతే, ఆ పేజీలోని టెక్స్ట్‌ను స్క్రీన్ రీడర్ సరిగ్గా చదివి వినిపించలేకపోవచ్చు. [`lang` లక్షణం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "'`<html>`' మూలకంలో '`[lang]`' మూలకం లేదు"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "'`<html>`' మూలకంలో `[lang]` లక్షణం ఉంది"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "చెల్లుబాటయ్యే [BCP 47 భాషను](https://www.w3.org/International/questions/qa-choosing-language-tags#question) పేర్కొనడం అనేది, టెక్స్ట్‌ను సక్రమంగా చదివి వినిపించడంలో స్క్రీన్ రీడర్‌లకు సహాయపడుతుంది. [`lang` లక్షణాన్ని ఎలా ఉపయోగించాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "'`<html>`' మూలకంలో దాని '`[lang]`' లక్షణం కోసం చెల్లుబాటయ్యే విలువ లేదు."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "'`<html>`' మూలకంలో దాని '`[lang]`' లక్షణానికి చెల్లుబాటయ్యే విలువ ఉంది"}, "core/audits/accessibility/image-alt.js | description": {"message": "సమాచారాత్మక ఎలిమెంట్‌లు చిన్నగా, అలాగే వివరణాత్మక ప్రత్యామ్నాయ టెక్స్ట్‌ను కలిగి ఉండాలి. అలంకార ఎలిమెంట్‌లను ఖాళీ alt లక్షణంతో విస్మరించవచ్చు. [`alt` లక్షణం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "చిత్రం మూలకాలలో '`[alt]`' లక్షణాలు ఏవీ లేవు"}, "core/audits/accessibility/image-alt.js | title": {"message": "చిత్ర మూలకాలు '`[alt]`' లక్షణాలను కలిగి ఉన్నాయి"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "ఒక ఇమేజ్ `<input>` బటన్‌గా ఉపయోగించబడుతున్నప్పుడు, ప్రత్యామ్నాయ టెక్స్ట్‌ను అందించడమనేది బటన్ ప్రయోజనాన్ని అర్థం చేసుకోవడంలో స్క్రీన్ రీడర్ యూజర్‌లకు సహాయపడగలదు. [ఇన్‌పుట్ ఇమేజ్ ప్రత్యామ్నాయ టెక్స్ట్ గురించి తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` ఎలిమెంట్‌లలో `[alt]` టెక్స్ట్ లేదు"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` ఎలిమెంట్‌లు `[alt]` టెక్స్ట్‌ను కలిగి ఉన్నాయి"}, "core/audits/accessibility/label.js | description": {"message": "స్క్రీన్ రీడర్‌ల లాంటి సహాయక టెక్నాలజీలు, ఫారమ్ కంట్రోల్స్‌ను సక్రమంగా చదివి వినిపించేలా లేబుల్స్ చూసుకుంటాయి. [ఫారమ్ ఎలిమెంట్ లేబుల్స్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "ఫారమ్ మూలకాలలో అనుబంధిత లేబుళ్లు లేవు"}, "core/audits/accessibility/label.js | title": {"message": "ఫారమ్ మూలకాలు అనుబంధిత లేబుళ్లను కలిగి ఉన్నాయి"}, "core/audits/accessibility/link-name.js | description": {"message": "గుర్తించదగిన, విభిన్నమైన, ఇంకా ఫోకస్ చేయదగిన లింక్ టెక్స్ట్ (అలాగే ఇమేజ్‌లను లింక్‌లుగా ఉపయోగించినప్పుడు వాటి ప్రత్యామ్నాయ టెక్స్ట్) సహాయంతో స్క్రీన్ రీడర్ యూజర్‌లకు నావిగేషన్ అనుభవం మెరుగవుతుంది. [లింక్‌లను యాక్సెస్ చేయదగినవిగా ఎలా చేయాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "లింక్‌లలో కనుగొనదగిన పేరు లేదు"}, "core/audits/accessibility/link-name.js | title": {"message": "లింక్‌లలో కనుగొనదగిన పేరు ఉంది"}, "core/audits/accessibility/list.js | description": {"message": "లిస్ట్‌లను స్క్రీన్ రీడర్‌లు ఒక నిర్దిష్ట రకమైన రీతిలో ప్రకటిస్తాయి. లిస్ట్ నిర్మాణక్రమం సక్రమంగా ఉండేలా చూసుకుంటే, స్క్రీన్ రీడర్ అవుట్‌పుట్ బాగుంటుంది. [సరైన లిస్ట్ నిర్మాణక్రమం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "లిస్ట్‌లు కేవలం '`<li>`' మూలకాలు, స్క్రిప్ట్ మద్దతు మూలకాలు ('`<script>`', '`<template>`')తో ఉండకూడదు."}, "core/audits/accessibility/list.js | title": {"message": "లిస్ట్‌లలో కేవలం '`<li>`' మూలకాలు, స్క్రిప్ట్ మద్దతు మూలకాలు (`<script>`, `<template>`) మాత్రమే ఉన్నాయి."}, "core/audits/accessibility/listitem.js | description": {"message": "లిస్ట్ ఐటెమ్‌లను (`<li>`) స్క్రీన్ రీడర్‌లు సక్రమంగా చదివి వినిపించాలంటే, వాటిని పేరెంట్ `<ul>`, `<ol>` లేదా `<menu>`‌లో ఉంచాలి. [సరైన లిస్ట్ నిర్మాణక్రమం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "లిస్ట్ ఐటెమ్‌లు (`<li>`) అనేవి `<ul>`, `<ol>` లేదా `<menu>` పేరెంట్ ఎలిమెంట్‌లలో లేవు."}, "core/audits/accessibility/listitem.js | title": {"message": "లిస్ట్ ఐటెమ్‌లు (`<li>`) అనేవి `<ul>`, `<ol>` లేదా `<menu>` పేరెంట్ ఎలిమెంట్‌లలో ఉన్నాయి"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "యూజర్‌లు పేజీ ఆటోమేటిక్‌గా రిఫ్రెష్ కావాలని కోరుకోరు, అలా చేయడం వలన ఫోకస్ తిరిగి పేజీ పైభాగానికి వెళ్తుంది. ఇది విసుగు తెప్పించవచ్చు లేదా అయోమయానికి గురి చేయవచ్చు. [రిఫ్రెష్ మెటా ట్యాగ్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "డాక్యుమెంట్‌ '`<meta http-equiv=\"refresh\">`'ను వినియోగిస్తోంది"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "డాక్యుమెంట్‌లో '`<meta http-equiv=\"refresh\">`'ను వినియోగించలేదు"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "జూమ్ చేయగల సామర్థ్యాన్ని డిజేబుల్ చేస్తే, స్క్రీన్ మ్యాగ్నిఫికేషన్‌పై ఆధారపడే తక్కువ కంటిచూపు ఉన్న యూజర్‌లు వెబ్ పేజీ కంటెంట్‌లను సరిగ్గా చూడలేరు. [వ్యూపోర్ట్ మెటా ట్యాగ్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "'`[user-scalable=\"no\"]`' అన్నది '`<meta name=\"viewport\">`' మూలకంలో ఉపయోగించబడింది, అలాగే '`[maximum-scale]`' లక్షణం 5 కంటే తక్కువ ఉంది."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "'`[user-scalable=\"no\"]`' అన్నది '`<meta name=\"viewport\">`' మూలకంలో ఉపయోగించలేదు, అలాగే '`[maximum-scale]`' లక్షణం 5 కంటే తక్కువగా లేదు."}, "core/audits/accessibility/object-alt.js | description": {"message": "స్క్రీన్ రీడర్‌లు టెక్స్ట్ కాని కంటెంట్‌ను అనువదించలేవు. `<object>` ఎలిమెంట్‌లకు ప్రత్యామ్నాయ టెక్స్ట్‌ను జోడించడం వలన స్క్రీన్ రీడర్‌లు వాటి అర్థాన్ని యూజర్‌లకు సరిగ్గా అందించగలుగుతాయి. [`object` ఎలిమెంట్‌లకు సంబంధించిన ప్రత్యామ్నాయ టెక్స్ట్ గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` ఎలిమెంట్‌లలో ప్రత్యామ్నాయ టెక్స్ట్ లేదు"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` ఎలిమెంట్‌లు ప్రత్యామ్నాయ టెక్స్ట్‌ను కలిగి ఉన్నాయి"}, "core/audits/accessibility/tabindex.js | description": {"message": "0 కంటే పెద్ద విలువ, స్పష్టమైన నావిగేషన్ క్రమాన్ని సూచిస్తుంది. టెక్నికల్‌గా చెల్లుబాటే అయినప్పటికీ, సహాయక టెక్నాలజీలపై ఆధారపడే యూజర్‌లకు ఇది తరచుగా విసుగు తెప్పిస్తూ ఉంటుంది. [`tabindex` లక్షణం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "కొన్ని మూలకాలు 0 కంటే పెద్దవైన ``[tabindex]`` విలువను కలిగి ఉన్నాయి"}, "core/audits/accessibility/tabindex.js | title": {"message": "ఏ మూలకానికీ సున్నా కంటే పెద్ద ``[tabindex]`` విలువ లేదు"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "టేబుల్స్‌ను నావిగేట్ చేయడం సులభతరం చేసే ఫీచర్‌లు స్క్రీన్ రీడర్‌లలో ఉంటాయి. `[headers]` లక్షణాన్ని ఉపయోగించి `<td>` సెల్స్ కేవలం అదే టేబుల్‌లోని ఇతర సెల్స్‌ను రెఫర్ చేసేలా చూసుకుంటే, స్క్రీన్ రీడర్ యూజర్‌ల అనుభవం మెరుగవ్వవచ్చు. [`headers` లక్షణం గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "'`<table>`' మూలకంలో '`[headers]`' లక్షణాన్ని ఉపయోగించే సెల్స్‌ అదే పట్టికలో కనుగొనబడని '`id`' మూలకాన్ని సూచిస్తున్నాయి."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "'`<table>`' మూలకంలో '`[headers]`' లక్షణాన్ని ఉపయోగించే సెల్స్‌ అదే పట్టికలోని పట్టిక సెల్స్‌ను సూచిస్తున్నాయి."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "టేబుల్స్‌ను నావిగేట్ చేయడం సులభతరం చేసే ఫీచర్‌లు స్క్రీన్ రీడర్‌లలో ఉంటాయి. టేబుల్ హెడర్‌లు ఎల్లప్పుడూ కొన్ని సెల్స్ సెట్‌ను రెఫర్ చేసేలా చూసుకుంటే, స్క్రీన్ రీడర్ యూజర్‌ల అనుభవం మెరుగవ్వవచ్చు. [టేబుల్ హెడర్‌ల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "'`<th>`' మూలకాలలో, అలాగే '`[role=\"columnheader\"/\"rowheader\"]`' కలిగి ఉండే మూలకాలలో అవి వివరిస్తున్న డేటా సెల్స్‌ లేవు."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "'`<th>`' మూలకాలు, '`[role=\"columnheader\"/\"rowheader\"]`' కలిగి ఉన్న మూలకాలలో అవి వివరిస్తున్న డేటా సెల్స్‌ ఉన్నాయి."}, "core/audits/accessibility/valid-lang.js | description": {"message": "ఎలిమెంట్‌లలో చెల్లుబాటయ్యే [BCP 47 భాషను](https://www.w3.org/International/questions/qa-choosing-language-tags#question) పేర్కొనడం అనేది, టెక్స్‌ను స్క్రీన్ రీడర్ సరైన పద్ధతిలో ఉచ్చరించగలదని నిర్ధారించడంలో సహాయపడుతుంది. [`lang` లక్షణాన్ని ఎలా ఉపయోగించాలో తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "``[lang]`` లక్షణాలలో చెల్లుబాటు అయ్యే విలువ లేదు"}, "core/audits/accessibility/valid-lang.js | title": {"message": "'`[lang]`' లక్షణాలు చెల్లుబాటయ్యే విలువను కలిగి ఉన్నాయి"}, "core/audits/accessibility/video-caption.js | description": {"message": "వీడియోలో క్యాప్షన్‌ను అందిస్తే, చెవిటి, ఇంకా వినికిడి సమస్య ఉన్న యూజర్‌లు వీడియోలోని సమాచారాన్ని సులభంగా యాక్సెస్ చేయగలుగుతారు. [వీడియో క్యాప్షన్‌ల గురించి మరింత తెలుసుకోండి](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` ఎలిమెంట్‌లు `[kind=\"captions\"]`తో ఉన్న `<track>` ఎలిమెంట్‌ను కలిగి లేవు."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` ఎలిమెంట్‌లు `[kind=\"captions\"]`తో ఉన్న `<track>` ఎలిమెంట్‌ను కలిగి ఉన్నాయి"}, "core/audits/autocomplete.js | columnCurrent": {"message": "ప్రస్తుత విలువ"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "సూచించబడిన టోకెన్"}, "core/audits/autocomplete.js | description": {"message": "ఫారమ్‌లను వేగంగా సమర్పించడంలో `autocomplete`, యూజర్‌లకు సహాయపడుతుంది. యూజర్ శ్రమను తగ్గించడానికి, `autocomplete` లక్షణాన్ని చెల్లుబాటు అయ్యే విలువకు సెట్ చేయడం ద్వారా ఎనేబుల్ చేయడాన్ని పరిశీలించండి. [ఫారమ్‌లలో `autocomplete` గురించి మరింత తెలుసుకోండి](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` ఎలిమెంట్‌లకు సరైన `autocomplete` లక్షణాలు లేవు"}, "core/audits/autocomplete.js | manualReview": {"message": "దీనికి మాన్యువల్ రివ్యూ అవసరం"}, "core/audits/autocomplete.js | reviewOrder": {"message": "టోకెన్‌ల ఆర్డర్‌ను రివ్యూ చేయండి"}, "core/audits/autocomplete.js | title": {"message": "`<input>` ఎలిమెంట్‌లు `autocomplete`ను సరిగ్గా ఉపయోగిస్తాయి"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` టోకెన్(లు): {snippet}లో \"{token}\" చెల్లదు"}, "core/audits/autocomplete.js | warningOrder": {"message": "టోకెన్‌ల ఆర్డర్‌ను రివ్యూ చేయండి: \"{tokens}\"ను {snippet}లో"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "చర్య తీసుకోదగినవి"}, "core/audits/bf-cache.js | description": {"message": "చాలా నావిగేషన్‌లు మునుపటి పేజీకి, లేదా తర్వాతి పేజీలకు తిరిగి వెళ్లడం ద్వారా రన్ చేయబడతాయి. వెనుకకు/ముందుకు కాష్ (bfcache) ఈ రిటర్న్ నావిగేషన్‌లను వేగవంతం చేయగలవు. [bfcache గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 వైఫల్య కారణం}other{# వైఫల్య కారణాలు}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "వైఫల్యానికి కారణం"}, "core/audits/bf-cache.js | failureTitle": {"message": "వెనుకకు/ముందుకు కాష్‌ను రీస్టోర్ అవ్వకుండా పేజీ నిరోధించింది"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "వైఫల్యం రకం"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "చర్య తీసుకోవడం సాధ్యం కాదు"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "బ్రౌజర్ సపోర్ట్ పెండింగ్‌లో ఉంది"}, "core/audits/bf-cache.js | title": {"message": "వెనుకకు/ముందుకు కాష్‌ను రీస్టోర్ అవ్వకుండా పేజీ నిరోధించలేదు"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome ఎక్స్‌టెన్షన్‌లు ఈ పేజీ లోడ్ పనితీరును ప్రతికూలంగా ప్రభావితం చేసాయి. ఎక్స్టెన్షన్‌లు లేకుండా పేజీని అజ్ఞాత మోడ్‌లో లేదా ఎక్స్‌టెన్షన్‌లు లేని Chrome ప్రొఫైల్‌లో ఆడిట్ చేయడాన్ని ప్రయత్నించండి."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "స్క్రిప్ట్ మూల్యనిర్ధారణ"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "స్క్రిప్ట్ అన్వయింపు"}, "core/audits/bootup-time.js | columnTotal": {"message": "మొత్తం CPU సమయం"}, "core/audits/bootup-time.js | description": {"message": "JSను పార్స్ చేయడం, కంపైల్ చేయడం, ఎగ్జిక్యూట్ చేయడం కోసం వెచ్చించే సమయాన్ని తగ్గించడాన్ని పరిశీలించండి. దీని ద్వారా చిన్న చిన్న JS పేలోడ్‌లను పంపడం మీకు సహాయకరంగా అనిపించవచ్చు. [JavaScript ఎగ్జిక్యూట్ అయ్యే సమయాన్ని తగ్గించడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "JavaScript అమలు సమయాన్ని తగ్గించండి"}, "core/audits/bootup-time.js | title": {"message": "JavaScript అమలు సమయం"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "నెట్‌వర్క్ యాక్టివిటీలో అనవసరమైన బైట్‌ల వినియోగం తగ్గించడానికి బండిల్స్ నుండి పెద్దగా ఉండే, డూప్లికేట్ JavaScript మాడ్యూల్స్‌ను తీసివేయండి. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "JavaScript బండిల్స్‌లోని డూప్లికేట్‌ మాడ్యూల్‌లను తీసివేయండి"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "యానిమేట్ చేయబడిన కంటెంట్‌ను డెలివరీ చేయడంలో పెద్ద GIFలు సమర్థవంతంగా పని చేయవు. నెట్‌వర్క్ బైట్‌లను పొదుపు చేయడానికి, GIFకు బదులుగా యానిమేషన్‌ల కోసం MPEG4/WebM వీడియోలను, స్టాటిక్ ఇమేజ్‌ల కోసం PNG/WebPను ఉపయోగించడాన్ని పరిశీలించండి. [సమర్థవంతమైన వీడియో ఫార్మాట్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "యానిమేటెడ్ కంటెంట్ కోసం వీడియో ఫార్మాట్‌లను ఉపయోగించండి"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "పాలీఫిల్స్, ఇంకా ట్రాన్స్‌ఫామ్‌లు, కొత్త JavaScript ఫీచర్‌లను ఉపయోగించగలిగేలా లెగసీ బ్రౌజర్‌లను ఎనేబుల్ చేస్తాయి. అయితే, ఆధునిక బ్రౌజర్‌లకు వాటిలో చాలా వరకు అవసరం ఉండదు. బండిల్ చేయబడిన మీ JavaScript కోసం, లెగసీ బ్రౌజర్‌లకు సపోర్ట్‌ను కొనసాగిస్తూనే, ఆధునిక బ్రౌజర్‌లకు పంపబడే కోడ్ సైజును తగ్గించడానికి మాడ్యూల్/నోమాడ్యూల్ ఫీచర్ గుర్తింపును ఉపయోగించి ఆధునిక స్క్రిప్ట్ అమలు వ్యూహాన్ని అనుసరించండి. [ఆధునిక JavaScriptను ఉపయోగించడం ఎలాగో తెలుసుకోండి](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "లెగసీ JavaScriptను మోడ్రన్ బ్రౌజర్‌లకు అందించడం మానివేయండి"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "PNG లేదా JPEG ఫార్మాట్‌లతో పోలిస్తే WebP, ఇంకా AVIF వంటి ఇమేజ్ ఫార్మాట్‌లు సాధారణంగా మెరుగైన కుదింపును అందిస్తాయి, అంటే డౌన్‌లోడ్‌లు వేగంగా అవుతాయి, అలాగే డేటా వినియోగం తక్కువగా ఉంటుందని అర్థం. [ఆధునిక ఇమేజ్ ఫార్మాట్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "చిత్రాలను తర్వాతి-తరం ఫార్మాట్‌లలో అందించండి"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "ఇంటరాక్షన్ టైమ్‌ను తగ్గించడానికి, అతి ముఖ్యమైన రిసోర్స్‌లన్నీ లోడ్ అవ్వడం పూర్తి అయిన తర్వాతే ఆఫ్‌స్క్రీన్, అలాగే దాచబడి ఉన్న ఇమేజ్‌లను ప్రాధాన్యతను బట్టి లోడ్ చెయడాన్ని పరిశీలించండి. [ఆఫ్‌స్క్రీన్ ఇమేజ్‌ల లోడింగ్‌ను వాయిదా వేయడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "ఆఫ్‌స్క్రీన్ చిత్రాలను వాయిదా వేయండి"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "రిసోర్స్‌లు మీ పేజీలోని ఫస్ట్ పెయింట్‌ను బ్లాక్ చేస్తున్నాయి. అతి ముఖ్యమైన JS/CSSను ఇన్‌లైన్‌లో అందించడం, ముఖ్యం కానటువంటి అన్ని JS/స్టయిల్స్‌ను వాయిదా వేయడాన్ని పరిశీలించండి. [నెమ్మదైన రెండరింగ్‌కు కారణమయ్యే రిసోర్స్‌లను ఎలా తొలగించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "రెండర్-బ్లాకింగ్ వనరులను నివారించండి"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "పెద్ద నెట్‌వర్క్ పేలోడ్‌ల వల్ల యూజర్‌లకు చాలా ఖర్చు అవుతుంది, అలాగే వాటి వల్ల లోడ్ అవ్వడానికి ఎక్కువ సమయం పడుతుంది. [పేలోడ్ సైజులను ఎలా తగ్గించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "మొత్తం సైజ్‌ {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "అతి పెద్ద నెట్‌వర్క్ పేలోడ్‌లను నివారించండి"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "అతి పెద్ద నెట్‌వర్క్ పేలోడ్‌లను నివారిస్తుంది"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS ఫైల్స్ సైజును తగ్గించడం వలన నెట్‌వర్క్ పేలోడ్ సైజులు తగ్గిపోగలవు. [CSS సైజును ఎలా తగ్గించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSSని చిన్నదిగా చేయండి"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript ఫైల్స్ సైజును తగ్గించడం వల్ల, పేలోడ్ సైజులు తగ్గుతాయి, అలాగే స్క్రిప్ట్‌ను పార్స్ చేయడానికి పట్టే సమయం కూడా తగ్గుతుంది. [JavaScript సైజును తగ్గించడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScriptను చిన్నదిగా చేయండి"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "నెట్‌వర్క్ యాక్టివిటీలో ఉపయోగించబడే బైట్‌లను తగ్గించడం కోసం, స్టయిల్‌షీట్‌ల నుండి ఉపయోగించబడని నియమాలను తగ్గించండి, అలాగే ఫోల్డ్‌కు ఎగువున ఉన్న కంటెంట్‌కు ఉపయోగించబడని CSSను మినహాయించండి. [ఉపయోగించబడని CSSను తగ్గించడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "ఉపయోగించని CSS తగ్గించండి"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "ఉపయోగించని JavaScriptను తగ్గించండి, అలాగే నెట్‌వర్క్ యాక్టివిటీ వినియోగించే బైట్‌లను తగ్గించడానికి స్క్రిప్ట్‌లు అవసరం అయ్యేంత వరకు, వాటిని లోడ్ చేయడం వాయిదా వేయండి. [ఉపయోగించని JavaScriptను తగ్గించడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "ఉపయోగించని JavaScriptను తగ్గించండి"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "కాష్ జీవిత కాలం ఎక్కువ ఉంటే, మీ పేజీకి రిపీట్‌గా వచ్చే సందర్శనల సంఖ్యలో వేగం పుంజుకుంటుంది. [సమర్థవంతమైన కాష్ పాలసీల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 వనరు కనుగొనబడింది}other{ # వనరులు కనుగొనబడ్డాయి}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "నిశ్చల ఆస్తులను సమర్ధవంతమైన కాష్ విధానంతో అందించండి"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "నిశ్చలమైన ఆస్తులపై సమర్ధవంతమైన కాష్ విధానాన్ని ఉపయోగిస్తుంది"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "ఆప్టిమైజ్ చేయబడిన ఇమేజ్‌లు వేగంగా లోడ్ అవుతాయి, అలాగే తక్కువ సెల్యులార్ డేటాను ఉపయోగిస్తాయి. [ఇమేజ్‌లను సమర్థవంతంగా ఎన్‌కోడ్ చేయడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "చిత్రాలను సమర్థవంతంగా ఎన్‌కోడ్ చేయండి"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "అసలు కొలతలు"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "ప్రదర్శించబడే కొలతలు"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "ఇమేజ్‌లు వాటి కనిపించే సైజ్ కన్నా పెద్దవిగా ఉన్నాయి"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "ఇమేజ్‌లు వాటి కనిపించే సైజ్‌కు తగినట్టుగా ఉన్నాయి"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "సెల్యులార్ డేటాను పొదుపు చేయడానికి, అలాగే లోడ్ అయ్యే సమయాన్ని మెరుగుపరచడానికి, సముచిత సైజులో ఉన్న ఇమేజ్‌లను అందించండి. [ఇమేజ్‌ల సైజును ఎలా సర్దుబాటు చేయాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "చిత్రాల పరిమాణాన్ని సరిగ్గా మార్చండి"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "మొత్తం నెట్‌వర్క్ బైట్‌లను వీలైనంతగా తగ్గించడానికి టెక్స్ట్-ఆధారిత రిసోర్స్‌లు ఖచ్చితంగా కుదింపు (gzip, deflate లేదా brotli) చేసి అందించబడాలి. [టెక్స్ట్ కుదింపు గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "వచనం కుదింపును ప్రారంభించండి"}, "core/audits/content-width.js | description": {"message": "ఒకవేళ వీక్షణ పోర్ట్ వెడల్పుతో మీ యాప్ కంటెంట్ వెడల్పు మ్యాచ్ అవ్వకపోతే, మొబైల్ స్క్రీన్‌లకు అనుగుణంగా మీ యాప్‌ను ఆప్టిమైజ్ చేయడం సాధ్యపడకపోవచ్చు. [వీక్షణ పోర్ట్ కోసం కంటెంట్ సైజును సర్దుబాటు ఎలా చేయాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "{outerWidth}px విండో సైజ్‌తో {innerWidth}px వీక్షణ పోర్ట్ సైజ్ సరిపోలలేదు."}, "core/audits/content-width.js | failureTitle": {"message": "వీక్షణ పోర్ట్‌కు తగినట్లుగా కంటెంట్ సైజ్ సర్దుబాటు చేయబడలేదు"}, "core/audits/content-width.js | title": {"message": "వీక్షణ పోర్ట్‌కు తగినట్లుగా కంటెంట్ సైజ్ సర్దుబాటు చేయబడింది"}, "core/audits/critical-request-chains.js | description": {"message": "కింద పేర్కొన్న అతి ముఖ్యమైన రిక్వెస్ట్ చెయిన్‌లు ఏ రిసోర్స్‌లు అధిక ప్రాధాన్యతతో లోడ్ అయ్యాయో మీకు చూపిస్తాయి. పేజీ లోడ్‌ను మెరుగుపరచడానికి చెయిన్‌ల పొడవును తగ్గించడం, రిసోర్స్‌ల డౌన్‌లోడ్ సైజును తగ్గించడం, లేదా అనవసరమైన రిసోర్స్‌లను డౌన్‌లోడ్ చేయడాన్ని వాయిదా వేయడం వంటివి పరిశీలించండి. [అతి ముఖ్యమైన రిక్వెస్ట్‌ల చైనింగ్‌ను ఎలా నివారించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 గొలుసు కనుగొనబడింది}other{# గొలుసులు కనుగొనబడ్డాయి}}"}, "core/audits/critical-request-chains.js | title": {"message": "అత్యంత ముఖ్యమైన రిక్వెస్ట్‌లను గొలుసు క్రమంలో అందించడం నివారించండి"}, "core/audits/csp-xss.js | columnDirective": {"message": "డైరెక్టివ్"}, "core/audits/csp-xss.js | columnSeverity": {"message": "తీవ్రత"}, "core/audits/csp-xss.js | description": {"message": "బలమైన కంటెంట్ సెక్యూరిటీ పాలసీ (CSP), క్రాస్-సైట్ స్క్రిప్టింగ్ (XSS) దాడుల రిస్క్‌ను గణనీయంగా తగ్గిస్తుంది. [XSSను నిరోధించడానికి CSPని ఎలా ఉపయోగించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "సింటాక్స్"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "<meta> ట్యాగ్‌లో పేర్కొనబడిన CSPని పేజీ కలిగి ఉంది. CSPని HTTP హెడర్‌కు తరలించడాన్ని లేదా HTTP హెడర్‌లో మరొక ఖచ్చితమైన CSPని పేర్కొనడాన్ని పరిగణించండి."}, "core/audits/csp-xss.js | noCsp": {"message": "ఆంక్ష మోడ్‌లో ఎలాంటి CSP కనుగొనబడలేదు"}, "core/audits/csp-xss.js | title": {"message": "CSP XSS దాడులను సమర్ధవంతంగా ఎదుర్కొంటుందని నిర్ధారించుకోండి"}, "core/audits/deprecations.js | columnDeprecate": {"message": "విస్మరణ / హెచ్చరిక"}, "core/audits/deprecations.js | columnLine": {"message": "పంక్తి"}, "core/audits/deprecations.js | description": {"message": "విస్మరించబడిన APIలు క్రమంగా బ్రౌజర్ నుండి తీసివేయబడతాయి. [విస్మరించబడిన APIల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 హెచ్చరిక కనుగొనబడింది}other{# హెచ్చరికలు కనుగొనబడ్డాయి}}"}, "core/audits/deprecations.js | failureTitle": {"message": "విస్మరించబడిన APIలను వినియోగిస్తోంది"}, "core/audits/deprecations.js | title": {"message": "విస్మరించబడిన APIలను నివారిస్తుంది"}, "core/audits/dobetterweb/charset.js | description": {"message": "అక్షరాల ఎన్‌కోడింగ్ ప్రకటన అవసరం. HTMLలోని మొదటి 1024 బైట్‌లలో లేదా కంటెంట్-రకం HTTP ప్రతిస్పందన హెడర్‌లో `<meta>` ట్యాగ్‌తో దీనిని చేయవచ్చు. [అక్షరాల ఎన్‌కోడింగ్‌ను ప్రకటించడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Charset డిక్లేరేషన్ అందించలేదు లేదా HTMLలో చాలా ఆలస్యంగా వస్తోంది"}, "core/audits/dobetterweb/charset.js | title": {"message": "charsetను సక్రమంగా నిర్వచిస్తుంది"}, "core/audits/dobetterweb/doctype.js | description": {"message": "DOCTYPEను పేర్కొనడం వలన క్విర్క్స్-మోడ్‌కు మారనివ్వకుండా బ్రౌజర్ నిరోధించబడుతుంది. [DOCTYPE ప్రకటన గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE పేరు తప్పనిసరిగా `html` స్ట్రింగ్ అయ్యి ఉండాలి"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "డాక్యుమెంట్‌లో `limited-quirks-mode`‌ను ట్రిగ్గర్ చేసే `doctype` ఉంది"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "డాక్యుమెంట్‌లో తప్పనిసరిగా doctype ఉండాలి"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "'publicId' ఒక ఖాళీ స్ట్రింగ్‌గా వదిలిపెట్టాలి"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "'systemId' ఒక ఖాళీ స్ట్రింగ్‌గా వదిలిపెట్టాలి"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "డాక్యుమెంట్‌లో `quirks-mode`‌ను ట్రిగ్గర్ చేసే `doctype` ఉంది"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "పేజీలో HTML doctype లేదు, కనుక క్విర్క్స్-మోడ్‌ను ట్రిగ్గర్ చేస్తోంది"}, "core/audits/dobetterweb/doctype.js | title": {"message": "పేజీలో 'HTML doctype' ఉంది"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "గణాంక రకం"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "విలువ"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "పెద్ద DOM వలన మెమరీ వినియోగం పెరుగుతుంది, [స్టయిల్ లెక్కింపులు](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ఎక్కువ సమయం తీసుకుంటాయి, [లేఅవుట్ రీఫ్లోలు](https://developers.google.com/speed/articles/reflow) ఖరీదైనవి అవుతాయి. [అధిక DOM సైజును ఎలా నివారించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 మూలకం}other{# మూలకాలు}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "అధిక DOM పరిమాణాన్ని నివారించండి"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "DOM గరిష్ట గాఢత్వము"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "మొత్తం DOM మూలకాలు"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "పిల్ల మూలకాల గరిష్ట సంఖ్య"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "అధిక DOM పరిమాణాన్ని నివారిస్తుంది"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "సందర్భం ఏమీ లేకుండా యూజర్‌ల లొకేషన్‌ను రిక్వెస్ట్ చేసే సైట్‌లను యూజర్‌లు నమ్మరు లేదా వారు అయోమయానికి గురి అవుతారు. అందుకు బదులుగా, రిక్వెస్ట్‌ను యూజర్ చర్యకు లింక్ చేయడాన్ని పరిశీలించండి. [భౌగోళిక స్థానం అనుమతి గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "పేజీ లోడ్ సమయంలో భౌగోళిక స్థానం అనుమతిని అభ్యర్థిస్తుంది"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "పేజీ లోడ్ సమయంలో భౌగోళిక స్థానం అనుమతిని రిక్వెస్ట్ చేయడం నివారిస్తుంది"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "సమస్య రకం"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome డెవలపర్ టూల్స్‌లో `Issues` ప్యానెల్‌కు లాగ్ చేసిన సమస్యలు పరిష్కారం కాని సమస్యలను సూచిస్తాయి. నెట్‌వర్క్ రిక్వెస్ట్ వైఫల్యాలు, తగిన మేరకు లేని సెక్యూరిటీ కంట్రోల్‌లు, ఇతర బ్రౌజర్ ఇబ్బందుల వలన అవి ఏర్పడి ఉంటాయి. ప్రతి సమస్య గురించి మరిన్ని వివరాల కోసం Chrome డెవలపర్ టూల్స్‌లోని సమస్యల ప్యానెల్‌ని తెరవండి."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Chrome డెవలపర్ టూల్స్‌లోని `Issues` ప్యానెల్‌లో సమస్యలు లాగ్ అయ్యాయి"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "క్రాస్-ఆరిజిన్ పాలసీ కారణంగా బ్లాక్ అయింది"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "యాడ్‌ల ద్వారా అధిక రిసోర్స్ వినియోగం"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome డెవలపర్ టూల్స్‌లోని `Issues` ప్యానెల్‌లో సమస్యలు లేవు"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "వెర్షన్"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "ఈ పేజీలో గుర్తించబడిన అన్ని ఫ్రంట్-ఎండ్ JavaScript లైబ్రరీలు. [ఈ JavaScript లైబ్రరీ గుర్తింపు డయాగ్నాస్టిక్ ఆడిట్ గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript లైబ్రరీలు గుర్తించబడ్డాయి"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "కనెక్షన్‌లు నెమ్మదిగా పని చేస్తున్న యూజర్‌ల విషయంలో, `document.write()` ద్వారా డైనమిక్‌గా ఇంజెక్ట్ చేయబడే బయటి స్క్రిప్ట్‌ల వలన పేజీ పదుల సెకన్ల పాటు ఆలస్యంగా లోడ్ కాగలదు. [document.write()ను ఎలా నివారించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()`ను ఉపయోగించకండి"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "'`document.write()`'ను నివారిస్తుంది"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "సందర్భం ఏమీ లేకుండా నోటిఫికేషన్‌లను పంపడానికి అనుమతిని రిక్వెస్ట్ చేసే సైట్‌లను యూజర్‌లు నమ్మరు లేదా వారు అయోమయానికి గురి అవుతారు. అందుకు బదులుగా, రిక్వెస్ట్‌ను యూజర్ సంజ్ఞలకు లింక్ చేయడాన్ని పరిశీలించండి. [నోటిఫికేషన్‌ల కోసం బాధ్యతాయుతంగా అనుమతి ఎలా పొందాలి అనే దాని గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "పేజీ లోడ్ సమయంలో నోటిఫికేషన్ అనుమతిని అభ్యర్థిస్తుంది"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "పేజీ లోడ్ సమయంలో నోటిఫికేషన్ అనుమతిని రిక్వెస్ట్ చేయడం నివారిస్తుంది"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "ప్రోటోకాల్"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "బైనరీ హెడర్‌లు, మల్టీప్లెక్సింగ్‌తో సహా HTTP/2, HTTP/1.1 కంటే చాలా ఎక్కువ ప్రయోజనాలను అందిస్తుంది. [HTTP/2 గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 రిక్వెస్ట్‌ 'HTTP/2' ద్వారా అందించబడలేదు}other{# రిక్వెస్ట్‌లు 'HTTP/2' ద్వారా అందించబడలేదు}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2ను ఉపయోగించండి"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "మీ పేజీ స్క్రోలింగ్ పనితీరును మెరుగుపరచడానికి మీ స్పర్శ, చక్రం కదలికలను గుర్తుపట్టే ఈవెంట్ లిజనర్‌లను `passive`‌గా గుర్తించడాన్ని పరిశీలించండి. [పాసివ్ ఈవెంట్ లిజనర్‌లను ఉపయోగించడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "స్క్రోలింగ్ పనితీరును మెరుగుపరచడానికి పాసివ్ లిజనర్‌లను వినియోగించడం లేదు"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "స్క్రోలింగ్ పనితీరును మెరుగుపరచడానికి పాసివ్ లిజనర్‌లను వినియోగిస్తుంది"}, "core/audits/errors-in-console.js | description": {"message": "కన్సోల్‌లో లాగ్ చేయబడే ఎర్రర్‌లు పరిష్కారం కాని సమస్యలను సూచిస్తాయి. నెట్‌వర్క్ రిక్వెస్ట్ వైఫల్యాలు, ఇతర బ్రౌజర్ లోపాల వలన అవి ఏర్పడే అవకాశం ఉంది. [కన్సోల్ డయాగ్నాస్టిక్ ఆడిట్‌లో ఈ ఎర్రర్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "బ్రౌజర్ ఎర్రర్‌లు కన్సోల్‌లో లాగ్ చేయబడ్డాయి"}, "core/audits/errors-in-console.js | title": {"message": "కన్సోల్‌లో లాగ్ చేయబడిన బ్రౌజర్ ఎర్రర్‌లు ఏవీ లేవు"}, "core/audits/font-display.js | description": {"message": "వెబ్ ఫాంట్‌లు లోడ్ అవుతున్నప్పుడు టెక్స్ట్ యూజర్‌కు కనిపించేలా ఉందని నిర్ధారించుకోవడానికి `font-display` CSS ఫీచర్‌ను ఉపయోగించండి. [`font-display` గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "వెబ్ ఫాంట్ లోడ్ సమయంలో వచనం కనిపించేటట్లు నిర్ధారించుకోండి"}, "core/audits/font-display.js | title": {"message": "వెబ్ ఫాంట్ లోడ్‌ల సమయంలో వచనం మొత్తం కనిపిస్తూ ఉంటుంది"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{మూలం {fontOrigin}.కోసం `font-display` విలువను Lighthouse ఆటోమేటిక్‌గా చెక్ చేయలేకపోయింది.}other{`font-display` మూలానికి చెందిన విలువలను {fontOrigin} Lighthouse ఆటోమేటిక్‌గా చెక్ చేయలేకపోయింది.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "ఆకార నిష్పత్తి (ఉండాల్సినది)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "ఆకార నిష్పత్తి (ప్రదర్శించబడింది)"}, "core/audits/image-aspect-ratio.js | description": {"message": "ఇమేజ్ డిస్‌ప్లే కొలతలు సహజ ఆకార నిష్పత్తికి మ్యాచ్ అవ్వాలి. [ఇమేజ్‌కు సంబంధించిన ఆకార నిష్పత్తి గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "తప్పు ఆకార నిష్పత్తిని కలిగి ఉన్న చిత్రాలను ప్రదర్శిస్తుంది"}, "core/audits/image-aspect-ratio.js | title": {"message": "సరైన ఆకార నిష్పత్తితో చిత్రాలను ప్రదర్శిస్తుంది"}, "core/audits/image-size-responsive.js | columnActual": {"message": "అసలు సైజ్"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "ప్రదర్శించబడిన సైజ్"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "ఆశించిన సైజ్"}, "core/audits/image-size-responsive.js | description": {"message": "ఇమేజ్ స్పష్టతను వీలైనంతగా పెంచడానికి, ఆ ఇమేజ్ సహజ కొలతలు అనేవి, డిస్‌ప్లే సైజుకు, ఇంకా పిక్సెల్ నిష్పత్తికి ప్రపోర్షనల్‌గా ఉండాలి. [ఫ్లెక్సిబుల్ ఇమేజ్‌లను అందించడం ఎలాగో తెలుసుకోండి](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "తక్కువ రిజల్యూషన్‌తో ఇమేజ్‌లను అందిస్తుంది"}, "core/audits/image-size-responsive.js | title": {"message": "తగిన రిజల్యూషన్‌తో ఇమేజ్‌లను అందిస్తుంది"}, "core/audits/installable-manifest.js | already-installed": {"message": "యాప్ ఇప్పటికే ఇన్‌స్టాల్ అయింది"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "మ్యానిఫెస్ట్ నుండి అవసరమైన చిహ్నాన్ని డౌన్‌లోడ్ చేయడం సాధ్యపడలేదు"}, "core/audits/installable-manifest.js | columnValue": {"message": "వైఫల్యానికి కారణం"}, "core/audits/installable-manifest.js | description": {"message": "సర్వీస్ వర్కర్ టెక్నాలజీ అనేది, ఆఫ్‌లైన్ వినియోగం, హోమ్‌స్క్రీన్‌కు జోడించడం, పుష్ నోటిఫికేషన్‌ల లాంటి అనేక ప్రోగ్రెసివ్ వెబ్ యాప్ ఫీచర్‌లను ఉపయోగించే వీలు మీ యాప్‌నకు కల్పిస్తుంది. సరైన సర్వీస్ వర్కర్, ఇంకా మ్యానిఫెస్ట్ అమలు విధానాలతో, బ్రౌజర్‌లు మీ యాప్‌ను యూజర్‌ల హోమ్‌స్క్రీన్‌కు జోడించడానికి చురుకుగా వారిని ప్రాంప్ట్ చేయగలవు, దీని వలన ఎంగేజ్‌మెంట్ పెరిగే అవకాశం ఉంది. [మ్యానిఫెస్ట్ ఇన్‌స్టాల్ చేయడానికి సంబంధించిన ఆవశ్యకతల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 కారణం}other{# కారణాలు}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "ఇన్‌స్టాల్ సామర్థ్య అవసరాలకు అనుగుణంగా వెబ్ యాప్ మానిఫెస్ట్ లేదా సర్వీస్ వర్కర్ లేదు"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Store యాప్ URL, Play Store ID మ్యాచ్ కావడం లేదు"}, "core/audits/installable-manifest.js | in-incognito": {"message": "పేజీ అజ్ఞాత విండోలో లోడ్ అయింది"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "మ్యానిఫెస్ట్ 'display' ప్రాపర్టీ తప్పనిసరిగా 'standalone', 'fullscreen' లేదా 'minimal-ui'లో ఒకటి అయి ఉండాలి"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "మ్యానిఫెస్ట్‌లో 'display_override' ఫీల్డ్ ఉంది, సపోర్ట్ ఉండే మొదటి ప్రదర్శన మోడ్ తప్పనిసరిగా 'standalone', 'fullscreen' లేదా 'minimal-ui'లో ఒకటి అయి ఉండాలి"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "మ్యానిఫెస్ట్‌ని పొందడం సాధ్యపడలేదు, ఖాళీగా ఉంది లేదా అన్వయించడం సాధ్యపడలేదు"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "మ్యానిఫెస్ట్‌ను డౌన్‌లోడ్ చేస్తున్న సమయంలో మ్యానిఫెస్ట్ URL మారింది."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "మ్యానిఫెస్ట్‌లో 'name' లేదా 'short_name' ఫీల్డ్‌ను అందించలేదు"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "మ్యానిఫెస్ట్‌లో తగిన చిహ్నం లేదు - కనీసం {value0} px ఉండే PNG, SVG లేదా WebP ఫార్మాట్ అవసరం, సైజ్‌ల లక్షణాన్ని తప్పనిసరిగా సెట్ చేయాలి, అలాగే ప్రయోజన లక్షణం, సెట్ చేసి ఉంటే, తప్పనిసరిగా \"ఏదైనా\" ఎంపికను కలిగి ఉండాలి."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "దాని ప్రయోజన లక్షణం అన్‌సెట్ చేయబడి గానీ లేదా \"ఏదైనా\"కు సెట్ చేయబడి గానీ ఉన్న, కనీసం {value0}px స్క్వేర్‌తో PNG, SVG లేదా WebP ఫార్మాట్‌లోని చిహ్నం ఏదీ అందించబడలేదు"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "డౌన్‌లోడ్ చేయబడిన చిహ్నం ఖాళీగా ఉంది లేదా పాడైంది"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Play Store IDని అందించలేదు"}, "core/audits/installable-manifest.js | no-manifest": {"message": "పేజీకి మ్యానిఫెస్ట్ <link> URL లేదు"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "మ్యాచ్ అవుతున్న సర్వీస్ వర్కర్ ఏదీ గుర్తించలేదు. మీరు పేజీని రీలోడ్ చేయాల్సి రావచ్చు లేదా ప్రస్తుత పేజీ కోసం సర్వీస్ వర్కర్ పరిధిలో మానిఫెస్ట్ నుండి పరిధి, ప్రారంభ URL ఉన్నాయో లేదో చూసుకోండి."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "మ్యానిఫెస్ట్‌లో 'start_url' ఫీల్డ్ లేకుండా సర్వీస్ వర్కర్‌ను చెక్ చేయడం సాధ్యపడదు"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ఇన్‌స్టాల్ సామర్థ్యం ఎర్రర్ id '{errorId}'ని గుర్తించలేదు"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "పేజీని సురక్షిత ఆరిజిన్ నుండి అందించలేదు"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "ప్రధాన ఫ్రేమ్‌లో పేజీ లోడ్ కాలేదు"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "ఆఫ్‌లైన్‌లో పేజీ పని చేయదు"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA అన్‌ఇన్‌స్టాల్ చేయబడింది, ఇన్‌స్టాల్ సామర్థ్య తనిఖీలు రీసెట్ అవుతున్నాయి."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "పేర్కొన్న అప్లికేషన్ ప్లాట్‌ఫామ్‌కు Androidలో సపోర్ట్ లేదు"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "మ్యానిఫెస్ట్ prefer_related_applicationsని ఈ విధంగా పేర్కొంటుంది: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "కేవలం Chrome బీటా మరియు Androidలోని స్థిర ఛానెళ్లలో మాత్రమే prefer_related_applicationsకి సపోర్ట్ ఉంది."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "సర్వీస్ వర్కర్ ఉన్నారో, లేదో Lighthouse గుర్తించలేకపోయింది. దయచేసి Chrome కొత్త వెర్షన్‌తో ట్రై చేయండి."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "మ్యానిఫెస్ట్ URL స్కీమ్ ({scheme})కు Androidలో సపోర్ట్ లేదు."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "మ్యానిఫెస్ట్ ప్రారంభ URL చెల్లదు"}, "core/audits/installable-manifest.js | title": {"message": "ఇన్‌స్టాల్ సామర్థ్య అవసరాలకు అనుగుణంగా వెబ్ యాప్ మ్యానిఫెస్ట్, సర్వీస్ వర్కర్ ఉన్నాయి"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "మ్యానిఫెస్ట్‌లోని URLలో యూజర్‌నేమ్, పాస్‌వర్డ్‌ లేదా పోర్ట్ ఉన్నాయి"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "ఆఫ్‌లైన్‌లో పేజీ పని చేయదు. Chrome 93, స్థిరమైన వెర్షన్ రిలీజ్, ఆగస్ట్ 2021 తర్వాత పేజీ ఇన్‌స్టాల్ చేయలేరు."}, "core/audits/is-on-https.js | allowed": {"message": "అనుమతించబడింది"}, "core/audits/is-on-https.js | blocked": {"message": "బ్లాక్ అయింది"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "అసురక్షితమైన URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "రిజల్యూషన్ రిక్వెస్ట్ చేయండి"}, "core/audits/is-on-https.js | description": {"message": "సైట్‌లన్నింటికీ HTTPS రక్షణ ఉండాలి, సున్నితమైన వ్యక్తిగత సమాచారాన్ని హ్యాండిల్ చేయని వాటికి కూడా ఈ రక్షణ ఉండాలి. [మిశ్రమ కంటెంట్‌ను](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) నివారించడం కూడా ఇందులో భాగమే, మిశ్రమ కంటెంట్‌లో ప్రారంభ రిక్వెస్ట్ HTTPS ద్వారా అందించబడినప్పటికీ కొన్ని రిసోర్స్‌లు HTTP ద్వారా లోడ్ చేయబడతాయి. HTTPS రక్షణను జోడించడం వలన దాడులకు పాల్పడే వారు ఎవరూ కూడా మీ యాప్, ఇంకా మీ యూజర్‌ల మధ్య జరిగే కమ్యూనికేషన్‌లను ట్యాంపర్ చేయడం లేదా దొంగచాటుగా వినడం లాంటివి చేయలేరు, అలాగే HTTP/2, ఇంకా అనేక కొత్త వెబ్ ప్లాట్‌ఫామ్ APIల కోసం దీన్ని తప్పనిసరిగా వినియోగించాలి. [HTTPS గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 అసురక్షితమైన రిక్వెస్ట్‌ కనుగొనబడింది}other{# అసురక్షితమైన రిక్వెస్ట్‌లు కనుగొనబడ్డాయి}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "'HTTPS'ను ఉపయోగించడం లేదు"}, "core/audits/is-on-https.js | title": {"message": "HTTPSను ఉపయోగిస్తుంది"}, "core/audits/is-on-https.js | upgraded": {"message": "ఆటోమేటిక్‌గా HTTPSకు అప్‌గ్రేడ్ చేయబడింది"}, "core/audits/is-on-https.js | warning": {"message": "హెచ్చరికతో అనుమతించబడింది"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "ఇది వీక్షణ పోర్ట్‌లో పెయింట్ చేయబడిన, అన్నింటి కన్నా ఎక్కువ కంటెంట్ ఉన్న ఎలిమెంట్. [లార్జెస్ట్ కంటెంట్‌ఫుల్ పెయింట్ ఎలిమెంట్ గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "కంటెంట్ కలిగి ఉండే అతిపెద్ద పెయింట్ మూలకం"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS కంట్రిబ్యూషన్"}, "core/audits/layout-shift-elements.js | description": {"message": "ఈ పేజీ యొక్క CLSకు ఈ DOM ఎలిమెంట్‌లు అత్యధికంగా కంట్రిబ్యూట్ చేస్తాయి. [CLSని మెరుగుపరచడం ఎలాగో తెలుసుకోండి](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "పెద్ద లేఅవుట్ షిఫ్ట్‌లను నివారించండి"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "ప్రాధాన్యతను బట్టి లోడ్ చేయబడిన, ఫోల్డ్‌కు ఎగువున ఉన్న ఇమేజ్‌లు పేజీ లైఫ్ సైకిల్‌లో తర్వాత రెండర్ అవుతాయి, దీని వలన లార్జెస్ట్ కంటెంట్‌ఫుల్ పెయింట్ ఆలస్యం కావచ్చు. [ఉత్తమమైన 'ప్రాధాన్యతను బట్టి లోడింగ్' గురించి మరింత తెలుసుకోండి](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "కంటెంట్ కలిగి ఉండే అతిపెద్ద పెయింట్ ఇమేజ్ ప్రాధాన్యతను బట్టి లోడింగ్ చేయబడింది"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "కంటెంట్ కలిగి ఉండే అతిపెద్ద పెయింట్ ఇమేజ్ ప్రాధాన్యతను బట్టి లోడింగ్ చేయబడలేదు"}, "core/audits/long-tasks.js | description": {"message": "ప్రధాన థ్రెడ్‌లో అన్నింటి కంటే సుదీర్ఘవైన టాస్క్‌లను లిస్ట్ చేస్తుంది, ఇది ఇన్‌పుట్ ఆలస్యానికి దారితీసే పనికి రాని కంట్రిబ్యూటర్‌లను గుర్తించడంలో ఉపయోగకరంగా ఉంటుంది. [సుదీర్ఘమైన ప్రధాన థ్రెడ్ టాస్క్‌లను నివారించడం ఎలాగో తెలుసుకోండి](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# సుదీర్ఘ టాస్క్ కనుగొనబడింది}other{# సుదీర్ఘ టాస్క్‌లు కనుగొనబడ్డాయి}}"}, "core/audits/long-tasks.js | title": {"message": "సుధీర్గ మెయిన్-థ్రెడ్ టాస్క్‌లను నివారించండి"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "వర్గం"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "JSను పార్స్ చేయడం, కంపైల్ చేయడం, ఎగ్జిక్యూట్ చేయడం కోసం వెచ్చించే సమయాన్ని తగ్గించడాన్ని పరిశీలించండి. దీని ద్వారా చిన్న చిన్న JS పేలోడ్‌లను పంపడం మీకు సహాయకరంగా అనిపించవచ్చు. [ప్రధాన థ్రెడ్ పనిని వీలైనంతగా ఎలా తగ్గించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "ప్రధాన థ్రెడ్ పనిని తగ్గించండి"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "ప్రధాన థ్రెడ్ పనిని తగ్గిస్తుంది"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "అత్యధిక మంది యూజర్‌లకు చేరువ కావడానికి, సైట్‌లు ప్రతి ప్రధాన బ్రౌజర్‌లో పని చేయాలి. [క్రాస్-బ్రౌజర్ అనుకూలత గురించి తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "వివిధ రకాల బ్రౌజర్‌లలో సైట్ పని చేస్తుంది"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "పేజీలు విడివిడిగా URL ద్వారా డీప్ లింక్ చేయబడేలా చూసుకోండి, అలాగే సోషల్ మీడియాలో షేర్ చేయగలిగేలా ఆ URLలు విభిన్నంగా ఉండేలా కూడా చూసుకోండి. [లోపలి లింక్‌లను అందించడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "ప్రతి పేజీకి URL ఉంది"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "నెట్‌వర్క్ కనెక్షన్ నెమ్మదిగా ఉన్నప్పుడు కూడా, మీరు స్క్రీన్‌పై ఎక్కడైనా ట్యాప్ చేసినప్పుడు ట్రాన్సిషన్‌లు వీక్షణకు భంగం కలిగించకుండా చక్కగా, వేగంగా ఉండాలి. మీ వెబ్ యాప్ పనితీరు గురించి యూజర్ ఏర్పరచుకునే అభిప్రాయానికి ఈ అనుభూతి చాలా కీలకమైనది. [పేజీ ట్రాన్సిషన్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "పేజీ పరివర్తనలు నెట్‌వర్క్‌లో లోడ్ అవుతున్నప్పుడు బ్లాక్ అవుతున్నట్లుగా కనిపించకూడదు"}, "core/audits/maskable-icon.js | description": {"message": "ఫిట్ అయ్యే చిహ్నం అన్నది యాప్‌ను పరికరంలో ఇన్‌స్టాల్ చేస్తున్నప్పుడు, లెటర్‌బాక్స్ సైజు‌లో కాకుండా మొత్తం ఆకారానికి తగినట్లుగా ఇమేజ్ నిండేలా చేస్తుంది. [ఫిట్ అయ్యే మ్యానిఫెస్ట్ చిహ్నాల గురించి తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "మ్యానిఫెస్ట్‌లో మాస్క్ వేయగలిగే చిహ్నం లేదు"}, "core/audits/maskable-icon.js | title": {"message": "మ్యానిఫెస్ట్‌లో మాస్క్ వేయగల చిహ్నం ఉంది"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "వీక్షణ పోర్ట్‌లో కనిపించే ఎలిమెంట్‌ల కదలికను క్యుములేటివ్ లేఅవుట్ షిఫ్ట్ కొలుస్తుంది. [క్యుములేటివ్ లేఅవుట్ షిఫ్ట్ కొలమానం గురించి మరింత తెలుసుకోండి](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint పేజీ ప్రతిస్పందనా తీరును కొలుస్తుంది, అంటే యూజర్ ఇన్‌పుట్‌కు ప్రత్యక్షంగా ప్రతిస్పందించడానికి పేజీకి ఎంత సమయం పడుతుందో కొలుస్తుంది. [Interaction to Next Paint కొలమానం గురించి మరింత తెలుసుకోండి](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "ఫస్ట్ కంటెంట్‌ఫుల్ పెయింట్ అనేది, మొదటి టెక్స్ట్ లేదా ఇమేజ్ పెయింట్ చేయబడిన సమయాన్ని గుర్తిస్తుంది. [ఫస్ట్ కంటెంట్‌ఫుల్ పెయింట్ కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "పేజీకి సంబంధించిన ప్రధాన కంటెంట్ ఎప్పుడు కనిపిస్తుంది అనే దాన్ని ఫస్ట్ మీనింగ్‌ఫుల్ పెయింట్ కొలుస్తుంది. [ఫస్ట్ మీనింగ్‌ఫుల్ పెయింట్ కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "పూర్తిగా ఇంటరాక్టివ్ అవ్వడానికి పేజీకి ఎంత సమయం పడుతుందో, దాన్నే ఇంటరాక్షన్ టైమ్ అని అంటారు. [ఇంటరాక్షన్ టైమ్ కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "లార్జెస్ట్ కంటెంట్‌ఫుల్ పెయింట్ అనేది, అతిపెద్ద టెక్స్ట్ లేదా ఇమేజ్ పెయింట్ చేయబడిన సమయాన్ని గుర్తిస్తుంది. [లార్జెస్ట్ కంటెంట్‌ఫుల్ పెయింట్ కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "మీ యూజర్‌లు ఎదుర్కోగల గరిష్ఠ తొలి ప్రతిస్పందన వ్యవధి అనేది, అన్నింటి కంటే సుదీర్ఘంగా సాగే టాస్క్‌కు పట్టే సమయం. [యూజర్‌లు ఎదుర్కోగల గరిష్ఠ తొలి ప్రతిస్పందన వ్యవధి కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "స్పీడ్ ఇండెక్స్ అనేది, ఒక పేజీలోని కంటెంట్‌లు ఎంత వేగంగా ప్రత్యక్షంగా ప్రదర్శించబడతాయో తెలియజేస్తుంది. [స్పీడ్ ఇండెక్స్ కొలమానం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "టాస్క్ వ్యవధి 50 మిల్లీసెకన్లు మించిపోయినప్పుడు FCPకి, అలాగే ఇంటరాక్షన్ టైమ్‌కు మధ్య అన్ని సమయ వ్యవధుల మొత్తం, ఇది మిల్లీసెకన్లలో పేర్కొనబడుతుంది. [బ్లాక్ చేయడానికి పట్టే మొత్తం సమయానికి సంబంధించిన మెట్రిక్ గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "నెట్‌వర్క్ రౌండ్ ట్రిప్ సమయాలు (RTT) పనితీరుపై చాలా ఎక్కువ ప్రభావం చూపుతాయి. ఆరిజిన్‌కు RTT ఎక్కువగా ఉంటే, యూజర్‌కు దగ్గరగా ఉన్న సర్వర్‌లు పనితీరును మెరుగుపరచగలవు అని అర్థం. [రౌండ్ ట్రిప్ సమయం గురించి మరింత తెలుసుకోండి](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "నెట్‌వర్క్ రౌండ్ ట్రిప్ సమయాలు"}, "core/audits/network-server-latency.js | description": {"message": "సర్వర్ ప్రతిస్పందన సమయాలు వెబ్ పనితీరుపై ప్రభావం చూపగలవు. ఏదైనా ఆరిజిన్‌కు సంబంధించిన సర్వర్ ప్రతిస్పందన సమయం ఎక్కువగా ఉంటే, సర్వర్ ఓవర్‌లోడ్ అయిందని లేదా బ్యాక్ ఎండ్ పనితీరు సరిగ్గా లేదని అర్థం. [సర్వర్ ప్రతిస్పందన సమయం గురించి మరింత తెలుసుకోండి](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "సర్వర్ బ్యాక్ఎండ్ ప్రతిస్పందన సమయాలు"}, "core/audits/no-unload-listeners.js | description": {"message": "ఈ `unload` ఈవెంట్ ఆశించినట్టు పని చేయదు, దాని కోసం వేచి ఉంటే, వెనుకకు-ముందుకు కాష్ లాంటి బ్రౌజర్ ఆప్టిమైజేషన్‌లు నిరోధించబడవచ్చు. అందుకు బదులుగా `pagehide` లేదా `visibilitychange` ఈవెంట్‌లను ఉపయోగించండి. [అన్‌లోడ్ ఈవెంట్ లిజనర్‌ల గురించి మరింత తెలుసుకోండి](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "`unload` లిజనర్‌గా రిజిస్టర్ చేస్తుంది"}, "core/audits/no-unload-listeners.js | title": {"message": "`unload` ఈవెంట్ లిజనర్స్‌ను అవాయిడ్‌ చేస్తుంది"}, "core/audits/non-composited-animations.js | description": {"message": "కంపోజిట్ చేయని యానిమేషన్‌లు పేలవంగా ఉండవచ్చు, అవి CLSను పెంచవచ్చు. [కంపోజిట్ కాని యానిమేషన్‌లను నివారించడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# యానిమేట్ చేసిన ఎలిమెంట్ కనుగొనబడింది}other{# యానిమేట్ చేసిన ఎలిమెంట్‌లు కనుగొనబడ్డాయి}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "ఫిల్టర్-సంబంధిత ప్రాపర్టీ పిక్సెల్‌లను తరలించవచ్చు"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "టార్గెట్‌లోని మరో యానిమేషన్ అనుకూలంగా లేదు"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "ఎఫెక్ట్‌లో \"replace\" కాకుండా కంపోజిట్ మోడ్ ఉంది"}, "core/audits/non-composited-animations.js | title": {"message": "కంపోజిట్ చేయని యానిమేషన్‌లను ఉపయోగించకండి"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "రూపం మార్పునకు సంబంధించిన ప్రాపర్టీ, బాక్స్ సైజ్‌పై ఆధారపడి ఉంటుంది"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{సపోర్ట్ లేని CSS ప్రాపర్టీ: {properties}}other{సపోర్ట్ లేని CSS ప్రాపర్టీలు: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "ఎఫెక్ట్‌లో ఉన్న సమయ పారామీటర్‌లకు సపోర్ట్ లేదు"}, "core/audits/performance-budget.js | description": {"message": "నెట్‌వర్క్ రిక్వెస్ట్‌ల పరిమాణాన్ని, ఇంకా సైజును, అందించబడిన పనితీరు బడ్జెట్ సెట్ చేసిన టార్గెట్‌ల కంటే తక్కువకు ఉంచండి. [పనితీరుకు సంబంధించిన బడ్జెట్‌ల గురించి మరింత తెలుసుకోండి](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 రిక్వెస్ట్‌}other{# రిక్వెస్ట్‌లు}}"}, "core/audits/performance-budget.js | title": {"message": "పనితీరు బడ్జెట్"}, "core/audits/preload-fonts.js | description": {"message": "`optional` ఫాంట్‌లను ప్రీ - లోడ్ చేయండి, తద్వారా మొదటిసారిగా వచ్చే సందర్శకులు వాటిని ఉపయోగించగలరు. [ఫాంట్‌లను ప్రీ - లోడ్ చేయడం గురించి మరింత తెలుసుకోండి](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "`font-display: optional`తో ఉన్న ఫాంట్‌లు ముందే లోడ్ చేయబడలేదు"}, "core/audits/preload-fonts.js | title": {"message": "`font-display: optional`తో ఉన్న ఫాంట్‌లు ముందే లోడ్ చేయబడ్డాయి"}, "core/audits/prioritize-lcp-image.js | description": {"message": "ఒకవేళ LCP ఎలిమెంట్, డైనమిక్‌గా పేజీకి జోడించబడితే, LCPని మెరుగుపరచడానికి మీరు ఇమేజ్‌ను ప్రీ - లోడ్ చేయాలి. [LCP ఎలిమెంట్‌లను ప్రీ - లోడ్ చేయడం గురించి మరింత తెలుసుకోండి](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "కంటెంట్ కలిగి ఉండే అతిపెద్ద పెయింట్ ఇమేజ్‌ను ముందే లోడ్ చేయండి"}, "core/audits/redirects.js | description": {"message": "మళ్లింపుల వల్ల పేజీ లోడ్ అవ్వడానికి ఇంకాస్త ఆలస్యం జరుగుతుంది. [పేజీ మళ్లింపులను ఎలా నివారించాలి అనే దాని గురించి తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "అనేక పేజీ మళ్లింపులను నివారించండి"}, "core/audits/resource-summary.js | description": {"message": "పేజీ రిసోర్స్‌ల పరిమాణానికి, ఇంకా సైజుకు బడ్జెట్‌లను సెట్ చేయడానికి, budget.json ఫైల్‌ను జోడించండి. [పనితీరుకు సంబంధించిన బడ్జెట్‌ల గురించి మరింత తెలుసుకోండి](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 రిక్వెస్ట్ • {byteCount, number, bytes} KiB}other{# రిక్వెస్ట్‌లు • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "రిక్వెస్ట్‌ల సంఖ్యను తగ్గించుకోండి, బదిలీ పరిమాణాలు తక్కువగా ఉండేలా చూసుకోండి"}, "core/audits/seo/canonical.js | description": {"message": "సెర్చ్ ఫలితాలలో ఏ URLను చూపాలో కనోనికల్ లింక్‌లు సూచిస్తాయి. [కనోనికల్ లింక్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "వైరుధ్యమైన అనేక URLలు ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "చెల్లని URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "మరొక '`hreflang`' స్థానం ({url})కు నిర్దేశిస్తోంది"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "({url}) ఖచ్చితమైన URL కాదు"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "అలాంటి కంటెంట్‌ను కలిగిన పేజీకి బదులుగా డొమైన్ యొక్క మూలాధార URL (హోమ్‌పేజీ)ని సూచిస్తుంది"}, "core/audits/seo/canonical.js | failureTitle": {"message": "డాక్యుమెంట్‌లో చెల్లుబాటు అయ్యే '`rel=canonical`' లేదు"}, "core/audits/seo/canonical.js | title": {"message": "డాక్యుమెంట్‌లో చెల్లుబాటు అయ్యే '`rel=canonical`' ఉంది"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "క్రాల్ చేయలేని లింక్"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "సెర్చ్ ఇంజిన్‌లు లింక్‌లలో ఉన్న `href` లక్షణాలను వెబ్‌సైట్‌లను క్రాల్ చేయడానికి ఉపయోగించవచ్చు. యాంకర్ ఎలిమెంట్‌ల `href` లక్షణం తగిన గమ్యస్థానానికి లింక్ చేస్తుందని నిర్ధారించుకోండి, తద్వారా సైట్‌లోని మరిన్ని పేజీలను కనుగొనవచ్చు. [లింక్‌లను క్రాల్ చేయదగినవిగా ఎలా చేయాలో తెలుసుకోండి](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "లింక్‌లు క్రాల్ చేయదగినవి కావు"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "లింక్‌లు క్రాల్ చేయదగినవి"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "అదనపు అస్పష్టమైన టెక్స్ట్"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "ఫాంట్ సైజ్"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "పేజీ టెక్స్ట్‌లో %"}, "core/audits/seo/font-size.js | columnSelector": {"message": "ఎంపిక సాధనం"}, "core/audits/seo/font-size.js | description": {"message": "ఫాంట్ సైజులు 12px కంటే తక్కువగా ఉంటే, టెక్స్ట్ మరీ చిన్నగా ఉండటం వల్ల చదవడం కష్టం అవుతుంది, దాన్ని చదవడం కోసం మొబైల్ సందర్శకులు \"జూమ్ చేయడానికి వేళ్లతో స్క్రీన్‌ను నియంత్రించాల్సి ఉంటుంది.\" పేజీలో 60% కంటే ఎక్కువ కవర్ చేసే టెక్స్ట్ తాలూకు ఫాంట్ సైజు 12px కానీ, లేదా అంత కంటే ఎక్కువ కానీ ఉండేలా సెట్ చేయడానికి ట్రై చేయండి. [చదవడానికి అనువుగా ఉండే ఫాంట్ సైజుల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} సముచిత వచనం"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "మొబైల్ స్క్రీన్‌ల కోసం ఆప్టిమైజ్ చేసిన వీక్షణ పోర్ట్ మెటా ట్యాగ్ అందుబాటులో లేదు కనుక వచనం సముచితంగా లేదు."}, "core/audits/seo/font-size.js | failureTitle": {"message": "డాక్యుమెంట్‌లో సముచితమైన ఫాంట్ పరిమాణాలను ఉపయోగించలేదు"}, "core/audits/seo/font-size.js | legibleText": {"message": "స్పష్టమైన టెక్స్ట్"}, "core/audits/seo/font-size.js | title": {"message": "డాక్యుమెంట్‌లో ఫాంట్ పరిమాణాలు సముచితంగా ఉన్నాయి"}, "core/audits/seo/hreflang.js | description": {"message": "నిర్దిష్ట భాష లేదా ప్రాంతానికి సంబంధించి, పేజీ తాలూకు ఏ వెర్షన్‌ను సెర్చ్ ఫలితాలలో చూపాలో సెర్చ్ ఇంజిన్‌లకు hreflang లింక్‌లు తెలియజేస్తాయి. [`hreflang` గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "డాక్యుమెంట్‌లో చెల్లుబాటు అయ్యే ``hreflang`` లేదు"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "సంబంధిత href విలువ"}, "core/audits/seo/hreflang.js | title": {"message": "డాక్యుమెంట్‌లో చెల్లుబాటు అయ్యే '`hreflang`' ఉంది"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "ఉహించని భాష కోడ్"}, "core/audits/seo/http-status-code.js | description": {"message": "విజయవంతం కాని HTTP స్టేటస్ కోడ్‌లు గల పేజీలు సరిగ్గా ఇండెక్స్ చేయబడకపోవచ్చు. [HTTP స్టేటస్ కోడ్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "పేజీలో విజయవంతం కాని HTTP స్థితి కోడ్ ఉంది"}, "core/audits/seo/http-status-code.js | title": {"message": "పేజీలో విజయవంతమైన HTTP స్థితి కోడ్ ఉంది"}, "core/audits/seo/is-crawlable.js | description": {"message": "మీ పేజీలను క్రాల్ చేయడానికి సెర్చ్ ఇంజిన్‌లకు అనుమతి లేకపోతే, అవి సెర్చ్ ఫలితాలలో మీ పేజీలను చూపలేవు. [క్రాలర్ డైరెక్టివ్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "సూచిక చేయకుండా పేజీ బ్లాక్ చేయబడింది"}, "core/audits/seo/is-crawlable.js | title": {"message": "సూచికలో పేజీ బ్లాక్ చేయబడలేదు"}, "core/audits/seo/link-text.js | description": {"message": "మీ కంటెంట్‌ను అర్థం చేసుకోవడంలో సెర్చ్ ఇంజిన్‌లకు వివరణాత్మక లింక్ టెక్స్ట్ సహాయపడుతుంది. [లింక్‌లను మరింత యాక్సెస్ చేయదగినవిగా ఎలా చేయాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 లింక్ కనుగొనబడింది}other{# లింక్‌లు కనుగొనబడ్డాయి}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "లింక్‌లలో వివరణాత్మక వచనం లేదు"}, "core/audits/seo/link-text.js | title": {"message": "లింక్‌లలో వివరణాత్మక వచనం ఉంది"}, "core/audits/seo/manual/structured-data.js | description": {"message": "నిర్మాణాత్మక డేటాను వాలిడేట్ చేయడానికి, [నిర్మాణాత్మక డేటా టెస్టింగ్ టూల్‌ను](https://search.google.com/structured-data/testing-tool/), అలాగే [నిర్మాణాత్మక డేటా లింటర్‌ను](http://linter.structured-data.org/) రన్ చేయండి. [నిర్మాణాత్మక డేటా గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "నిర్మాణాత్మక డేటా చెల్లుబాటు అవుతుంది"}, "core/audits/seo/meta-description.js | description": {"message": "పేజీ కంటెంట్ సారాంశాన్ని క్లుప్తంగా అందించడం కోసం సెర్చ్ ఫలితాలలో మెటా వివరణలు జోడించబడవచ్చు. [మెటా వివరణ గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "వివరణ వచనం ఖాళీగా ఉంది."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "డాక్యుమెంట్‌లో మెటా వివరణ లేదు"}, "core/audits/seo/meta-description.js | title": {"message": "డాక్యుమెంట్‌లో మెటా వివరణ ఉంది"}, "core/audits/seo/plugins.js | description": {"message": "ప్లగ్ఇన్ కంటెంట్‌ను సెర్చ్ ఇంజిన్‌లు ఇండెక్స్ చేయలేవు, చాలా వరకు పరికరాలలో ప్లగ్ఇన్‌ల వినియోగం నియంత్రించబడి ఉంటుంది లేదా వాటికి సపోర్ట్ ఉండదు. [ప్లగ్ఇన్‌లను నివారించడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "డాక్యుమెంట్‌లో ప్లగ్ఇన్‌లు ఉపయోగించబడుతున్నాయి"}, "core/audits/seo/plugins.js | title": {"message": "డాక్యుమెంట్‌లో ప్లగ్ఇన్‌లు నివారించబడ్డాయి"}, "core/audits/seo/robots-txt.js | description": {"message": "మీ robots.txt ఫైల్ ఫార్మాట్ తప్పుగా ఉంటే, మీరు మీ వెబ్‌సైట్‌ను ఎలా క్రాల్ చేయాలనుకుంటున్నారు లేదా ఎలా ఇండెక్స్ చేయాలనుకుంటున్నారు అన్నది క్రాలర్‌లకు అర్థం కాకపోవచ్చు. [robots.txt గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt కోసం పంపిన రిక్వెస్ట్‌కు ప్రతిస్పందనగా అందించబడిన HTTP స్థితి: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 ఎర్రర్ కనుగొనబడింది}other{# ఎర్రర్‌లు కనుగొనబడ్డాయి}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "robots.txt ఫైల్‌ను డౌన్‌లోడ్ చేయడం లైట్‌హౌస్‌కు సాధ్యం కాలేదు"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt చెల్లుబాటు కాదు"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt చెల్లుబాటు అవుతుంది"}, "core/audits/seo/tap-targets.js | description": {"message": "బటన్‌లు, లింక్‌ల వంటి ఇంటరాక్టివ్ ఎలిమెంట్‌ల సైజు తగినంత పెద్దగా ఉండాలి (48x48px), అలాగే వాటి చుట్టూ తగినంత స్పేస్ ఉండాలి, అలా అయితే అవి ఇతర ఎలిమెంట్‌లలోకి ఓవర్‌ల్యాప్ కాకుండా ట్యాప్ చేయడానికి సులభంగా ఉంటాయి. [ట్యాప్ టార్గెట్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} సముచిత పరిమాణంలో ట్యాప్ టార్గెట్‌లను కలిగి ఉంది"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "మొబైల్ స్క్రీన్‌లలో ఆప్టిమైజ్ చేసిన వీక్షణ పోర్ట్ మెటా ట్యాగ్ అందుబాటులో లేదు కనుక ట్యాప్ టార్గెట్‌లు చాలా చిన్నవిగా ఉన్నాయి"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "ట్యాప్ టార్గెట్‌ల సైజ్‌ సముచితంగా ఉంది"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "లక్ష్యం ఓవర్‌ల్యాప్ అవుతోంది"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "ట్యాప్ టార్గెట్"}, "core/audits/seo/tap-targets.js | title": {"message": "ట్యాప్‌టార్గెట్‌ల సైజ్‌ సముచితంగా ఉంది"}, "core/audits/server-response-time.js | description": {"message": "ప్రధాన డాక్యుమెంట్ విషయంలో సర్వర్ ప్రతిస్పందన సమయాన్ని తక్కువగా ఉంచండి, ఎందుకంటే ఇతర రిక్వెస్ట్‌లన్నీ దానిపైనే ఆధారపడి ఉంటాయి. [మొదటి బైట్‌కు సమయం మెట్రిక్ గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "రూట్ డాక్యుమెంట్‌ {timeInMs, number, milliseconds} మి.సె తీసుకుంది"}, "core/audits/server-response-time.js | failureTitle": {"message": "ప్రారంభ సర్వర్ ప్రతిస్పందన సమయాన్ని తగ్గించండి"}, "core/audits/server-response-time.js | title": {"message": "ప్రారంభ సర్వర్ ప్రతిస్పందన సమయం తక్కువగా ఉంది"}, "core/audits/service-worker.js | description": {"message": "సర్వీస్ వర్కర్ టెక్నాలజీ అనేది, ఆఫ్‌లైన్ వినియోగం, హోమ్‌స్క్రీన్‌కు జోడించడం, పుష్ నోటిఫికేషన్‌ల లాంటి అనేక ప్రోగ్రెసివ్ వెబ్ యాప్ ఫీచర్‌లను ఉపయోగించే వీలు మీ యాప్‌నకు కల్పిస్తుంది. [సర్వీస్ వర్కర్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "ఈ పేజీ ఒక సర్వీస్ వర్కర్ ద్వారా నియంత్రించబడినప్పటికీ, చెల్లుబాటయ్యే JSON ఫార్మాట్‌లో అన్వయించడంలో మానిఫెస్ట్ విఫలమైనందున '`start_url`' ఏదీ కనుగొనబడలేదు"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "ఈ పేజీ ఒక సర్వీస్ వర్కర్ ద్వారా నియంత్రించబడినప్పటికీ, '`start_url`' ({startUrl}) అన్నది సర్వీస్ వర్కర్ పరిధి ({scopeUrl})లో లేదు"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "ఈ పేజీ ఒక సర్వీస్ వర్కర్ ద్వారా నియంత్రించబడినప్పటికీ, మానిఫెస్ట్ ఏదీ పొందనందున '`start_url`' ఏదీ కనుగొనబడలేదు."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "ఈ మూలాధారంలో ఒకటి లేదా అంతకంటే ఎక్కువ సర్వీస్ వర్కర్‌లు ఉన్నప్పటికీ, పేజీ ({pageUrl}) పరిధిలో లేదు."}, "core/audits/service-worker.js | failureTitle": {"message": "పేజీని, '`start_url`'ను నియంత్రించే సర్వీస్ వర్కర్ ఏదీ నమోదు చేయబడలేదు"}, "core/audits/service-worker.js | title": {"message": "పేజీని, '`start_url`'ను నియంత్రించే సర్వీస్ వర్కర్ నమోదు చేయబడింది"}, "core/audits/splash-screen.js | description": {"message": "థీమ్‌తో కూడిన స్ప్లాష్ స్క్రీన్ వలన, యూజర్‌లు వారి హోమ్‌స్క్రీన్‌ల నుండి మీ యాప్‌ను లాంచ్ చేసినప్పుడు, అధిక క్వాలిటీ గల అనుభవం అందించబడుతుంది. [స్ప్లాష్ స్క్రీన్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "అనుకూలమైన స్ప్లాష్ స్క్రీన్ కోసం కాన్ఫిగర్ చేయలేదు"}, "core/audits/splash-screen.js | title": {"message": "అనుకూలమైన స్ప్లాష్ స్క్రీన్ కోసం కాన్ఫిగర్ చేయబడింది"}, "core/audits/themed-omnibox.js | description": {"message": "బ్రౌజర్ అడ్రస్ బార్‌ను మీ సైట్‌తో మ్యాచ్ అయ్యే థీమ్‌లోకి మార్చుకోవచ్చు. [అడ్రస్ బార్‌కు థీమ్‌ను సెట్ చేయడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "అడ్రస్‌ బార్ కోసం థీమ్ రంగును సెట్ చేయలేదు."}, "core/audits/themed-omnibox.js | title": {"message": "అడ్రస్‌ బార్ కోసం థీమ్ రంగు సెట్ చేయబడింది."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (కస్టమర్ విజయం)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (మార్కెటింగ్)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (సామాజికం)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (వీడియో)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "ప్రోడక్ట్"}, "core/audits/third-party-facades.js | description": {"message": "కొన్ని థర్డ్-పార్టీ ఎంబెడ్‌లు ప్రాధాన్యతను బట్టి లోడ్ అవ్వవచ్చు. వాటి అవసరం వచ్చేంత వరకు వాటిని ఫసాడ్‌తో రీప్లేస్ చేసే అంశాన్ని పరిశీలించండి. [ఫసాడ్‌తో థర్డ్-పార్టీలను వాయిదా వేయడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# ఫసాడ్ ప్రత్యామ్నాయం అందుబాటులో ఉంది}other{# ఫసాడ్ ప్రత్యామ్నాయాలు అందుబాటులో ఉన్నాయి}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "కొన్ని థర్డ్-పార్టీ రిసోర్స్‌లను ఫసాడ్‌తో లేజీ లోడ్ చేయగలరు"}, "core/audits/third-party-facades.js | title": {"message": "ఫసాడ్‌లతో థర్డ్-పార్టీ రీసోర్స్‌లు లేజీ లోడ్ అవుతాయి"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "మూడవ పక్షం"}, "core/audits/third-party-summary.js | description": {"message": "థర్డ్-పార్టీ కోడ్ గణనీయమైన స్థాయిలో లోడ్ పనితీరుపై ప్రభావం చూపవచ్చు. అవసరం లేని థర్డ్-పార్టీ ప్రొవైడర్‌ల సంఖ్యను పరిమితం చేసి, మీ పేజీ ప్రాథమికంగా లోడ్ కావడం పూర్తయిన తర్వాత థర్డ్-పార్టీ కోడ్‌ను లోడ్ చేయడానికి ట్రై చేయండి. [థర్డ్-పార్టీ ప్రభావాన్ని వీలైనంతగా ఎలా తగ్గించాలో తెలుసుకోండి](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "మూడవ పక్షం కోడ్ ఒక ప్రధానమైన థ్రెడ్‌ను {timeInMs, number, milliseconds} మిల్లీసెకన్ల పాటు బ్లాక్ చేసింది"}, "core/audits/third-party-summary.js | failureTitle": {"message": "మూడవ పక్షం కోడ్ ప్రభావాన్ని తగ్గించండి"}, "core/audits/third-party-summary.js | title": {"message": "మూడవ పక్ష వినియోగాన్ని కనీస స్థాయికి తగ్గించండి"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "కొలమానం"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "కొలమానం"}, "core/audits/timing-budget.js | description": {"message": "మీ సైట్ పనితీరును పర్యవేక్షించడంలో మీకు సహాయపడటానికి ఒక టైమింగ్ బడ్జెట్‌ను సెట్ చేయండి. సమర్థవంతమైన సైట్‌లు వేగంగా లోడ్ అవుతాయి, యూజర్ ఇన్‌పుట్ ఈవెంట్‌లకు సత్వరం ప్రతిస్పందిస్తాయి. [పనితీరుకు సంబంధించిన బడ్జెట్‌ల గురించి మరింత తెలుసుకోండి](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "సమయం సంబంధిత బడ్జెట్"}, "core/audits/unsized-images.js | description": {"message": "లేఅవుట్ షిఫ్ట్‌లను తగ్గించడానికి, CLSను మెరుగుపరచడానికి ఇమేజ్ ఎలిమెంట్‌లకు స్పష్టమైన వెడల్పును, ఎత్తును సెట్ చేయండి. [ఇమేజ్ కొలతలను ఎలా సెట్ చేయాలో తెలుసుకోండి](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "ఇమేజ్ మూలకాలకు తగిన `width`, `height` లేదు"}, "core/audits/unsized-images.js | title": {"message": "ఇమేజ్ మూలకాలు తగిన `width`, `height`ను కలిగి ఉన్నాయి"}, "core/audits/user-timings.js | columnType": {"message": "రకం"}, "core/audits/user-timings.js | description": {"message": "కీలక యూజర్ అనుభవాల సమయంలో మీ యాప్ వాస్తవ ప్రపంచ పనితీరును కొలవడానికి, మీ యాప్‌ను యూజర్ టైమింగ్ APIతో అలైన్ చేసే అంశాన్ని పరిశీలించండి. [యూజర్ టైమింగ్ మార్క్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 వినియోగదారు సమయం}other{# వినియోగదారు సమయాలు}}"}, "core/audits/user-timings.js | title": {"message": "వినియోగదారు సమయం మార్కులు మరియు కొలమానాలు"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "\"{security<PERSON><PERSON><PERSON>}\" కోసం `<link rel=preconnect>` కనుగొనబడింది, కానీ బ్రౌజర్ దీన్ని ఉపయోగించలేదు. మీరు `crossorigin` లక్షణాన్ని సక్రమంగా ఉపయోగిస్తున్నారో లేదో చెక్ చేయండి."}, "core/audits/uses-rel-preconnect.js | description": {"message": "ముఖ్యమైన థర్డ్-పార్టీ ఆరిజిన్‌లకు ముందస్తు కనెక్షన్‌లను ఏర్పరచడానికి `preconnect` లేదా `dns-prefetch` రిసోర్స్ హింట్‌లను జోడించడాన్ని పరిశీలించండి. [అవసరమైన ఆరిజిన్‌లకు ముందస్తుగానే కనెక్ట్ చేయడం ఎలాగో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "అవసరమైన మూలాలకు ముందుగా కనెక్ట్ చేయండి"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "2 కంటే ఎక్కువ `<link rel=preconnect>` కనెక్షన్‌లు కనుగొనబడ్డాయి. వీటిని పరిమితంగా, కేవలం అత్యంత ముఖ్యమైన మూల స్థానాలలో మాత్రమే ఉపయోగించాలి."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "\"{security<PERSON><PERSON><PERSON>}\" కోసం `<link rel=preconnect>` కనుగొనబడింది, కానీ బ్రౌజర్ దీన్ని ఉపయోగించలేదు. పేజీ ఖచ్చితంగా రిక్వెస్ట్ చేసే ముఖ్యమైన మూలాల కోసం మాత్రమే `preconnect`ను ఉపయోగించండి."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "\"{preloadURL}\" కోసం ముందే లోడ్ చేసిన ఒక `<link>` కనుగొనబడింది, కానీ బ్రౌజర్ దీన్ని ఉపయోగించలేదు. మీరు `crossorigin` లక్షణాన్ని సక్రమంగా ఉపయోగిస్తున్నారో లేదో చెక్ చేయండి."}, "core/audits/uses-rel-preload.js | description": {"message": "ప్రస్తుతం, పేజీ లోడ్‌లో తర్వాత రిక్వెస్ట్ చేయబడే రిసోర్స్‌లను పొందడాన్ని ప్రాధాన్యపరచడానికి `<link rel=preload>`‌ను ఉపయోగించడాన్ని పరిశీలించండి. [ముఖ్యమైన రిక్వెస్ట్‌లను ప్రీ - లోడ్ ఎలా చేయాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "కీలక అభ్యర్ధనలను ముందుగా లోడ్ చేయండి"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "మ్యాప్ URL"}, "core/audits/valid-source-maps.js | description": {"message": "సోర్స్ మ్యాప్‌లు, కనిష్ఠీకరించిన కోడ్‌ను ఒరిజినల్ సోర్స్ కోడ్‌కు అనువదిస్తాయి. ఇది డెవలపర్‌లకు ప్రొడక్షన్‌లో డీబగ్ చేయడంలో సహాయపడుతుంది. అదనంగా, Lighthouse మరిన్ని గణాంకాలను అందించగలదు. ఈ ప్రయోజనాలను సద్వినియోగం చేసుకోవడానికి, సోర్స్ మ్యాప్‌లను అమలు చేయడాన్ని పరిశీలించండి. [సోర్స్ మ్యాప్‌ల గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "పెద్ద ఫస్ట్ పార్టీ JavaScript కోసం సోర్స్ మ్యాప్‌లు కనుగొనబడలేదు"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "పెద్ద JavaScript ఫైల్‌లో సోర్స్ మ్యాప్ లేదు"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{హెచ్చరిక: `.sourcesContent`లో 1 ఐటెమ్ లేదు}other{హెచ్చరిక: `.sourcesContent`లో # ఐటెమ్‌లు లేవు}}"}, "core/audits/valid-source-maps.js | title": {"message": "పేజీలో చెల్లుబాటు అయ్యే సోర్స్ మ్యాప్‌లు ఉన్నాయి"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">`, మీ యాప్‌ను మొబైల్ స్క్రీన్ సైజులకు తగ్గట్టుగా ఆప్టిమైజ్ చేయడమే కాకుండా, [యూజర్ ఇన్‌పుట్‌లో 300 మిల్లీసెకన్ల ఆలస్యాన్ని](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) కూడా నివారిస్తుంది. [వ్యూపోర్ట్ మెటా ట్యాగ్‌ను ఉపయోగించడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "'`<meta name=\"viewport\">`' ట్యాగ్ ఏదీ కనుగొనబడలేదు"}, "core/audits/viewport.js | failureTitle": {"message": "'`width`' లేదా '`initial-scale`'తో '`<meta name=\"viewport\">`' 'ట్యాగ్ ఏదీ లేదు"}, "core/audits/viewport.js | title": {"message": "'`width`' లేదా '`initial-scale`'తో '`<meta name=\"viewport\">`' ట్యాగ్‌ను కలిగి ఉంది"}, "core/audits/work-during-interaction.js | description": {"message": "ఇది Interaction to Next Paint కొలత సమయంలో జరిగే థ్రెడ్-బ్లాకింగ్ పని. [Interaction to Next Paint కొలమానం గురించి మరింత తెలుసుకోండి](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "'{interactionType}' ఈవెంట్‌కు {timeInMs, number, milliseconds} మిల్లీసెకన్ల సమయం పట్టింది"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "ఈవెంట్ టార్గెట్"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "కీలక ఇంటరాక్షన్ సమయంలో పనిని వీలైనంతగా తగ్గించండి"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "ఇన్‌పుట్ ఆలస్యం"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "ప్రెజెంటేషన్‌లో ఆలస్యం"}, "core/audits/work-during-interaction.js | processingTime": {"message": "ప్రాసెసింగ్ సమయం"}, "core/audits/work-during-interaction.js | title": {"message": "కీలక ఇంటరాక్షన్ సమయంలో పనిని వీలైనంతగా తగ్గిస్తుంది"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "మీ అప్లికేషన్‌లో ARIA వినియోగాన్ని మెరుగుపరచాడానికి ఇవి అవకాశాలుగా ఉపయోగపడతాయి, ఇది స్క్రీన్ రీడర్ లాంటి సహాయక సాంకేతిక పరిజ్ఞానం ఉపయోగించే వినియోగదారులకు మెరుగైన అనుభవాన్ని అందించవచ్చు."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "ఇవి, ఆడియో మరియు వీడియో కోసం ప్రత్యామ్నాయ వచనాన్ని అందించ‌గ‌ల అవకాశాలు. వినికిడి లేదా కంటిచూపులో సమస్యలు ఉన్న వినియోగదారులకు ఇది మెరుగైన అనుభవాన్ని అందించగలదు."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "ఆడియో మరియు వీడియో"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "సాధారణ యాక్సెసిబిలిటీ ఉత్తమ అభ్యాసాలను ఈ అంశాలు హైలైట్ చేస్తాయి."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "ఉత్తమ అభ్యాసాలు"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "[మీ వెబ్ యాప్ యొక్క యాక్సెసిబిలిటీని మెరుగుపరచగల](https://developer.chrome.com/docs/lighthouse/accessibility/) అవకాశాలను ఈ తనిఖీలు హైలైట్ చేస్తాయి. యాక్సెసిబిలిటీ సమస్యలలోని ఒక సబ్‌సెట్‌ను మాత్రమే ఆటోమేటిక్‌గా గుర్తించడం సాధ్యపడుతుంది, కనుక మాన్యువల్ పరీక్ష కూడా చేయాల్సిందిగా సిఫార్సు చేస్తున్నాము."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "ఆటోమేటెడ్ పరీక్ష సాధనం కవర్ చేయని ప్రాంతాలను ఈ అంశాలు పేర్కొంటాయి. [యాక్సెసిబిలిటీ రివ్యూను నిర్వహించడం](https://web.dev/how-to-review/) గురించి మా గైడ్‌లో మరింత తెలుసుకోండి."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "యాక్సెసిబిలిటీ"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "మీ కంటెంట్ స్పష్టతను మెరుగుపరచడానికి ఇవి అవకాశాలుగా ఉపయోగపడతాయి."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "కాంట్రాస్ట్"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "వివిధ లొకేల్‌లలో వినియోగదారుల ద్వారా మీ కంటెంట్ భావ వ్యక్తీకరణను మెరుగుపరచడానికి ఇవి అవకాశాలుగా ఉపయోగపడతాయి."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "అంతర్జాతీయీకరణ మరియు స్థానికీకరణ"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "మీ అప్లికేషన్‌లోని నియంత్రణల అర్థ విచారాలను మెరుగుపరచడానికి ఇవి అవకాశాలుగా ఉపయోగపడతాయి. స్క్రీన్ రీడర్ లాంటి సహాయక సాంకేతిక పరిజ్ఞాన వినియోగదారులకు ఇది మరింత మెరుగైన అనుభవాన్ని అందించవచ్చు."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "పేరు మరియు లేబుళ్లు"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "ఇవి, మీ అప్లికేషన్‌లో కీబోర్డ్ నావిగేషన్‌ను మెరుగ‌ప‌ర‌చ‌గ‌ల అవ‌కాశాలు."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "నావిగేషన్"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "ఇవి, స్క్రీన్ రీడర్ లాంటి సహాయకరమైన సాంకేతికతను ఉపయోగించి పట్టికను లేదా లిస్ట్ డేటాను చదువుతున్నప్పుడు మెరుగైన అనుభవాన్ని అందించగల అవకాశాలు."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "పట్టికలు మరియు లిస్ట్‌లు"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "బ్రౌజర్ అనుకూలత"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "ఉత్తమ అభ్యాసాలు"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "సాధారణం"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "నమ్మకం, భద్రత"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "యూజర్ అనుభవం"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "పనితీరు బడ్జెట్‌లు అనేవి మీ సైట్ యొక్క పనితీరు ప్రమాణాలను నిర్దేశిస్తాయి."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "బడ్జెట్‌లు"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "మీ అప్లికేషన్ పనితీరు గురించి మరింత సమాచారం. ఈ సంఖ్యలు పనితీరు స్కోర్‌ను [నేరుగా ప్రభావితం చేయవు](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "సమస్య విశ్లేషణ"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "పిక్సెల్‌లు ఎంత వేగంగా స్క్రీన్ పై ప్రదర్శింపబడతాయి అనేది పనితీరులో అతి క్లష్టమైన అంశం. కీలక గణంకాలు: మొదటి కంటెంట్ సహిత పెయింట్, మొదటి అర్ధవంతమైన పెయింట్"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "మొదటి పెయింట్ మెరుగుదలలు"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "మీ పేజీని వేగంగా లోడ్ చేయడంలో ఈ సూచనలు సహాయపడగలవు. అవి పనితీరు స్కోర్‌పై [నేరుగా ప్రభావం](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) చూపవు."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "అవకాశాలు"}, "core/config/default-config.js | metricGroupTitle": {"message": "గణాంకాలు"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "పేజీ ఎంత త్వరగా వీలైతే అంత త్వరగా ప్రతిస్పందించి, ఉపయోగించడానికి సిద్దంగా ఉండడానికి, మొత్తం లోడింగ్ అనుభవాన్ని మెరుగుపరచండి. కీలక గణాంకాలు: పేజీలో పూర్తి పరస్పర చర్యకు పట్టే సమయం, వేగం సూచిక"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "మొత్తం మొరుగుదలలు"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "పనితీరు"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "ఈ చెక్‌ల ద్వారా ప్రోగ్రెసివ్ వెబ్ యాప్ అంశాలు ధృవీకరించబడతాయి. [మంచి ప్రోగ్రెసివ్ వెబ్ యాప్‌ను ఏమి చేస్తుందో తెలుసుకోండి](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "మౌళికమైన [PWA తనిఖీ లిస్ట్‌](https://web.dev/pwa-checklist/) ప్రకారం ఈ తనిఖీలను తప్పనిసరిగా నిర్వహించాలి, కానీ ఇవి Lighthouse ద్వారా ఆటోమేటిక్‌గా తనిఖీ చేయబడవు. ఇవి మీ స్కోర్‌పై ప్రభావం చూపవు, కానీ మీరు వీటిని మాన్యువల్‌గా అయినా ధృవీకరించడం ముఖ్యం."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "ఇన్‌స్టాల్ చేయదగినవి"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA ఆప్టిమైజ్ చేసినవి"}, "core/config/default-config.js | seoCategoryDescription": {"message": "మీ పేజీ ప్రాథమిక సెర్చ్ ఇంజిన్ ఆప్టిమైజేషన్ సలహాను ఫాలో అవుతుందా లేదా అని ఈ తనిఖీలు నిర్ధారిస్తాయి. [ప్రధాన వెబ్ కొలమానాలకు](https://web.dev/learn-core-web-vitals/) సంబంధించిన పనితీరుతో సహా, మీ సెర్చ్ ర్యాంకింగ్‌పై ప్రభావం చూపగల, Lighthouse పరిగణనలోకి తీసుకోని అదనపు అంశాలు చాలా ఉన్నాయి. [Google Search Essentials గురించి మరింత తెలుసుకోండి](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "అదనపు SEO ఉత్తమ అభ్యాసాలను చెక్ చేయడం కోసం మీ సైట్‌లో అదనపు వాలిడేటర్‌లను అమలు చేయండి."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "మీ యాప్ యొక్క కంటెంట్‌ను బాగా అర్థం చేసుకోవడానికి, crawlerలు ప్రారంభయ్యే విధంగా మీ HTMLను ఫార్మాట్ చేయండి."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "కంటెంట్ ఉత్తమ అభ్యాసాలు"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "సెర్చ్ ఫలితాలలో కనిపించేందుకు, crawlerలకు మీ యాప్ యాక్సెస్ అవసరం."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "క్రాలింగ్ మరియు అనుక్రమణ"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "యూజర్‌లు కంటెంట్ పేజీలను చదవడం కోసం స్క్రీన్‌పై రెండు వేళ్లను ఉంచి దగ్గరకు లేదా దూరానికి లాగుతూ లేదా దగ్గరగా జూమ్ చేసి ఇబ్బందిపడేలా కాకుండా మీ పేజీలు మొబైల్ ఫ్రెండ్లీగా ఉన్నాయని నిర్ధారించుకోండి. [పేజీలను మొబైల్-ఫ్రెండ్లీగా చేయడం ఎలాగో తెలుసుకోండి](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "మొబైల్-అనుకూలం"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "పరీక్షించిన పరికరం Lighthouse ఆశించే దాని కంటే తక్కువ CPUని కలిగి ఉన్నట్లుంది. మీ పనితీరు స్కోర్‌పై ఇది ప్రతికూల ప్రభావం చూపగలదు. [సముచిత CPU నెమ్మది చలనం మల్టీప్లయర్‌ను క్యాలిబ్రేట్ చేయడం](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling) గురించి మరింత తెలుసుకోండి."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "మీ పరీక్ష URL ({requested})ను {final}కు మళ్లించినందున, పేజీ అనుకున్న విధంగా లోడ్ కాలేకపోతుండవచ్చు. నేరుగా రెండవ URLను పరీక్షించడం ట్రై చేయండి."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "సమయ పరిమితి లోపల పూర్తి కావడానికి పేజీ మరీ నిదానంగా లోడ్ అయ్యింది. ఫలితాలు సంపూర్ణంగా ఉండకపోవచ్చు."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "బ్రౌజర్ కాష్‌ను క్లియర్ చేస్తున్నప్పుడు, సమయం ముగిసింది. ఈ పేజీని మళ్లీ ఆడిట్ చేయడానికి ట్రై చేసి, ఇంకా సమస్య ఉంటే బగ్‌ను ఫైల్ చేయండి."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{ఈ లొకేషన్‌లో 'లోడింగ్ పనితీరు'ను ప్రభావితం చేసే స్టోర్డ్ డేటా ఉండవచ్చు: {locations}. మీ స్కోర్‌లను ఆ రిసోర్స్‌లు ప్రభావితం చేయకుండా నివారించడానికి అజ్ఞాత విండోలో ఈ పేజీని ఆడిట్ చేయండి.}other{ఈ లొకేషన్‌లలో ఉండే స్టోర్డ్ డేటా వల్ల 'లోడింగ్ పనితీరు' ప్రభావితం కావచ్చు: {locations}. మీ స్కోర్‌లను ఆ రిసోర్స్‌లు ప్రభావితం చేయకుండా నివారించడానికి అజ్ఞాత విండోలో ఈ పేజీని ఆడిట్ చేయండి.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "ఆరిజిన్ డేటాను క్లియర్ చేస్తున్నప్పుడు, సమయం ముగిసింది. ఈ పేజీని మళ్లీ ఆడిట్ చేయడానికి ట్రై చేసి, ఇంకా సమస్య ఉంటే బగ్‌ను ఫైల్ చేయండి."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "GET రిక్వెస్ట్ ద్వారా లోడ్ చేయబడిన పేజీలకు మాత్రమే వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత ఉంది."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "2XX స్టేటస్ కోడ్‌ను కలిగి ఉన్న పేజీలు మాత్రమే కాష్ చేయబడతాయి."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "కాష్‌లో ఉన్నప్పుడు JavaScriptను ఎగ్జిక్యూట్ చేయడానికి చేసిన ప్రయత్నాన్ని Chrome గుర్తించింది."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "AppBanner కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "ఫ్లాగ్‌లు వెనుకకు/ముందుకు కాష్‌ను డిజేబుల్ చేశాయి. chrome://flags/#back-forward-cache‌కు వెళ్లి, దాన్ని ఈ పరికరంలో లోకల్‌గా ఎనేబుల్ చేయండి."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "కమాండ్ లైన్ వెనుకకు/ముందుకు కాష్‌ను డిజేబుల్ చేసింది."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "తగినంత మెమరీ లేనందున వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "వెనుకకు/ముందుకు కాష్‌ను డెలిగేట్ సపోర్ట్ చేయదు."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "ప్రీరెండరర్ కోసం వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "రిజిస్టర్ అయిన లిజనర్‌లతో ఈ పేజీ BroadcastChannel సందర్భాన్ని కలిగి ఉంది కనుక, ఈ పేజీని కాష్ చేయడం సాధ్యం కాదు."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "cache-control:no-store హెడర్‌ను కలిగి ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "కాష్ ఉద్దేశపూర్వకంగా క్లియర్ చేయబడింది."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "మరొక పేజీని కాష్ చేయడానికి ఈ పేజీ కాష్ నుండి తీసివేయబడింది."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "ప్లగ్ఇన్‌లను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcherను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు మీడియా ప్లేయర్ ప్లే అవుతోంది."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession APIని ఉపయోగించి, ప్లేబ్యాక్ స్టేటస్‌ను సెట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession APIని ఉపయోగించే, యాక్షన్ హ్యాండ్లర్‌లను సెట్ చేసే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "స్క్రీన్ రీడర్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "SecurityHandlerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthetication APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB APIని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి అర్హత లేదు."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "ప్రత్యేకమైన వర్కర్ లేదా వర్క్‌లెట్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "డాక్యుమెంట్ లోడ్ అవ్వడం పూర్తి కాకుండానే పేజీ నుండి నిష్క్రమించారు."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు యాప్ బ్యానర్ రన్ అవుతూ ఉంది."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు Chrome Password Manager రన్ అవుతూ ఉండిపోయింది."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు DOM డిస్టిలేషన్ ప్రోగ్రెస్‌లో ఉంది."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు DOM Distiller వ్యూయర్ రన్ అవుతూ ఉండిపోయింది."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "మెసేజింగ్ APIని ఉపయోగించే ఎక్స్‌టెన్షన్‌ల కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "దీర్ఘకాలిక కనెక్షన్‌ను కలిగి ఉన్న ఎక్స్‌టెన్షన్‌లు, వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ముందు కనెక్షన్‌ను నిలిపివేయాలి."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "దీర్ఘకాలిక కనెక్షన్‌ను కలిగి ఉన్న ఎక్స్‌టెన్షన్‌లు వెనుకకు/ముందుకు కాష్‌లో ఫ్రేమ్‌లకు మెసేజ్‌లను పంపే ప్రయత్నం చేశాయి."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "ఎక్స్‌టెన్షన్‌ల కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు ఫారమ్ తిరిగి సమర్పించడానికి సంబంధించిన సమాచారం లేదా HTTP పాస్‌వర్డ్ డైలాగ్ వంటి మోడల్ డైలాగ్ కనిపించింది."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు ఆఫ్‌లైన్ పేజీ చూపబడింది."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు మెమరీ నిండింది అని చూపే ఇంటర్వెన్షన్ బార్ రన్ అవుతూ ఉంది."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు అనుమతికి సంబంధించిన రిక్వెస్ట్‌లు ఎదురయ్యాయి."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు పాప్‌అప్ బ్లాకర్ రన్ అవుతూ ఉంది."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు సురక్షిత బ్రౌజింగ్ వివరాలు చూపబడ్డాయి."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "సురక్షిత బ్రౌజింగ్, ఈ పేజీ దుర్వినియోగమైనదిగా గుర్తించి, పాప్‌అప్‌ను బ్లాక్ చేసింది."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌లో ఉన్నప్పుడు ఒక సర్వీస్ వర్కర్ యాక్టివేట్ చేయబడింది."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "డాక్యుమెంట్‌లో ఏర్పడిన ఎర్రర్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "FencedFramesను ఉపయోగిస్తున్న పేజీలను వెనుకకు/ముందుకు కాష్‌లో స్టోర్ చేయడం సాధ్యం కాదు."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "మరొక పేజీని కాష్ చేయడానికి ఈ పేజీ కాష్ నుండి తీసివేయబడింది."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "మీడియా స్ట్రీమ్ యాక్సెస్‌ను మంజూరు చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "పోర్టల్స్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "IdleManagerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "తెరిచి ఉన్న IndexedDB కనెక్షన్ ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "అర్హత లేని APIలు ఉపయోగించబడ్డాయి."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "ఎక్స్‌టెన్షన్‌ల ద్వారా JavaScript ఇంజెక్ట్ చేయబడిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "ఎక్స్‌టెన్షన్‌ల ద్వారా ఇంజెక్ట్ చేయబడిన స్టయిల్‌షీట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | internalError": {"message": "అంతర్గత ఎర్రర్."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "యాక్టివ్‌గా ఉంచమని అందిన రిక్వెస్ట్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "కీబోర్డ్ లాక్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | loading": {"message": "పేజీ లోడ్ అవ్వడం పూర్తి కాకుండానే పేజీ నుండి నిష్క్రమించారు."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "cache-control:no-cache ప్రధాన రిసోర్స్‌గా ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "ఏ పేజీల ప్రధాన రిసోర్స్ అయితే cache-control:no-storeను కలిగి ఉన్నాయో, అవి వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "వెనుకకు/ముందుకు కాష్ నుండి పేజీని రీస్టోర్ చేయడానికి ముందే నావిగేషన్ రద్దు చేయబడింది."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "కాష్ నుండి ఈ పేజీ తీసివేయబడింది, ఎందుకంటే ప్రస్తుతం ఉన్న నెట్‌వర్క్ కనెక్షన్ చాలా ఎక్కువ డేటాను అందుకుంది. ఏదైనా పేజీ కాష్ చేయబడుతున్నప్పుడు ఆ పేజీ అందుకునే డేటా మొత్తాన్ని Chrome పరిమితం చేస్తుంది."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ఇన్‌ఫ్లయిట్ ఫెచ్() లేదా XHR ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "వెనుకకు/ముందుకు కాష్ నుండి ఈ పేజీ తీసివేయబడింది, ఎందుకంటే ప్రస్తుతం ఉన్న నెట్‌వర్క్‌లో మళ్లింపు ఉంది."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "కాష్ నుండి ఈ పేజీ తీసివేయబడింది, ఎందుకంటే నెట్‌వర్క్ కనెక్షన్ చాలా సేపు తెరిచి ఉంది. పేజీ కాష్ అయినప్పుడు, డేటాను పొందే సమయాన్ని Chrome పరిమితం చేస్తుంది."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "చెల్లుబాటు అయ్యే ప్రతిస్పందన హెడర్ లేని పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "నావిగేషన్ ప్రధాన ఫ్రేమ్‌లో కాకుండా వేరొక ఫ్రేమ్‌లో జరిగింది."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "పేజీలో, ఇండెక్స్ చేయబడిన DB లావాదేవీలు జరుగుతూ ఉంటే, వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి దానికి ప్రస్తుతం అర్హత ఉండదు."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "ఇన్-ఫ్లయిట్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "ఇన్-ఫ్లయిట్ ఫెచ్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "ఇన్-ఫ్లయిట్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "ఇన్-ఫ్లయిట్ XHR నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "PaymentManagerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "పిక్చర్-ఇన్-పిక్చర్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | portal": {"message": "పోర్టల్స్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | printing": {"message": "ప్రింటింగ్ UIని చూపించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "ఈ పేజీ '`window.open()`'ను ఉపయోగించి తెరవబడింది, అలాగే ఇది వేరొక ట్యాబ్ రెఫరెన్స్‌ను కలిగి ఉంది, లేదా ఆ పేజీ విండోను తెరిచింది."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "వెనుకకు/ముందుకు కాష్‌లో పేజీకి సంబంధించిన రెండరర్ ప్రాసెస్ క్రాష్ అయింది."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "వెనుకకు/ముందుకు కాష్‌లోని పేజీకి సంబంధించిన రెండరర్ ప్రాసెస్ ఆపివేయబడింది."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "ఆడియో క్యాప్చర్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "సెన్సార్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "బ్యాక్‌గ్రౌండ్ సింక్ కోసం లేదా బ్యాక్‌గ్రౌండ్‌లో అనుమతులను పొందడానికి రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "MIDI అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "నోటిఫికేషన్‌ల అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "స్టోరేజ్ యాక్సెస్ కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "వీడియో క్యాప్చర్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPSను URL స్కీమ్‌గా కలిగి ఉన్న పేజీలు మాత్రమే కాష్ చేయబడతాయి."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "ఈ పేజీని వెనుకకు/ముందుకు కాష్ చేస్తున్నప్పుడు సర్వీస్ వర్కర్ ద్వారా పేజీ వినియోగించబడింది."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "సర్వీస్ వర్కర్, వెనుకకు/ముందుకు కాష్‌లో ఉన్న పేజీకి `MessageEvent`‌ను పంపడానికి ప్రయత్నించింది."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌లో ఉన్నప్పుడు ServiceWorker అన్‌రిజిస్టర్ చేయబడింది."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "సర్వీస్ వర్కర్‌కు సంబంధించిన యాక్టివేషన్ కారణంగా పేజీ వెనుకకు/ముందుకు కాష్ నుండి తీసివేయబడింది."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome రీస్టార్ట్ చేయబడింది, ఇంకా అది వెనుకకు/ముందుకు కాష్ ఎంట్రీలను క్లియర్ చేసింది."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "SharedWorkerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecognizerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesisను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "పేజీలోని iframe నావిగేషన్‌ను ప్రారంభించింది, కానీ అది పూర్తి కాలేదు."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "పేజీలు వేటి సబ్‌రిసోర్స్ అయితే cache-control:no-cacheను కలిగి ఉన్నాయో, అవి వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "పేజీలు వేటి సబ్‌రిసోర్స్ అయితే cache-control:no-storeను కలిగి ఉన్నాయో, అవి వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | timeout": {"message": "ఈ పేజీ వెనుకకు/ముందుకు కాష్‌లో గరిష్ఠ సమయాన్ని మించిపోయింది, అలాగే దాని గడువు ముగిసింది."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేస్తున్నప్పుడు సమయం ముగిసింది (pagehide హ్యాండ్లర్‌లు ఎక్కువ సేపు రన్ అయిన కారణంగా ఇలా జరిగి ఉండవచ్చు)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "ఈ పేజీ, ప్రధాన ఫ్రేమ్‌లో అన్‌లోడ్ హ్యాండ్లర్‌ను కలిగి ఉంది."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "ఈ పేజీ సబ్ ఫ్రేమ్‌లో అన్‌లోడ్ హ్యాండ్లర్ ఉంది."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "బ్రౌజర్, వెబ్‌సైట్ యాక్సెస్ సాధనం ఓవర్‌రైడ్ హెడర్‌ను మార్చింది."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "వీడియో లేదా ఆడియోను రికార్డ్ చేయడానికి యాక్సెస్‌ను మంజూరు చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabaseను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHIDని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLockలను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfcని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPServiceను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "WebRTCని కలిగిన పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebShareను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "WebSocket ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "WebTransportను కలిగి ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXRను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "పాత బ్రౌజర్‌లతో అనుకూలంగా ఉండటానికి https:, http: URL స్కీమ్ ('strict-dynamic'ను సపోర్ట్ చేసే బ్రౌజర్‌ల ద్వారా విస్మరించబడినవి)లను జోడించడాన్ని పరిగణించండి."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "CSP3 నుండి disown-opener విస్మరించబడింది. దయచేసి, బదులుగా Cross-Origin-Opener-Policy హెడర్‌ను ఉపయోగించండి."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "CSP2 నుండి referrer విస్మరించబడింది. దయచేసి, బదులుగా Referrer-Policy హెడర్‌ను ఉపయోగించండి."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "CSP2 నుండి reflected-xss విస్మరించబడింది. దయచేసి, బదులుగా X-XSS-Protection హెడర్‌ను ఉపయోగించండి."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "దాడి చేసే వారి ద్వారా కంట్రోల్ చేయబడే డొమైన్‌కు అన్ని సంబంధిత URLల (ఉదా. స్క్రిప్ట్‌లు) కోసం బేస్ URLను సెట్ చేయడానికి, పంపబడిన <base> ట్యాగ్‌లను మిస్ అయిన base-uri అనుమతిస్తుంది. base-uriని 'none' లేదా 'self'కు సెట్ చేయడాన్ని పరిగణించండి."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "అందుబాటులో లేని object-src, సురక్షితం కాని స్క్రిప్ట్‌లను అమలు చేసే ప్లగ్ఇన్‌ల ఇంజక్షన్‌ను అనుమతిస్తుంది. object-srcను 'none'కు సెట్ చేయడాన్ని పరిగణించండి."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src డైరెక్టివ్ మిస్ అయింది. ఇది సురక్షితంకాని స్క్రిప్ట్‌ల అమలును అనుమతిస్తుంది."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "మీరు సెమీకోలన్‌ను మర్చిపోయారా? {keyword} కీవర్డ్ కాదు, డైరెక్టివ్ అనిపిస్తుంది."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces base64 charsetను ఉపయోగించాలి."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces కనీసం 8 అక్షరాల పొడవు ఉండాలి."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "ఈ డైరెక్టివ్‌లో సాధారణ URL స్కీమ్‌లను ({keyword}) ఉపయోగించడం మానేయండి. సాధారణ URL స్కీమ్‌లు, సురక్షితం కాని డొమైన్ నుండి సేకరించిన స్క్రిప్ట్‌లను అనుమతిస్తాయి."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "ఈ డైరెక్టివ్‌లో సాధారణ వైల్డ్‌కార్డ్‌లను ({keyword}) ఉపయోగించడం మానేయండి. సాధారణ వైల్డ్‌కార్డ్‌లు, సురక్షితం కాని డొమైన్ నుండి సేకరించిన స్క్రిప్ట్‌లను అనుమతిస్తాయి."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "రిపోర్ట్ చేసే గమ్యస్థానం report-to డైరెక్టివ్ ద్వారా మాత్రమే కాన్ఫిగర్ చేయబడుతుంది. ఈ డైరెక్టివ్ Chromium-ఆధారిత బ్రౌజర్‌లలో మాత్రమే సపోర్ట్ చేస్తుంది, కాబట్టి report-uri డైరెక్టివ్‌ను కూడా ఉపయోగించమని సిఫార్సు చేయబడింది."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "ఎలాంటి CSP రిపోర్ట్ చేసే గమ్యస్థానాన్ని కాన్ఫిగర్ చేయలేదు. ఇది కాలానుగుణంగా CSPని నిర్వహించడం, అలాగే ఏదైనా విచ్ఛిన్నాల కోసం పర్యవేక్షించడం కష్టతరం చేస్తుంది."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "హోస్ట్ వైట్‌లిస్ట్‌లు తరచుగా బైపాస్ చేయబడవచ్చు. అవసరమైతే 'strict-dynamic'తో పాటు CSP nonces లేదా hashesను ఉపయోగించడాన్ని పరిగణించండి."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "తెలియని CSP డైరెక్టివ్."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} చెల్లని కీవర్డ్‌లాగా అనిపిస్తుంది."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "'unsafe-inline' సురక్షితంకాని unsafe in-page స్క్రిప్ట్‌లు, అలాగే ఈవెంట్ హ్యాండ్లర్‌ల అమలును అనుమతిస్తుంది. స్క్రిప్ట్‌లను వ్యక్తిగతంగా అనుమతించేందుకు CSP nonces లేదా hashesను ఉపయోగించడాన్ని పరిగణించండి."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "పాత బ్రౌజర్‌లతో అనుకూలంగా ఉండటానికి 'unsafe-inline' (nonces/hashes సపోర్ట్ చేసే బ్రౌజర్‌ల ద్వారా విస్మరించబడినవి) జోడించడాన్ని పరిగణించండి."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "CORS `Access-Control-Allow-Headers` హ్యాండ్లింగ్‌లో ప్రామాణీకరణ అనేది వైల్డ్ కార్డ్ చిహ్నం (*) ద్వారా కవర్ కాదు."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "ఏ రిసోర్స్ రిక్వెస్ట్‌ల URLలలో అయితే తీసివేయబడిన వైట్‌స్పేస్ `(n|r|t)` అక్షరాలతో పాటు 'దీని కంటే తక్కువ' అక్షరాలు (`<`) కూడా ఉంటాయో, అవి బ్లాక్ చేయబడ్డాయి. ఈ రిసోర్స్‌లను లోడ్ చేయడానికి, ఎలిమెంట్ లక్షణం విలువల వంటి స్థలాల నుండి దయచేసి కొత్త లైన్‌లను తీసివేయండి, ఇంకా 'దీని కంటే తక్కువ' అక్షరాలను ఎన్‌కోడ్ చేయండి."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` విస్మరించబడింది, దీనికి బదులుగా స్టాండర్డైజ్ చేయబడిన APIని ఉపయోగించండి: నావిగేషన్ టైమింగ్ 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` విస్మరించబడింది, అందుకు బదులుగా స్టాండర్డైజ్ చేయబడిన APIని ఉపయోగించండి: పెయింట్ టైమింగ్."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` విస్మరించబడింది, దీనికి బదులుగా స్టాండర్డైజ్ చేయబడిన APIని ఉపయోగించండి: నావిగేషన్ టైమింగ్ 2లో `nextHopProtocol`."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "`(0|r|n)` అక్షరం ఉన్న కుక్కీలు కుదించబడటానికి బదులుగా తిరస్కరించబడతాయి."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "`document.domain`‌ను సెట్ చేయడం ద్వారా ఒకే ఆరిజిన్ పాలసీని సడలించడం అనేది విస్మరించబడింది, అది ఆటోమేటిక్‌గా డిజేబుల్ చేయబడుతుంది. `document.domain`‌ను సెట్ చేయడం ద్వారా ఎనేబుల్ చేయబడిన క్రాస్-ఆరిజిన్ యాక్సెస్‌కు సంబంధించినది ఈ విస్మరణ హెచ్చరిక."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "క్రాస్ ఆరిజిన్ iframeల నుండి {PH1}‌ను ట్రిగ్గర్ చేయడం విస్మరించబడింది, భవిష్యత్తులో ఈ ఆప్షన్ తీసివేయబడుతుంది."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "ఆటోమేటిక్‌గా సెట్ చేయబడి ఉండే కాస్ట్ ఇంటిగ్రేషన్‌ను డిజేబుల్ చేయడానికి `-internal-media-controls-overlay-cast-button` ఎంపిక సాధనానికి బదులుగా `disableRemotePlayback` లక్షణాన్ని ఉపయోగించాలి."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} విస్మరించబడింది. అందుకు బదులుగా దయచేసి {PH2}‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "విస్మరించబడిన ఫీచర్‌కు సంబంధించిన సమస్యను వివరించే, అనువదించిన మెసేజ్‌కు ఉదాహరణ ఇది."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "`document.domain`‌ను సెట్ చేయడం ద్వారా ఒకే ఆరిజిన్ పాలసీని సడలించడం అనేది విస్మరించబడింది, అది ఆటోమేటిక్‌గా డిజేబుల్ చేయబడుతుంది. ఈ ఫీచర్‌ను ఉపయోగించడం కొనసాగించడానికి, డాక్యుమెంట్, ఫ్రేమ్‌లకు సంబంధించిన HTTP ప్రతిస్పందనతో పాటు `Origin-Agent-Cluster: ?0` హెడర్‌ను పంపడం ద్వారా ఆరిజిన్ ఆధారిత ఏజెంట్ క్లస్టర్‌లకు దయచేసి సమ్మతి నిలిపివేయండి. మరిన్ని వివరాల కోసం https://developer.chrome.com/blog/immutable-document-domain/ లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` విస్మరించబడింది, అలాగే అది తీసివేయబడుతుంది. అందుకు బదులుగా దయచేసి `Event.composedPath()`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` హెడర్ విస్మరించబడింది, అలాగే తీసివేయబడుతుంది. Chromeను ఉపయోగించే విషయంలో, ఏప్రిల్ 30, 2018 తర్వాత జారీ చేయబడిన, పబ్లిక్‌గా విశ్వసనీయమైన సర్టిఫికెట్‌లన్నింటికీ పారదర్శకత సర్టిఫికెట్ అవసరం అవుతుంది."}, "core/lib/deprecations-strings.js | feature": {"message": "మరిన్ని వివరాల కోసం ఫీచర్ స్టేటస్ పేజీని చెక్ చేయండి."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()`, `watchPosition()` ఇకపై సురక్షితం కాని ఆరిజిన్‌లలో పని చేయవు. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు మార్చే అంశాన్ని మీరు పరిశీలించాలి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "సురక్షితం కాని ఆరిజిన్‌లలో `getCurrentPosition()`, `watchPosition()` విస్మరించబడ్డాయి. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు మార్చే అంశాన్ని మీరు పరిశీలించాలి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ఇకపై సురక్షితం కాని ఆరిజిన్‌లలో పని చేయదు. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు మార్చే అంశాన్ని మీరు పరిశీలించాలి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` విస్మరించబడింది. బదులుగా దయచేసి `RTCPeerConnectionIceErrorEvent.address` లేదా `RTCPeerConnectionIceErrorEvent.port`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "`canmakepayment` సర్వీస్ వర్కర్ ఈవెంట్‌లోని వ్యాపారి ఆరిజిన్, ఇంకా ఆర్బిట్రరీ డేటా విస్మరించబడ్డాయి, అవి తీసివేయబడతాయి: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "వెబ్‌సైట్ దాని యూజర్‌లకు ఉన్న ప్రత్యేకమైన నెట్‌వర్క్ స్థానం కారణంగా మాత్రమే అది యాక్సెస్ చేయగల నెట్‌వర్క్ నుండి సబ్‌రిసోర్స్‌ను రిక్వెస్ట్ చేసింది. ఈ రిక్వెస్ట్‌లు పబ్లిక్ కాని పరికరాలను, సర్వర్‌లను ఇంటర్నెట్‌కు బహిర్గతం చేస్తాయి, దీని వలన క్రాస్-సైట్ రిక్వెస్ట్ ఫోర్జరీ (CSRF) దాడి, మరియు/లేదా సమాచారం లీక్ అయ్యే రిస్క్ పెరుగుతుంది. ఈ రిస్క్‌లను తగ్గించడానికి, సురక్షితం కాని కాంటెక్స్ట్‌ల నుండి పబ్లిక్ కాని సబ్‌రిసోర్స్‌లకు చేసిన రిక్వెస్ట్‌లను Chrome విస్మరిస్తుంది, వాటిని బ్లాక్ చేయడం ప్రారంభిస్తుంది."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "`file:` URLల చివర `.css` ఫైల్ ఎక్స్‌టెన్షన్ ఉంటే మినహా, వాటి నుండి CSSను లోడ్ చేయడం సాధ్యపడదు."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "స్పెసిఫికేషన్‌లో మార్పు కారణంగా, `remove()`‌కు సంబంధించిన సింక్రనస్ కాని పరిధి తీసివేతను రద్దు చేయడానికి `SourceBuffer.abort()`‌ను ఉపయోగించడం విస్మరించబడింది. భవిష్యత్తులో సపోర్ట్ తీసివేయబడుతుంది. బదులుగా మీరు `updateend` ఈవెంట్‌ను వినాలి. సింక్రనస్ కాని మీడియా జోడింపును రద్దు చేయడానికి లేదా పార్సర్ స్టేట్‌ను రీసెట్ చేయడానికి మాత్రమే `abort()`‌ను ఉపయోగించవచ్చు."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "స్పెసిఫికేషన్‌లో మార్పు కారణంగా, బఫర్ చేయబడిన కోడెడ్ ఫ్రేమ్‌లలో ఏ ఫ్రేమ్‌కు సంబంధించిన అత్యధిక ప్రెజెంటేషన్ టైమ్ స్టాంప్ కంటే తక్కువకు అయినా `MediaSource.duration`‌ను సెట్ చేయడం అనేది విస్మరించబడింది. కుదించబడిన 'బఫర్ చేయబడిన మీడియా'ను పరోక్షంగా తీసివేయడానికి సపోర్ట్ భవిష్యత్తులో తీసివేయబడుతుంది. అందుకు బదులుగా, `newDuration < oldDuration` వద్ద ఉండే `sourceBuffers` అన్నింటిపై మీరు స్పష్టమైన `remove(newDuration, oldDuration)`‌ను అమలు చేయాలి."}, "core/lib/deprecations-strings.js | milestone": {"message": "ఈ మార్పు {milestone} మైల్‌స్టోన్‌తో అమలులోకి వస్తుంది."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "`MIDIOptions`‌లో sysex పేర్కొనబడనప్పటికీ, ఉపయోగించడానికి వెబ్ MIDI అనుమతిని అడుగుతుంది."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "నోటిఫికేషన్ APIని ఇకపై సురక్షితం కాని ఆరిజిన్‌ల నుండి ఉపయోగించడం కుదరకపోవచ్చు. మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు మార్చే అంశాన్ని మీరు పరిశీలించాలి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "నోటిఫికేషన్ APIకి అనుమతిని ఇకపై క్రాస్-ఆరిజిన్ iframe నుండి రిక్వెస్ట్ చేయడం కుదరకపోవచ్చు. టాప్-లెవెల్ ఫ్రేమ్ నుండి అనుమతిని రిక్వెస్ట్ చేయడాన్ని, లేదా అందుకు బదులుగా కొత్త విండోను తెరవడాన్ని మీరు పరిశీలించాలి."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "మీ పార్ట్‌నర్ వాడుకలో లేని (D)TLS వెర్షన్‌ను నెగోషియేట్ చేస్తున్నారు. దీన్ని పరిష్కరించడం కోసం దయచేసి మీ పార్ట్‌నర్‌ను సంప్రదించండి."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "సురక్షితం కాని సందర్భాలలో WebSQL వినియోగం విస్మరించబడుతుంది, అలాగే ఇది త్వరలోనే తీసివేయబడుతుంది. దయచేసి వెబ్ స్టోరేజ్‌ను లేదా ఇండెక్స్ చేయబడిన డేటాబేస్‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "`overflow: visible`ను img, వీడియో, కాన్వాస్ ట్యాగ్‌లలో పేర్కొనడం వలన అవి ఎలిమెంట్ పరిధికి వెలుపల విజువల్ కంటెంట్‌ను క్రియేట్ చేయవచ్చు. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md‌ని చూడండి."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` విస్మరించబడింది. బదులుగా పేమెంట్ హ్యాండ్లర్‌ల కోసం దయచేసి జస్ట్ ఇన్-టైమ్ ఇన్‌స్టాల్‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "మీ `PaymentRequest` కాల్, కంటెంట్-సెక్యూరిటీ-పాలసీ (CSP) `connect-src` డైరెక్టివ్‌ను బైపాస్ చేసింది. ఈ బైపాస్ విస్మరించబడింది. దయచేసి API `PaymentRequest` (`supportedMethods` ఫీల్డ్‌లో) నుండి CSP `connect-src` డైరెక్టివ్‌కు పేమెంట్ ఆప్షన్ ఐడెంటిఫైయర్‌ను జోడించండి."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` విస్మరించబడింది. బదులుగా దయచేసి స్టాండర్డైజ్ చేయబడిన `navigator.storage`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<picture>` పేరెంట్ గల `<source src>` చెల్లదు, కాబట్టి అది విస్మరించబడింది. అందుకు బదులుగా దయచేసి `<source srcset>`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` విస్మరించబడింది. బదులుగా దయచేసి స్టాండర్డైజ్ చేయబడిన `navigator.storage`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "ఏ సబ్‌రిసోర్స్ రిక్వెస్ట్‌ల URLలలో అయితే పొందుపరిచిన ఆధారాలు (ఉదా. `**********************/`) ఉన్నాయో, అవి బ్లాక్ చేయబడ్డాయి."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "`DtlsSrtpKeyAgreement` పరిమితి తీసివేయబడింది. మీరు ఈ పరిమితికి `false` విలువను పేర్కొన్నారు, అంటే తీసివేసిన `SDES key negotiation` విధానాన్ని మీరు ఉపయోగించడానికి ప్రయత్నించారని అర్ఠం. ఈ ఫంక్షనాలిటీ తీసివేయబడింది; బదులుగా `DTLS key negotiation`‌ను సపోర్ట్ చేసే సర్వీస్‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "`DtlsSrtpKeyAgreement` పరిమితి తీసివేయబడింది. మీరు ఈ పరిమితికి `true` విలువను పేర్కొన్నారు, ఇది ఎటువంటి ప్రభావమూ చూపలేదు, కానీ స్పష్టంగా, సింపుల్‌గా ఉంచటానికి మీరు దీన్ని తీసివేయవచ్చు."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` గుర్తించబడింది. `Session Description Protocol`‌కు సంబంధించిన ఈ వెర్షన్ ఇకపై సపోర్ట్ చేయబడదు. అందుకు బదులుగా దయచేసి `Unified Plan SDP`‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`{sdpSemantics:plan-b}`‌తో `RTCPeerConnection`‌ను క్రియేట్ చేయడానికి ఉపయోగించబడిన `Plan B SDP semantics`, వెబ్ ప్లాట్‌ఫామ్ నుండి శాశ్వతంగా తొలగించబడిన `Session Description Protocol`‌కు లెగసీ నాన్-స్టాండర్డ్ వెర్షన్. `IS_FUCHSIA`‌తో బిల్డ్ చేస్తున్నప్పుడు కూడా ఇది అందుబాటులో ఉంది, కానీ వీలైనంత త్వరగా దీన్ని తొలగించాలనుకుంటున్నాము. దీని మీద ఆధారపడటం ఆపివేయండి. స్టేటస్ కోసం https://crbug.com/1302249 లింక్‌ను రెఫర్ చేయండి."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` ఆప్షన్ విస్మరించబడింది, అలాగే అది తీసివేయబడుతుంది."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer`‌కు క్రాస్-ఆరిజిన్ ఐసోలేషన్ అవసరం అవుతుంది. మరిన్ని వివరాల కోసం https://developer.chrome.com/blog/enabling-shared-array-buffer/ లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "యూజర్ యాక్టివేషన్ లేని `speechSynthesis.speak()` విస్మరించబడింది, అలాగే అది తీసివేయబడుతుంది."}, "core/lib/deprecations-strings.js | title": {"message": "విస్మరించబడిన ఫీచర్ ఉపయోగించబడింది"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "`SharedArrayBuffer`‌ను ఉపయోగించడం కొనసాగించడానికి, ఎక్స్‌టెన్షన్‌లు క్రాస్-ఆరిజిన్ ఐసోలేషన్‌కు సమ్మతించాలి. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ లింక్‌లోని కంటెంట్‌ను చూడండి."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1}, ప్రత్యేకంగా వెండార్‌కు మాత్రమే సంబంధించినది. అందుకు బదులుగా దయచేసి స్టాండర్డ్ {PH2}‌ను ఉపయోగించండి."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "`XMLHttpRequest`‌లో రెస్పాన్స్ json, UTF-16ను సపోర్ట్ చేయదు"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "ప్రధాన థ్రెడ్‌లో సింక్రనస్ `XMLHttpRequest` ఉపయోగం విస్మరించబడింది, ఎందుకంటే ఇది ఎండ్ యూజర్ అనుభవంపై ప్రతికూల ప్రభావం చూపుతోంది. మరింత సహాయం కోసం, https://xhr.spec.whatwg.org/ లింక్‌లోని కంటెంట్‌ను రెఫర్ చేయండి."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` విస్మరించబడింది. దయచేసి `isSessionSupported()`‌ను ఉపయోగించి, బదులుగా పరిష్కరించబడిన బూలియన్ విలువను చెక్ చేయండి."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "ప్రధాన థ్రెడ్ బ్లాక్ చేయబడే సమయం"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "కాష్ TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "వివరణ"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "వ్యవధి"}, "core/lib/i18n/i18n.js | columnElement": {"message": "మూలకం"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "విఫలం అవుతున్న మూలకాలు"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "స్థానం"}, "core/lib/i18n/i18n.js | columnName": {"message": "పేరు"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "బడ్జెట్ దాటిపోయింది"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "రిక్వెస్ట్‌లు"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "రిసోర్స్ సైజ్"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "వనరు రకం"}, "core/lib/i18n/i18n.js | columnSize": {"message": "సైజ్‌"}, "core/lib/i18n/i18n.js | columnSource": {"message": "సోర్స్"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "ప్రారంభ సమయం"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "వెచ్చించిన సమయం"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "బదిలీ సైజ్‌"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "ఆదా చేయగల సైజ్‌"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "ఆదా చేయగల వ్యవధి"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "ఆదా చేయగల సైజ్‌ {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 ఎలిమెంట్ కనుగొనబడింది}other{# ఎలిమెంట్‌లు కనుగొనబడ్డాయి}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "ఆదా చేయగల వ్యవధి {wastedMs, number, milliseconds} మి.సెలు"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "డాక్యుమెంట్‌"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "మొదటి అర్థవంతమైన పెయింట్"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "ఫాంట్"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "చిత్రం"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "అధికం"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "తక్కువ"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "మధ్యస్థం"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "మొదటి ఇన్‌పుట్ ఆలస్య గరిష్ఠ వ్యవధి"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "మీడియా"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} మి.సె"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "ఇతరం"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "ఇతర రిసోర్స్‌లు"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "స్క్రిప్ట్"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} సె"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "స్టైల్‌షీట్"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "మూడవ పక్షం"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "మొత్తం"}, "core/lib/lh-error.js | badTraceRecording": {"message": "మీ పేజీ లోడ్ చేసే సమయంలో స్థితిగతిని రికార్డ్ చేస్తున్నప్పుడు ఏదో తప్పు జరిగింది. దయచేసి Lighthouseని మళ్లీ అమలు చేయండి. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "ప్రారంభ డీబగ్గర్ ప్రోటోకాల్ కనెక్షన్ నిరీక్షణ సమయం గడువు ముగిసింది."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "పేజీ లోడ్ సమయంలో Chrome ఎలాంటి స్క్రీన్‌షాట్‌లను సేకరించలేదు. దయచేసి పేజీలో కనిపించే కంటెంట్ ఉందని నిర్ధారించుకుని, ఆపై Lighthouseని తిరిగి అమలు చేయడం ప్రయత్నించండి. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS సర్వర్‌లు అందించిన డొమైన్‌ని పరిష్కరించలేవు."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "అవసరమైన '{artifactName}' గ్యాదరర్ ఒక ఎర్రర్‌ను ఎదుర్కొంది: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "అంతర్గత Chrome ఎర్రర్ ఏర్పడింది. దయచేసి Chromeని పునఃప్రారంభించి, Lighthouseని తిరిగి అమలు చేయడం ప్రయత్నించండి."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "అవసరమైన '{artifactName}' గ్యాదరర్ అమలు కాలేదు."}, "core/lib/lh-error.js | noFcp": {"message": "పేజీలోని కంటెంట్ దేనికీ పెయింట్ లేదు. బ్రౌజర్ విండోను లోడ్ చేసే సమయంలో దాన్ని తప్పకుండా ముందు భాగంలో ఉంచి, ఆ తర్వాత మళ్లీ ట్రై చేయండి. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "లార్జెస్ట్ కంటెంట్‌ఫుల్ పెయింట్ (LCP)గా అర్హత పొందిన కంటెంట్‌ను పేజీ డిస్‌ప్లే చేయలేదు. పేజీ చెల్లుబాటయ్యే LCP ఎలిమెంట్‌ను కలిగి ఉందని నిర్ధారించుకొని, ఆపై మళ్లీ ట్రై చేయండి. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "అందించబడిన పేజీ HTML కాదు (MIME రకం {mimeType}గా అందించబడింది)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "ఈ Chrome వెర్షన్ చాలా పాతది, కాబట్టి '{featureName}'కు సపోర్ట్ చెయ్యదు. పూర్తి ఫలితాలు చూడడానికి మరింత కొత్త వెర్షన్‌ను ఉపయోగించండి."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "మీరు రిక్వెస్ట్ చేసిన పేజీని Lighthouse విశ్వసనీయ రీతిలో పేజీని లోడ్ చేయలేకపోయింది. మీరు సరైన URLని పరీక్షిస్తున్నారని, సర్వర్ అన్ని రిక్వెస్ట్‌లకు సరిగ్గా ప్రతిస్పందిస్తుందని నిర్ధారించుకోండి."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "పేజీ ప్రతిస్పందించడం ఆపివేసినందున, మీరు రిక్వెస్ట్ చేసిన URLని విశ్వసనీయ రీతిలో Lighthouse లోడ్ చేయలేకపోయింది."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "మీరు అందించిన URLకు చెల్లుబాటయ్యే భద్రత సర్టిఫికెట్ లేదు. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "మధ్యలో వచ్చే సందేశ బ్యానర్‌తో పేజీ లోడ్ కావడాన్ని Chrome నిరోధించింది. మీరు సరైన URLను పరీక్షిస్తున్నారని, సర్వర్ అన్ని రిక్వెస్ట్‌లకు సరిగ్గా ప్రతిస్పందిస్తోందని నిర్ధారించుకోండి."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "మీరు రిక్వెస్ట్ చేసిన పేజీని Lighthouse విశ్వసనీయ రీతిలో లోడ్ చేయలేకపోయింది. మీరు సరైన URLను పరీక్షిస్తున్నారని, సర్వర్ అన్ని రిక్వెస్ట్‌లకు సరిగ్గా ప్రతిస్పందిస్తోందని నిర్ధారించుకోండి. (వివరాలు: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "మీరు రిక్వెస్ట్ చేసిన పేజీని Lighthouse విశ్వసనీయ రీతిలో లోడ్ చేయలేకపోయింది. మీరు సరైన URLను పరీక్షిస్తున్నారని, సర్వర్ అన్ని రిక్వెస్ట్‌లకు సరిగ్గా ప్రతిస్పందిస్తోందని నిర్ధారించుకోండి. (స్థితి కోడ్: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "మీ పేజీ లోడ్ కావడానికి చాలా ఎక్కువ సమయం పట్టింది. మీ పేజీ లోడ్ సమయం తగ్గించడానికి దయచేసి రిపోర్ట్‌లో ఉన్న అవకాశాలను అనుసరించి, ఆపై Lighthouseను తిరిగి అమలు చేయడానికి ప్రయత్నించండి. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools ప్రోటోకాల్ ప్రతిస్పందన కోసం వేచి ఉండటం వలన కేటాయించిన సమయాన్ని దాటిపోయింది. (పద్ధతి: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "వనరు కంటెంట్‌ను పొందడంలో కేటాయించిన సమయం దాటిపోయింది"}, "core/lib/lh-error.js | urlInvalid": {"message": "మీరు అందించిన URL చెల్లనిదిగా కనిపిస్తోంది."}, "core/lib/navigation-error.js | warningXhtml": {"message": "పేజీ MIME రకం XHTML: ఈ డాక్యుమెంట్ రకాన్ని Lighthouse నేరుగా సపోర్ట్ చేయదు"}, "core/user-flow.js | defaultFlowName": {"message": "యూజర్ ఫ్లో ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "నావిగేషన్ రిపోర్ట్ ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "స్నాప్‌షాట్ రిపోర్ట్ ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "కాల వ్యవధి రిపోర్ట్ ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "అన్ని రిపోర్ట్‌లు"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "కేటగిరీలు"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "యాక్సెసిబిలిటీ"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "ఉత్తమ అభ్యాసాలు"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "పనితీరు"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "ప్రోగ్రెసివ్ వెబ్ యాప్"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "డెస్క్‌టాప్"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Lighthouse ఫ్లో రిపోర్ట్‌ను అర్థం చేసుకోవడం"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "ఫ్లో రిపోర్ట్‌లను అర్థం చేసుకోవడం"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "నావిగేషన్ రిపోర్ట్‌లను ఉపయోగించి..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "స్నాప్‌షాట్ రిపోర్ట్‌లను ఉపయోగించి..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "కాలవ్యవధి రిపోర్ట్‌లను ఉపయోగించి..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Lighthouse పనితీరు స్కోర్‌ను పొందండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "కంటెంట్ కలిగి ఉండే అతిపెద్ద పెయింట్, వేగం ఇండెక్స్ వంటి పేజీ లోడ్ పనితీరు కొలమానాలను లెక్కించండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "ప్రోగ్రెసివ్ వెబ్ యాప్ సామర్థ్యాలను అంచనా వేయండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "సింగిల్ పేజీ యాప్‌లు లేదా సంక్లిష్ట ఫారమ్‌లలో ఉన్న యాక్సెసిబిలిటీ సమస్యలను కనుగొనండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "ఇంటరాక్షన్ లోపల ఉన్న మెనూలు, UI ఎలిమెంట్‌ల బెస్ట్ ప్రాక్టీసులను పరిశీలించండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "వివిధ ఇంటరాక్షన్‌లకు సంబంధించిన లేఅవుట్ షిఫ్ట్‌లను, JavaScript అమలయ్యే సమయాన్ని లెక్కించండి."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "దీర్ఘకాలిక పేజీలు, సింగిల్-పేజీ యాప్‌ల అనుభవాన్ని మెరుగుపరచడానికి పనితీరు అవకాశాలను కనుగొనండి."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "అత్యంత ప్రభావవంతమైనవి"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} సమాచారాత్మక ఆడిట్}other{{numInformative} సమాచారాత్మక ఆడిట్‌లు}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "మొబైల్"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "పేజీ లోడ్"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "నావిగేషన్ రిపోర్ట్‌లు, ఒరిజినల్ Lighthouse రిపోర్ట్‌ల మాదిరిగానే సింగిల్ పేజీ లోడ్‌ను విశ్లేషిస్తాయి."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "నావిగేషన్ రిపోర్ట్"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} నావిగేషన్ రిపోర్ట్}other{{numNavigation} నావిగేషన్ రిపోర్ట్‌లు}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{పాస్ అయ్యే అవకాశం ఉన్న {numPassableAudits} ఆడిట్}other{పాస్ అయ్యే అవకాశం ఉన్న {numPassableAudits} ఆడిట్‌లు}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} ఆడిట్ పాస్ అయ్యింది}other{{numPassed} ఆడిట్‌లు పాస్ అయ్యాయి}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "ఓ మోస్తరుగా ఉంది"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "ఎర్రర్"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "బాగా లేదు"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "బాగుంది"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "సేవ్ చేయండి"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "పేజీ తాలూకు క్యాప్చర్ చేయబడిన స్టేట్"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "స్నాప్‌షాట్ రిపోర్ట్‌లు, ఒక నిర్దిష్ట స్థితిలో ఉన్న పేజీని విశ్లేషిస్తాయి, సాధారణంగా ఈ విశ్లేషణ అనేది యూజర్ ఇంటరాక్షన్‌ల తర్వాత జరుగుతుంది."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "స్నాప్‌షాట్ రిపోర్ట్"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} స్నాప్‌షాట్ రిపోర్ట్}other{{numSnapshot} స్నాప్‌షాట్ రిపోర్ట్‌లు}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "సారాంశం"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "యూజర్ ఇంటరాక్షన్‌లు"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "కాలవ్యవధి రిపోర్ట్‌లు ఒక యాదృచ్ఛిక సమయ వ్యవధిని విశ్లేషిస్తాయి, సాధారణంగా ఈ వ్యవధి యూజర్ ఇంటరాక్షన్‌లను కలిగి ఉంటుంది."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "పేజీలో యూజర్ ఇంటరాక్టివిటీకి సంబంధించిన రిపోర్ట్"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} కాలవ్యవధి రిపోర్ట్}other{{numTimespan} కాలవ్యవధి రిపోర్ట్‌లు}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse యూజర్ ఫ్లో రిపోర్ట్"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "యానిమేట్ చేసిన కంటెంట్ కోసం, కంటెంట్ ఆఫ్‌స్క్రీన్‌లో ఉన్నప్పుడు CPU వినియోగాన్ని తగ్గించడానికి, [`amp-anim`](https://amp.dev/documentation/components/amp-anim/)ను ఉపయోగించండి."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "ఇతర బ్రౌజర్‌ల కోసం సముచితంగా ఉండే మునుపటి వాటిని పేర్కొంటున్నప్పుడు, మీ అన్ని [ `amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) కాంపోనెంట్‌లను WebP ఫార్మాట్‌లలో ప్రదర్శించడానికి ప్రయత్నించండి. [మరింత తెలుసుకోండి](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "ఇమేజ్‌లను ఆటోమేటిక్‌గా లేజీ-లోడ్ చేయడానికి, మీరు [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)ను ఉపయోగిస్తున్నారని నిర్ధారించుకోండి. [మరింత తెలుసుకోండి](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[సర్వర్ వైపు అమలు చేసే AMP లేఅవుట్‌ల](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) కోసం [AMP ఆప్టిమైజర్](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) లాంటి సాధనాలను ఉపయోగించండి."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "అన్ని స్టయిల్‌లకు సపోర్ట్ ఉందో లేదో నిర్ధారించుకోవడానికి [AMP డాక్యుమెంటేషన్](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)ను చూడండి."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "స్క్రీన్ సైజ్ ఆధారంగా ఏయే ఇమేజ్ అస్సెట్‌లను ఉపయోగించాలో పేర్కొనడానికి [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) కాంపోనెంట్ [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) లక్షణానికి సపోర్ట్ అందిస్తుంది. [మరింత తెలుసుకోండి](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "చాలా ఎక్కువ లిస్ట్‌లను అమలు చేస్తున్నట్లయితే, కాంపొనెంట్ డెవలపర్ కిట్ (CDK)తో వర్చువల్ స్క్రోలింగ్ చేయడానికి ప్రయత్నించండి. [మరింత తెలుసుకోండి](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "మీ JavaScript బండిల్‌ల సైజ్‌ను తగ్గించడానికి [మూల స్థాయిలో కోడ్ విభజన](https://web.dev/route-level-code-splitting-in-angular/)ను వర్తింపజేయండి. అలాగే, [Angular సర్వీస్ వర్కర్](https://web.dev/precaching-with-the-angular-service-worker/) సహాయంతో, అస్సెట్‌లను ముందుగానే కాష్ చేయడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "మీరు Angular CLIని ఉపయోగిస్తున్నట్లయితే, బిల్డ్‌లను ఉత్పాదన మోడ్‌లో రూపొందించినట్లు నిర్ధారించుకోండి. [మరింత తెలుసుకోండి](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "మీరు Angular CLIను ఉపయోగిస్తున్నట్లయితే, మీ బండిల్‌లను పర్యవేక్షించడానికి మీ ప్రొడక్షన్ బిల్డ్‌లో సోర్స్ మ్యాప్‌లను చేర్చండి. [మరింత తెలుసుకోండి](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "నావిగేషన్ వేగంగా అందించడానికి తగిన సమయం కంటే ముందుగానే మార్గాలను లోడ్ చేస్తుంది. [మరింత తెలుసుకోండి](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "చిత్రం బ్రేక్ పాయింట్‌లను మేనేజ్ చేయడానికి, కాంపొనెంట్ డెవలపర్ కిట్ (CDK)లోని `BreakpointObserver` యుటిలిటీని ఉపయోగించడానికి ప్రయత్నించండి. [మరింత తెలుసుకోండి](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "మీ GIFను HTML5 వీడియోగా పొందుపరచడానికి అందుబాటులో ఉండేలా చేసే సర్వీస్‌కు దానిని అప్‌లోడ్ చేయవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "మీ థీమ్‌లో అనుకూల ఫాంట్‌లను నిర్వచించినప్పుడు `@font-display`ను పేర్కొనండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "మీ సైట్‌లో [ఇమేజ్ స్టయిల్ కన్వర్షన్ పద్దతిలో WebP ఇమేజ్ ఫార్మాట్‌ల](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)ను కాన్ఫిగర్ చేయడాన్ని పరిగణించండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "లేజీ ఇమేజ్‌లను లోడ్ చేయగల [ఒక Drupal మాడ్యూల్](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search)ను ఇన్‌స్టాల్ చేయండి. అలాంటి మాడ్యూల్‌లు, పనితీరును మెరుగుపరచడానికి ఎలాంటి ఆఫ్‌స్క్రీన్ ఇమేజ్‌లను అయినా మినహాయించగల సామర్థ్యాన్ని అందిస్తాయి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "కీలకమైన CSS, JavaSciptను ఇన్‌లైన్‌లో ఉంచడానికి, లేదా [అధునాతన CSS/JS అగ్రిగేషన్](https://www.drupal.org/project/advagg) మాడ్యూల్ లాంటి JavaScript ద్వారా అస్సెట్‌లను అసమకాలికంగా లోడ్ చేసే అవకాశం ఉండటానికి, మాడ్యూల్‌ను ఉపయోగించడానికి ప్రయత్నించండి. ఈ మాడ్యూల్ ద్వారా అందించబడే ఆప్టిమైజేషన్‌లు మీ సైట్‌ను విడగొట్టవచ్చని గుర్తుంచుకోండి, కనుక మీరు కోడ్‌కు మార్పులు చేయాల్సి రావచ్చు."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "థీమ్‌లు, మాడ్యూల్‌లు, సర్వర్ నిర్దేశాలన్నీ కూడా సర్వర్ ప్రతిస్పందన సమయాన్ని ప్రభావితం చేస్తాయి. చాలా జాగ్రత్తగా ఆప్టిమైజేషన్ మాడ్యూల్‌ను ఎంచుకోవడం, మరియు/లేదా మీ సర్వర్‌ను అప్‌గ్రేడ్ చేయడం ద్వారా, మరింత ఆప్టిమైజ్ చేయబడిన థీమ్‌ను కనుగొనడానికి ప్రయత్నించండి. మీ హోస్టింగ్ సర్వర్‌లు Redis లేదా Memcached లాంటి డేటాబేస్ క్వెరీ సమయాలను తగ్గించడానికి PHP opcode కాషింగ్, మెమరీ-కాషింగ్‌లను ఉపయోగించాలి, అలాగే పేజీలను వేగవంతంగా సిద్ధం చేయడానికి ఆప్టిమైజ్ చేయబడిన అప్లికేషన్ లాజిక్‌ను ఉపయోగించాలి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "మీ పేజీలో లోడ్ చేయబడిన ఇమేజ్‌ల సైజ్‌ను తగ్గించడానికి [ప్రతిస్పందనాత్మక ఇమేజ్ స్టయిల్‌ల](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి. మీరు పేజీలో అనేక కంటెంట్ ఐటెమ్‌లను చూపించడానికి వీక్షణలను ఉపయోగిస్తున్నట్లయితే, ఇవ్వబడిన పేజీలో చూపబడే కంటెంట్ ఐటెమ్‌ల సంఖ్యను పరిమితం చేయడానికి పేజీల రూపకల్పనను అమలు చేయవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "మీరు \"సముదాయ CSS ఫైళ్ల\"ను \"అడ్మినిస్ట్రేషన్ » కాన్ఫిగరేషన్ » డెవలప్మెంట్\" పేజీలో ఎనేబుల్ చేశారని నిర్ధారించుకోండి. మీ CSS స్టయిల్‌లను సంగ్రహించడం, సైజు తగ్గించడం ఇంకా కుదించడం ద్వారా మీ సైట్‌ను వేగవంతం చేయడానికి, మీరు [అదనపు మాడ్యూల్‌ల](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) ద్వారా మరింత అధునాతన సముదాయ ఆప్షన్‌లను కూడా కాన్ఫిగర్ చేయవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "మీరు \"సముదాయ JavaScript ఫైల్స్\"ను \"అడ్మినిస్ట్రేషన్ » కాన్ఫిగరేషన్ » డెవలప్మెంట్\" పేజీలో ఎనేబుల్ చేశారని నిర్ధారించుకోండి. మీ JavaScript అస్సెట్‌లను సంగ్రహించడం, సైజు తగ్గించడం ఇంకా కుదించడం ద్వారా మీ సైట్‌ను వేగవంతం చేయడానికి, మీరు [అదనపు మాడ్యూల్‌ల](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) ద్వారా మరింత అధునాతన సముదాయ ఆప్షన్‌లను కూడా కాన్ఫిగర్ చేయవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "ఉపయోగించని CSS నియమాలను తీసివేయవచ్చు, దాన్ని పరిశీలించండి, సంబంధిత పేజీకి లేదా పేజీలోని భాగానికి అవసరమైన Drupal లైబ్రరీలను మాత్రమే అటాచ్ చేయండి. వివరాల కోసం [Drupal డాక్యుమెంటేషన్ లింక్‌](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)ను చూడండి. అసంబద్ధమైన CSSను జోడిస్తున్న 'అటాచ్ చేయబడిన' లైబ్రరీలను గుర్తించడానికి, Chrome DevToolsలో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ని రన్ చేయడానికి ట్రై చేయండి. మీ Drupal సైట్‌లో CSS సముదాయం డిజేబుల్ చేయబడినప్పుడు అందుకు కారణమైన థీమ్/మాడ్యూల్‌ను, స్టయిల్‌షీట్‌కు చెందిన URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్టయిల్‌షీట్‌లను కలిగి ఉన్న థీమ్/మాడ్యూల్‌ల కోసం చూడండి. నిజంగా థీమ్/మాడ్యూల్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్టయిల్‌షీట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "ఉపయోగించబడని JavaScipt అస్సెట్‌లను తీసివేయడానికి ప్రయత్నించండి, సంబంధిత పేజీకి లేదా పేజీలోని కాంపోనెంట్‌కు అవసరమైన Drupal లైబ్రరీలను మాత్రమే అటాచ్ చేయండి. వివరాల కోసం [Drupal డాక్యుమెంటేషన్ లింక్](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)ను చూడండి. అదనపు JavaScriptను జోడించే అటాచ్ చేయబడిన లైబ్రరీలను గుర్తించడానికి, Chrome DevToolsలో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ను రన్ చేయడానికి ట్రై చేయండి. మీ Drupal సైట్‌లో JavaScript అగ్రిగేషన్ డిజేబుల్ చేయబడినప్పుడు, దానికి కారణమైన థీమ్/మాడ్యూల్‌ను, స్క్రిప్ట్‌కు చెందిన URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్క్రిప్ట్‌లను కలిగి ఉన్న థీమ్‌లు/మాడ్యూల్‌ల కోసం చూడండి. నిజంగా థీమ్/మాడ్యూల్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్క్రిప్ట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "\"బ్రౌజర్, ప్రాక్సీ కాష్ గరిష్ఠ వయసు\"ను \"అడ్మినిస్ట్రేషన్ » కాన్ఫిగరేషన్ » డెవలప్మెంట్\" పేజీలో సెట్ చేయండి. [Dr<PERSON>al కాష్, పనితీరు కోసం ఆప్టిమైజ్ చేయడం](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources) గురించి చదవండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "క్వాలిటీని కొనసాగిస్తూ సైట్ ద్వారా అప్‌లోడ్ చేయబడిన ఇమేజ్‌లను ఆటోమేటిక్‌గా ఆప్టిమైజ్ చేసి, సైజ్‌ను తగ్గించే [మాడ్యూల్‌](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి. అలాగే, మీరు సైట్‌లో రెండర్ చేయబడే ఇమేజ్‌లు అన్నింటి కోసం Drupal నుండి అందించబడే (Drupal 8, ఆపైన అందుబాటులో ఉన్నవి) స్థానిక [ప్రతిస్పందనాత్మక ఇమేజ్ స్టయిల్‌ల](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ను ఉపయోగిస్తున్నారని నిర్ధారించుకోండి."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "వెబ్‌సైట్ యాక్సెస్ సాధనం రిసోర్స్ సూచనల కోసం సౌకర్యాలను అందించే [మాడ్యూల్‌](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)ను ఇన్‌స్టాల్ చేసి, కాన్ఫిగ‌ర్ చేయడం ద్వారా ప్రీకనెక్ట్ లేదా dns-ప్రీఫెచ్ రిసోర్స్ సూచనలు జోడించబడవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "మీరు Drupal నుండి అందించబడే (Drupal 8, ఆపైన అందుబాటులో ఉన్నవి) స్థానిక [ప్రతిస్పందనాత్మక ఇమేజ్ స్టయిల్‌ల](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ను ఉపయోగిస్తున్నారని నిర్ధారించుకోండి. వీక్షణ మోడ్‌లు, వీక్షణల ద్వారా ఇమేజ్ ఫీల్డ్‌లను, లేదా WYSIWYG ఎడిటర్ ద్వారా అప్‌లోడ్ చేయబడిన ఇమేజ్‌లను రెండరింగ్ చేస్తున్నప్పుడు ప్రతిస్పందనాత్మక ఇమేజ్ స్టయిల్‌లను ఉపయోగించండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, వెబ్ ఫాంట్‌లు లోడ్ అవుతున్నప్పుడు యూజర్‌కు టెక్స్ట్ కనిపిస్తోందని నిర్ధారించుకోవడానికి `font-display` CSS ఫీచర్‌ను ఆటోమేటిక్‌గా ప్రభావితం చేయడానికి `Optimize Fonts`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ఇమేజ్‌లను WebPగా మార్చడానికి `Next-Gen Formats`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ఆఫ్-స్క్రీన్ ఇమేజ్‌లను అవసరమైనంత వరకు లోడ్ చేయడం మినహాయించడానికి `Lazy Load Images`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ముఖ్యం-కానటువంటి JS/CSSను మినహాయించడానికి `Critical CSS` మరియు `Script Delay`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "ప్రపంచ వ్యాప్తంగా ఉన్న మా కంటెంట్‌ను కాష్ చేయడానికి [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching)‌ను ఉపయోగించండి, తద్వారా మొదటి బైట్‌కు సమయం మెరుగవుతుంది."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, నెట్‌వర్క్ పేలోడ్ సైజ్‌లను తగ్గించడానికి మీ CSS సైజ్‌ను ఆటోమేటిక్‌గా తగ్గించడానికి `Minify CSS`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, నెట్‌వర్క్ పేలోడ్ సైజ్‌లను తగ్గించడానికి ఆటోమేటిక్‌గా మీ JS సైజ్ తగ్గించడానికి `Minify Javascript`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ఈ సమస్యకు సంబంధించి సహాయం చేయడానికి `Remove Unused CSS`‌ను ఎనేబుల్ చేయండి. ఇది మీ సైట్‌లోని ప్రతి పేజీలో వాస్తవంగా ఉపయోగించబడే CSS క్లాస్‌లను గుర్తిస్తుంది, ఫైల్ సైజ్‌ను చిన్నగా ఉంచడానికి ఏవైనా మిగతా వాటిని తీసివేస్తుంది."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, స్టాటిక్ అస్సెస్ట్‌ల కాషింగ్ హెడర్‌లో సిఫార్సు చేయబడిన విలువలను సెటప్ చేయడానికి `Efficient Static Cache Policy`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ఇమేజ్‌లను WebPగా మార్చడానికి `Next-Gen Formats`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, ముఖ్యమైన థర్డ్-పార్టీ ఆరిజిన్‌లకు ముందస్తు కనెక్షన్‌లను ఏర్పరచడానికి `preconnect` రిసోర్స్ హింట్‌లను ఆటోమేటిక్‌గా జోడించడానికి `Pre-Connect Origins`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, తర్వాత పేజీ లోడ్‌లో ప్రస్తుతం రిక్వెస్ట్ చేయబడిన సోర్స్‌లను పొందేలా ప్రాధాన్యతనివ్వడానికి `preload` లింక్‌లను జోడించడానికి `Preload Fonts`, `Preload Background Images`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) ఫ్రేమ్‌వర్క్‌ను ఉపయోగించండి, పరికరానికి తగ్గట్టు ఇమేజ్‌ల సైజ్ మార్చడానికి, నెట్‌వర్క్ పేలోడ్ సైజ్‌లను తగ్గించడానికి `Resize Images`‌ను ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "మీ GIFను HTML5 వీడియోగా పొందుపరచడానికి అందుబాటులో ఉండేలా చేసే సర్వీస్‌కు దానిని అప్‌లోడ్ చేయవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "మీరు అప్‌లోడ్ చేసిన చిత్రాలను అనుకూలమైన ఫార్మాట్‌లలోకి ఆటోమేటిక్‌గా మార్చే [ప్లగ్ఇన్](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) లేదా సేవను ఉపయోగించడం పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "ఏవైనా ఆఫ్‌స్క్రీన్ ఇమేజ్‌లు ఉంటే, వాటిని మినహాయించగల సామర్థ్యాన్ని అందించే లేదా ఆ ఫంక్షనాలిటీని అందించే టెంప్లేట్‌కు స్విచ్ చేయగలిగే [లేజీ-లోడ్ Joomla ప్లగ్ఇన్‌](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading)ను ఇన్‌స్టాల్ చేయండి. Joomla 4.0 నుండి, అన్ని కొత్త ఇమేజ్‌లు ప్రధాన భాగం నుండి [ఆటోమేటిక్‌గా](https://github.com/joomla/joomla-cms/pull/30748) `loading` లక్షణాన్ని పొందుతాయి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "[కీలకమైన అస్సెట్‌లను ఇన్‌లైన్‌లో ఉంచడానికి](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) లేదా [తక్కువ ప్రాముఖ్యత ఉన్న రిసోర్స్‌లను మినహాయించడానికి](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) మీకు సహాయపడగల అనేక Joomla ప్లగ్ఇన్‌లు ఉన్నాయి. ఈ ప్లగ్ఇన్‌ల ద్వారా అందించబడే ఆప్టిమైజేషన్‌లు మీ టెంప్లేట్‌ల లేదా ప్లగ్ఇన్‌ల ఫీచర్‌లను విడగొట్టవచ్చని గుర్తుంచుకోండి, కనుక మీరు వీటిని బాగా పరీక్షించవలసి ఉంటుంది."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "టెంప్లేట్‌లు, ఎక్స్‌టెన్షన్‌లు, సర్వర్ నిర్దేశాలన్నీ సర్వర్ ప్రతిస్పందన సమయాన్ని ప్రభావితం చేస్తాయి. చాలా జాగ్రత్తగా ఆప్టిమైజేషన్ ఎక్స్‌టెన్షన్‌ను ఎంచుకోవడం, మరియు/లేదా మీ సర్వర్‌ను అప్‌గ్రేడ్ చేయడం ద్వారా, మరింత ఆప్టిమైజ్ చేయబడిన టెంప్లేట్‌ను కనుగొనవచ్చు, దాని కోసం ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "ఇవ్వబడిన పేజీలో చూపబడే కథనాల సంఖ్యను తగ్గించడం, మీ పొడవైన పోస్ట్‌లను అనేక పేజీలుగా విడగొట్టడం, లేదా కామెంట్‌లను లేజీ-లోడ్ చేయడానికి ప్లగ్ఇన్‌ను ఉపయోగించడం ద్వారా (ఉదా. మరింత చదవండి లింక్ ద్వారా) మీ కథనం కేటగిరీలలోని సారాంశాలను ప్రదర్శించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "మీ css స్టయిల్‌లను సంగ్రహించడం, సైజు తగ్గించడం ఇంకా కుదించడం ద్వారా అనేక [<PERSON><PERSON><PERSON> ఎక్స్‌టెన్షన్‌లు](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) మీ సైట్‌ను వేగవంతం చేయగలవు. ఈ ఫంక్షనాలిటీని అందించే టెంప్లేట్‌లు కూడా ఉన్నాయి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "అనేక [<PERSON><PERSON><PERSON> ఎక్స్‌టెన్షన్‌లు](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) మీ స్క్రిప్ట్‌లను సంగ్రహించడం, సైజు తగ్గించడం ఇంకా కుదించడం ద్వారా మీ సైట్‌ను వేగవంతం చేయవచ్చు. ఈ ఫంక్షనాలిటీని అందించే టెంప్లేట్‌లు కూడా ఉన్నాయి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "మీ పేజీలో ఉపయోగించని CSSను లోడ్ చేస్తున్న [<PERSON><PERSON><PERSON> ఎక్స్‌టెన్షన్‌ల](https://extensions.joomla.org/) సంఖ్యను తగ్గించడం లేదా స్విచ్ చేయడం చేయవచ్చు, దాన్ని పరిశీలించండి. అసంబద్ధమైన CSSను జోడిస్తున్న ఎక్స్‌టెన్షన్‌లను గుర్తించడానికి, Chrome DevToolsలో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ని రన్ చేయడానికి ట్రై చేయండి. అందుకు కారణమైన థీమ్/ప్లగ్ఇన్‌ను, స్టయిల్‌షీట్‌కు చెందిన URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్టయిల్‌షీట్‌లను కలిగి ఉన్న ప్లగ్ఇన్‌ల కోసం చూడండి. నిజంగా ప్లగ్ఇన్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్టయిల్‌షీట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "మీ పేజీలో ఉపయోగించని JavaScriptను లోడ్ చేస్తున్న [<PERSON><PERSON><PERSON> ఎక్స్‌టెన్షన్‌ల](https://extensions.joomla.org/) సంఖ్యను తగ్గించడం లేదా స్విచ్ చేయవచ్చు, దాన్ని పరిశీలించండి. అసంబద్ధమైన JSను జోడించే ప్లగ్ఇన్‌లను గుర్తించడానికి, Chrome DevToolsలో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ని రన్ చేయడానికి ట్రై చేయండి. అందుకు కారణమైన ఎక్స్‌టెన్షన్‌ను, స్క్రిప్ట్‌కు చెందిన URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్క్రిప్ట్‌లను కలిగి ఉన్న ఎక్స్‌టెన్షన్‌ల కోసం చూడండి. నిజంగా ఎక్స్‌టెన్షన్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్క్రిప్ట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Joomlaలో బ్రౌజర్ కాష్ విధానం](https://docs.joomla.org/Cache) గురించి చదవండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "క్వాలిటీని కొనసాగిస్తూ మీ ఇమేజ్‌లను కుదించే [ఇమేజ్ ఆప్టిమైజేషన్ ప్లగ్ఇన్‌](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "మీ కంటెంట్‌లో ప్రతిస్పందనాత్మక ఇమేజ్‌లను ఉపయోగించడానికి [ప్రతిస్పందనాత్మక ఇమేజ్‌ల ప్లగ్ఇన్‌](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "మీరు Joomlaలో (సిస్టమ్ > గ్లోబల్ కాన్ఫిగరేషన్ > సర్వర్) Gzip పేజీ కుదింపును ఎనేబుల్ చేయడం ద్వారా టెక్స్ట్ కుదింపును ఎనేబుల్ చేయవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "మీ JavaScript అస్సెట్‌లను మీరు బండిల్ చేయనట్లయితే, [బేలర్‌](https://github.com/magento/baler)ను ఉపయోగించడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Magento అంతర్నిర్మిత [JavaScript బండ్లింగ్, కనిష్ఠీకరణ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)ను నిలిపివేసి, దానికి బదులుగా [బేలర్‌](https://github.com/magento/baler/)ను ఉపయోగించడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[అనుకూల ఫాంట్‌లను నిర్వచిస్తున్నప్పుడు](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html), `@font-display`ను పేర్కొనండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "సరికొత్త చిత్ర ఫార్మాట్‌లను అందించే వివిధ రకాల మూడవ పక్ష ఎక్స్‌టెన్షన్‌ల కోసం [Magento మార్కెట్‌ప్లేస్](https://marketplace.magento.com/catalogsearch/result/?q=webp)‌లో వెతకడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "వెబ్ ప్లాట్‌ఫామ్‌లోని [లేజీ లోడింగ్](https://web.dev/native-lazy-loading) ఫీచర్‌ను వినియోగించడం కోసం మీ ఉత్పత్తి, కేటలాగ్ టెంప్లేట్‌లను ఎడిట్ చేయడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Magento సంబంధిత [వార్నిష్ ఇంటిగ్రేషన్‌](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)ను ఉపయోగించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "మీ స్టోర్ డెవలపర్ సెట్టింగ్‌లలో \"CSS ఫైళ్లను చిన్నవిగా చేయి\" ఎంపికను ప్రారంభించండి. [మరింత తెలుసుకోండి](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "స్టాటిక్ కంటెంట్ అమలు చేయడం నుండి ఉత్పన్నమయ్యే అన్ని JavaScript అస్సెట్‌ల సైజు తగ్గించడానికి [టెర్సర్‌](https://www.npmjs.com/package/terser)ను ఉపయోగించండి, అలాగే బిల్ట్-ఇన్ కనిష్ఠీకరణ ఫీచర్‌ను డిజేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Magento అంతర్నిర్మిత [JavaScript బండ్లింగ్‌](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)ను నిలిపివేయండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "చిత్రాలను ఆప్టిమైజ్ చేయగలిగే వివిధ రకాల మూడవ పక్ష ఎక్స్‌టెన్షన్‌ల కోసం [Magento మార్కెట్‌ప్లేస్‌](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image)లో వెతకడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[థీమ్ లేఅవుట్‌ను ఎడిట్ చేయడం](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) ద్వారా ప్రీకనెక్ట్ లేదా dns ప్రీఫెట్చ్ వనరు సూచనలను జోడించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "[థీమ్ లేఅవుట్‌ను ఎడిట్ చేయడం](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) ద్వారా `<link rel=preload>` ట్యాగ్‌లను జోడించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "ఇమేజ్ ఫార్మాట్‌ను ఆటోమేటిక్‌గా ఆప్టిమైజ్ చేయడానికి `<img>`కు బదులుగా `next/image` కాంపోనెంట్‌ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "ప్రాధాన్యతను బట్టి ఇమేజ్‌లను ఆటోమేటిక్‌గా లోడ్ చేయడానికి `<img>`కు బదులుగా `next/image`ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "LCP ఇమేజ్‌ను ప్రీ - లోడ్ చేయడానికి `next/image` కాంపోనెంట్‌ను ఉపయోగించండి, \"ప్రాధాన్యతను\" ఒప్పునకు సెటప్ చేయండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "`next/script` కాంపోనెంట్‌ను ఉపయోగించి సాధారణ థర్డ్-పార్టీ స్క్రిప్ట్‌ల లోడింగ్‌ను మినహాయించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "ఇమేజ్‌లు ఎల్లప్పుడూ తగిన సైజ్‌లో ఉన్నాయని నిర్ధారించుకోవడానికి `next/image` కాంపోనెంట్‌ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "వినియోగంలో లేని నియమాలను స్టైల్‌షీట్‌ల నుండి తీసివేయడానికి `Next.js`లో `PurgeCSS` కాన్ఫిగరేషన్‌ను సెటప్ చేయవచ్చు, దాన్ని పరిశీలించండి. [మరింత తెలుసుకోండి](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "ఉపయోగించని JavaScript కోడ్‌ను గుర్తించడానికి`Webpack Bundle Analyzer`ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "మీ యాప్ వాస్తవ పనితీరును అంచనా వేయడానికి `Next.js Analytics`ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "మారకుండా ఉండే అస్సెట్‌లు, `Server-side Rendered` (SSR) పేజీలకు కాష్‌ను కాన్ఫిగర్ చేయండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "ఇమేజ్ క్వాలిటీని సర్దుబాటు చేయడానికి `<img>`కు బదులుగా `next/image` కాంపోనెంట్‌ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "సరైన `sizes`ను సెట్ చేయడానికి `next/image` కాంపోనెంట్‌ను ఉపయోగించండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "మీ Next.js సర్వర్‌లో కుదింపును ఎనేబుల్ చేయండి. [మరింత తెలుసుకోండి](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "`nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, `format=\"webp\"`ను సెట్ చేయండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "ఆఫ్‌స్క్రీన్ ఇమేజ్‌ల కోసం `nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, `loading=\"lazy\"`ను సెట్ చేయండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "LCP ఇమేజ్ కోసం `nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, `preload`ను పేర్కొనండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "`nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, ఖచ్చితమైన `width`, `height`ను పేర్కొనండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "`nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, సరైన `quality`ను సెట్ చేయండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "`nuxt/image` కాంపోనెంట్‌ను ఉపయోగించి, సరైన `sizes`ను సెట్ చేయండి. [మరింత తెలుసుకోండి](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "వెబ్ పేజీ మరింత వేగంగా లోడ్ కేవడానికి [యానిమేట్ చేసిన GIFలను వీడియోతో భర్తీ చేయండి](https://web.dev/replace-gifs-with-videos/), అలాగే కుదింపు సామర్థ్యాన్ని ప్రస్తుత స్టేట్-ఆఫ్-ది-ఆర్ట్ వీడియో కోడెక్, VP9 కంటే 30% ఎక్కువగా ఉంటే [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) లేదా [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) లాంటి ఆధునిక ఫైల్ ఫార్మాట్‌లను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "అప్‌లోడ్ చేసిన ఇమేజ్‌లను అనుకూలమైన ఫార్మాట్‌లలోకి ఆటోమేటిక్‌గా మార్చే [ప్లగ్ఇన్](https://octobercms.com/plugins?search=image) లేదా సర్వీస్‌ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి. [WebP నష్టరహిత ఇమేజ్‌లు](https://developers.google.com/speed/webp) PNG ఇమేజ్‌లతో పోల్చితే 26% తక్కువ సైజ్, JPEG ఇమేజ్‌లతో పోల్చితే 25-34% తక్కువ సైజ్ ఉంటూ సమానమైన SSIM క్వాలిటీ ఇండెక్స్‌ను కలిగి ఉంటాయి. పరిగణనలోకి తీసుకోవాల్సిన మరొక తర్వాతి తరం ఇమేజ్ ఫార్మాట్, [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "ఏవైనా ఆఫ్‌స్క్రీన్ ఇమేజ్‌లు ఉంటే, వాటిని మినహాయించగల సామర్థ్యాన్ని అందించే లేదా ఆ ఫంక్షనాలిటీ అందించే టెంప్లేట్‌ను స్విచ్ చేయగలిగే [ఇమేజ్ లేజీ లోడింగ్ ప్లగ్ఇన్‌](https://octobercms.com/plugins?search=lazy)ను ఇన్‌స్టాల్ చేయవచ్చు, దాన్ని పరిశీలించండి. అలాగే, [AMP ప్లగ్ఇన్‌](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)ను ఉపయోగించడం కూడా పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "[కీలకమైన అస్సెట్‌లను ఇన్‌లైన్‌లో ఉంచడానికి](https://octobercms.com/plugins?search=css) సహాయపడే అనేక ప్లగ్ఇన్‌లు ఉన్నాయి. ఈ ప్లగ్ఇన్‌లు ఇతర ప్లగ్ఇన్‌లను నిరోధించవచ్చు, కనుక మీరు వీటిని బాగా పరీక్షించవలసి ఉంటుంది."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "థీమ్‌లు, ప్లగ్ఇన్‌లు, సర్వర్ లక్షణాలన్నీ కూడా సర్వర్ ప్రతిస్పందన సమయాన్ని ప్రభావితం చేస్తాయి. చాలా జాగ్రత్తగా ఆప్టిమైజేషన్ ప్లగ్ఇన్‌ను ఎంచుకోవడం మరియు/లేదా సర్వర్‌ను అప్‌గ్రేడ్ చేయడం ద్వారా మరింత ఆప్టిమైజ్ చేసిన థీమ్‌ను కనుగొనడానికి ప్రయత్నించండి. October CMS ఈమెయిల్‌ పంపడం లాంటి సమయం తీసుకునే పనిని మినహాయించడానికి [`Queues`](https://octobercms.com/docs/services/queues)ని ఉపయోగించేందుకు డెవలపర్‌లను అనుమతిస్తుంది. ఇది వెబ్ రిక్వెస్ట్‌లను చాలా వేగవంతం చేస్తుంది."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "ఇవ్వబడిన వెబ్ పేజీలో చూపించే పోస్ట్‌ల సంఖ్యను తగ్గించడం, పొడవైన పోస్ట్‌లను అనేక పేజీలుగా విడగొట్టడం లేదా కామెంట్‌లను లేజీ-లోడ్ చేయడానికి ప్లగ్ఇన్‌ను ఉపయోగించడం ద్వారా (ఉదా. `show more` బటన్‌ను ఉపయోగించడం) పోస్ట్ లిస్ట్‌లలో సారాంశాలను చూపించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "స్టైల్‌లను గొలుసుగా జోడించడం, కనిష్ఠ స్థాయికి తగ్గించడం, కుదించడం ద్వారా వెబ్‌సైట్‌ను వేగవంతం చేసే అనేక [ప్లగ్ఇన్‌లు](https://octobercms.com/plugins?search=css) ఉన్నాయి. ఈ కనిష్ఠీకరణను ముందుగానే మేనేజ్ చేయడానికి బిల్డ్ ప్రాసెస్‌ను ఉపయోగించడం వలన డెవలప్‌మెంట్ వేగవంతం కాగలదు."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "స్క్రిప్ట్‌లను గొలుసుగా జోడించడం, కనిష్ఠ స్థాయికి తగ్గించడం, కుదించడం ద్వారా వెబ్‌సైట్‌ను వేగవంతం చేసే అనేక [ప్లగ్ఇన్‌లు](https://octobercms.com/plugins?search=javascript) ఉన్నాయి. ఈ కనిష్ఠీకరణను ముందుగానే మేనేజ్ చేయడానికి బిల్డ్ ప్రాసెస్‌ను ఉపయోగించడం వలన డెవలప్‌మెంట్ వేగవంతం కాగలదు."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "వెబ్‌సైట్‌లో నిరుపయోగమైన CSSని లోడ్ చేస్తున్న [ప్లగ్ఇన్‌లను](https://octobercms.com/plugins) రివ్యూ చేయవచ్చు, దాన్ని పరిశీలించండి. అనవసరమైన CSSని యాడ్ చేసే ప్లగ్ఇన్‌లను గుర్తించడానికి, Chrome డెవలపర్ టూల్స్‌లో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ని రన్ చేయండి. స్టైల్‌షీట్ URL నుండి బాధ్యత కలిగి ఉండే థీమ్/ప్లగ్ఇన్‌ని గుర్తించండి. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగుతో అనేక స్టైల్‌షీట్‌లను కలిగి ఉండే ప్లగ్ఇన్‌ల కోసం చూడండి. నిజంగా ప్లగ్ఇన్‌ను వెబ్ పేజీలో ఉపయోగించినప్పుడు మాత్రమే దానికి స్టైల్‌షీట్‌ను యాడ్ చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "వెబ్ పేజీలో ఉపయోగించని JavaScriptను లోడ్ చేసే [ప్లగ్ఇన్‌లను](https://octobercms.com/plugins?search=javascript)ను రివ్యూ చేయడం పరిశీలించండి. అనవసరమైన JavaScriptను యాడ్ చేసే ప్లగ్ఇన్‌లను గుర్తించడానికి, డెవలపర్ టూల్స్‌లో [కోడ్ కవరేజీ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ని రన్ చేయండి. స్క్రిప్ట్ URL నుండి బాధ్యత కలిగి ఉండే థీమ్/ప్లగ్ఇన్‌ను గుర్తించండి. కోడ్ కవరేజీలో ఎక్కువ ఎరుపు రంగుతో అనేక స్క్రిప్ట్‌లను కలిగి ఉండే ప్లగ్ఇన్‌ల కోసం చూడండి. వాస్తవానికి ప్లగ్ఇన్‌ను వెబ్ పేజీలో ఉపయోగిస్తున్నప్పుడు మాత్రమే దానికి స్క్రిప్ట్‌ను యాడ్ చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[HTTP కాష్‌తో అనవసరమైన నెట్‌వర్క్ రిక్వెస్ట్‌లను నిరోధించడం](https://web.dev/http-cache/#caching-checklist) గురించి చదవండి. కాష్ చేయడం వేగవంతం చేయడానికి అనేక [ప్లగ్ఇన్‌లు](https://octobercms.com/plugins?search=Caching) ఉన్నాయి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "క్వాలిటీని కొనసాగిస్తూ ఇమేజ్‌లను కుదించే [ఇమేజ్ ఆప్టిమైజేషన్ ప్లగ్ఇన్‌](https://octobercms.com/plugins?search=image)ను ఉపయోగించవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "అవసరమైన ఇమేజ్ సైజ్‌లు అందుబాటులో ఉన్నాయో లేదో నిర్ధారించుకోవడానికి ఇమేజ్‌లను నేరుగా మీడియా మేనేజర్‌లో అప్‌లోడ్ చేయండి. అనుకూలమైన ఇమేజ్ సైజ్‌లను ఉపయోగించేలా చూడటానికి [సైజ్ మార్పు ఫిల్టర్](https://octobercms.com/docs/markup/filter-resize) లేదా [ఇమేజ్ సైజ్ మార్పు ప్లగ్ఇన్‌](https://octobercms.com/plugins?search=image)ను ఉపయోగించేందుకు పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "వెబ్ సర్వర్ కాన్ఫిగరేషన్‌లో టెక్స్ట్ కుదింపును ఎనేబుల్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "మీరు పేజీలోని అనేక రిపీట్ అయ్యే ఎలిమెంట్‌లను రెండరింగ్ చేస్తున్నట్లయితే, క్రియేట్ చేసే DOM నోడ్‌ల సంఖ్యను కనిష్ఠ స్థాయికి తగ్గించడానికి, `react-window` లాంటి “విండో తరహా” లైబ్రరీని ఉపయోగించచవచ్చు, దాన్ని పరిశీలించండి. [మరింత తెలుసుకోండి](https://web.dev/virtualize-long-lists-react-window/). అలాగే, [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent), లేదా [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo)ను ఉపయోగించి అనవసరమైన రీ-రెండర్‌లను తగ్గించండి, అలాగే మీరు రన్‌టైమ్ పనితీరును మెరుగుపరచడానికి `Effect` హుక్‌ను ఉపయోగిస్తున్నప్పుడు కొన్ని ఆధారిత అంశాలు మారినప్పుడు మాత్రమే[ఎఫెక్ట్‌లను స్కిప్ చేయండి](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects)."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "మీరు 'రియాక్ట్ రూటర్'ను ఉపయోగిస్తున్నట్లయితే, [మార్గం నావిగేషన్‌ల](https://reacttraining.com/react-router/web/api/Redirect) కోసం `<Redirect>` కాంపొనెంట్ వినియోగాన్ని కనీస స్థాయికి తగ్గించండి."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "మీరు సర్వర్ వైపు నుండి ఏవైనా 'రియాక్ట్' కాంపొనెంట్‌లను అమలు చేస్తున్నట్లయితే, `renderToPipeableStream()` లేదా `renderToStaticNodeStream()`ను వినియోగించడానికి ప్రయత్నించడం ద్వారా మార్క్అప్‌లో ఉండే విభాగాలన్నీ ఒకేసారి కాకుండా, వివిధ భాగాలుగా అందుకోవడానికి, హైడ్రేట్ చేయడానికి క్లయింట్‌ను అనుమతించండి. [మరింత తెలుసుకోండి](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "మీ బిల్డ్ సిస్టమ్, CSS ఫైళ్ల సైజును ఆటోమేటిక్‌గా తగ్గిస్తున్నట్లయితే, మీ అప్లికేషన్ ప్రొడక్షన్ బిల్డ్‌ను మీరు అమలు చేస్తున్నట్లు నిర్ధారించుకోండి. React Developer Tools ఎక్స్‌టెన్షన్ సహాయంతో మీరు దీనిని చెక్ చేయవచ్చు. [మరింత తెలుసుకోండి](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "మీ బిల్డ్ సిస్టమ్, JS ఫైళ్ల సైజును ఆటోమేటిక్‌గా తగ్గిస్తున్నట్లయితే, మీ అప్లికేషన్ ప్రొడక్షన్ బిల్డ్‌ను మీరు అమలు చేస్తున్నట్లు నిర్ధారించుకోండి. React Developer Tools ఎక్స్‌టెన్షన్ సహాయంతో మీరు దీనిని చెక్ చేయవచ్చు. [మరింత తెలుసుకోండి](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "మీరు సర్వర్ వైపు అమలు చేయనట్లయితే, `React.lazy()` ద్వారా [మీ JavaScript బండిల్‌లను విభజించండి](https://web.dev/code-splitting-suspense/). లేదంటే, '[లోడ్ చేయదగిన కాంపొనెంట్‌లు](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)' లాంటి మూడవ పక్షం లైబ్రరీని ఉపయోగించడం ద్వారా కోడ్‌ను విభజించండి."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "మీ కాంపొనెంట్‌ల అమలు పనితీరును అంచనా వేయడానికి, ప్రొఫైలర్ API వినియోగించబడే రియాక్ట్ DevTools ప్రొఫైలర్‌ను ఉపయోగించండి. [మరింత తెలుసుకోండి.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "మీ GIFను HTML5 వీడియోగా పొందుపరచడానికి అందుబాటులో ఉండేలా చేసే సర్వీస్‌కు దానిని అప్‌లోడ్ చేయవచ్చు, దాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "సపోర్ట్ ఉన్న చోట, అప్‌లోడ్ చేయబడిన మీ JPEG ఇమేజ్‌లను WebPకి ఆటోమేటిక్‌గా మార్చడానికి [Performance Lab](https://wordpress.org/plugins/performance-lab/) ప్లగ్ఇన్‌ను ఉపయోగించడాన్ని పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "ఏవైనా ఆఫ్‌స్క్రీన్ చిత్రాలు ఉంటే, వాటిని మినహాయించగల సామర్థ్యం కలిగి ఉండే లేదా ఆ కార్యశీలతను అందించే థీమ్‌కు మార్చగలిగే [లేజీ-లోడ్ WordPress ప్లగ్ఇన్‌](https://wordpress.org/plugins/search/lazy+load/)ను ఇన్‌స్టాల్ చేసుకోండి. అలాగే, [AMP ప్లగ్ఇన్‌](https://wordpress.org/plugins/amp/)ను ఉపయోగించడం కూడా పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "[కీలకమైన ఆస్తులను ఇన్‌లైన్‌లో ఉంచడానికి](https://wordpress.org/plugins/search/critical+css/) లేదా [తక్కువ ప్రాముఖ్యత ఉన్న వనరులను మినహాయించడానికి](https://wordpress.org/plugins/search/defer+css+javascript/) మీకు సహాయపడగల అనేక WordPress ప్లగ్ఇన్‌లు ఉన్నాయి. ఈ ప్లగ్ఇన్‌ల ద్వారా అందించబడే ఆప్టిమైజేషన్‌లు మీ థీమ్ లేదా ప్లగ్ఇన్‌ల ఫీచర్‌లను విడగొట్టవచ్చని గుర్తుంచుకోండి, దీని వలన మీరు కోడ్‌కు మార్పులు చేయాల్సి రావచ్చు."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "థీమ్‌లు, ప్లగ్ఇన్‌లు, సర్వర్ లక్షణాలన్నీ కూడా సర్వర్ ప్రతిస్పందన సమయాన్ని ప్రభావితం చేస్తాయి. మరింత ఆప్టిమైజ్ చేయబడిన థీమ్‌ను కనుగొనడానికి, చాలా జాగ్రత్తగా ఆప్టిమైజేషన్ ప్లగ్ఇన్‌ను ఎంచుకోవడానికి మరియు/లేదా మీ సర్వర్‌ను అప్‌గ్రేడ్ చేయడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "ఇవ్వబడిన పేజీలో చూపబడే పోస్ట్‌ల సంఖ్యను తగ్గించడం, మీ పొడవైన పోస్ట్‌లను బహుళ పేజీలుగా విడగొట్టడం లేదా మందకొడి లోడింగ్ కామెంట్‌ల కోసం ప్లగ్ఇన్‌ను ఉపయోగించడం ద్వారా (అంటే, మరిన్ని ట్యాగ్ ద్వారా) మీ పోస్ట్ లిస్ట్‌లలో సారాంశాలను చూపడానికి ప్రయత్నించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "అనేక '[WordPress ప్లగ్ఇన్‌లు](https://wordpress.org/plugins/search/minify+css/)' మీ శైలులను క్రమపరచడం, చిన్నవిగా చేయడం, కుదించడం ద్వారా మీ సైట్‌ను మరింత వేగవంతం చేయవచ్చు. వీలైతే, ఈ కనిష్ఠీకరణను ముందుగానే నిర్వహించేలా కూడా మీరు ఒక బిల్డ్ ప్రాసెస్‌ను ఉపయోగించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "అనేక [WordPress ప్లగ్ఇన్‌లు](https://wordpress.org/plugins/search/minify+javascript/) మీ స్క్రిప్ట్‌లను క్రమపరచడం, చిన్నవిగా చేయడం, కుదించడం ద్వారా మీ సైట్‌ను మరింత వేగవంతం చేయవచ్చు. వీలైతే, ఈ కనిష్ఠీకరణను ముందుగానే నిర్వహించేలా కూడా మీరు ఒక బిల్డ్ ప్రాసెస్‌ను ఉపయోగించడం పరిగణించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "మీ పేజీలో ఉపయోగించని CSSను లోడ్ చేస్తున్న '[WordPress ప్లగ్ఇన్‌‌ల](https://wordpress.org/plugins/)' సంఖ్యను తగ్గించడం లేదా మార్చడాన్ని పరిశీలించండి. అదనపు CSSను జోడించే ప్లగ్ఇన్‌లను గుర్తించడానికి, Chrome DevToolsలో '[కోడ్ కవరేజీ](https://developer.chrome.com/docs/devtools/coverage/)' అమలు చేయడానికి ప్రయత్నించండి. అందుకు కారణమైన థీమ్/ప్లగ్ఇన్‌ను స్టైల్‌షీట్‌లోని URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజ్‌లో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్టైల్‌షీట్‌లను కలిగి ఉన్న ప్లగ్ఇన్‌లను వెతకండి. నిజంగా ప్లగ్ఇన్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్టైల్‌షీట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "మీ పేజీలో ఉపయోగించని JavaScriptను లోడ్ చేస్తున్న '[WordPress ప్లగ్ఇన్‌‌ల](https://wordpress.org/plugins/)' సంఖ్యను తగ్గించడం లేదా మార్చడాన్ని పరిశీలించండి. అదనపు JSను జోడించే ప్లగ్ఇన్‌లను గుర్తించడానికి, Chrome DevToolsలో '[కోడ్ కవరేజీ](https://developer.chrome.com/docs/devtools/coverage/)' అమలు చేయడానికి ప్రయత్నించండి. అందుకు కారణమైన థీమ్/ప్లగ్ఇన్‌ను స్క్రిప్ట్ యొక్క URL నుండి మీరు గుర్తించవచ్చు. కోడ్ కవరేజ్‌లో ఎక్కువ ఎరుపు రంగు కలిగి ఉన్న లిస్ట్‌లో అనేక స్క్రిప్ట్‌లను కలిగి ఉన్న ప్లగ్ఇన్‌లను వెతకండి. నిజంగా ప్లగ్ఇన్‌ను ఆ పేజీలో ఉపయోగించినప్పుడు మాత్రమే స్క్రిప్ట్‌కు జత చేయాలి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[WordPressలో బ్రౌజర్ కాష్ విధానం](https://wordpress.org/support/article/optimization/#browser-caching) గురించి చదవండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "క్వాలిటీ తగ్గకుండా మీ చిత్రాలను కుదించే [చిత్రం ఆప్టిమైజేషన్ WordPress ప్లగ్ఇన్‌](https://wordpress.org/plugins/search/optimize+images/)ను ఉపయోగించడం పరిశీలించండి."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "అవసరమైన చిత్ర పరిమాణాలు అందుబాటులో ఉన్నాయో లేదో నిర్ధారించుకోవడానికి చిత్రాలను నేరుగా [మీడియా లైబ్రరీ](https://wordpress.org/support/article/media-library-screen/) ద్వారా అప్‌లోడ్ చేయండి. ఆపై వాటిని మీడియా లైబ్రరీ నుండి చొప్పించండి లేదా అనుకూలమైన చిత్ర పరిమాణాలు ఉపయోగించబడ్డాయో లేదో నిర్ధారించుకోవడానికి (ప్రతిస్పందనాత్మక బ్రేక్ పాయింట్‌లు కలిగి ఉండే వాటికి కూడా) చిత్ర విడ్జెట్‌ను ఉపయోగించండి. కొలతలు వాటి వినియోగానికి తగినట్లుగా ఉంటే తప్పితే, '`Full Size`' చిత్రాలను వినియోగించడం నివారించండి. [మరింత తెలుసుకోండి](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "మీరు మీ వెబ్ సర్వర్ కన్ఫిగరేషన్‌లో వచన కుదింపును ప్రారంభించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "'WP Rocket'లోని ఇమేజ్ ఆప్టిమైజేషన్ ట్యాబ్‌లో 'Imagify'ని ఎనేబుల్ చేసి, మీ ఇమేజ్‌లను WebPకి మార్చండి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "ఈ సిఫార్సును పరిష్కరించడానికి WP Rocketలో [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images)‌ను ఎనేబుల్ చేయండి. సందర్శకులు పేజీని కిందికి స్క్రోల్ చేసి, వాస్తవంగా వాటిని చూడాలనుకునే వరకు, ఈ ఫీచర్ ఇమేజ్‌లు లోడ్ అవ్వడాన్ని ఆలస్యం చేస్తాయి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "ఈ సిఫార్సు వివరాల కోసం, 'WP Rocket'లో [ఉపయోగించని CSSని తీసివేయండి](https://docs.wp-rocket.me/article/1529-remove-unused-css), అలాగే [JavaScript లోడ్ అవ్వడాన్ని వాయిదా వేయండి](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. మీ పేజీని రెండర్ చేయకుండా వరుసగా CSS, JavaScript ఫైల్స్‌ను ఈ ఫీచర్‌లు ఆప్టిమైజ్ చేస్తాయి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "ఈ సమస్యను పరిష్కరించడానికి, 'WP Rocket'లో [CSS ఫైల్స్ సైజ్‌ను తగ్గించండి](https://docs.wp-rocket.me/article/1350-css-minify-combine) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. ఫైల్ సైజ్‌ను తగ్గించి, వేగవంతంగా డౌన్‌లోడ్ చేయడానికి మీ సైట్ CSS ఫైల్స్‌లో ఏవైనా స్పేస్‌లు, కామెంట్‌లు ఉంటే తీసివేయబడతాయి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "ఈ సమస్యను పరిష్కరించడానికి, 'WP Rocket'లో [JavaScript ఫైల్స్ సైజ్‌ను తగ్గించండి](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. ఫైల్స్ సైజ్‌ను తగ్గించి, వేగవంతంగా డౌన్‌లోడ్ చేయడానికి JavaScript ఫైల్స్‌లో ఉన్న ఖాళీ స్పేస్‌లు, కామెంట్‌లు తీసివేయబడతాయి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "ఈ సమస్యను పరిష్కరించడానికి 'WP Rocket'లో [ఉపయోగించని CSSని తీసివేయండి](https://docs.wp-rocket.me/article/1529-remove-unused-css) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. ప్రతి పేజీలో ఉపయోగించే CSSని మాత్రమే ఉంచుతూనే, ఉపయోగించని CSS, స్టయిల్‌షీట్‌లు అన్నింటిని తీసివేయడం ద్వారా ఇది పేజీ సైజ్‌ను తగ్గిస్తుంది."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "ఈ సమస్యను పరిష్కరించడం కోసం 'WP Rocket'లో [JavaScript ఎగ్జిక్యూషన్‌ను ఆలస్యం చేయండి](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. యూజర్ ఇంటరాక్షన్ అయ్యేంత వరకు ఎగ్జిక్యూషన్‌ను ఆలస్యం చేయడం ద్వారా ఇది మీ పేజీ లోడ్ అవ్వడాన్ని మెరుగుపరుస్తుంది. మీ సైట్ iframeలను కలిగి ఉంటే, మీరు WP Rocket యొక్క [iframes, వీడియోల కోసం నిర్దేశించి ఉన్న LazyLoad](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)ను, అలాగే [YouTube iframeని ప్రివ్యూ ఇమేజ్‌తో రీప్లేస్ చేయండి](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)ని ఉపయోగించవచ్చు."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "'WP Rocket'లోని ఇమేజ్ ఆప్టిమైజేషన్ ట్యాబ్‌లో 'Imagify'ని ఎనేబుల్ చేసి, మీ ఇమేజ్‌లను కుదించడానికి బల్క్‌లో ఆప్టిమైజేషన్‌ను రన్ చేయండి."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "'WP Rocket'లో [ప్రీఫెచ్ DNS రిక్వెస్ట్‌ల](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)ను ఉపయోగించి, \"dns-prefetch\"ను జోడించి, బాహ్య డొమైన్‌లతో కనెక్షన్‌ను వేగవంతం చేయండి. అలాగే, 'WP Rocket' \"ప్రీకనెక్ట్‌\"ను [Google Fonts డొమైన్‌](https://docs.wp-rocket.me/article/1312-optimize-google-fonts)కు ఆటోమేటిక్‌గా జోడిస్తుంది, [CDNని ఎనేబుల్ చేయండి](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) అనే ఫీచర్ ద్వారా జోడించిన CNAME(లు) ఏవైనా ఉంటే, వాటిని కూడా జోడిస్తుంది."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "ఫాంట్‌లకు సంబంధించిన ఈ సమస్యను పరిష్కరించడానికి, 'WP Rocket'లోని [ఉపయోగించని CSSను తీసివేయండి](https://docs.wp-rocket.me/article/1529-remove-unused-css) అనే ఆప్షన్‌ను ఎనేబుల్ చేయండి. మీ సైట్ యొక్క కీలకమైన ఫాంట్‌లు ప్రాధాన్యత ఆధారంగా ప్రీ - లోడ్ చేయబడతాయి."}, "report/renderer/report-utils.js | calculatorLink": {"message": "కాలిక్యులేటర్‌ను చూడండి."}, "report/renderer/report-utils.js | collapseView": {"message": "వీక్షణను కుదించండి"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "ప్రారంభ నావిగేషన్"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "గరిష్ట క్లిష్టమైన మార్గ ప్రతిస్పందన సమయం:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSONను కాపీ చేయి"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "ముదురు రంగు థీమ్‌ను టోగుల్ చేయి"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "ప్రింట్ విస్తరించబడింది"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "ప్రింట్ సారాంశం"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Gist లాగా సేవ్ చేయండి"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "HTML లాగా సేవ్ చేయండి"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "JSON లాగా సేవ్ చేయండి"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "వ్యూయర్‌లో తెరువు"}, "report/renderer/report-utils.js | errorLabel": {"message": "ఎర్రర్ ఏర్పడింది!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "రిపోర్ట్‌ ఎర్రర్: ఆడిట్ సమాచారం లేదు"}, "report/renderer/report-utils.js | expandView": {"message": "వీక్షణను విస్తరించండి"}, "report/renderer/report-utils.js | footerIssue": {"message": "సమస్యను ఫైల్ చేయండి"}, "report/renderer/report-utils.js | hide": {"message": "దాచండి"}, "report/renderer/report-utils.js | labDataTitle": {"message": "ల్యాబ్ డేటా"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "అనుకరణ మొబైల్ నెట్‌వర్క్‌లో ప్రస్తుత పేజీకి సంబంధించిన [Lighthouse](https://developers.google.com/web/tools/lighthouse/) విశ్లేషణ. విలువలు కేవలం అంచనా మాత్రమే, ఇవి మారే అవకాశం ఉంది."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "మాన్యువల్‌గా తనిఖీ చేయవలసిన అదనపు అంశాలు"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "వర్తించదు"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "అవకాశం"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "అంచనా వేసిన పొదుపులు"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "ఉత్తీర్ణత సాధించిన ఆడిట్‌లు"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "మొదటి పేజీ లోడ్"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "అనుకూల త్రోట్లింగ్"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "అనుకరణ డెస్క్‌టాప్"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "అనుకరణ లేదు"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe వెర్షన్"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "కుదించిన CPU/మెమరీ పవర్"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU త్రోట్లింగ్"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "పరికరం"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "నెట్‌వర్క్ త్రోట్లింగ్"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "స్క్రీన్ అనుకరణ"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "వెబ్‌సైట్ యాక్సెస్ సాధనం (నెట్‌వర్క్)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "సింగిల్ పేజీ లోడ్"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "అనేక సెషన్‌లను కలిగిన ఫీల్డ్ డేటా లాగా కాకుండా, ఈ డేటా సింగిల్ పేజీ లోడ్ నుండి తీసుకోబడింది."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "నెమ్మదిగా ఉన్న 4G థ్రోట్లింగ్"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "తెలియదు"}, "report/renderer/report-utils.js | show": {"message": "చూపండి"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "వీటికి సంబంధించిన ఆడిట్‌లను చూపండి:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "స్నిప్పెట్‌ను కుదించు"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "స్నిప్పెట్‌ను విస్తరించు"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "3వ పక్షం వనరులను చూపు"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "ఎన్విరాన్మెంట్ ద్వారా అందించబడింది"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Lighthouse యొక్క ఈ అమలును ప్రభావితం చేసిన సమస్యలు ఉన్నాయి:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "విలువలు కేవలం అంచనా మాత్రమే, ఇవి మారే అవకాశం ఉంది. నేరుగా ఈ కొలమానాల ఆధారంగా [పనితీరు స్కోరు లెక్కించబడుతుంది](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "అసలైన ట్రేస్‌ను చూపించు"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "ట్రేస్‌ను చూపించు"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "ట్రీమ్యాప్‌ను చూడండి"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "ఆడిట్‌లు పాస్ అయ్యాయి కానీ హెచ్చరికలు ఉన్నాయి"}, "report/renderer/report-utils.js | warningHeader": {"message": "హెచ్చరికలు: "}, "treemap/app/src/util.js | allLabel": {"message": "అన్నీ"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "అన్ని స్క్రిప్ట్‌లు"}, "treemap/app/src/util.js | coverageColumnName": {"message": "కవరేజీ"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "డూప్లికేట్ మాడ్యూల్‌లు"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "రిసోర్స్ బైట్‌లు"}, "treemap/app/src/util.js | tableColumnName": {"message": "పేరు"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "టోగుల్ టేబుల్"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "వినియోగించని బైట్‌లు"}}