{"core/audits/accessibility/accesskeys.js | description": {"message": "Ключі доступу дають змогу швидко виділяти частину сторінки. Для належної навігації кожний ключ доступу має бути унікальним. [Докладніше про ключі доступу.](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Значення `[accesskey]` неунікальні"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Значення `[accesskey]` унікальні"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Ко<PERSON>на `role` ARIA підтримує конкретний набір атрибутів `aria-*`. Як<PERSON><PERSON> вони не збігаються, атрибути `aria-*` стають недійсними. [Дізнайтесь, як зіставляти атрибути ARIA з ролями.](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` не відповідають своїм ролям"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибути `[aria-*]` відповідають своїм ролям"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) елемента, що незручно для користувачів, які застосовують програми зчитування з екрана. [Дізнайтесь, як зробити командні елементи доступнішими.](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Елементи з ролями `button`, `link` і `menuitem` не мають зрозумілих назв для зчитування з екрана."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Елементи з ролями `button`, `link` і `menuitem` мають зрозумілі назви для зчитування з екрана"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Технології для людей з обмеженими можливостями, як-от програми зчитування з екрана, працюють неналежно, коли `aria-hidden=\"true\"` налаштовано в документі `<body>`. [Д<PERSON><PERSON><PERSON>йтесь, як `aria-hidden` впливає на текст документа.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` є в документі `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` немає в документі `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Фокусовані нащадки в елементі `[aria-hidden=\"true\"]` забороняють користувачам технологій для людей з обмеженими можливостями (наприклад, програм зчитування з екрана) доступ до цих інтерактивних елементів. [Дізнайтесь, як `aria-hidden` впливає на фокусовані елементи.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Елементи `[aria-hidden=\"true\"]` містять інтерактивні похідні"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Елементи `[aria-hidden=\"true\"]` не містять інтерактивних похідних"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) поля введення, що незручно для користувачів спеціальних можливостей. [Докладніше про мітки для полів введення.](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Поля введення ARIA не мають доступних для зчитування назв"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Поля введення ARIA мають доступні для зчитування назви"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) елемента meter, що незручно для користувачів спеціальних можливостей. [Діз<PERSON>йтесь, як давати назви елементам `meter`.](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Елементи ARIA з роллю `meter` не мають зрозумілих назв для зчитування з екрана."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Елементи ARIA з роллю `meter` мають зрозумілі назви для зчитування з екрана"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) елемента `progressbar`, що незручно для користувачів спеціальних можливостей. [Діз<PERSON>йтесь, як позначати `progressbar`.](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Елементи ARIA з роллю `progressbar` не мають зрозумілих назв для зчитування з екрана."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Елементи ARIA з роллю `progressbar` мають зрозумілі назви для зчитування з екрана"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Деякі ролі ARIA мають обов’язкові атрибути, що описують стан елемента для програм зчитування з екрана. [Докладніше про ролі й обов’язкові атрибути.](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Ролі `[role]` не мають усіх обов'язкових атрибутів `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Ролі `[role]` мають усі потрібні атрибути `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Щоб виконувати потрібні функції спеціальних можливостей, деякі батьківські ролі ARIA повинні містити відповідні дочірні ролі. [Докладніше про ролі й обов’язкові дочірні елементи.](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "В елементах з ARIA `[role]`, які вимагають дочірні елементи з певним атрибутом `[role]`, немає кількох або всіх дочірніх елементів."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Елементи з ARIA `[role]`, які вимагають дочірні елементи з певним атрибутом `[role]`, мають усі необхідні дочірні елементи."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Щоб належно виконувати потрібні функції спеціальних можливостей, відповідні батьківські ролі повинні містити деякі дочірні ролі ARIA. [Докладніше про ролі ARIA й обов’язковий батьківський елемент.](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Ролі `[role]` не містяться в обов'язковому батьківському елементі"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Ролі `[role]` містяться у відповідному батьківському елементі"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Щоб належно виконувати функції спеціальних можливостей, ролі ARIA повинні мати дійсні значення. [Докладніше про дійсні ролі ARIA.](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Значення `[role]` недійсні"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Значення `[role]` дійсні"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) перемикача, що незручно для користувачів спеціальних можливостей. [Докладніше про поля для перемикачів.](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Поля перемикача ARIA не мають доступних для зчитування назв"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Поля перемикача ARIA мають доступні для зчитування назви"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) елемента tooltip, що незручно для користувачів спеціальних можливостей. [Діз<PERSON>йтесь, як давати назви елементам `tooltip`.](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Елементи ARIA з роллю `tooltip` не мають зрозумілих назв для зчитування з екрана."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Елементи ARIA з роллю `tooltip` мають зрозумілі назви для зчитування з екрана"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Без доступної назви програма зачитує тільки загальну назву (тобто роль або тип) елемента `treeitem`, що незручно для користувачів спеціальних можливостей. [Діз<PERSON>йтесь, як додавати мітки до елементів `treeitem`.](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Елементи ARIA з роллю `treeitem` не мають зрозумілих назв для зчитування з екрана."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Елементи ARIA з роллю `treeitem` мають зрозумілі назви для зчитування з екрана"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Технології для людей з обмеженими можливостями, як-от програми зчитування з екрана, не можуть тлумачити атрибути ARIA з недійсними значеннями. [Докладніше про дійсні значення атрибутів ARIA.](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Атрибути `[aria-*]` не мають дійсних значень"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Атрибути `[aria-*]` мають дійсні значення"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Технології для людей з обмеженими можливостями, як-от програми зчитування з екрана, не можуть тлумачити атрибути ARIA з недійсними назвами. [Докладніше про дійсні атрибути ARIA.](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` недійсні або написані неправильно"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибути `[aria-*]` дійсні та написані правильно"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи, що не пройшли перевірку"}, "core/audits/accessibility/button-name.js | description": {"message": "Без доступної назви програма озвучує тільки слово \"кнопка\", що незручно для користувачів спеціальних можливостей. [Дізнайтесь, як зробити кнопки доступнішими.](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Кнопки не мають доступних для зчитування назв"}, "core/audits/accessibility/button-name.js | title": {"message": "Кнопки мають доступну для зчитування назву"}, "core/audits/accessibility/bypass.js | description": {"message": "Якщо додати способи обходу контенту, що повторюється, користувачам буде простіше переходити між елементами на сторінці за допомогою клавіатури. [Докладніше про обхід блокувань.](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Сторінка не містить заголовка, посилання для пропуску вмісту чи мітки області"}, "core/audits/accessibility/bypass.js | title": {"message": "Сторінка містить заголовок, посилання для пропуску вмісту чи мітку області"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Для багатьох користувачів складно або неможливо читати текст із низькою контрастністю. [Дізнайтесь, як забезпечити достатній контраст кольорів.](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON>іж кольорами фону та переднього плану недостатній коефіцієнт контрастності."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>іж кольорами фону та переднього плану достатній коефіцієнт контрастності"}, "core/audits/accessibility/definition-list.js | description": {"message": "Коли списки визначень мають неправильну розмітку, програми зчитування з екрана можуть генерувати незрозумілі або неточні дані. [Дізнайтесь, як правильно структурувати списки визначень.](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Списки визначення `<dl>` містять не лише належно впорядковані групи `<dt>` і `<dd>` чи елементи `<script>`, `<template>` або `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Списки визначення `<dl>` містять лише належно впорядковані групи `<dt>` та `<dd>` чи елементи `<script>`, `<template>` або `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Щоб програми зчитування з екрана правильно озвучували елементи списку визначень (`<dt>` і `<dd>`), елементи має бути згруповано в батьківському елементі `<dl>`. [Дізнайтесь, як правильно структурувати списки визначень.](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Елементи списку визначень не згруповано в елементах `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Елементи списку визначень згруповано в елементах `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Завдяки назві користувачі програми зчитування з екрана дізнаються загальну інформацію про сторінку, а користувачі пошукових систем визначають, чи сторінка відповідає їхньому запиту. [Докладніше про назви документів.](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Документ не має елемента `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Документ містить елемент `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Усі фокусовані елементи мають містити унікальний `id`, щоб їх могли розпізнавати технології для людей з обмеженими можливостями. [Дізнайтесь, як виправити повторювані `id`.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Атрибути `[id]` в активних інтерактивних елементах неунікальні"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Атрибути `[id]` в активних інтерактивних елементах унікальні"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Значення ідентифікатора ARIA має бути унікальним, щоб технології для людей з обмеженими можливостями не пропускали інші копії. [Діз<PERSON>йтесь, як виправити повторювані ідентифікатори ARIA.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Ідентифікатори ARIA неунікальні"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Ідентифікатори ARIA унікальні"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Поля форми з кількома мітками можуть помилково озвучуватися технологіями для людей з обмеженими можливостями (наприклад, програмами зчитування з екрана), які використовують першу, останню або всі мітки. [Дізнайтесь, як використовувати мітки форм.](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Поля форми містять кілька міток"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Немає полів форми з кількома мітками"}, "core/audits/accessibility/frame-title.js | description": {"message": "Користувачі програм зчитування з екрана використовують назви фреймів, щоб дізнатися їх вміст. [Докладніше про назви фреймів.](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Елементи `<frame>` або `<iframe>` не мають назви"}, "core/audits/accessibility/frame-title.js | title": {"message": "Елементи `<frame>` або `<iframe>` мають назву"}, "core/audits/accessibility/heading-order.js | description": {"message": "Правильно впорядковані заголовки, які не пропускають рівні, передають семантичну структуру сторінки, що полегшує навігацію та розуміння під час використання технологій для людей з обмеженими можливостями. [Докладніше про впорядкування заголовків.](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Елементи заголовка не розташовані послідовно в порядку спадання"}, "core/audits/accessibility/heading-order.js | title": {"message": "Елементи заголовка розташовані послідовно в порядку спадання"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Якщо для сторінки не вказано атрибут `lang`, програма зчитування з екрана припускає, що мовою сторінки є мова за умовчанням, яку користувач вибрав під час налаштування програми зчитування. Якщо насправді мова сторінки інша, програма зчитування з екрана може неправильно озвучувати текст на сторінці. [Докладніше про атрибут `lang`.](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елемент `<html>` не має атрибута `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Елемент `<html>` містить атрибут `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Якщо указати дійсний тег мови за [стандартом BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), це допоможе програмі зчитування з екрана правильно озвучувати текст. [Дізнайтесь, як використовувати атрибут `lang`.](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елемент `<html>` не містить дійсного значення для атрибута `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Елемент `<html>` має дійсне значення атрибута `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Інформативні елементи повинні містити короткий, описовий текст заміщення. Декоративні елементи можуть ігноруватись і мати порожній атрибут alt. [Докладніше про атрибут `alt`.](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Зображення не мають атрибутів `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Зображення мають атрибути `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Коли зображення застосовується як кнопка `<input>`, додавши текст заміщення, ви допоможете користувачам програм зчитування з екрана зрозуміти призначення такої кнопки. [Дізнайтесь, як додати текст заміщення для зображення.](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Елементи `<input type=\"image\">` не містять текст `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Елементи `<input type=\"image\">` містять текст `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Завдяки міткам технології для людей з обмеженими можливостями, як-от програми зчитування з екрана, правильно озвучують елементи керування формою. [Докладніше про мітки для елементів форми.](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Елементи форми не мають пов’язаних міток"}, "core/audits/accessibility/label.js | title": {"message": "Елементи форми мають пов’язані мітки"}, "core/audits/accessibility/link-name.js | description": {"message": "Унікальний і доступний для виділення текст посилання (а також текст заміщення для зображень, коли вони застосовуються як посилання), що можна розпізнати, покращує навігацію для користувачів програм зчитування з екрана. [Дізнайтесь, як зробити посилання доступними.](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Посилання не мають назв, які можна розпізнати"}, "core/audits/accessibility/link-name.js | title": {"message": "Посилання мають назви, які можна розпізнати"}, "core/audits/accessibility/list.js | description": {"message": "Програми зчитування з екрана озвучують списки в особливий спосіб. Правильна структура списків допомагає таким програмам правильно розпізнавати інформацію. [Докладніше про правильну структуру списків.](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Списки не містять лише елементи `<li>` і елементи для підтримки виконання сценарію (`<script>` та `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Списки містять лише елементи `<li>` й елементи для підтримки виконання сценарію (`<script>` і `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Щоб програми зчитування з екрана правильно озвучували елементи списку (`<li>`), ці елементи повинні міститися в батьківських елементах `<ul>`, `<ol>` або `<menu>`. [Докладніше про правильну структуру списків.](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Елементів списку (`<li>`) немає в батьківських елементах `<ul>`, `<ol>` або `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Елементи списку (`<li>`) містяться в батьківських елементах `<ul>`, `<ol>` або `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Користувачі не очікують, що сторінка оновлюватиметься автоматично. Таке оновлення перемістить фокус назад угору сторінки. Це може роздратувати або спантеличити користувачів. [Докладніше про метатег оновлення.](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документ використовує `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Документ не використовує тег `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Якщо вимкнути масштабування, користувачі з поганим зором не зможуть збільшити екран, щоб краще бачити вміст веб-сторінки. [Докладніше про метатег області перегляду.](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Параметр `[user-scalable=\"no\"]` використовується в елементі `<meta name=\"viewport\">` або атрибут `[maximum-scale]` менший за 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Параметр `[user-scalable=\"no\"]` не використовується в елементі `<meta name=\"viewport\">`, а атрибут `[maximum-scale]` має значення не менше 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Програми зчитування з екрана розпізнають лише текст. Якщо додати текст заміщення до елементів `<object>`, програми зможуть краще передати їх значення користувачам. [Докладніше про текст заміщення для елементів `object`.](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Елементи `<object>` не містять тексту заміщення"}, "core/audits/accessibility/object-alt.js | title": {"message": "Елементи `<object>` містять текст заміщення"}, "core/audits/accessibility/tabindex.js | description": {"message": "Значення, що перевищує 0, передбачає явне встановлення порядку навігації. Хоча технічно воно дійсне, це часто ускладнює взаємодію для користувачів, які застосовують технології для людей з обмеженими можливостями. [Докладніше про атрибут `tabindex`.](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Деякі елементи мають значення `[tabindex]`, що перевищує 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Жоден елемент не має значення `[tabindex]`, що перевищує 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Програми зчитування з екрана мають функції, які полегшують навігацію в таблицях. Якщо клітинки `<td>`, які використовують атрибут `[headers]`, посилаються лише на інші клітинки в тій самій таблиці, це може покращити взаємодію для користувачів програми зчитування з екрана. [Докладніше про атрибут `headers`.](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Клітинки в елементі `<table>`, які використовують атрибут `[headers]`, посилаються на елемент `id`, відсутній в тій самій таблиці."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Клітинки в елементі `<table>`, які використовують атрибут `[headers]`, посилаються на клітинки тієї ж таблиці."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Програми зчитування з екрана мають функції, які полегшують навігацію в таблицях. Якщо заголовки таблиці завжди посилаються на певні набори клітинок, це може покращити взаємодію для користувачів програм зчитування з екрана. [Докладніше про заголовки таблиць.](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Елементи `<th>` і елементи з роллю `[role=\"columnheader\"/\"rowheader\"]` не містять клітинок із даними, які описують."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементи `<th>` і елементи з роллю `[role=\"columnheader\"/\"rowheader\"]` містять клітинки з даними, які описують."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Якщо для елементів указати дійсний тег мови за [стандартом BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), це допоможе програмі зчитування з екрана правильно озвучувати текст. [Дізнайтесь, як використовувати атрибут `lang`.](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Атрибути `[lang]` не мають дійсного значення"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Атрибути `[lang]` мають дійсне значення"}, "core/audits/accessibility/video-caption.js | description": {"message": "Коли відео має субтитри, користувачам із вадами слуху простіше зрозуміти його. [Докладніше про субтитри відео.](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Елементи `<video>` не містять елемент `<track>` з атрибутом `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Елементи `<video>` містять елемент `<track>` з атрибутом `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Поточне значення"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Пропонований маркер"}, "core/audits/autocomplete.js | description": {"message": "Атрибут `autocomplete` допомагає користувачам швидше надсилати форми. Щоб спростити процес, спробуйте ввімкнути атрибут `autocomplete`, указавши для нього дійсне значення. [Дізнайтеся більше про `autocomplete` у формах.](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Елементи `<input>` не мають правильних атрибутів `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Потрібна перевірка вручну"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Перевірте порядок маркерів"}, "core/audits/autocomplete.js | title": {"message": "Елементи `<input>` використовують атрибут `autocomplete` правильно"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Маркери `autocomplete`: \"{token}\" недійсний у {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Перевірка порядку маркерів: \"{tokens}\" у {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Можна виправити"}, "core/audits/bf-cache.js | description": {"message": "На сайті часто виконуються переходи на попередню сторінку або повернення на вже відвідані. Таку навігацію можна пришвидшити за допомогою зворотного кешу. [Докладніше](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 причина помилки}one{# причина помилки}few{# причини помилки}many{# причин помилки}other{# причини помилки}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Причина помилки"}, "core/audits/bf-cache.js | failureTitle": {"message": "Сторінку не вдалося відновити зі зворотного кешу"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Тип помилки"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Не можна виправити"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Очікується підтримка веб-переглядача"}, "core/audits/bf-cache.js | title": {"message": "Сторінку вдалося відновити зі зворотного кешу"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Розширення Chrome негативно впливають на завантаження цієї сторінки. Спробуйте перевірити сторінку в режимі анонімного перегляду або в профілі Chrome без розширень."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Оцінка сценарію"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Синтаксичний аналіз сценарію"}, "core/audits/bootup-time.js | columnTotal": {"message": "Загальний процесорний час"}, "core/audits/bootup-time.js | description": {"message": "Зменште час виконання синтаксичного аналізу, компілювання й запуску скриптів JavaScript. Для цього корисно завантажувати менші обсяги даних JavaScript. [Дізнайтесь, як зменшити час виконання синтаксичного аналізу для JavaScript.](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Зменште час виконання JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Час виконання JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Видаліть великі копії модулів JavaScript із пакетів, щоб зменшити кількість непотрібних байтів під час активності в мережі. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Видаліть копії модулів у пакетах JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Великі файли GIF неефективні для передавання анімованого контенту. Щоб заощадити мережевий трафік даних, радимо замість формату GIF використовувати MPEG4 або WebM для анімацій і PNG чи WebP для статичних зображень. [Докладніше про ефективні формати відео.](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Використовуйте формати відео для анімованого вмісту"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Поліфіли та перетворення дають змогу застарілим веб-переглядачам використовувати нові функції JavaScript. Однак багато з них не потрібні для сучасних веб-переглядачів. Для пакетів JavaScript прийміть сучасну стратегію введення скрипту в дію за допомогою виявлення модульних/немодульних функцій, щоб зменшити обсяг коду, який надсилається сучасним веб-переглядачам зі збереженням підтримки застарілих. [Дізнайтесь, як використовувати сучасний код JavaScript.](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Не показуйте застарілі елементи JavaScript у сучасних веб-переглядачах"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Формати зображень WebP і AVIF часто стискаються краще, ніж PNG чи JPEG. Тому вони швидше завантажуються й використовують менше даних. [Докладніше про сучасні формати зображень.](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Показуйте зображення в нових форматах"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Щоб зменшити час до повного завантаження, використовуйте відкладене завантаження закадрових і прихованих зображень. Тоді важливі ресурси сайту завантажуватимуться в першу чергу. [Дізнайтесь, як відкласти завантаження закадрових зображень.](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Відкладіть закадрові зображення"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокують першу візуалізацію сторінки. Надсилайте спершу важливі фрагменти JavaScript або таблиці CSS і відкладайте всі некритичні елементи. [Діз<PERSON>йтесь, як вилучити ресурси, які блокують відображення.](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Вилучіть ресурси, які блокують відображення"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Великі обсяги мережевих даних використовують багато коштовного трафіку відвідувачів і довго завантажуються. [Дізнайтесь, як зменшити обсяг даних.](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Загальний розмір – {totalBytes, number, bytes} КіБ"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Уникайте великих обсягів даних у мережі"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Уникається великий обсяг даних мережі"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Стиснення файлів CSS може зменшити обсяг даних у мережі. [Дізнайтесь, як зменшити файли CSS.](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Зменште СSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Мініфікація файлів JavaScript може зменшити обсяг даних і час обробки скрипту. [Дізнайтесь, як зменшити файл JavaScript.](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Зменште файл JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Щоб скоротити трафік даних у мережі, зменште кількість непотрібних правил у таблицях стилів і відкладіть завантаження коду CSS, що не використовуються для контенту у видимій частині сторінки. [Дізнайтесь, як зменшити код CSS, який не використовується.](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Зменште код CSS, який не використовуєте"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Щоб скоротити трафік даних у мережі, зменште код JavaScript, який не використовуєте, і відкладіть завантаження скриптів, доки вони не знадобляться. [Діз<PERSON>йтесь, як зменшити код JavaScript, який не використовується.](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Зменште код JavaScript, який не використовуєте"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Якщо зберігати кеш за тривалий період часу, сторінки можуть завантажуватися швидше під час повторних відвідувань. [Докладніше про правила ефективного кешування.](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ресурс}one{Знайдено # ресурс}few{Знайдено # ресурси}many{Знайдено # ресурсів}other{Знайдено # ресурсу}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Показуйте статичні об’єкти за допомогою ефективних правил кешування"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Використовуються ефективні правила кешування статичних об’єктів"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимізовані зображення завантажуються швидше й використовують менше мобільного трафіку. [Дізнайтесь, як ефективно кодувати зображення.](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Ефективно кодуйте зображення"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Фактичні розміри"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Відображені розміри"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Розміри зображення більші за вказані"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Зображення відповідають указаним розмірам"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Показуйте зображення правильного розміру, щоб заощадити мобільний трафік і покращити час завантаження. [Діз<PERSON>йтесь, як змінювати розмір зображень.](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Правильно виберіть розмір зображень"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Щоб скоротити загальний трафік даних у мережі, текстові ресурси потрібно відображати зі стисненням (Gzip, Deflate ч<PERSON>). [Докладніше про стиснення тексту.](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Увімкніть стиснення тексту"}, "core/audits/content-width.js | description": {"message": "Якщо ширина контенту додатка не збігається з шириною області перегляду, можливо, додаток не вдасться оптимізувати для екранів мобільних пристроїв. [Дізнайтесь, як визначити розмір контенту для області перегляду.](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Розмір області перегляду ({innerWidth} пікс.) не збігається з розміром вікна ({outerWidth} пікс.)."}, "core/audits/content-width.js | failureTitle": {"message": "Розмір контенту не відповідає області перегляду"}, "core/audits/content-width.js | title": {"message": "Розмір контенту відповідає області перегляду"}, "core/audits/critical-request-chains.js | description": {"message": "Ланцюжки важливих запитів нижче показують, які ресурси мають високий пріоритет. Щоб пришвидшити завантаження сторінки, зменште довжину ланцюжків і розмір завантажень або відкладіть завантаження непотрібних ресурсів. [Дізнайтесь, як не створювати ланцюжки критичних запитів.](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ланцюжок}one{Знайдено # ланцюжок}few{Знайдено # ланцюжки}many{Знайдено # ланцюжків}other{Знайдено # ланцюжка}}"}, "core/audits/critical-request-chains.js | title": {"message": "Намагайтеся не створювати ланцюжки критичних запитів"}, "core/audits/csp-xss.js | columnDirective": {"message": "Директива"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Рівень серйозності"}, "core/audits/csp-xss.js | description": {"message": "Надійні Правила щодо безпеки контенту (CSP) значно зменшують ризик міжсайтного скриптингу (XSS). [Діз<PERSON><PERSON><PERSON>е<PERSON><PERSON>, як використовувати CSP, щоб запобігти XSS.](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Сторінка містить Правила щодо безпеки контенту (CSP), визначені в тегу <meta>. Спробуйте перемістити їх у заголовок HTTP або вказати інші суворі правила CSP в заголовку HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "CSP не знайдено в режимі примусового застосування"}, "core/audits/csp-xss.js | title": {"message": "Переконайтеся, що CSP допомагає проти атак XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Припинення підтримки/застереження"}, "core/audits/deprecations.js | columnLine": {"message": "Рядок"}, "core/audits/deprecations.js | description": {"message": "API, які більше не підтримуються, будуть остаточно вилучені з веб-переглядача. [Докладніше про непідтримувані API.](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 застереження}one{Знайдено # застереження}few{Знайдено # застереження}many{Знайдено # застережень}other{Знайдено # застереження}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Використовує непідтримувані API"}, "core/audits/deprecations.js | title": {"message": "Уникає інтерфейсів API, які більше не підтримуються"}, "core/audits/dobetterweb/charset.js | description": {"message": "Декларація кодування символів обов’язкова. Це можна зробити за допомогою тегу `<meta>` у перших 1024 байтах HTML або в заголовку відповіді HTTP Content-Type. [Дізнайтесь, як задати кодування символів.](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Декларація набору символів відсутня або виникає запізно в HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Належно визначає набір символів"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Якщо вказати елемент doctype, веб-переглядач не перейде в режим сумісності. [Докладніше про декларацію doctype.](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Назва елемента doctype має бути сегментом `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Документ містить фрагмент `doctype`, який активує режим `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документ має містити тег doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Очікується, що поле publicId буде порожнім"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Очікується, що поле systemId буде порожнім"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Документ містить фрагмент `doctype`, який активує режим `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Для сторінки не вказано елемента HTML doctype, що активує режим сумісності"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Сторінка має елемент HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистика"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Значення"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Через великий файл DOM використовується більше пам’яті, [стилі обчислюються](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) довше, а [перекомпонування макетів](https://developers.google.com/speed/articles/reflow) коштує дорого. [Дізнайтесь, як уникнути надмірного розміру DOM.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}one{# елемент}few{# елементи}many{# елементів}other{# елемента}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Уникайте надмірного розміру DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимальна глибина DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Усього елементів DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Макси<PERSON>а<PERSON>ьна кількість дочірніх елементів"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Уникається надмірний розмір DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Сайти, які надсилають запит на доступ до місцезнаходження без пояснення, викликають у користувачів недовіру або спантеличеність. Радимо пов’язувати запит із діями користувача. [Докладніше про дозвіл на доступ до геолокації.](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Надсилає запит на доступ до геолокації під час завантаження сторінки"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Уникає надсилання запитів на доступ до геолокації під час завантаження сторінки"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Тип проблеми"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Пом<PERSON><PERSON><PERSON><PERSON>, зареєстровані на панелі \"`Issues`\" в Інструментах розробника Chrome, указують на невирішені проблеми. Їх можуть спричиняти збої запитів мережі, низький рівень захисту або інші невиконані вимоги веб-переглядача. Щоб дізнатися більше про кожну помилку, відкрийте панель Issues в Інструментах розробника Chrome."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "На панелі \"`Issues`\" в Інструментах розробника Chrome наведено дані про помилки"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Заблоковано правилом щодо міждоменних запитів"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Оголошення використовують багато ресурсів"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "На панелі \"`Issues`\" в Інструментах розробника Chrome немає помилок"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Версія"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Усі бібліотеки JavaScript зовнішнього інтерфейсу виявлено на сторінці. [Докладніше про діагностичну перевірку для виявлення бібліотеки JavaScript.](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Виявлені бібліотеки JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Зовнішні скрипти, динамічно вставлені методом `document.write()`, можуть затримувати завантаження сторінки на десятки секунд для користувачів із повільним з’єднанням. [Дізнайтесь, як уникнути використання методу document.write().](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Уникайте `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Уникає `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Сайти, які надсилають запит на показ сповіщень без пояснення, викликають у користувачів недовіру або спантеличеність. Радимо пов’язувати запит із жестами користувача. [Дізнайтесь, як відповідально отримувати дозволи на показ сповіщень.](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Надсилає запит на показ сповіщень під час завантаження сторінки"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Уникає надсилання запитів на показ сповіщень під час завантаження сторінки"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Протокол HTTP/2 має низку переваг над HTTP/1.1, як-от двійкові заголовки та мультиплексування. [Докладніше про протокол HTTP/2.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 запит не розміщено через протокол HTTP/2}one{# запит не розміщено через протокол HTTP/2}few{# запити не розміщено через протокол HTTP/2}many{# запитів не розміщено через протокол HTTP/2}other{# запиту не розміщено через протокол HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Увімкніть протокол HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Щоб сторінка краще прокручувалася, позначте блоки прослуховування подій сенсорного екрана та коліщатка як `passive`. [Дізнайтесь, як використовувати пасивні блоки прослуховування подій.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не використовує пасивні прослуховувачі, щоб покращити функцію прокручування"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Використовує пасивні прослуховувачі, щоб покращити прокручування сторінки"}, "core/audits/errors-in-console.js | description": {"message": "Помилки, записані в журнал консолі, указують на невирішені проблеми. Вони можуть бути викликані збоями запитів мережі або іншими проблемами веб-переглядача. [Докладніше про ці помилки під час діагностичної перевірки консолі.](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Помилки веб-переглядача записано в журнал консолі"}, "core/audits/errors-in-console.js | title": {"message": "Помилки веб-переглядача не записано в журнал консолі"}, "core/audits/font-display.js | description": {"message": "Використовуйте функцію CSS `font-display`, щоб текст було видно під час завантаження веб-шрифтів. [Докладніше про `font-display`.](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "Переконайтеся, що текст залишається видимим під час завантаження веб-шрифту"}, "core/audits/font-display.js | title": {"message": "Увесь текст залишається видимим під час завантаження веб-шрифтів"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Інструменту Lighthouse не вдалося автоматично перевірити значення елемента `font-display` для URL-адреси {fontOrigin}.}one{Інструменту Lighthouse не вдалося автоматично перевірити значення елемента `font-display` для URL-адреси {fontOrigin}.}few{Інструменту Lighthouse не вдалося автоматично перевірити значення елемента `font-display` для URL-адреси {fontOrigin}.}many{Інструменту Lighthouse не вдалося автоматично перевірити значення елемента `font-display` для URL-адреси {fontOrigin}.}other{Інструменту Lighthouse не вдалося автоматично перевірити значення елемента `font-display` для URL-адреси {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Формат (фактичний)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Формат (відображуваний)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Розміри показаного зображення мають відповідати реальному формату. [Докладніше про формат зображення.](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Показує зображення неправильного формату"}, "core/audits/image-aspect-ratio.js | title": {"message": "Показує зображення правильного формату"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Дійсний розмір"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Показаний розмір"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Очікуваний розмір"}, "core/audits/image-size-responsive.js | description": {"message": "Для максимальної чіткості зображення співвідношення його сторін має бути пропорційним до розмірів і роздільної здатності екрана. [Дізнайтесь, як додавати адаптивні зображення.](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Показує зображення з низькою роздільною здатністю"}, "core/audits/image-size-responsive.js | title": {"message": "Показує зображення з правильною роздільною здатністю"}, "core/audits/installable-manifest.js | already-installed": {"message": "Цей додаток уже встановлено"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Не вдалося завантажити потрібний значок із маніфесту"}, "core/audits/installable-manifest.js | columnValue": {"message": "Причина помилки"}, "core/audits/installable-manifest.js | description": {"message": "Синтаксис Service Worker забезпечує багато функцій прогресивного веб-додатка, як-от режим офлайн, додавання на головний екран і push-сповіщення. Якщо правильно застосувати цей синтаксис і маніфест, веб-переглядачі зможуть активно заохочувати користувачів установити ваш додаток на головний екран, що сприятиме кращій взаємодії. [Докладніше про вимоги до маніфесту для встановлюваних додатків.](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 причина}one{# причина}few{# причини}many{# причин}other{# причини}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Маніфест або синтаксис Service Worker не відповідає вимогам до придатного для встановлення веб-додатка"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Розбіжність між URL-адресою та ідентифікатором додатка в Google Play"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Сторінка завантажується у вікні в режимі анонімного перегляду"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Для властивості display у маніфесті слід вибрати одне з таких значень: standalone, fullscreen або minimal-ui"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Маніфест містить поле display_override, тому для першого підтримуваного режиму відображення слід вибрати одне з таких значень: standalone, fullscreen або minimal-ui"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Не вдалося завантажити чи проаналізувати маніфест, або він порожній"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Під час отримання маніфесту його URL-адреса змінилася."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Маніфест не містить полів name або short_name"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "У маніфесті немає відповідного значка. Потрібен значок у форматі PNG, SVG або WebP із роздільною здатністю принаймні {value0} пікс. і визначеним атрибутом purpose. Якщо він задається, слід указати значення \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Немає значка розміром принаймні {value0} пікс. (квадрат) у форматі PNG, SVG або WebP, у якого атрибут purpose не заданий або заданий зі значенням \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Завантажений значок порожній або пошкоджений"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Не вказано ідентифікатор Google Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "На сторінці немає URL-адреси маніфесту в тегах <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Не виявлено відповідного синтаксису Service Worker. Можливо, потрібно перезавантажити сторінку або перевірити, чи область дії синтаксису Service Worker для поточної сторінки включає область дії та початкову URL-адресу з маніфесту."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Не вдалося перевірити синтаксис Service Worker без поля start_url у маніфесті"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Помилка перевірки придатності для встановлення з нерозпізнаним ідентифікатором \"{errorId}\""}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Сторінка завантажується не з надійного джерела"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Сторінка не завантажується в основному фреймі"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Сторінка не працює офлайн"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Прогресивний веб-додаток видалено. Перевірки придатності для встановлення скидаються."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Указана платформа додатка не підтримується на Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Значення властивості prefer_related_applications у маніфесті: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Властивість prefer_related_applications підтримується лише в бета-версії Chrome і стабільних версіях на Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Сервісу Lighthouse не вдалося знайти синтаксис Service Worker. Повторіть спробу в новішій версії Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Схема URL-адреси маніфесту ({scheme}) не підтримується на Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Початкова URL-адреса в маніфесті недійсна"}, "core/audits/installable-manifest.js | title": {"message": "Маніфест і синтаксис Service Worker відповідають вимогам до придатного для встановлення веб-додатка"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL-адреса в маніфесті містить ім'я користувача, пароль або порт"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Сторінка не працює офлайн. Після випуску Chrome 93 (стабільної версії від серпня 2021 року) сторінка вважатиметься непридатною для встановлення."}, "core/audits/is-on-https.js | allowed": {"message": "Дозволено"}, "core/audits/is-on-https.js | blocked": {"message": "Заблоковано"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Ненадійна URL-адреса"}, "core/audits/is-on-https.js | columnResolution": {"message": "Розв'язання запиту"}, "core/audits/is-on-https.js | description": {"message": "Усі сайти мають бути захищені протоколом HTTPS, навіть якщо вони не обробляють чутливі дані. Потрібно уникати [змішаного контенту](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), тобто коли деякі ресурси завантажуються через HTTP, незважаючи на те, що початковий запит було надіслано через HTTPS. HTTPS не дає зловмисникам змогу втручатись в обмін даними між додатком і користувачами або пасивно прослуховувати супутні події. Це обов’язкова умова для протоколу HTTP/2 та багатьох нових API веб-платформ. [Докладніше про HTTPS.](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ненадійний запит}one{Знайдено # ненадійний запит}few{Знайдено # ненадійні запити}many{Знайдено # ненадійних запитів}other{Знайдено # ненадійного запиту}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Не використовує протокол HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Використовує протокол HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Автоматичне перетворення на HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Дозволено із застереженням"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Це найбільший елемент контенту, який видно в області перегляду. [Докладніше про візуалізацію великого контенту.](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Елемент візуалізації великого контенту"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Дані CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Ці елементи DOM найбільше впливають на сукупне зміщення макета (CLS) сторінки. [Дізнайтесь, як покращити показник CLS.](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Уникайте великих зсувів макета"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Відкладене завантаження зображень видимої частини сторінки призводить до того, що вони відображаються пізніше. Це може затримати візуалізацію великого контенту. [Докладніше про оптимальний час для відкладеного завантаження.](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Зображення візуалізації великого контенту було завантажено відкладено"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Зображення візуалізації великого контенту не було завантажено відкладено"}, "core/audits/long-tasks.js | description": {"message": "Указано найтриваліші завдання в головному потоці. Ці дані корисні, щоб визначати, що найбільше впливає на затримку вхідного сигналу. [Діз<PERSON>йтесь, як уникнути тривалих завдань в основному потоці.](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено # тривале завдання}one{Знайдено # тривале завдання}few{Знайдено # тривалі завдання}many{Знайдено # тривалих завдань}other{Знайдено # тривалого завдання}}"}, "core/audits/long-tasks.js | title": {"message": "Уникайте тривалих завдань у головному потоці"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категорія"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Зменште час виконання синтаксичного аналізу, компілювання й запуску скриптів JavaScript. Для цього корисно завантажувати менші обсяги даних JavaScript. [Дізнайтесь, як зменшити обсяг роботи в основному потоці.](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Мінімізуйте роботу основного потоку"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Мінімізується робота основного потоку"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Щоб охопити якомога більше користувачів, сайти мають працювати в усіх відомих веб-переглядачах. [Докладніше про сумісність із різними веб-переглядачами.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Сайт працює у різних веб-переглядачах"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Переконайтеся, що окремі сторінки можна відкрити за допомогою прямого посилання, а URL-адреси унікальні, щоб ними можна було ділитися в соціальних мережах. [Докладніше про надання посилань на контент.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Кожна сторінка має URL-адресу"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Додаток має швидко реагувати на дії користувача, навіть якщо мережа повільна. Це значно впливає на оцінку роботи вашого додатка користувачем. [Докладніше про перенесення сторінок.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Схоже, перехід між сторінками не блокує мережу"}, "core/audits/maskable-icon.js | description": {"message": "Значок, який маскується, гарантує, що зображення заповнює всю форму, не перетворюючись на леттербокс під час встановлення додатка на пристрої. [Докладніше про значки маніфесту, які можна маскувати.](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Маніфест не має маскувального значка"}, "core/audits/maskable-icon.js | title": {"message": "Маніфест має маскувальний значок"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Сукупне зміщення макета вимірює рух видимих елементів у межах області перегляду. [Докладніше про показник \"Сукупне зміщення макета\".](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Показник Interaction to Next Paint вимірює, наскільки швидко сторінка може відповідати на ввід користувача. [Докладніше про показник Interaction to Next Paint.](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Перша візуалізація контенту показує, коли з’являється текст чи зображення. [Докладніше про показник \"Перша візуалізація контенту\".](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Перше значуще відображення вказує, коли видно основний контент сторінки. [Докладніше про показник \"Перше значуще відображення\".](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "Час до повного завантаження – це період часу, через який сторінка стане повністю інтерактивною. [Докладніше про показник \"Час до повного завантаження\".](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Візуалізація великого контенту показує, коли з’являється найбільший текст чи зображення. [Докладніше про показник \"Візуалізація великого контенту\".](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Максимальна потенційна затримка відповіді на першу дію – це тривалість найдовшого завдання. [Докладніше про показник \"Максимальна потенційна затримка відповіді на першу дію\".](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "Індекс швидкості показує, через скільки часу відображається вміст сторінки. [Докладніше про показник \"Індекс швидкості\".](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Загальна тривалість усіх періодів часу в мілісекундах між першою візуалізацією контенту та часом до повного завантаження, коли час виконання завдання перевищує 50 мс. [Докладніше про показник \"Загальний час блокування\".](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Час затримки передачі сигналу мережі (RTT) суттєво впливає на ефективність. Якщо показник RTT високий, це означає, що сервери, розташовані ближче до користувача, можуть покращити ефективність. [Докладніше про час передачі сигналу.](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "<PERSON>ас затримки передачі сигналу мережі"}, "core/audits/network-server-latency.js | description": {"message": "Затримка сервера може впливати на ефективність веб-сайту. Якщо показник затримки сервера високий, це означає, що сервер перевантажено або в нього низька ефективність. [Докладніше про час відповіді сервера.](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Затримка сервера"}, "core/audits/no-unload-listeners.js | description": {"message": "Подія `unload` активується ненадійно, і її прослуховування може запобігти оптимізації веб-переглядача, наприклад зворотного кешу. Натомість використовуйте подію `pagehide` або `visibilitychange`. [Докладніше про вивантаження блоків прослуховування подій.](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Реєструє прослуховувач події \"`unload`\""}, "core/audits/no-unload-listeners.js | title": {"message": "Уникає прослуховувачів події \"`unload`\""}, "core/audits/non-composited-animations.js | description": {"message": "Анімації без композитингу можуть бути неякісними та підвищувати показник CLS. [Дізнайтесь, як уникати анімацій без композитингу.](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено # анімований елемент}one{Знайдено # анімований елемент}few{Знайдено # анімовані елементи}many{Знайдено # анімованих елементів}other{Знайдено # анімованого елемента}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Ресурс, пов'язаний із фільтром, може переміщати пікселі"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Цільовий об'єкт містить несумісну анімацію"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Ефект не використовує композитний режим \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Уникайте анімацій без композитингу"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Ресурс, пов'язаний із трансформацією, залежить від розміру вікна"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Непідтримуваний ресурс CSS: {properties}}one{Непідтримувані ресурси CSS: {properties}}few{Непідтримувані ресурси CSS: {properties}}many{Непідтримувані ресурси CSS: {properties}}other{Непідтримувані ресурси CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Ефект містить непідтримувані параметри хронометражу"}, "core/audits/performance-budget.js | description": {"message": "Стежте, щоб кількість і розмір мережевих запитів не перевищували цільових значень, указаних у бюджеті продуктивності. [Докладніше про бюджети продуктивності.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 запит}one{# запит}few{# запити}many{# запитів}other{# запиту}}"}, "core/audits/performance-budget.js | title": {"message": "Бюджет ефективності"}, "core/audits/preload-fonts.js | description": {"message": "Попередньо завантажте шрифти з параметром \"`optional`\", щоб нові відвідувачі могли використовувати їх. [Докладніше про попереднє завантаження шрифтів.](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Шрифти з параметром \"`font-display: optional`\" не завантажено попередньо"}, "core/audits/preload-fonts.js | title": {"message": "Шрифти з параметром \"`font-display: optional`\" попередньо завантажено"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Якщо ви динамічно додаєте елемент LCP на сторінку, вам потрібно попередньо завантажити зображення, щоб оптимізувати час його візуалізації. [Докладніше про попереднє завантаження елементів LCP.](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Попередньо завантажте зображення для візуалізації великого контенту"}, "core/audits/redirects.js | description": {"message": "Переспрямування викликають додаткові затримки під час завантаження сторінки. [Дізнайтесь, як уникнути переспрямування сторінок.](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Уникайте переспрямувань кількох сторінок"}, "core/audits/resource-summary.js | description": {"message": "Щоб установити бюджет відповідно до кількості та розміру ресурсів на сторінці, додайте файл budget.json. [Докладніше про бюджети продуктивності.](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 запит • {byteCount, number, bytes} КіБ}one{# запит • {byteCount, number, bytes} КіБ}few{# запити • {byteCount, number, bytes} КіБ}many{# запитів • {byteCount, number, bytes} КіБ}other{# запиту • {byteCount, number, bytes} КіБ}}"}, "core/audits/resource-summary.js | title": {"message": "Не надсилайте багато запитів і передавайте вміст малого розміру"}, "core/audits/seo/canonical.js | description": {"message": "Канонічні посилання вказують, які URL-адреси відображати в результатах пошуку. [Докладніше про канонічні посилання.](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Кілька URL-адрес конфліктують ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Нед<PERSON><PERSON>сна URL-адреса ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Указує на інше місце `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Це не абсолютна URL-адреса ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Указує на основну URL-адресу домену (домашню сторінку), а не на еквівалентну сторінку вмісту"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Документ не має дійсного посилання `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Документ має дійсний атрибут `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Посила<PERSON>ня, які не можна сканувати"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Щоб сканувати веб-сайти, пошукові системи можуть використовувати в посиланнях атрибути `href`. Переконайтеся, що атрибут елементів прив’язки `href` перенаправляє в потрібне місце, щоб можна було зісканувати більше сторінок сайту. [Дізнайтесь, як зробити посилання доступними для сканування.](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Посилання не можна сканувати"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Посилання можна сканувати"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Інший нечитабельний текст"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Розмір шрифту"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% тексту на сторінці"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Селектор"}, "core/audits/seo/font-size.js | description": {"message": "Розміри шрифту до 12px замалі й нечитабельні. Щоб користувачі мобільних пристроїв змогли прочитати текст, їм потрібно буде збільшувати масштаб пальцями. Бажано, щоб понад 60% тексту на сторінці було написано щонайменше шрифтом 12px. [Докладніше про читабельні розміри шрифтів.](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} читабельного тексту"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Текст нечитабельний, оскільки метатег області перегляду не оптимізовано для мобільних пристроїв."}, "core/audits/seo/font-size.js | failureTitle": {"message": "У документі використано нечитабельні розміри шрифтів"}, "core/audits/seo/font-size.js | legibleText": {"message": "Читабельний текст"}, "core/audits/seo/font-size.js | title": {"message": "У документі використано читабельні розміри шрифтів"}, "core/audits/seo/hreflang.js | description": {"message": "Посилання hreflang указують пошуковим системам версію сторінки, яку потрібно додати в результати пошуку для певної мови чи регіону. [Докладніше про атрибут `hreflang`.](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Документ не має дійсного атрибута `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Відносне значення атрибута href"}, "core/audits/seo/hreflang.js | title": {"message": "Документ має дійсний атрибут `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Неочікуваний код мови"}, "core/audits/seo/http-status-code.js | description": {"message": "Сторінки з недійсними кодами статусу HTTP можуть неправильно індексуватися. [Докладніше про коди статусу HTTP.](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Сторінка має недійсний код статусу HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "Сторінка має дійсний код статусу HTTP"}, "core/audits/seo/is-crawlable.js | description": {"message": "Пошукові системи не зможуть включити ваші сторінки в результати пошуку, якщо в них немає дозволу сканувати їх. [Докладніше про директиви веб-сканерів.](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Сторінку не можна індексувати"}, "core/audits/seo/is-crawlable.js | title": {"message": "Сторінку можна індексувати"}, "core/audits/seo/link-text.js | description": {"message": "Описовий текст посилання допомагає пошуковим системам розуміти ваш контент. [Дізнайтесь, як зробити посилання доступнішими.](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 посилання}one{Знайдено # посилання}few{Знайдено # посилання}many{Знайдено # посилань}other{Знайдено # посилання}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Посилання не містять опису"}, "core/audits/seo/link-text.js | title": {"message": "Посилання містять опис"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Щоб перевірити структуровані дані, скористайтесь [інструментом тестування](https://search.google.com/structured-data/testing-tool/) й [інструментом статичного аналізу структурованих даних](http://linter.structured-data.org/). [Докладніше про структуровані дані.](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Структуровані дані дійсні"}, "core/audits/seo/meta-description.js | description": {"message": "Результати пошуку можуть містити метаописи для короткого підсумку вмісту сторінки. [Докладніше про метаопис.](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Немає опису."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Документ не містить метаопису"}, "core/audits/seo/meta-description.js | title": {"message": "Документ містить метаопис"}, "core/audits/seo/plugins.js | description": {"message": "Пошукові системи не можуть індексувати вміст плагінів. Багато пристроїв обмежують або не підтримують плагіни. [Докладніше про те, як уникати плагінів.](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Документ використовує плагіни"}, "core/audits/seo/plugins.js | title": {"message": "Документ уникає плагінів"}, "core/audits/seo/robots-txt.js | description": {"message": "Якщо файл robots.txt недійсний, веб-сканери можуть не зрозуміти, як потрібно індексувати або сканувати ваш веб-сайт. [Докладніше про файл robots.txt.](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "У відповідь на запит robots.txt отримано такий статус HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Знайдено 1 помилку}one{Знайдено # помилку}few{Знайдено # помилки}many{Знайдено # помилок}other{Знайдено # помилки}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Інструменту Lighthouse не вдалося завантажити файл robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Файл robots.txt недійсний"}, "core/audits/seo/robots-txt.js | title": {"message": "Файл robots.txt дійсний"}, "core/audits/seo/tap-targets.js | description": {"message": "Інтерактивні елементи, як-от кнопки та посилання, мають бути достатньо великі (48 x 48 пікселів), а навколо них має бути достатньо місця, щоб їх можна було легко натиснути, не зачепивши інші елементи. [Докладніше про інтерактивні елементи.](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} елементів для натискання мають правильний розмір"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Елементи для натискання замалі, оскільки метатег області перегляду не оптимізовано для мобільних пристроїв"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Елементи для натискання мають неправильний розмір"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Елементи для натискання накладаються"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Елемент для натискання"}, "core/audits/seo/tap-targets.js | title": {"message": "Елементи для натискання мають правильний розмір"}, "core/audits/server-response-time.js | description": {"message": "Сервер основного документа має відповідати швидко, оскільки всі інші запити залежать від нього. [Докладніше про показник \"Час до першого байта\".](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Кореневий документ відповів через {timeInMs, number, milliseconds} мс"}, "core/audits/server-response-time.js | failureTitle": {"message": "Зменште час відповіді сервера"}, "core/audits/server-response-time.js | title": {"message": "Сервер відповідає швидко"}, "core/audits/service-worker.js | description": {"message": "Синтаксис Service Worker – це технологія, яка дає змогу додатку використовувати багато функцій прогресивного веб-додатка, як-от режим офлайн, додавання на головний екран і push-сповіщення. [Докладніше про синтаксис Service Worker.](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Цією сторінкою керує синтаксис Service Worker, однак `start_url` не знайдено, оскільки не вдалося виконати синтаксичний аналіз маніфесту як дійсного файлу JSON"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Цією сторінкою керує синтаксис Service Worker, однак параметр `start_url` ({startUrl}) перебуває за межами дії служби ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Цією сторінкою керує синтаксис Service Worker, однак `start_url` не знайдено, оскільки маніфест не завантажено."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Це джерело містить один або кілька синтаксисів Service Worker, але сторінка ({pageUrl}) перебуває за межами дії служби."}, "core/audits/service-worker.js | failureTitle": {"message": "Немає синтак<PERSON>и<PERSON>у Service Worker, який керує сторінкою та `start_url`"}, "core/audits/service-worker.js | title": {"message": "Наявний синтаксис Service Worker, який керує сторінкою та `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Тематична заставка покращує взаємодію з користувачами під час запуску додатка з головного екрана. [Докладніше про заставки.](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "Власну заставку не налаштовано"}, "core/audits/splash-screen.js | title": {"message": "Налаштовано власну заставку"}, "core/audits/themed-omnibox.js | description": {"message": "Для адресного рядка веб-переглядача можна створити тему, яка відповідатиме вашому сайту. [Докладніше про теми для адресного рядка.](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Не змінює колір адресного рядка відповідно до теми."}, "core/audits/themed-omnibox.js | title": {"message": "Змінює колір адресного рядка відповідно до теми."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (підтримка клієнтів)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (маркетинг)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (соціальна мережа)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (відео)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Продукт"}, "core/audits/third-party-facades.js | description": {"message": "Для певних сторонніх вставок можливе відкладене завантаження. Замініть їх фасадом до моменту, коли вони знадобляться. [Діз<PERSON>йтесь, як відкладати сторонні вставки за допомогою фасаду.](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Доступний # варіант фасаду}one{Доступний # варіант фасаду}few{Доступні # варіанти фасаду}many{Доступні # варіантів фасаду}other{Доступні # варіанта фасаду}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Для певних сторонніх ресурсів доступні фасади й відкладене завантаження"}, "core/audits/third-party-facades.js | title": {"message": "Використовуйте фасади для відкладеного завантаження сторонніх ресурсів"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Сторонні розробники"}, "core/audits/third-party-summary.js | description": {"message": "Сторонній код може значно погіршити швидкість завантаження. Не використовуйте зайвий раз елементи коду сторонніх розробників та налаштуйте сторінку так, щоб сторонній код завантажувався після її основної частини. [Дізнайтесь, як мінімізувати вплив сторонніх кодів.](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "Сторонній код заблокував основний ланцюжок на {timeInMs, number, milliseconds} мс"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Зменште вплив стороннього коду"}, "core/audits/third-party-summary.js | title": {"message": "Зменште використання стороннього коду"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Значення"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Показник"}, "core/audits/timing-budget.js | description": {"message": "Установіть обмеження часу, щоб слідкувати за продуктивністю свого сайту. Ефективні сайти швидко завантажуються й миттєво відповідають на ввід користувача. [Докладніше про бюджети продуктивності.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Обмеження часу"}, "core/audits/unsized-images.js | description": {"message": "Явно вказуючи ширину й висоту для зображень, ви зможете зменшити зміщення макета й покращити показник CLS. [Діз<PERSON>йтесь, як налаштувати розміри зображень.](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Для зображень не задано явним чином атрибути `width` та `height`"}, "core/audits/unsized-images.js | title": {"message": "Для зображень явним чином задано атрибути `width` та `height`"}, "core/audits/user-timings.js | columnType": {"message": "Тип"}, "core/audits/user-timings.js | description": {"message": "Використовуйте в додатку User Timing API, щоб отримувати показники ефективності додатка під час основних моментів взаємодії з користувачами. [Докладніше про позначки користувацького часу.](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 позначка часу користувача}one{# позначка часу користувача}few{# позначки часу користувача}many{# позначок часу користувача}other{# позначки часу користувача}}"}, "core/audits/user-timings.js | title": {"message": "Показники й мітки часу користувача"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Для {<PERSON><PERSON><PERSON><PERSON>} знайдено елемент `<link rel=preconnect>`, але веб-переглядач його не використав. Переконайтеся, що ви правильно використовуєте атрибут `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Додайте в ресурси корективи `preconnect` чи `dns-prefetch`, щоб заздалегідь встановлювати з’єднання з важливими сторонніми джерелами. [Дізнайтесь, як попередньо підключитися до потрібних джерел.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Попередньо під’єднуйтеся до потрібних джерел"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Знайдено більше ніж 2 підключення `<link rel=preconnect>`. Такі засоби слід використовувати зрідка й лише для найважливіших джерел."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Для {security<PERSON><PERSON><PERSON>} знайдено елемент `<link rel=preconnect>`, але веб-переглядач його не використав. Використовуйте `preconnect` тільки для важливих джерел, яким сторінка точно надсилатиме запит."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Для {preloadURL} знайдено елемент `<link>` типу preload, але веб-переглядач його не використав. Переконайтеся, що ви правильно використовуєте атрибут `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Щоб під час завантаження сторінки пріоритет надавався ресурсам, на які надіслано запит, використовуйте `<link rel=preload>`. [Діз<PERSON>йтесь, як попередньо завантажувати основні запити.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Попередньо завантажуйте основні запити"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL-адреси карт"}, "core/audits/valid-source-maps.js | description": {"message": "Карти джерела перетворюють зменшені коди в оригінальні вихідні коди. Завдяки цьому розробники можуть здійснювати налагодження в робочому режимі. Крім того, інструмент Lighthouse може надавати іншу статистику. Спробуйте ввести в дію карти джерела, щоб скористатися цими перевагами. [Докладніше про карти джерела.](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Великий власний файл JavaScript не містить карт джерела"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Великий файл JavaScript не містить карту джерела"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Попередження: в атрибуті `.sourcesContent` немає 1 елемента}one{Попередження: в атрибуті `.sourcesContent` немає # елемента}few{Попередження: в атрибуті `.sourcesContent` немає # елементів}many{Попередження: в атрибуті `.sourcesContent` немає # елементів}other{Попередження: в атрибуті `.sourcesContent` немає # елемента}}"}, "core/audits/valid-source-maps.js | title": {"message": "Сторінка містить дійсні карти джерел"}, "core/audits/viewport.js | description": {"message": "За допомогою метатегу `<meta name=\"viewport\">` можна не лише оптимізувати додаток для мобільних пристроїв, а й запобігти [затримці тривалістю 300 мілісекунд під час того, як користувач вводить дані](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Дізнайтесь, як використовувати метатег області перегляду.](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Тег `<meta name=\"viewport\">` не знайдено"}, "core/audits/viewport.js | failureTitle": {"message": "Немає тегу `<meta name=\"viewport\">` з атрибутами `width` або `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Має тег `<meta name=\"viewport\">` з атрибутами `width` або `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Це блокування ланцюжка, що сталося під час вимірювання взаємодії з наступною візуалізацією. [Докладніше про показник Interaction to Next Paint.](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} мс витрачено на подію {interactionType}"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Ціль події"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Мінімізуйте роботу під час основної взаємодії"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Затримка відповіді"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Затримка перед відображенням відповіді"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON>ас обробки"}, "core/audits/work-during-interaction.js | title": {"message": "Мінімізується робота під час основної взаємодії"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Ці рекомендації допоможуть покращити використання ролей ARIA у вашому додатку, що може позитивно вплинути на взаємодію для користувачів допоміжних технологій, як-от програм зчитування з екрана."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ці рекомендації допоможуть надати альтернативний вміст для аудіо та відео. Це може покращити взаємодію для користувачів із вадами слуху або зору."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Аудіо та відео"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ці елементи надають поширені практичні поради щодо спеціальних можливостей."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Практичні поради"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ці перевірки визначають можливості для [покращення доступності веб-додатка](https://developer.chrome.com/docs/lighthouse/accessibility/). Радимо також проводити перевірки вручну, оскільки не всі проблеми з доступністю визначаються автоматично."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ці елементи опрацьовують області, які не може охопити автоматизований інструмент перевірки. Докладніше читайте в нашому посібнику з [перевірки доступності](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Спеціальні можливості"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ці рекомендації допоможуть покращити читабельність вмісту."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ці рекомендації допоможуть покращити те, як користувачі тлумачать ваш вміст різними мовами."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Інтернаціоналізація та локалізація"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ці рекомендації допоможуть удосконалити семантику елементів керування в додатку. Це може покращити взаємодію для користувачів допоміжних технологій, як-от програм зчитування з екрана."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Назви та мітки"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ці рекомендації допоможуть покращити навігацію клавіатурою в додатку."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навігація"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ці рекомендації допоможуть покращити перегляд даних у таблиці чи списку за допомогою технології для людей з обмеженими можливостями, наприклад програми зчитування з екрана."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Таблиці та списки"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Сумісність веб-переглядача"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Оптимальні методи"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Загальні"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Довіра й безпека"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Взаємодія з користувачами"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Бюджети визначають стандарти ефективності вашого сайту."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Бюджети"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Докладніше про ефективність додатка. Ці числа не [впливають безпосередньо](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) на значення ефективності."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Діагностика"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Найважливішим аспектом ефективності є швидкість відображення пікселів на екрані. Основні показники: перше відображення вмісту, перше значне відображення"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Покращення першого відображення"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ці пропозиції допоможуть завантажувати сторінку швидше. Вони не [впливають безпосередньо](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) на значення ефективності."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Можливості"}, "core/config/default-config.js | metricGroupTitle": {"message": "Показники"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Покращте загальну ефективність завантаження, щоб сторінка швидко реагувала й завантажувалася. Основні показники: час до повної взаємодії, індекс швидкості"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Загальні покращення"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Ефективність"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Ці категорії підтверджують аспекти прогресивного веб-додатка. [Дізнайтесь, як створити якісний прогресивний веб-додаток.](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ці категорії обов'язкові відповідно до основного [контрольного списку для прогресивних веб-додатків](https://web.dev/pwa-checklist/), але Lighthouse не перевіряє їх автоматично. Вони не впливають на ваш показник, проте їх потрібно підтвердити вручну."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "Прогресивний веб-додаток"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Можна встановити"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимізовано для прогресивного веб-додатка"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Ці перевірки допоможуть подбати про те, щоб ваша сторінка відповідала основним рекомендаціям щодо пошукової оптимізації. Є чимало інших факторів, які не враховує Lighthouse і які можуть вплинути на рейтинг сторінки в результатах пошуку (наприклад, продуктивність відповідно до [Основних веб-показників](https://web.dev/learn-core-web-vitals/)). [Докладніше про основи Google Пошуку.](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Щоб отримати додаткові практичні поради щодо оптимізації пошукових систем, скористайтеся додатковими засобами перевірки на своєму сайті."}, "core/config/default-config.js | seoCategoryTitle": {"message": "Оптим. пошук. систем"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Відформатуйте HTML так, щоб веб-сканери краще розуміли вміст вашого додатка."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Практичні поради щодо вмісту"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Щоб ваш вміст з’являвся в результатах пошуку, веб-сканерам потрібно надати доступ до додатка."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Сканування й індексування"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Переконайтеся, що ваші сторінки адаптовані для мобільних пристроїв і користувачам не потрібно зменшувати чи збільшувати масштаб, щоб читати їх вміст. [Д<PERSON>з<PERSON>йтесь, як оптимізувати сторінки для мобільних пристроїв.](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Для мобільних пристроїв"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "ЦП пристрою для тестування працює повільніше, ніж потрібно для роботи Lighthouse. Це може негативно позначитися на оцінці ефективності. Докладніше про [калібрування відповідного множника сповільнення ЦП](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Ця сторінка може не завантажуватися належним чином, оскільки тестову URL-адресу ({requested}) було переспрямовано на {final}. Спробуйте перейти безпосередньо на другу URL-адресу."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Сторінка завантажувалася занадто повільно, щоб показати всі результати в межах визначеного часу, тому вони можуть бути неповними."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Час очікування на очищення кешу веб-переглядача минув. Перевірте сторінку ще раз. Якщо проблема не зникне, повідомте про помилку."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{У цьому місці можуть зберігатися дані, що впливають на тривалість завантаження: {locations}. Щоб такі ресурси не впливали на ваші показники, перевірте цю сторінку у вікні в режимі анонімного перегляду.}one{У цих місцях можуть зберігатися дані, що впливають на тривалість завантаження: {locations}. Щоб такі ресурси не впливали на ваші показники, перевірте цю сторінку у вікні в режимі анонімного перегляду.}few{У цих місцях можуть зберігатися дані, що впливають на тривалість завантаження: {locations}. Щоб такі ресурси не впливали на ваші показники, перевірте цю сторінку у вікні в режимі анонімного перегляду.}many{У цих місцях можуть зберігатися дані, що впливають на тривалість завантаження: {locations}. Щоб такі ресурси не впливали на ваші показники, перевірте цю сторінку у вікні в режимі анонімного перегляду.}other{У цих місцях можуть зберігатися дані, що впливають на тривалість завантаження: {locations}. Щоб такі ресурси не впливали на ваші показники, перевірте цю сторінку у вікні в режимі анонімного перегляду.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Час очікування на очищення даних минув. Перевірте сторінку ще раз. Якщо проблема не зникне, повідомте про помилку."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Для зворотного кешу підтримуються лише сторінки, завантажені через запити GET."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Можна кешувати лише сторінки з кодом статусу 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Веб-переглядач Chrome виявив спробу виконання JavaScript для сторінки в кеші."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які надіслали запит на AppBanner, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Зворотний кеш вимкнено експериментальними параметрами. Щоб увімкнути його локально на цьому пристрої, перейдіть на сторінку chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Зворотний кеш вимкнено командним рядком."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Зворотний кеш вимкнено через недостатній обсяг пам’яті."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Зворотний кеш не підтримується делегатом."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Зворотний кеш вимкнено засобом попереднього завантаження."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Сторінку не можна додати в кеш, оскільки вона містить екземпляр BroadcastChannel із зареєстрованими прослуховувачами."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Сторінки із заголовком cache-control:no-store не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Кеш було навмисно очищено."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Сторінку вилучено з кешу, щоб можна було кешувати іншу сторінку."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Сторінки з плагінами зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Сторінки, які використовують FileChooser API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Сторінки, які використовують File System Access API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують Media Device Dispatcher, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Медіапрогравач відтворював контент під час виходу."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Сторінки, які використовують MediaSession API і для яких вибрано статус відтворення, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Сторінки, які використовують MediaSession API та для яких налаштовано обробники дій, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Зворотний кеш вимкнено через програму зчитування з екрана."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують клас SecurityHandler, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Сторінки, які використовують Serial API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Сторінки, які використовують WebAuthetication API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Сторінки, які використовують WebBluetooth API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Сторінки, які використовують WebUSB API, не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Стор<PERSON>н<PERSON><PERSON>, які використовують Dedicated Worker або Worklet, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Документ не було завантажено перед виходом."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Під час виходу відображався банер додатка."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Під час виходу відображався Менеджер паролів Chrome."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Під час виходу тривала дистиляція DOM."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Під час виходу працював засіб перегляду DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Зворотний кеш вимкнено через розширення, що використовують інтерфейси API для обміну повідомленнями."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Розширення з постійним з’єднанням мають розірвати його перед переходом у зворотний кеш."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Розширення з постійним з’єднанням намагалися надіслати повідомлення фреймам у зворотному кеші."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Зворотний кеш вимкнено через розширення."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Під час виходу на сторінці відображалося вікно (наприклад, для повторного надсилання форми чи введення пароля http)."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Під час виходу відобража<PERSON>а<PERSON>ь офлайн-сторінка."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Під час виходу відображалася панель втручання через нестачу пам’яті."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Під час виходу відображалися запити на дозволи."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Під час виходу працював блокувальник спливаючих вікон."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Під час виходу відображалась інформація від Безпечного перегляду."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Безпечний перегляд виявив порушення на цій сторінці й заблокував спливаюче вікно."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Синтаксис Service Worker активовано, коли сторінка була у зворотному кеші."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Зворотний кеш вимкнено через помилку документа."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Сторінки, які використовують елемент FencedFrames, не можна зберегти у зворотному кеші."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Сторінку вилучено з кешу, щоб можна було кешувати іншу сторінку."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Сторінки, які надали доступ до потоку медіа, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Сторінки, які використовують портали, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які використовують IdleManager, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Сторінки з активованим з’єднанням IndexedDB зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Використано непридатні інтерфейси API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Сторінки, на які розширення вставили фрагменти JavaScript, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Стор<PERSON>н<PERSON><PERSON>, на які розширення вставили фрагменти StyleSheet, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Внутрішня помилка."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Зворотний кеш вимкнено через запит повідомлення keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Сторінки, які використовують блокування клавіатури, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | loading": {"message": "Сторінка не завершила завантаження перед тим, як ви вийшли з неї."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Сторінки, головний ресурс яких містить cache-control:no-cache, не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Сторінки, головний ресурс яких містить cache-control:no-store, не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Перехід скасовано до того, як сторінку було відновлено зі зворотного кешу."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Сторінку вилучено з кешу, оскільки активне з’єднання з мережею отримало забагато даних. У Chrome діють обмеження щодо обсягу даних, який кешована сторінка може отримувати."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Сторінки з надісланими запитами fetch() або XHR зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Сторінку вилучено зі зворотного кешу, оскільки активний запит мережі містив переспрямування."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Сторінку вилучено з кешу, оскільки з’єднання з мережею тривало занадто довго. У Chrome діють обмеження щодо тривалості часу, упродовж якого кешована сторінка може отримувати дані."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Сторінки, які не мають дійсного заголовка відповіді, не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Перехід відбувся не в головному фреймі."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Сторінки з активними індексованими транзакціями баз даних зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Сторінки з надісланими запитами мережі зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Сторінки з надісланими запитами мережі fetch зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Сторінки з надісланими запитами мережі зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Сторінки з надісланими запитами мережі XHR зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які використовують PaymentManager, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Сторінки, які використовують функцію Picture-in-Picture, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | portal": {"message": "Сторінки, які використовують портали, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | printing": {"message": "Стор<PERSON>нк<PERSON>, на яких відображається інтерфейс друку, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Сторінку відкрито за допомогою методу `window.open()`, а інша вкладка має посилання на неї, або сторінка відкрила вікно."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Під час процесу обробки для цієї сторінки у зворотному кеші відбувся збій."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Процес обробки для цієї сторінки у зворотному кеші завершився."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Сторінки, які надіслали запит на дозвіл записувати аудіо, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Стор<PERSON>нки, які надіслали запит на доступ до датчиків, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Сторінки, які надіслали запит на синхронізацію у фоновому режимі чи запит на отримання даних, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Сторінки, які надіслали запит на доступ до пристроїв MIDI, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Сторінки, які надіслали запит на доступ до сповіщень, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Сторінки, які надіслали запит на доступ до пам’яті, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Сторінки, які надіслали запит на дозвіл записувати відео, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "У кеш можна додавати лише сторінки, у яких схеми URL-адрес – це HTTP / HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Синтаксис Service Worker заявив права на цю сторінку, додану у зворотний кеш."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Синтаксис Service Worker намагався надіслати `MessageEvent` сторінці у зворотному кеші."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Реєстрацію ServiceWorker скасовано, коли сторінка була у зворотному кеші."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Сторінку вилучено зі зворотного кешу через активацію синтаксису Service Worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome перезапущено, записи зворотного кешу очищено."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують SharedWorker, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують SpeechRecognizer, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Сторінки, які використовують SpeechSynthesis, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Елемент iframe на сторінці розпочав перехід, який не було завершено."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Сторінки, субресурси яких містять cache-control:no-cache, не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Сторінки, субресурси яких містять cache-control:no-store, не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Ця сторінка перевищила максимальний час зберігання у зворотному кеші. Її термін дії завершився."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Час очікування сторінки на додавання у зворотний кеш минув (імовірно, через довготривалу роботу обробників подій pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Сторінка містить обробник вивантаження в основному фреймі."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Сторінка містить обробник вивантаження в додатковому фреймі."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Веб-переглядач змінив заголовок перевизначення агента користувача."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Сторінки, які надали дозвіл на запис відео чи аудіо, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Сторінки, які використовують WebDatabase, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Сторінки, які використовують WebHID, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Сторінки, які використовують WebLocks, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують WebNfc, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Стор<PERSON>нк<PERSON>, які використовують WebOTPService, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Сторінки з WebRTC не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Сторінки, які використовують WebShare, зараз не підтримуються для зворотного кешу."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Сторінки з WebSocket не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Сторінки з WebTransport не можна додавати у зворотний кеш."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Сторінки, які використовують WebXR, зараз не підтримуються для зворотного кешу."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Додайте схеми URL-адрес https: і http: (ігноруються веб-переглядачами, що підтримують strict-dynamic) для зворотної сумісності зі старішими веб-переглядачами."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Директива disown-opener не підтримується, починаючи з версії CSP3. Натомість використовуйте заголовок Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Директива referrer не підтримується, починаючи з версії CSP2. Натомість використовуйте заголовок Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Директива reflected-xss не підтримується, починаючи з версії CSP2. Натомість використовуйте заголовок X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Якщо директиви base-uri немає, за допомогою включених тегів <base> основною URL-адресою для всіх відносних URL-адрес (наприклад, скриптів) можна вказати домен, яким керує зловмисник. Рекомендуємо вибрати для директиви base-uri значення none або self."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Якщо немає директиви object-src, то можна вставляти плагіни, які виконують ненадійні скрипти. Радимо вибрати для директиви object-src значення none."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Немає директиви script-src. Через це можуть виконуватися ненадійні скрипти."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Можливо, ви забули додати крапку з комою? Схоже, {keyword} – це директива, а не ключове слово."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces мають бути набором символів base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Мінімальна довжина для nonces – 8 символів."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Не використовуйте прості схеми URL-адрес ({keyword}) у цій директиві. Через них джерелом скриптів може бути ненадійний домен."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Не використовуйте прості символи підстановки ({keyword}) у цій директиві. Через них джерелом скриптів може бути ненадійний домен."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Призначення звітів можна налаштувати лише через директиву report-to. Вона підтримується тільки у веб-переглядачах на основі Chromium, тому радимо також скористатися директивою report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Немає CSP, що налаштовує призначення звітів. Це ускладнює підтримку CSP впродовж тривалого часу та відстеження поломок."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Білі списки хостів часто можна обходити. Радимо натомість використовувати nonces або hashes правила CSP, а також за потреби директиву strict-dynamic."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Невідома директива CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "Схоже, {keyword} – це недійсне ключове слово."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "unsafe-inline дозволяє виконувати ненадійні вбудовані скрипти та застосовувати обробники подій. Щоб дозволити окремі скрипти, скористатися nonces або hashes для CSP."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Додайте команду unsafe-inline (ігнорується веб-переглядачами, що підтримують nonces/hashes) для зворотної сумісності зі старішими веб-переглядачами."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "На авторизацію під час обробки CORS `Access-Control-Allow-Headers` не поширюватиметься символ підстановки (*)."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Запити ресурсів до URL-адрес, які містять вилучені пробіли `(n|r|t)` та знаки менше (`<`), блокуються. Щоб завантажити ці ресурси, вилучіть символи нового рядка з таких місць, як значення атрибутів елементів, і закодуйте знаки менше."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` не підтримується. Натомість використовуйте стандартизований інтерфейс API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` не підтримується, натомість використовуйте стандартизований інтерфейс API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` не підтримується. Натомість використовуйте стандартизований метод API: `nextHopProtocol` у специфікації Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Файли cookie, які містять символ `(0|r|n)`, будуть відхилятися, а не скорочуватися."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Правило одного джерела більше не можна буде послаблювати, налаштувавши `document.domain`, і цю функцію буде вилучено. Це сповіщення про припинення міждоменного доступу, активованого завдяки налаштуванню `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Запуск {PH1} із міждоменних елементів iframe не підтримується, і в майбутньому цю функцію буде вилучено."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Щоб вимкнути інтеграцію Google Cast за умовчанням, використовуйте атрибут `disableRemotePlayback` замість засобу вибору `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} не підтримується. Натомість використовуйте {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Це приклад перекладеного повідомлення, пов’язаного з припиненням підтримки."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Правило одного джерела більше не можна буде послаблювати, налаштувавши `document.domain`, і цю функцію буде вилучено. Щоб продовжити користуватися цією функцією, вимкніть кластери агентів за ключем джерела. Для цього надішліть заголовок `Origin-Agent-Cluster: ?0` із відповіддю HTTP для документа й фреймів. Докладніше читайте на сторінці https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Метод `Event.path` не підтримується, його буде вилучено. Натомість використовуйте `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Заголовок `Expect-CT` не підтримується, і його буде вилучено. Chrome вимагає перевірки всіх загальнодоступних надійних сертифікатів, виданих після 30 квітня 2018 року."}, "core/lib/deprecations-strings.js | feature": {"message": "Щоб дізнатися більше, перегляньте сторінку зі статусом функції."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` і `watchPosition()` більше не працюють із незахищеними джерелами. Щоб користуватися цією функцією, перенесіть свій додаток у захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` і `watchPosition()` з незахищених джерел не підтримуються. Щоб користуватися цією функцією, перенесіть свій додаток у захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` більше не працює з незахищеними джерелами. Щоб користуватися цією функцією, перенесіть свій додаток у захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` не підтримується. Натомість використовуйте `RTCPeerConnectionIceErrorEvent.address` або `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Джерело продавця й довільні дані події синтаксису Service Worker `canmakepayment` не підтримуються. Їх буде вилучено: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Веб-сайт надіслав запит на субресурс у мережі, доступній лише завдяки повноваженням її користувачів. Такі запити відкривають інтернет-доступ до приватних пристроїв і серверів, що збільшує ризик атак із підробкою міжсайтових запитів (CSRF) та/або витоку інформації. Щоб знизити ці ризики, Chrome ігнорує запити до приватних субресурсів, які надходять із небезпечного контексту, і почне блокувати їх."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS не можна завантажити з URL-адрес `file:`, які не мають розширення файлу `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "У зв’язку зі зміною специфікацій скасування асинхронного вилучення діапазону елемента `remove()` через `SourceBuffer.abort()` не підтримується. У майбутньому цю функцію буде вилучено. Натомість вам слід керуватися подією `updateend`. `abort()` слід використовувати лише для того, щоб скасувати асинхронне додавання медіафайлів або скинути стан синтаксичного аналізатора."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "У зв’язку зі зміною специфікацій налаштування значення `MediaSource.duration` нижче за найвищу відображувану позначку часу будь-яких закодованих фреймів у буфері не підтримується. У майбутньому неявне вилучення стиснених медіафайлів із буфера не підтримуватиметься. Натомість застосовуйте явну функцію `remove(newDuration, oldDuration)` для всіх елементів `sourceBuffers`, де `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Ця зміна почне діяти для версії {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Навіть якщо в `MIDIOptions` не вказано значення SysEx, веб-сайти проситимуть дозвіл на використання MIDI."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Notification API більше не можна використовувати з незахищених джерел. Перенесіть свій додаток у захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Дозвіл на Notification API більше не можна запитувати з міждоменних елементів iframe. Запитайте дозвіл із фрейму верхнього рівня або відкрийте нове вікно."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Ваш партнер використовує застарілу версію (D)TLS. Щоб вирішити проблему, зверніться до партнера."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "Інтерфейс WebSQL не підтримується в незахищених контекстах, і невдовзі його буде вилучено. Використовуйте Web Storage або Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Якщо вказати властивість `overflow: visible` у тегах img, video й canvas, візуальний контент може відображатися за межами цих елементів. Докладніше: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` не підтримується. Натомість скористайтесь актуальним інтерфейсом API для обробників платежів."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Ваш виклик `PaymentRequest` обійшов директиву `connect-src` Правил щодо безпеки контенту (CSP). Такий обхід не підтримується. Додайте в директиву CSP `connect-src` ідентифікатор способу оплати з `PaymentRequest` API (у полі `supportedMethods`)."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` не підтримується. Натомість використовуйте стандартну властивість `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Елемент `<source src>` із батьківським елементом `<picture>` недійсний і тому ігнорується. Натомість використовуйте `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` не підтримується. Натомість використовуйте стандартну властивість `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Запити субресурсів за URL-адресами, що містять вставлені облікові дані (наприклад, `**********************/`), блокуються."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Обмеження `DtlsSrtpKeyAgreement` вилучено. Ви вказали значення `false` для цього обмеження, що ми витлумачили як спробу застосувати вилучений метод \"`SDES key negotiation`\". Цю функцію вилучено; натомість використовуйте сервіс, що підтримує \"`DTLS key negotiation`\"."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Обмеження `DtlsSrtpKeyAgreement` вилучено. Ви вказали значення `true`, що не вплинуло на це обмеження, але можете вилучити його для більшої ясності."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Розпізнано протокол `Complex Plan B SDP`. Діалект протоколу `Session Description Protocol` більше не підтримується. Натомість використовуйте `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "Протокол `Plan B SDP semantics`, який використовується для налаштування `RTCPeerConnection` із `{sdpSemantics:plan-b}`, – це застаріла нестандартна версія протоколу `Session Description Protocol`, яку назавжди видалено з веб-платформи. Вона досі доступна, якщо ви здійснюєте налаштування за допомогою `IS_FUCHSIA`, але ми плануємо вилучити цю версію в найближчому майбутньому. Намагайтеся не використовувати її. Щоб переглянути статус вилучення, перейдіть на сторінку https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Опція `rtcpMuxPolicy` не підтримується, її буде вилучено."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "Для `SharedArrayBuffer` вимагатиметься ізоляція від міждоменних джерел. Докладніше читайте на сторінці https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Виклик `speechSynthesis.speak()` без активації користувача не підтримується, і цю функцію буде вилучено."}, "core/lib/deprecations-strings.js | title": {"message": "Використовується функція, яка більше не підтримується"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Щоб і надалі використовувати `SharedArrayBuffer`, для розширень потрібно ввімкнути ізоляцію від міждоменних джерел. Докладніше читайте на сторінці https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} залежить від постачальника. Натомість використовуйте стандартний метод {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 не підтримується відповіддю JSON у `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхронний виклик `XMLHttpRequest` в основному потоці не підтримується, оскільки негативно впливає на взаємодію з кінцевим користувачем. Докладніше читайте на сторінці https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` не підтримується. Натомість використовуйте `isSessionSupported()`, щоб перевірити передане логічне значення."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON>ас блокування основного ланцюжка"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL кешу"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Тривалість"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Елемент"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Відхилені елементи"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Місцезнаходження"}, "core/lib/i18n/i18n.js | columnName": {"message": "Назва"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Перевищення бюджету"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Запити"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Розмір ресурсу"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурсу"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Розмір"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Час початку"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Витрачений час"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Розмір передавання"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL-адреса"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенційне заощадження"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенційне заощадження"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Можна зменшити на {wastedBytes, number, bytes} КіБ"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Знайдено 1 елемент}one{Знайдено # елемент}few{Знайдено # елементи}many{Знайдено # елементів}other{Знайдено # елемента}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенційне заощадження – {wastedMs, number, milliseconds} мс"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Перше значне відображення"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON>ри<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Зображення"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Високий"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Низький"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Середній"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Максима<PERSON>ьна потенційна затримка відповіді на першу дію"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Медіа"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} мс"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Інше"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Інші ресурси"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Сценарій"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} с"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Таблиця стилів"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Сторонні"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Усього"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Не вдалося записати результати трасування для завантаження вашої сторінки. Запустіть інструмент Lighthouse ще раз. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Час очікування початкового з’єднання з протоколом Debugger минув."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Веб-переглядач Chrome не отримав знімки екрана під час завантаження сторінки. Переконайтеся, що на сторінці є видимий вміст, а потім спробуйте перезапустити інструмент Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-серверам не вдалось обробити вказаний домен."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "У збирачі обов'язкових ресурсів {artifactName} сталася помилка: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Сталася внутрішня помилка Chrome. Перезапустіть Chrome і спробуйте знову запустити інструмент Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Зб<PERSON><PERSON><PERSON>ч обов'язкових ресурсів {artifactName} не запущено."}, "core/lib/lh-error.js | noFcp": {"message": "Ця сторінка не відобразила контент. Переконайтеся, що вікно веб-переглядача перебуває в активному режимі під час завантаження, і повторіть спробу. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "На сторінці не було показано контент, який може вважатися візуалізацією великого контенту (LCP). Переконайтеся, що сторінка містить дійсний елемент LCP, і повторіть спробу. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Надана сторінка не у форматі HTML (використовується як тип MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ця версія Chrome застаріла та не підтримує функцію \"{featureName}\". Щоб переглянути повну версію результатів, скористайтеся новішою версією."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити URL-адресу, яку ви вказали, оскільки сторінка перестала відповідати."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Указана вами URL-адреса не має дійсного сертифіката безпеки. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Веб-переглядач Chrome заблокував завантаження сторінки та відобразив проміжний екран. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити. (Деталі: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити. (Код статусу: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Сторінка завантажувалася задовго. Дотримуйтеся рекомендацій у звіті, щоб зменшити час завантаження сторінки, а потім спробуйте перезапустити інструмент Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Час очікування відповіді протоколу DevTools перевищив установлений період. (Метод: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Час отримання вмісту ресурсу перевищив установлений час"}, "core/lib/lh-error.js | urlInvalid": {"message": "Схоже, указана вами URL-адреса недійсна."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Тип сторінки MIME – XHTML: Lighthouse не підтримує цей тип документів"}, "core/user-flow.js | defaultFlowName": {"message": "Послідовність переходів ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Звіт про навігацію ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Звіт про стан на певний момент часу ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Звіт про період часу ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Усі звіти"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Категорії"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Спеціальні можливості"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Оптимальні методи"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Ефективність"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Прогресивний веб-додаток"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "Оптим. пошук. систем"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Версія для комп’ютера"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Пояснення звіту Lighthouse про послідовність переходів"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Пояснення звіту про переходи"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Звіти про навігацію допоможуть вам…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Звіти про стан на певний момент часу допоможуть вам…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Звіти про період часу допоможуть вам…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Отримати оцінку ефективності в Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Вимірювати показники ефективності завантаження сторінки, такі як візуалізація великого контенту й індекс швидкості."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Оцінювати можливості прогресивного веб-додатка."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Знаходити проблеми з доступністю в односторінкових додатках чи складних формах."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Оцінювати меню й елементи інтерфейсу на відповідність оптимальним методам підтримки взаємодії."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Вимірювати зміщення макета й час виконання JavaScript для серії взаємодій."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Знаходити можливості покращити ефективність постійних сторінок і односторінкових додатків."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Найвагоміші"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} інформативна перевірка}one{{numInformative} інформативна перевірка}few{{numInformative} інформативні перевірки}many{{numInformative} інформативних перевірок}other{{numInformative} інформативної перевірки}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Мобільна версія"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Завантаження сторінки"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Звіти про навігацію аналізують завантаження однієї сторінки, як і оригінальні звіти Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Звіт про навігацію"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} звіт про навігацію}one{{numNavigation} звіт про навігацію}few{{numNavigation} звіти про навігацію}many{{numNavigation} звітів про навігацію}other{{numNavigation} звіту про навігацію}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{Можна здійснити {numPassableAudits} перевірку}one{Можна здійснити {numPassableAudits} перевірку}few{Можна здійснити {numPassableAudits} перевірки}many{Можна здійснити {numPassableAudits} перевірок}other{Можна здійснити {numPassableAudits} перевірки}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} перевірку пройдено}one{{numPassed} перевірку пройдено}few{{numPassed} перевірки пройдено}many{{numPassed} перевірок пройдено}other{{numPassed} перевірки пройдено}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Посередньо"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Помилка"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Погано"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Добре"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Зберегти"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Зафіксований статус сторінки"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Звіти про стан на певний момент часу аналізують сторінку в конкретному стані, зазвичай після дій користувача."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Звіт про стан на певний момент часу"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} звіт про стан на певний момент часу}one{{numSnapshot} звіт про стан на певний момент часу}few{{numSnapshot} звіти про стан на певний момент часу}many{{numSnapshot} звітів про стан на певний момент часу}other{{numSnapshot} звіту про стан на певний момент часу}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Підсумок"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Взаємодії користувача"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Звіти про період часу аналізують довільний період часу та зазвичай містять дані про дії користувачів."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Звіт про період часу"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} звіт про період часу}one{{numTimespan} звіт про період часу}few{{numTimespan} звіти про період часу}many{{numTimespan} звітів про період часу}other{{numTimespan} звіту про період часу}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Звіт про послідовність переходів у Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Для анімованого контенту застосовуйте [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), щоб зменшити використання ЦП, коли контент поза межами екрана."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Радимо показувати всі компоненти [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) у форматах WebP, указуючи відповідні резервні варіанти для інших веб-переглядачів. [Докладніше](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Переконайтеся, що ви використовуєте [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites), щоб зображення автоматично відкладено завантажувалися. [Докладніше](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Використовуйте інструменти, наприклад [Оптимізатор сторінок AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), щоб [обробляти макети AMP на сервері](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Перегляньте [документацію AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), щоб переконатися, що всі стилі підтримуються."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Елемент [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) підтримує атрибут [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), щоб на основі розміру екрана указувати, які зображення використовувати. [Докладніше](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Якщо обробляються дуже великі списки, скористайтеся віртуальним прокручуванням за допомогою Component Dev Kit (CDK). [Докладніше](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "[Розділіть код на рівні маршруту](https://web.dev/route-level-code-splitting-in-angular/), щоб зменшити розмір пакетів JavaScript. Також радимо попередньо кешувати об'єкти за допомогою [синтаксису Service Worker для Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Якщо ви користуєтесь інтерфейсом командного рядк<PERSON>, переконайтеся, що складання генеруються в режимі робочої версії. [Докладніше](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Якщо ви користуєтесь інтерфейсом командного рядка <PERSON>, додайте карти джерел у робочу версію складання, щоб перевірити пакети. [Докладніше](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Попередньо завантажуйте маршрути, щоб прискорити навігацію. [Докладніше](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Можете скористатись інструментом `BreakpointObserver` у Component Dev Kit (CDK), щоб керувати точками переходу зображень. [Докладніше](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Спробуйте завантажити анімацію GIF у сервіс, де її можна вставити як відео HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Укажіть `@font-display` під час визначення власних шрифтів у темі."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Спробуйте змінити [стиль зображень у форматі WebP](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) на своєму сайті."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Установіть [модуль Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), який може відкладено завантажувати зображення. Такі модулі дають змогу відкласти завантаження закадрових зображень для покращення ефективності."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Скористайтеся модулем, щоб вбудувати важливі таблиці CSS і фрагменти JavaScript, або завантажуйте об'єкти асинхронно через JavaScript, як-от модуль [розширеного зведення CSS/JS](https://www.drupal.org/project/advagg). Зауважте, що така оптимізація може порушити роботу сайту, тож доведеться змінити код."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Теми, модулі та характеристики сервера впливають на час відповіді. Спробуйте знайти більш оптимізовану тему, підібрати модуль для оптимізації та/або оновити сервер. Сервери хостингу повинні використовувати кешування коду операції PHP та кешування пам'яті, щоб зменшити час запитів до баз даних, як-от Redis або Memcached, а також оптимізовану логіку додатка для швидшої підготовки сторінок."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Застосуйте [стилі адаптивних зображень](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), щоб зменшити розмір зображень, що завантажуються на сторінці. Якщо для показу кількох елементів контенту на сторінці використовується опція \"Перегляди\", застосуйте поділ на сторінки, щоб обмежити їхню кількість на певній сторінці."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Переконайтеся, що функцію \"Звести файли CSS\" на сторінці \"Керування\" » \"Налаштування\" » \"Розробка\" ввімкнено. За допомогою [додаткових модулів](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) можна також налаштувати інші параметри зведення, щоб пришвидшити свій сайт, поєднуючи, зменшуючи та стискаючи стилі CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Переконайтеся, що функцію \"Звести файли JavaScript\" на сторінці \"Керування\" » \"Налаштування\" » \"Розробка\" ввімкнено. За допомогою [додаткових модулів](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) можна також налаштувати інші параметри зведення, щоб пришвидшити свій сайт, поєднуючи, зменшуючи та стискаючи об'єкти JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Видаліть правила CSS, що не використовуються, і додайте лише потрібні бібліотеки Drupal на відповідну сторінку чи в компонент сторінки. Щоб дізнатися більше, перегляньте [документацію Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) за цим посиланням. Щоб визначити долучені бібліотеки, які додають зайві таблиці CSS, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Ви можете визначити тему чи модуль через URL-адресу таблиці стилів, коли зведення CSS вимкнено на вашому сайті Drupal. У покритті коду знайдіть теми чи модулі з багатьма таблицями стилів за великим обсягом червоного тексту. Тема чи модуль мають ставити таблицю стилів у чергу, лише коли вона дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Видаліть об'єкти JavaScript, що не використовуються, і додавайте лише потрібні бібліотеки Drupal на відповідну сторінку чи в компонент сторінки. Щоб дізнатися більше, перегляньте [документацію Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) за цим посиланням. Щоб визначити вкладені бібліотеки, які додають зайві фрагменти JavaScript, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Ви можете визначити тему чи модуль через URL-адресу скрипту, коли зведення JavaScript вимкнено на вашому сайті Drupal. У покритті коду знайдіть теми чи модулі з багатьма скриптами за великим обсягом червоного тексту. Тема чи модуль мають ставити скрипт у чергу, лише коли він дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Укажіть \"Максимальний час зберігання кешу веб-переглядача та проксі-сервера\" на сторінці \"Керування\" » \"Налаштування\" » \"Розробка\". Докладніше про [кеш Drupal і оптимізацію для підвищення ефективності](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Скористайтеся [модулем](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), який автоматично оптимізує та зменшує розмір зображень, завантажених через сайт, але зберігає їх якість. Також переконайтеся, що використовуються оригінальні [стилі адаптивних зображень](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) від Drupal (доступні в Drupal 8 і новіших версій) для всіх зображень, що обробляються на сайті."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Попередньо підключені або завантажені через dns корективи ресурсів можна додати, установивши та налаштувавши [модуль](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), який надає можливості для коректив ресурсів агента користувача."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Переконайтеся, що використовуються оригінальні [стилі адаптивних зображень](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) від Drupal (доступні в Drupal 8 і новіших версій). Використовуйте стилі адаптивних зображень під час обробки полів зображення в режимах перегляду, переглядах або зображеннях, завантажених через редактор WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Optimize Fonts`, щоб автоматично застосувати функцію CSS `font-display`. Так користувачі бачитимуть текст, поки будуть завантажуватися веб-шрифти."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Next-Gen Formats`, щоб конвертувати зображення у формат WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Lazy Load Images`, щоб відкласти завантаження прихованих зображень, доки вони не знадобляться."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Critical CSS` та `Script Delay`, щоб відкласти завантаження некритичних фрагментів JS і таблиць CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Скористайтесь [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), щоб додати свій контент у кеш у нашій глобальній мережі та покращити час до першого байта."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Minify CSS`, щоб автоматично зменшити таблиці CSS і, відповідно, обсяг даних у мережі."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Minify Javascript`, щоб автоматично зменшити фрагменти JS і, відповідно, обсяг даних у мережі."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Remove Unused CSS`, щоб отримати допомогу з цією проблемою. Буде виявлено класи CSS, які наразі використовуються на кожній сторінці вашого сайту, і видалено всі інші, щоб забезпечити менший розмір файлу."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Efficient Static Cache Policy`, щоб задати рекомендовані значення в заголовку кешування для статичних об’єктів."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Next-Gen Formats`, щоб конвертувати зображення у формат WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Pre-Connect Origins`, щоб автоматично додати корективи ресурсів `preconnect`. Таким чином можна заздалегідь встановлювати з’єднання з важливими сторонніми джерелами."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Preload Fonts` та `Preload Background Images`, щоб додати посилання `preload`. Таким чином під час завантаження сторінки пріоритет надаватиметься ресурсам, на які надіслано запит."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Скористайтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) і ввімкніть налаштування `Resize Images`, щоб змінити розміри зображень відповідно до пристрою та, відповідно, зменшити обсяг даних у мережі."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Спробуйте завантажити анімацію GIF у сервіс, де її можна вставити як відео HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Скористайтеся [плагіном](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) або сервісом, який автоматично конвертує завантажені зображення в оптимальні формати."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Щоб відкладати завантаження закадрових зображень, установіть [плагін lazy-load для Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) або виберіть відповідний шаблон. Починаючи з версії Joomla 4.0, усі нові зображення [автоматично](https://github.com/joomla/joomla-cms/pull/30748) отримуватимуть атрибут `loading`."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Існує багато плагін<PERSON><PERSON>, які можуть допомогти [вбудувати важливі об'єкти](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) або [відкласти менш важливі ресурси](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Зауважте, що така оптимізація може порушити функції шаблонів або плагінів, тож вам потрібно ретельно протестувати їх."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Шаблони, розширення та характеристики сервера впливають на час відповіді. Спробуйте знайти більш оптимізований шаблон, підібрати розширення для оптимізації та/або оновити сервер."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Спробуйте показувати витяги в категоріях статей (через посилання \"Докладніше\"), зменшити кількість показаних статей на сторінці, розділити довгі дописи на кілька сторінок або скористатися плагіном, щоб відкласти завантаження коментарів."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Багато [розширен<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) можуть пришвидшити ваш сайт, поєднуючи, зменшуючи та стискаючи стилі. Існують також шаблони з такими функціями."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Багато [розширен<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) можуть пришвидшити ваш сайт, поєднуючи, зменшуючи та стискаючи скрипти. Існують також шаблони з такими функціями."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Спробуйте зменшити кількість [розширен<PERSON> Joomla](https://extensions.joomla.org/), що завантажують на сторінці непотрібні таблиці CSS. Щоб визначити розширення, які додають зайві таблиці CSS, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Ви можете визначити тему чи плагін через URL-адресу таблиці стилів. У покритті коду знайдіть плагіни з багатьма таблицями стилів за великим обсягом червоного тексту. Плагін має ставити таблицю стилів у чергу, лише коли вона дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Спробуйте зменшити кількість [розшир<PERSON><PERSON><PERSON> Joomla](https://extensions.joomla.org/), що завантажують на сторінці непотрібні фрагменти JavaScript. Щоб визначити плагіни, які додають зайвий код JavaScript, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Розширення можна визначити через URL-адресу скрипту. У покритті коду знайдіть розширення з багатьма скриптами за великим обсягом червоного тексту. Розширення має ставити скрипт у чергу, лише коли він дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Дізнайтеся про [кешування веб-переглядача в Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Спробуйте [плагін для оптимізації зображень](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), який стискає зображення, але зберігає їх якість."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Скористайтеся [плагіном для адаптивних зображень](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), щоб застосовувати такі зображення у своєму контенті."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Ви можете активувати стиснення тексту, увімкнувши стиснення сторінок Gzip у Joomla (Система > Загальні налаштування > Сервер)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Якщо ви не створюєте пакет об'єктів JavaScript, можете скористатись [інструментом пакування](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Вимкніть вбудовану функцію [створення пакетів JavaScript і мініфікації](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) в Magento й натомість скористайтесь [інструментом пакування](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Укажіть `@font-display` під час [визначення власних шрифтів](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "На [торговельному майданчику Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) можна знайти різні сторонні розширення, щоб використовувати новіші формати зображень."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Можете змінити шаблони свого продукту й каталогу, щоб скористатися функцією веб-платформи для [відкладеного завантаження](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Скористайтесь [інтеграцією Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) від Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Увімкніть опцію \"Зменшувати файли CSS\" у налаштуваннях розробника в магазині. [Докладніше](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Скористайтеся плагіном [Terser](https://www.npmjs.com/package/terser), щоб зменшити всі об'єкти JavaScript у розгортанні статичного контенту й вимкнути вбудовану функцію мініфікації."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Вимкніть вбудовану функцію [створення пакетів JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) у Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Можете пошукати на [торговельному майданчику Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) різні сторонні розширення для оптимізації зображень."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Щоб додати підказки preconnect і dns-prefetch, [змініть макет теми](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Теги `<link rel=preload>` можна додати, лише [змінивши макет теми](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Щоб автоматично оптимізувати формат зображень, замість `<img>` використовуйте компонент `next/image`. [Докладніше](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Щоб зображення автоматично відкладено завантажувалися, замість `<img>` використовуйте компонент `next/image`. [Докладніше](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Скористайтеся компонентом `next/image` і задайте параметру priority значення true, щоб попередньо завантажити зображення LCP. [Докладніше.](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Щоб відкласти завантаження некритичних сторонніх скриптів, використайте компонент `next/script`. [Докладніше.](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Скористайтеся компонентом `next/image`, щоб зображення підбиралися правильного розміру. [Докладніше](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Щоб вилучати з таблиць стилів правила, які не використовуються, налаштуйте плагін `PurgeCSS` у конфігурації `Next.js`. [Докладніше.](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Щоб виявити код <PERSON>, який не застосовується, скористайтесь інструментом `Webpack Bundle Analyzer`. [Докладніше](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Щоб отримувати показники ефективності додатка під час взаємодії з користувачами, використовуйте `Next.js Analytics`. [Докладніше.](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Налашуйте кешування для незмінюваних об’єктів і сторінок`Server-side Rendered` (SSR). [Докладніше.](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Щоб покращити якість зображення, замість `<img>` використовуйте компонент `next/image`. [Докладніше](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Щоб налаштувати потрібні `sizes`, використовуйте компонент `next/image`. [Докладніше.](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Увімкніть стиснення на сервері Next.js. [Докладніше.](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Скористайтеся компонентом `nuxt/image`, щоб налаштувати значення `format=\"webp\"`. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Скористайтеся компонентом `nuxt/image`, щоб налаштувати значення `loading=\"lazy\"` для закадрових зображень. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Скористайтеся компонентом `nuxt/image`, щоб налаштувати значення `preload` для зображення LCP. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Скористайтеся компонентом `nuxt/image`, щоб чітко задати значення для параметрів `width` і `height`. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Скористайтеся компонентом `nuxt/image`, щоб налаштувати потрібне значення `quality`. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Скористайтеся компонентом `nuxt/image`, щоб налаштувати потрібне значення `sizes`. [Докладніше](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Замініть анімації GIF на відео](https://web.dev/replace-gifs-with-videos/) для швидшого завантаження веб-сторінок і скористайтеся сучасними форматами файлів, як-от [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) або [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), для підвищення ефективності стиснення більш як на 30% порівняно із сучасним відеокодеком VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Скористайтеся [плагіном](https://octobercms.com/plugins?search=image) або сервісом, який автоматично конвертує завантажені зображення в оптимальні формати. [Зображення WebP без утрат](https://developers.google.com/speed/webp) на 26% менші за файли PNG та на 25–34% менші за аналогічні зображення JPEG за еквівалентного індексу якості SSIM. Ще одна сучасна альтернатива – формат [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Щоб відкладати завантаження закадрових зображень, установіть [плагін](https://octobercms.com/plugins?search=lazy) або виберіть відповідну тему. Також можете скористатися [плагіном AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Існує багато плагінів, які допомагають [вбудувати важливі об'єкти](https://octobercms.com/plugins?search=css). Вони можуть порушувати роботу інших плагінів, тож вам потрібно ретельно протестувати їх."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Теми, плагіни й характеристики сервера впливають на час відповіді. Спробуйте знайти більш оптимізовану тему, підібрати плагін для оптимізації та/або оновити сервер. October CMS також дає розробникам змогу використовувати [`Queues`](https://octobercms.com/docs/services/queues), щоб відкласти обробку тривалого завдання (як-от надсилання електронного листа). Це значно пришвидшує виконання веб-запитів."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Спробуйте замість повного тексту показувати в списках публікацій витяги (наприклад, з кнопкою \"`show more`\"), зменшити кількість публікацій на одну веб-сторінку, розділяти довгі дописи на кілька сторінок або застосувати плагін для відкладеного завантаження коментарів."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Існує багато [плагінів](https://octobercms.com/plugins?search=css), які можуть пришвидшити роботу веб-сайту, поєднуючи, зменшуючи та стискаючи стилі. Також можна пришвидшити розробку, скориставшись процесом складання, щоб заздалегідь зменшити розмір."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Існує багато [плагінів](https://octobercms.com/plugins?search=javascript), які можуть пришвидшити роботу веб-сайту, поєднуючи, зменшуючи та стискаючи скрипти. Також можна пришвидшити розробку, скориставшись процесом складання, щоб заздалегідь зменшити розмір."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Радимо знайти [плагіни](https://octobercms.com/plugins), які завантажують на веб-сайті непотрібні таблиці CSS. Щоб визначити їх, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Інструментах розробника Chrome. Проблемні теми чи плагіни можна ідентифікувати за URL-адресою таблиці стилів. На проблему вказує довжина червоної смуги на діаграмі покриття коду. Що довша червона смуга, то більше зайвих таблиць стилів додає плагін. Плагін має додавати таблицю стилів, лише коли вона дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Радимо знайти [плагіни](https://octobercms.com/plugins?search=javascript), що завантажують на веб-сторінці непотрібні фрагменти JavaScript. Щоб визначити їх, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Інструментах розробника Chrome. Проблемні теми й плагіни можна ідентифікувати за URL-адресами скриптів. На проблему вказує довжина червоної смуги на діаграмі покриття коду. Що довша червона смуга, то більше зайвих скриптів додає плагін. Плагін має додавати скрипт, лише коли він дійсно використовується на веб-сторінці."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Дізнайтеся, як [кеш HTTP допомагає уникнути непотрібних запитів мережі](https://web.dev/http-cache/#caching-checklist). Існує багато [плагінів](https://octobercms.com/plugins?search=Caching), які прискорюють кешування."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Спробуйте [плагін для оптимізації зображень](https://octobercms.com/plugins?search=image), щоб стискати зображення, не втрачаючи якість."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Завантажуйте зображення через менеджер медіа<PERSON><PERSON><PERSON><PERSON><PERSON>в, щоб переконатися, що вони доступні в потрібному розмірі. Скористайтеся [фільтром](https://octobercms.com/docs/markup/filter-resize) або [плагіном для зміни розміру](https://octobercms.com/plugins?search=image), щоб оптимізувати розміри зображень."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Увімкніть стиснення тексту в конфігурації веб-сервера."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Якщо на сторінці відрисовується багато повторюваних елементів, радимо скористатися бібліотекою для віконізації, як-от `react-window`, щоб створювати менше вузлів DOM. [Докладніше.](https://web.dev/virtualize-long-lists-react-window/) Крім того, скоротіть повторні відрисовки за допомогою [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) або [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) й [пропускайте ефекти](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), доки не зміняться певні залежності, якщо для покращення швидкодії застосовується хук `Effect`."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Якщо ви користуєтеся React Router, зменште використання компонента `<Redirect>` для [навігації за маршрутом](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Якщо ви обробляєте компоненти React на сервері, можете застосувати `renderToPipeableStream()` або `renderToStaticNodeStream()`, щоб дозволити клієнту отримувати та вмикати не всю розмітку відразу, а її різні частини. [Докладніше](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Якщо ваша система складання автоматично зменшує файли CSS, переконайтеся, що ви вводите в дію робочу версію додатка. Це можна перевірити за допомогою розширення Інструменти розробника React. [Докладніше](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Якщо ваша система складання автоматично зменшує файли JS, переконайтеся, що ви вводите в дію робочу версію додатка. Це можна перевірити за допомогою розширення Інструменти розробника React. [Докладніше](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Якщо у вас не відбувається обробка на сервері, [розділіть пакети JavaScript](https://web.dev/code-splitting-suspense/) за допомогою `React.lazy()`. Або розділіть код за допомогою сторонньої бібліотеки, як-от [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Скористайтеся профілювальником React DevTools, який застосовує Profiler API, щоб визначити ефективність обробки ваших компонентів. [Докладніше.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Спробуйте завантажити анімацію GIF у сервіс, де її можна вставити як відео HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "За допомогою плагіна [Performance Lab](https://wordpress.org/plugins/performance-lab/) можна автоматично конвертувати додані зображення JPEG у формат WebP (де він підтримується)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Установіть [плагін lazy-load від WordPress](https://wordpress.org/plugins/search/lazy+load/), який дає змогу відкласти завантаження закадрових зображень, або виберіть тему, що дозволяє це зробити. Також можете скористатися [плагіном AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Існує багато плагінів WordPress, які можуть допомогти [вбудувати важливі об'єкти](https://wordpress.org/plugins/search/critical+css/) або [відкласти менш важливі ресурси](https://wordpress.org/plugins/search/defer+css+javascript/). Зауважте, що така оптимізація може порушити функції теми або плагінів, тож доведеться змінити код."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Теми, плагіни й характеристики сервера впливають на час відповіді. Спробуйте знайти більш оптимізовану тему, підібрати плагін для оптимізації та/або оновити сервер."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Спробуйте показувати витяги в списках дописів (через тег \"більше\"), зменшити кількість показаних публікацій на сторінці, розділити довгі дописи на кілька сторінок або скористатися плагіном, щоб відкласти завантаження коментарів."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Багато [плагінів WordPress](https://wordpress.org/plugins/search/minify+css/) можуть пришвидшити ваш сайт: вони об'єднують, зменшують і стискають стилі. Також можна скористатися процесом складання, щоб завчасно зменшити розмір, якщо це можливо."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Багато [плагінів WordPress](https://wordpress.org/plugins/search/minify+javascript/) можуть пришвидшити ваш сайт: вони об'єднують, зменшують і стискають сценарії. Також можна скористатися процесом складання, щоб завчасно зменшити розмір, якщо це можливо."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Спробуйте зменшити кількість [плагінів WordPress](https://wordpress.org/plugins/), що завантажують на сторінці непотрібні таблиці стилів CSS. Щоб визначити плагіни, які додають зайві таблиці CSS, перевірте [покриття коду](https://developer.chrome.com/docs/devtools/coverage/) в Chrome DevTools. Ви можете визначити тему чи плагін через URL-адресу таблиці стилів. У покритті коду знайдіть плагіни з багатьма таблицями стилів за великим обсягом червоного тексту. Плагін має ставити таблицю стилів у чергу, лише коли вона дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Спробуйте зменшити кількість [плагінів WordPress](https://wordpress.org/plugins/), що завантажують на сторінці непотрібні фрагменти JavaScript. Щоб визначити плагіни, які додають зайвий код JavaScript, перевірте [покриття коду](https://developer.chrome.com/docs/devtools/coverage/) в Chrome DevTools. Ви можете визначити тему чи плагін через URL-адресу сценарію. У покритті коду знайдіть плагіни з багатьма сценаріями за великим обсягом червоного тексту. Плагін має ставити сценарій у чергу, лише коли він дійсно використовується на сторінці."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Дізнайтеся про [кешування веб-переглядача у WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Спробуйте [плагін WordPress для оптимізації зображень](https://wordpress.org/plugins/search/optimize+images/), який стискає зображення, але зберігає їх якість."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Завантажуйте зображення через [бібліотеку медіафайлів](https://wordpress.org/support/article/media-library-screen/), щоб переконатися, що вони доступні в потрібному розмірі. Потім вставляйте їх в оптимальному розмірі з бібліотеки або через віджет для зображень (зокрема для адаптивних точок переходу). Використовуйте зображення, що мають `Full Size`, лише якщо вони повністю поміщаються. [Докладніше](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Ви можете ввімкнути стиснення тексту в конфігурації веб-сервера."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Щоб конвертувати зображення у формат WebP, увімкніть доповнення Imagify на вкладці Image Optimization (Оптимізація зображень) у WP Rocket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Щоб скористатися цією рекомендацією, увімкніть функцію [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) у WP Rocket. Ця функція затримує завантаження зображень, доки відвідувач не прокрутить сторінку вниз, щоб побачити їх."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Щоб скористатися цією рекомендацією, увімкніть функції WP Rocket [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Вилучити контент CSS, який не використовується) та [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (Завантажувати JavaScript відстрочено). Ці функції оптимізуватимуть файли CSS і JavaScript відповідно, щоб вони не блокували візуалізацію сторінки."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Щоб вирішити цю проблему, увімкніть функцію [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Зменшувати файли CSS) у WP Rocket. З файлів CSS вашого сайту буде вилучено пустий простір і коментарі, щоб зменшити файли та швидше їх завантажувати."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Щоб вирішити цю проблему, увімкніть функцію [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Зменшувати файли JavaScript) у WP Rocket. З файлів JavaScript буде вилучено пустий простір і коментарі, щоб зменшити файли та швидше їх завантажувати."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Щоб вирішити цю проблему, увімкніть функцію [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Вилучити контент CSS, який не використовується) у WP Rocket. Розмір сторінки зменшиться, оскільки буде вилучено весь контент CSS і таблиці стилів, які не використовуються. Залишиться тільки потрібний контент CSS для кожної сторінки."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Щоб вирішити цю проблему, увімкніть функцію [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Затримувати запуск JavaScript) у WP Rocket. Так сторінка краще завантажуватиметься, оскільки скрипти не виконуватимуться до моменту взаємодії з користувачем. Якщо на вашому сайті є елементи iframe, також можна використовувати такі функції WP Rocket, як [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (LazyLoad для iframe і відео) та [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Замінити iframe YouTube на зображення попереднього перегляду)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Щоб стиснути зображення, увімкніть доповнення Imagify на вкладці Image Optimization (Оптимізація зображень) у WP Rocket і запустіть функцію Bulk Optimization (Масова оптимізація)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Щоб додати параметр dns-prefetch і прискорити з’єднання із зовнішніми доменами, використовуйте функцію [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Попередньо вибирати DNS-запити) у WP Rocket. Крім того, цей фреймворк автоматично включає параметр preconnect у [домен Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) і всі записи CNAME, додані за допомогою функції [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Увімкнути мережу CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Щоб вирішити цю проблему зі шрифтами, увімкніть функцію [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Вилучити контент CSS, який не використовується) у WP Rocket. Важливі для вашого сайту шрифти матимуть пріоритет під час попереднього завантаження."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Показати калькулятор."}, "report/renderer/report-utils.js | collapseView": {"message": "Згорнути"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Початкова навігація"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Максимальна критична затримка шляху:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Копіювати JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Увімкнути або вимкнути темну тему"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Розгорнути вікно друку"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Друкувати підсумок"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Зберегти як Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Зберегти як HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Зберегти як JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Відкрити в засобі перегляду"}, "report/renderer/report-utils.js | errorLabel": {"message": "Помилка."}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Повідомлення про помилку: немає інформації про перевірку"}, "report/renderer/report-utils.js | expandView": {"message": "Розгорнути"}, "report/renderer/report-utils.js | footerIssue": {"message": "Повідомити про проблему"}, "report/renderer/report-utils.js | hide": {"message": "Сховати"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Дані тестів"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) поточної сторінки в емульованій мобільній мережі. Значення приблизні й можуть відрізнятися."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Додаткові елементи, які потрібно перевірити вручну"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Не застосовуються"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Можливість"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Приблизне заощадження"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Виконані перевірки"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Перше завантаження сторінки"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Спеціальне обмеження пропускної спроможності"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Емульований комп'ютер"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Без емуля<PERSON><PERSON>ї"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Версія Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Необмежена пропускна спроможність ЦП чи пам’яті"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Обмеження пропускної спроможності ЦП"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Прис<PERSON><PERSON><PERSON>й"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Зменшення пропускної спроможності мережі"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Емуляція екрана"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Агент користувача (мережа)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Одне завантаження сторінки"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Було взято дані одного завантаження сторінки, а не дані поля, що охоплюють кілька сеансів."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Обмеження пропускної спроможності для мережі 4G з низькою швидкістю"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Невідомо"}, "report/renderer/report-utils.js | show": {"message": "Показати"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Показано аудити, релевантні для:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Згорнути фрагмент"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Розгорнути фрагмент"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Показати сторонні ресурси"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Надано середовищем"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Під час запуску Lighthouse виникли перелічені нижче проблеми."}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Значення приблизні й можуть відрізнятися. [Значення ефективності визначено](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) на основі цих показників."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Переглянути оригінальне трасування"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Переглянути трасування"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Переглянути веб-додаток Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Перевірки зі статусом \"Пройдено\", що містять застереження"}, "report/renderer/report-utils.js | warningHeader": {"message": "Застереження. "}, "treemap/app/src/util.js | allLabel": {"message": "Усе"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Усі скрипти"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Дані про використання"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Задубльовані модулі"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Розмір ресурсу в байтах"}, "treemap/app/src/util.js | tableColumnName": {"message": "Назва"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Показати або сховати таблицю"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Невикористані байти"}}