{"version": 3, "file": "IsolatedWorld.js", "sourceRoot": "", "sources": ["../../../../src/common/IsolatedWorld.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAMH,iDAAyC;AACzC,mEAAiE;AAOjE,2DAAgE;AAChE,+DAAgF;AAUhF,uCAKmB;AACnB,+CAAoD;AAmDpD;;GAEG;AACH,MAAa,aAAa;IAaxB,IAAI,WAAW;QACb,OAAO,uBAAA,IAAI,kCAAa,CAAC;IAC3B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,uBAAA,IAAI,+BAAU,CAAC;IACxB,CAAC;IAED,YAAY,KAAY;;QApBxB,uCAAc;QACd,0CAAoC;QACpC,iCAAW,IAAA,0CAAqB,GAAoB,EAAC;QACrD,kCAAY,KAAK,EAAC;QAElB,oEAAoE;QACpE,yCAAmB,IAAI,GAAG,EAAU,EAAC;QAErC,+EAA+E;QAC/E,kCAAY,IAAI,GAAG,EAAmB,EAAC;QACvC,qCAAe,IAAI,yBAAW,EAAE,EAAC;QAqPjC,yEAAyE;QACzE,yEAAyE;QACzE,+BAAS,IAAI,KAAK,EAAE,EAAC;QAwCrB,yCAAmB,KAAK,EACtB,KAA0C,EAC3B,EAAE;YACjB,IAAI,OAAuB,CAAC;YAC5B,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aACrC;YAAC,MAAM;gBACN,mEAAmE;gBACnE,6CAA6C;gBAC7C,OAAO;aACR;YACD,MAAM,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC;YACnD,IAAI,IAAI,KAAK,UAAU,EAAE;gBACvB,OAAO;aACR;YACD,IAAI,CAAC,uBAAA,IAAI,sCAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpC,OAAO;aACR;YAED,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,8BAAS,CAAC;YACpC,IAAI,KAAK,CAAC,kBAAkB,KAAK,OAAO,CAAC,UAAU,EAAE;gBACnD,OAAO;aACR;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA,CAAC;QACpD,CAAC,EAAC;QA9SA,+EAA+E;QAC/E,0BAA0B;QAC1B,uBAAA,IAAI,wBAAU,KAAK,MAAA,CAAC;QACpB,uBAAA,IAAI,2DAAQ,CAAC,EAAE,CAAC,uBAAuB,EAAE,uBAAA,IAAI,sCAAiB,CAAC,CAAC;IAClE,CAAC;IAcD,KAAK;QACH,OAAO,uBAAA,IAAI,4BAAO,CAAC;IACrB,CAAC;IAED,YAAY;QACV,uBAAA,IAAI,2BAAa,SAAS,MAAA,CAAC;QAC3B,uBAAA,IAAI,0BAAY,IAAA,0CAAqB,GAAE,MAAA,CAAC;IAC1C,CAAC;IAED,UAAU,CAAC,OAAyB;QAClC,uBAAA,IAAI,sCAAiB,CAAC,KAAK,EAAE,CAAC;QAC9B,uBAAA,IAAI,8BAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,uBAAA,IAAI,kCAAa,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAED,UAAU;QACR,OAAO,uBAAA,IAAI,8BAAS,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,OAAO;QACL,uBAAA,IAAI,2BAAa,IAAI,MAAA,CAAC;QACtB,uBAAA,IAAI,2DAAQ,CAAC,GAAG,CAAC,uBAAuB,EAAE,uBAAA,IAAI,sCAAiB,CAAC,CAAC;QACjE,uBAAA,IAAI,kCAAa,CAAC,YAAY,CAC5B,IAAI,KAAK,CAAC,6CAA6C,CAAC,CACzD,CAAC;IACJ,CAAC;IAED,gBAAgB;QACd,IAAI,uBAAA,IAAI,+BAAU,EAAE;YAClB,MAAM,IAAI,KAAK,CACb,yDAAyD,uBAAA,IAAI,4BAAO,CAAC,GAAG,EAAE,iCAAiC,CAC5G,CAAC;SACH;QACD,IAAI,uBAAA,IAAI,8BAAS,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,OAAO,uBAAA,IAAI,8BAAS,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,CAAC,CACL,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,EAAE,CACN,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,uBAAA,IAAI,+BAAU,EAAE;YAClB,OAAO,uBAAA,IAAI,+BAAU,CAAC;SACvB;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,uBAAA,IAAI,2BAAa,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE;YACjD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,MAAA,CAAC;QACH,OAAO,uBAAA,IAAI,+BAAU,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,EAAE,CAAC,UAAkB;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,KAAK,CAQT,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,MAAM,CAQV,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAClE;YACD,IAAI,QAAQ,CAAC,eAAe,EAAE;gBAC5B,MAAM,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;aAC9C;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CACd,IAAY,EACZ,UAGI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,uBAAA,IAAI,oEAAiB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,IAAA,wBAAc,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEjC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,uBAAA,IAAI,iEAAc,EAClB,uBAAA,IAAI,4BAAO,EACX,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC/B,OAAO,CAAC,2BAA2B,EAAE;YACrC,OAAO,CAAC,gBAAgB,EAAE;SAC3B,CAAC,CAAC;QACH,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE;YACT,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,UAAkC,EAAE;QAEpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,GAAG,MAAgB;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;QAC9C,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,QAAgB;QACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;QACnB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAgB,EAChB,IAAY,EACZ,OAAyB;QAEzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,OAAyB,EACzB,IAAY;QAEZ,IAAI,uBAAA,IAAI,sCAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnC,OAAO;SACR;QAED,MAAM,uBAAA,IAAI,4BAAO,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI;YACF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC/C,IAAI;gBACJ,oBAAoB,EAAE,OAAO,CAAC,YAAY;aAC3C,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAc,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAEzD,uBAAA,IAAI,sCAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACd,iEAAiE;YACjE,uEAAuE;YACvE,mCAAmC;YACnC,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,qBAAqB;gBACrB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;oBAC7D,OAAO;iBACR;gBACD,mBAAmB;gBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE;oBACnE,OAAO;iBACR;aACF;YAED,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;SACnB;gBAAS;YACR,uBAAA,IAAI,4BAAO,CAAC,OAAO,EAAE,CAAC;SACvB;IACH,CAAC;IA8BD,eAAe,CAMb,YAA2B,EAC3B,UAKI,EAAE,EACN,GAAG,IAAY;QAEf,MAAM,EACJ,OAAO,GAAG,KAAK,EACf,OAAO,GAAG,uBAAA,IAAI,oEAAiB,CAAC,OAAO,EAAE,EACzC,IAAI,EACJ,MAAM,GACP,GAAG,OAAO,CAAC;QACZ,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,QAAQ,GAAG,IAAI,sBAAQ,CAC3B,IAAI,EACJ;YACE,OAAO;YACP,IAAI;YACJ,OAAO;YACP,MAAM;SACP,EACD,YAEU,EACV,GAAG,IAAI,CACR,CAAC;QACF,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACxB,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,aAA0C;QAE1C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,uBAAA,IAAI,2DAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC1D,aAAa,EAAE,aAAa;YAC5B,kBAAkB,EAAE,gBAAgB,CAAC,UAAU;SAChD,CAAC,CAAC;QACH,OAAO,IAAA,wBAAc,EAAC,gBAAgB,EAAE,MAAM,CAAmB,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,WAAW,CAA2B,MAAS;QACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAA,kBAAM,EACJ,MAAM,CAAC,gBAAgB,EAAE,KAAK,OAAO,EACrC,oEAAoE,CACrE,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,2DAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC3D,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAM,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,cAAc,CAA2B,MAAS;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,MAAM,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE;YACzC,OAAO,MAAM,CAAC;SACf;QACD,MAAM,IAAI,GAAG,MAAM,uBAAA,IAAI,2DAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACvD,QAAQ,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ;SACzC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,CACxB,CAAM,CAAC;QACR,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAzZD,sCAyZC;;IA5XG,OAAO,uBAAA,IAAI,4BAAO,CAAC,OAAO,EAAE,CAAC;AAC/B,CAAC;IAGC,OAAO,uBAAA,IAAI,4BAAO,CAAC,aAAa,CAAC;AACnC,CAAC;IAGC,OAAO,uBAAA,IAAI,iEAAc,CAAC,eAAe,CAAC;AAC5C,CAAC;AAqXH,MAAM,KAAK;IAAX;QACE,wBAAU,KAAK,EAAC;QAChB,2BAAgC,EAAE,EAAC;IAwBrC,CAAC;IAtBC,gBAAgB;IAChB,OAAO;QACL,IAAI,CAAC,uBAAA,IAAI,qBAAQ,EAAE;YACjB,uBAAA,IAAI,iBAAW,IAAI,MAAA,CAAC;YACpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,IAAI,OAAoB,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,GAAG,CAAC,EAAE;YACtC,OAAO,GAAG,GAAG,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,uBAAA,IAAI,wBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO;QACL,MAAM,OAAO,GAAG,uBAAA,IAAI,wBAAW,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE;YACZ,uBAAA,IAAI,iBAAW,KAAK,MAAA,CAAC;YACrB,OAAO;SACR;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF"}