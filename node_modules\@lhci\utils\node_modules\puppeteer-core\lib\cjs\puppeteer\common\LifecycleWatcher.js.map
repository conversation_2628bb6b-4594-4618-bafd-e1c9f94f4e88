{"version": 3, "file": "LifecycleWatcher.js", "sourceRoot": "", "sources": ["../../../../src/common/LifecycleWatcher.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,iDAAyC;AACzC,mEAGoC;AAEpC,mDAAwD;AACxD,2CAAyC;AAEzC,uDAA0E;AAE1E,2DAAgE;AAChE,uCAImB;AAmBnB,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAG1C;IACA,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,cAAc,EAAE,aAAa,CAAC;IAC/B,CAAC,cAAc,EAAE,mBAAmB,CAAC;CACtC,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,GAAS,EAAE,GAAE,CAAC,CAAC;AAE5B;;GAEG;AACH,MAAa,gBAAgB;IAsB3B,YACE,YAA0B,EAC1B,KAAY,EACZ,SAA8D,EAC9D,OAAe;;QAzBjB,sDAA6C;QAC7C,iDAA4B;QAC5B,0CAAc;QACd,4CAAiB;QACjB,8CAAyC,IAAI,EAAC;QAC9C,mDAA0C;QAC1C,oDAAyB;QAEzB,0DAAiC,IAAA,0CAAqB,GAAqB,EAAC;QAC5E,6CAAoB,IAAA,0CAAqB,GAAQ,EAAC;QAClD,yDAAgC,IAAA,0CAAqB,GAAqB,EAAC;QAC3E,+CAAsB,IAAA,0CAAqB,GAAqB,EAAC;QAEjE,mDAAmD;QAEnD,iDAA+B;QAC/B,8DAAqC;QACrC,4CAAmB;QAEnB,+DAAoD;QAQlD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC5B,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;SAC/B;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;SACzB;QACD,uBAAA,IAAI,qCAAoB,KAAK,CAAC,SAAS,MAAA,CAAC;QACxC,uBAAA,IAAI,uCAAsB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC9C,MAAM,aAAa,GAAG,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9D,IAAA,kBAAM,EAAC,aAAa,EAAE,uCAAuC,GAAG,KAAK,CAAC,CAAC;YACvE,OAAO,aAAuC,CAAC;QACjD,CAAC,CAAC,MAAA,CAAC;QAEH,uBAAA,IAAI,kCAAiB,YAAY,MAAA,CAAC;QAClC,uBAAA,IAAI,2BAAU,KAAK,MAAA,CAAC;QACpB,uBAAA,IAAI,6BAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,oCAAmB;YACrB,IAAA,0BAAgB,EACd,YAAY,CAAC,MAAM,EACnB,uCAAuB,CAAC,YAAY,EACpC,uBAAA,IAAI,gEAAW,CAAC,IAAI,CAClB,IAAI,EACJ,IAAI,KAAK,CAAC,qDAAqD,CAAC,CACjE,CACF;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,EAClB,2CAAyB,CAAC,cAAc,EACxC,uBAAA,IAAI,6EAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,EAClB,2CAAyB,CAAC,4BAA4B,EACtD,uBAAA,IAAI,8EAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CACzC;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,EAClB,2CAAyB,CAAC,cAAc,EACxC,uBAAA,IAAI,gEAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,EAClB,2CAAyB,CAAC,YAAY,EACtC,uBAAA,IAAI,mEAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,EAClB,2CAAyB,CAAC,aAAa,EACvC,uBAAA,IAAI,sEAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,CAAC,cAAc,EACjC,+CAA2B,CAAC,OAAO,EACnC,uBAAA,IAAI,gEAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,CAAC,cAAc,EACjC,+CAA2B,CAAC,QAAQ,EACpC,uBAAA,IAAI,iEAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5B;YACD,IAAA,0BAAgB,EACd,uBAAA,IAAI,sCAAc,CAAC,cAAc,EACjC,+CAA2B,CAAC,aAAa,EACzC,uBAAA,IAAI,sEAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC;SACF,MAAA,CAAC;QAEF,uBAAA,IAAI,oCAAmB,uBAAA,IAAI,2EAAsB,MAA1B,IAAI,CAAwB,MAAA,CAAC;QACpD,uBAAA,IAAI,6EAAwB,MAA5B,IAAI,CAA0B,CAAC;IACjC,CAAC;IAyCD,KAAK,CAAC,kBAAkB;;QACtB,0CAA0C;QAC1C,MAAM,CAAA,MAAA,uBAAA,IAAI,oDAA4B,0CAAE,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA,CAAC;QACxD,OAAO,uBAAA,IAAI,2CAAmB,CAAC,CAAC,CAAC,uBAAA,IAAI,2CAAmB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAMD,6BAA6B;QAC3B,OAAO,uBAAA,IAAI,uDAA+B,CAAC;IAC7C,CAAC;IAED,4BAA4B;QAC1B,OAAO,uBAAA,IAAI,sDAA8B,CAAC;IAC5C,CAAC;IAED,gBAAgB;QACd,OAAO,uBAAA,IAAI,0CAAkB,CAAC;IAChC,CAAC;IAED,2BAA2B;QACzB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,uBAAA,IAAI,wCAAgB,EAAE,uBAAA,IAAI,4CAAoB,CAAC,CAAC,CAAC;IACxE,CAAC;IAuED,OAAO;QACL,IAAA,8BAAoB,EAAC,uBAAA,IAAI,wCAAgB,CAAC,CAAC;QAC3C,uBAAA,IAAI,sCAAc,KAAK,SAAS,IAAI,YAAY,CAAC,uBAAA,IAAI,sCAAc,CAAC,CAAC;IACvE,CAAC;CACF;AA5OD,4CA4OC;s7BA1IY,OAAoB;;IAC7B,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,uBAAA,IAAI,+BAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE;QACrE,OAAO;KACR;IACD,uBAAA,IAAI,uCAAsB,OAAO,MAAA,CAAC;IAClC,kEAAkE;IAClE,yEAAyE;IACzE,yCAAyC;IACzC,MAAA,uBAAA,IAAI,oDAA4B,0CAAE,OAAO,EAAE,CAAC;IAC5C,uBAAA,IAAI,gDAA+B,IAAA,0CAAqB,GAAE,MAAA,CAAC;IAC3D,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC/B,MAAA,uBAAA,IAAI,oDAA4B,0CAAE,OAAO,EAAE,CAAC;KAC7C;AACH,CAAC,iFAEgB,OAAoB;;IACnC,IAAI,CAAA,MAAA,uBAAA,IAAI,2CAAmB,0CAAE,UAAU,MAAK,OAAO,CAAC,UAAU,EAAE;QAC9D,OAAO;KACR;IACD,MAAA,uBAAA,IAAI,oDAA4B,0CAAE,OAAO,EAAE,CAAC;AAC9C,CAAC,uEAEW,QAAsB;;IAChC,IAAI,CAAA,MAAA,uBAAA,IAAI,2CAAmB,0CAAE,UAAU,MAAK,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE;QACzE,OAAO;KACR;IACD,MAAA,uBAAA,IAAI,oDAA4B,0CAAE,OAAO,EAAE,CAAC;AAC9C,CAAC,iFAEgB,KAAY;IAC3B,IAAI,uBAAA,IAAI,+BAAO,KAAK,KAAK,EAAE;QACzB,uBAAA,IAAI,4CAAoB,CAAC,OAAO,CAC9B,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAC3C,CAAC;QACF,OAAO;KACR;IACD,uBAAA,IAAI,6EAAwB,MAA5B,IAAI,CAA0B,CAAC;AACjC,CAAC,qEAQU,KAAY;IACrB,uBAAA,IAAI,4CAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC,2CAkBD,KAAK;IACH,IAAI,CAAC,uBAAA,IAAI,iCAAS,EAAE;QAClB,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;KAC1B;IACD,MAAM,YAAY,GAChB,wBAAwB,GAAG,uBAAA,IAAI,iCAAS,GAAG,cAAc,CAAC;IAC5D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,CAAC,uBAAA,IAAI,kCAAiB,UAAU,CAAC,OAAO,EAAE,uBAAA,IAAI,iCAAS,CAAC,MAAA,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,wBAAY,CAAC,YAAY,CAAC,CAAC;AACxC,CAAC,iGAEwB,KAAY;IACnC,IAAI,KAAK,KAAK,uBAAA,IAAI,+BAAO,EAAE;QACzB,OAAO;KACR;IACD,uBAAA,IAAI,+CAA8B,IAAI,MAAA,CAAC;IACvC,uBAAA,IAAI,6EAAwB,MAA5B,IAAI,CAA0B,CAAC;AACjC,CAAC,qEAEU,KAAY;IACrB,IAAI,KAAK,KAAK,uBAAA,IAAI,+BAAO,EAAE;QACzB,OAAO;KACR;IACD,uBAAA,IAAI,6EAAwB,MAA5B,IAAI,CAA0B,CAAC;AACjC,CAAC,2EAEa,KAAY;IACxB,IAAI,KAAK,KAAK,uBAAA,IAAI,+BAAO,EAAE;QACzB,OAAO;KACR;IACD,uBAAA,IAAI,6BAAY,IAAI,MAAA,CAAC;IACrB,uBAAA,IAAI,6EAAwB,MAA5B,IAAI,CAA0B,CAAC;AACjC,CAAC;IAGC,kCAAkC;IAClC,IAAI,CAAC,cAAc,CAAC,uBAAA,IAAI,+BAAO,EAAE,uBAAA,IAAI,2CAAmB,CAAC,EAAE;QACzD,OAAO;KACR;IACD,uBAAA,IAAI,0CAAkB,CAAC,OAAO,EAAE,CAAC;IACjC,IAAI,uBAAA,IAAI,mDAA2B,EAAE;QACnC,uBAAA,IAAI,uDAA+B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KACxD;IACD,IAAI,uBAAA,IAAI,iCAAS,IAAI,uBAAA,IAAI,+BAAO,CAAC,SAAS,KAAK,uBAAA,IAAI,yCAAiB,EAAE;QACpE,uBAAA,IAAI,sDAA8B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KACvD;IAED,SAAS,cAAc,CACrB,KAAY,EACZ,iBAA2C;QAE3C,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE;YACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACtC,OAAO,KAAK,CAAC;aACd;SACF;QACD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;YACvC,IACE,KAAK,CAAC,kBAAkB;gBACxB,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,EACzC;gBACA,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}