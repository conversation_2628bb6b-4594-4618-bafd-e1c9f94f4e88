'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  FileText, 
  Building2,
  Crown,
  Shield,
  Banknote,
  Map,
  Users,
  GraduationCap,
  HeartPulse,
  Car,
  Building,
  Folder,
  ArrowRight,
  Filter,
  BarChart3
} from 'lucide-react'
import DependencyService, { type Dependency, type DependencyStats } from '@/lib/services/dependencyService'

// Mapeo de iconos
const iconMap = {
  crown: Crown,
  shield: Shield,
  banknote: Banknote,
  map: Map,
  users: Users,
  'graduation-cap': GraduationCap,
  'heart-pulse': HeartPulse,
  car: Car,
  building: Building,
  folder: Folder
}

interface DependencyGridProps {
  onDependencySelect?: (dependency: Dependency) => void
  showSearch?: boolean
  showStats?: boolean
  maxItems?: number
  className?: string
}

export function DependencyGrid({
  onDependencySelect,
  showSearch = true,
  showStats = true,
  maxItems,
  className = ''
}: DependencyGridProps) {
  const [dependencies, setDependencies] = useState<Dependency[]>([])
  const [filteredDependencies, setFilteredDependencies] = useState<Dependency[]>([])
  const [stats, setStats] = useState<DependencyStats | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Cargar datos iniciales
  useEffect(() => {
    loadDependencies()
  }, [])

  // Filtrar dependencias cuando cambia la búsqueda
  useEffect(() => {
    filterDependencies()
  }, [searchQuery, dependencies])

  const loadDependencies = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const [allDependencies, dependencyStats] = await Promise.all([
        DependencyService.getAllDependencies(),
        showStats ? DependencyService.getDependencyStats() : Promise.resolve(null)
      ])

      setDependencies(allDependencies)
      setStats(dependencyStats)
    } catch (err) {
      console.error('Error cargando dependencias:', err)
      setError('Error al cargar las dependencias. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const filterDependencies = async () => {
    try {
      if (!searchQuery.trim()) {
        let result = dependencies
        if (maxItems) {
          result = result.slice(0, maxItems)
        }
        setFilteredDependencies(result)
        return
      }

      const searchResults = await DependencyService.searchDependencies(searchQuery)
      let result = searchResults
      if (maxItems) {
        result = result.slice(0, maxItems)
      }
      setFilteredDependencies(result)
    } catch (err) {
      console.error('Error filtrando dependencias:', err)
    }
  }

  const handleDependencyClick = (dependency: Dependency) => {
    if (onDependencySelect) {
      onDependencySelect(dependency)
    } else {
      // Navegación por defecto - podría redirigir a página de dependencia
      console.log('Dependencia seleccionada:', dependency)
    }
  }

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Folder
    return IconComponent
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {showStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-red-600 mb-4">
          <Building2 className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p className="text-lg font-medium">{error}</p>
        </div>
        <Button onClick={loadDependencies} variant="outline">
          Reintentar
        </Button>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Estadísticas */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-chia-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Dependencias</p>
                  <p className="text-2xl font-bold text-chia-blue-900">{stats.totalDependencies}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-chia-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Trámites</p>
                  <p className="text-2xl font-bold text-chia-green-900">{stats.totalTramites.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">OPAs</p>
                  <p className="text-2xl font-bold text-blue-900">{stats.totalOPAs.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Procedimientos</p>
                  <p className="text-2xl font-bold text-purple-900">{stats.totalProcedures.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Barra de búsqueda */}
      {showSearch && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Buscar dependencias..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full max-w-md"
          />
        </div>
      )}

      {/* Grid de dependencias */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDependencies.map((dependency) => {
          const IconComponent = getIcon(dependency.icon || 'folder')
          
          return (
            <Card
              key={dependency.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 ${dependency.color}`}
              onClick={() => handleDependencyClick(dependency)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-white/50">
                      <IconComponent className="h-6 w-6 text-chia-blue-700" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg font-semibold text-chia-blue-900 leading-tight">
                        {dependency.name}
                      </CardTitle>
                      <Badge variant="secondary" className="mt-1 text-xs">
                        {dependency.sigla}
                      </Badge>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-chia-blue-600 transition-colors" />
                </div>
                
                {dependency.description && (
                  <CardDescription className="text-sm text-gray-600 mt-2">
                    {dependency.description}
                  </CardDescription>
                )}
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <FileText className="h-4 w-4 text-chia-green-600" />
                      <span className="font-medium text-chia-green-700">
                        {dependency.tramitesCount} trámites
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <BarChart3 className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-700">
                        {dependency.opasCount} OPAs
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-bold text-chia-blue-900">
                      {dependency.totalProcedures}
                    </p>
                    <p className="text-xs text-gray-500">total</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Mensaje cuando no hay resultados */}
      {filteredDependencies.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No se encontraron dependencias
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery 
              ? `No hay dependencias que coincidan con "${searchQuery}"`
              : 'No hay dependencias disponibles en este momento'
            }
          </p>
          {searchQuery && (
            <Button 
              variant="outline" 
              onClick={() => setSearchQuery('')}
            >
              Limpiar búsqueda
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
