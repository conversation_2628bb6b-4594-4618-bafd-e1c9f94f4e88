{"name": "chromium-bidi", "version": "0.4.7", "description": "An implementation of the WebDriver BiDi protocol for Chromium implemented as a JavaScript layer translating between BiDi and CDP, running inside a Chrome tab.", "scripts": {"build": "tsc -b src/tsconfig.json && npm run rollup", "clean": "<PERSON><PERSON><PERSON> lib", "coverage": "nyc mocha", "e2e-headful": "npm run server-no-build -- --headless=false & npm run e2e-only", "e2e-headless": "npm run server-no-build & npm run e2e-only", "e2e": "npm run e2e-headless", "e2e-only": "python3 -m pytest", "eslint": "eslint --ext .js,.ts --fix .", "flake8": "flake8 examples/ tests/", "format": "npm run eslint && npm run prettier && npm run yapf", "pre-commit": "pre-commit run --all-files", "prepare": "npm run clean && npm run build", "prettier": "prettier --write .", "rollup": "rollup -c", "server": "npm run build && npm run server-no-build --", "server-no-build": "node lib/cjs/bidiServer/index.js", "test": "npm run build && mocha", "watch": "tsc -b src/tsconfig.json --watch && rollup -c --watch", "yapf": "yapf --in-place --parallel --recursive --exclude=wpt examples/ tests/"}, "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/chromium-bidi.git"}, "author": "The Chromium Authors", "license": "Apache-2.0", "peerDependencies": {"devtools-protocol": "*"}, "devDependencies": {"@puppeteer/browsers": "0.1.1", "@rollup/plugin-commonjs": "22.0.0", "@rollup/plugin-node-resolve": "13.3.0", "@rollup/plugin-terser": "^0.3.0", "@types/argparse": "2.0.10", "@types/chai": "4.3.1", "@types/chai-as-promised": "7.1.5", "@types/debug": "4.1.7", "@types/mocha": "9.1.1", "@types/node": "18.11.14", "@types/sinon": "10.0.11", "@types/websocket": "1.0.5", "@types/ws": "8.5.4", "@typescript-eslint/eslint-plugin": "5.58.0", "@typescript-eslint/parser": "5.58.0", "argparse": "2.0.1", "chai": "4.3.6", "chai-as-promised": "7.1.1", "chai-exclude": "2.1.0", "debug": "4.3.4", "devtools-protocol": "0.0.1107588", "eslint": "8.38.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.1.0", "eslint-plugin-prettier": "4.2.1", "gts": "3.1.1", "mocha": "10.0.0", "nyc": "^15.1.0", "prettier": "2.8.4", "rimraf": "3.0.2", "rollup": "2.74.1", "sinon": "15.0.3", "source-map-support": "^0.5.21", "terser": "5.14.2", "tslib": "2.5.0", "typescript": "4.7.4", "websocket": "1.0.34", "ws": "8.13.0", "zod": "3.21.4"}, "dependencies": {"mitt": "3.0.0"}}