{"version": 3, "file": "CommandProcessor.js", "sourceRoot": "", "sources": ["../../../src/bidiMapper/CommandProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yDAMiC;AACjC,4CAAkD;AAClD,8DAAsD;AAEtD,+FAAuF;AAIvF,qEAA6D;AA+B7D,MAAM,cAAc;IAClB,2BAA2B,CACzB,MAAc;QAEd,OAAO,MAA2C,CAAC;IACrD,CAAC;IAED,8BAA8B,CAC5B,MAAc;QAEd,OAAO,MAA8C,CAAC;IACxD,CAAC;IAED,oBAAoB,CAAC,MAAc;QACjC,OAAO,MAAoC,CAAC;IAC9C,CAAC;IACD,uBAAuB,CAAC,MAAc;QACpC,OAAO,MAAuC,CAAC;IACjD,CAAC;IACD,mBAAmB,CAAC,MAAc;QAChC,OAAO,MAAmC,CAAC;IAC7C,CAAC;IACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,MAAiC,CAAC;IAC3C,CAAC;IACD,sBAAsB,CAAC,MAAc;QACnC,OAAO,MAA+B,CAAC;IACzC,CAAC;IACD,qBAAqB,CAAC,MAAc;QAClC,OAAO,MAA8B,CAAC;IACxC,CAAC;IACD,oBAAoB,CAAC,MAAc;QACjC,OAAO,MAAqC,CAAC;IAC/C,CAAC;IACD,mBAAmB,CAAC,MAAc;QAChC,OAAO,MAA4C,CAAC;IACtD,CAAC;IACD,kBAAkB,CAAC,MAAc;QAC/B,OAAO,MAA2C,CAAC;IACrD,CAAC;IACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,MAA0C,CAAC;IACpD,CAAC;IACD,gBAAgB,CAAC,MAAc;QAC7B,OAAO,MAAyC,CAAC;IACnD,CAAC;IACD,4BAA4B,CAC1B,MAAc;QAEd,OAAO,MAAqD,CAAC;IAC/D,CAAC;IACD,gBAAgB,CAAC,MAAc;QAC7B,OAAO,MAAyC,CAAC;IACnD,CAAC;CACF;AAED,MAAa,gBAAiB,SAAQ,8BAAoC;IACxE,iBAAiB,CAA2B;IAC5C,aAAa,CAAgB;IAC7B,OAAO,CAAa;IACpB,OAAO,CAAY;IAEnB,YACE,YAA0B,EAC1B,aAA4B,EAC5B,YAA2B,EAC3B,YAAoB,EACpB,SAAqB,IAAI,cAAc,EAAE,EACzC,sBAA8C,EAC9C,MAAiB;QAEjB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,sDAAwB,CACnD,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,sBAAsB,EACtB,MAAM,CACP,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,uBAAuB;QAC5B,OAAO,EAAC,MAAM,EAAE,EAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAC,EAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,0BAA0B,CAC9B,MAAmC,EACnC,OAAsB;QAEtB,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAChC,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EACzB,OAAO,CACR,CAAC;QACF,OAAO,EAAC,MAAM,EAAE,EAAE,EAAC,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,MAAmC,EACnC,OAAsB;QAEtB,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAClC,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EACzB,OAAO,CACR,CAAC;QACF,OAAO,EAAC,MAAM,EAAE,EAAE,EAAC,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,WAAsC;QAEtC,QAAQ,WAAW,CAAC,MAAM,EAAE;YAC1B,KAAK,gBAAgB;gBACnB,OAAO,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;YACpD,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,0BAA0B,CACpC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,MAAM,CAAC,EACrD,WAAW,CAAC,OAAO,IAAI,IAAI,CAC5B,CAAC;YACJ,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,4BAA4B,CACtC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,MAAM,CAAC,EACrD,WAAW,CAAC,OAAO,IAAI,IAAI,CAC5B,CAAC;YAEJ,KAAK,wBAAwB;gBAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,8BAA8B,CAC1D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CACnD,CAAC;YACJ,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CACzD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,CAClD,CAAC;YACJ,KAAK,yBAAyB;gBAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,+BAA+B,CAC3D,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,CACpD,CAAC;YACJ,KAAK,0BAA0B;gBAC7B,OAAO,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAC5D,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CACrD,CAAC;YACJ,KAAK,mCAAmC;gBACtC,OAAO,IAAI,CAAC,iBAAiB,CAAC,yCAAyC,CACrE,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC,CAC9D,CAAC;YACJ,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CACzD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,CAClD,CAAC;YAEJ,KAAK,yBAAyB;gBAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,+BAA+B,CAC3D,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,WAAW,CAAC,MAAM,CAAC,CAC7D,CAAC;YACJ,KAAK,4BAA4B;gBAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,kCAAkC,CAC9D,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,WAAW,CAAC,MAAM,CAAC,CAChE,CAAC;YACJ,KAAK,kBAAkB;gBACrB,OAAO,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CACpD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,MAAM,CAAC,CACtD,CAAC;YACJ,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,CACvD,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,MAAM,CAAC,CACzD,CAAC;YACJ,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CACnD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CACrD,CAAC;YACJ,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CACjD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CACnD,CAAC;YAEJ,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CACnD,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,WAAW,CAAC,MAAM,CAAC,CACxD,CAAC;YACJ,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAClD,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAM,CAAC,CACvD,CAAC;YAEJ;gBACE,MAAM,IAAI,qBAAO,CAAC,uBAAuB,CACvC,oBAAoB,WAAW,CAAC,MAAM,IAAI,CAC3C,CAAC;SACL;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAkC;QACrD,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEnD,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,GAAG,MAAM;aACV,CAAC;YAEF,IAAI,CAAC,IAAI,CACP,UAAU,EACV,4CAAmB,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CACtE,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,qBAAO,CAAC,aAAa,EAAE;gBACtC,MAAM,aAAa,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,IAAI,CACP,UAAU,EACV,4CAAmB,CAAC,cAAc,CAChC,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,EACzC,OAAO,CAAC,OAAO,IAAI,IAAI,CACxB,CACF,CAAC;aACH;iBAAM;gBACL,MAAM,KAAK,GAAG,CAAU,CAAC;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,CACP,UAAU,EACV,4CAAmB,CAAC,cAAc,CAChC,IAAI,qBAAO,CAAC,aAAa,CACvB,qBAAO,CAAC,SAAS,CAAC,YAAY,EAC9B,KAAK,CAAC,OAAO,CACd,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,EAC7B,OAAO,CAAC,OAAO,IAAI,IAAI,CACxB,CACF,CAAC;aACH;SACF;IACH,CAAC;CACF;AAnLD,4CAmLC"}