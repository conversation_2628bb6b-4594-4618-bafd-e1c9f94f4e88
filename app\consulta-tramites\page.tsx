import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout'
import { RouteGuard } from '@/components/navigation'
import { ProcedureSearchInterface } from '@/components/procedures/ProcedureSearchInterface'
import { ContextualFAQSection } from '@/components/faq/ContextualFAQSection'

export default async function ConsultaTramitesPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  const supabase = createClient()

  // Get all active procedures with their dependencies
  const { data: procedures } = await supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(
        id,
        name,
        acronym,
        description,
        contact_email,
        contact_phone,
        address
      )
    `)
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get all dependencies for filtering
  const { data: dependencies } = await supabase
    .from('dependencies')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get procedure categories
  const { data: categoriesData } = await supabase
    .from('procedures')
    .select('category')
    .not('category', 'is', null)
    .eq('is_active', true)

  const categories = [...new Set(categoriesData?.map(p => p.category).filter(Boolean))]

  // Get user's procedures for tracking
  const { data: userProcedures } = await supabase
    .from('citizen_procedures')
    .select(`
      procedure_id,
      status:procedure_statuses(name, display_name, color)
    `)
    .eq('citizen_id', user.id)

  return (
    <RouteGuard allowedRoles={['ciudadano', 'admin', 'super_admin']}>
      <ProtectedLayout>
        <div className="min-h-screen bg-gray-50">
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-green-800 text-white shadow-lg">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-8">
                <div>
                  <h1 className="text-3xl font-bold">
                    Consulta de Trámites
                  </h1>
                  <p className="text-green-100 mt-1">
                    Busca y consulta información detallada sobre trámites municipales
                  </p>
                  <div className="flex items-center mt-3 space-x-4 text-sm">
                    <div className="flex items-center">
                      <span className="font-medium">{procedures?.length || 0}</span>
                      <span className="ml-1">trámites disponibles</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{dependencies?.length || 0}</span>
                      <span className="ml-1">dependencias</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{categories.length}</span>
                      <span className="ml-1">categorías</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ProcedureSearchInterface
              procedures={procedures || []}
              dependencies={dependencies || []}
              categories={categories}
              userProcedures={userProcedures || []}
              currentUserId={user.id}
            />
          </div>

          {/* FAQ Section - Contextual for procedure consultation */}
          <div className="bg-white border-t">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <ContextualFAQSection context="procedures" />
            </div>
          </div>
        </div>
      </ProtectedLayout>
    </RouteGuard>
  )
}
