{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\global-setup.ts", "globalTeardown": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "faq-system.spec.ts", "file": "faq-system.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "FAQ System E2E Tests - Simplified", "file": "faq-system.spec.ts", "line": 13, "column": 6, "specs": [], "suites": [{"title": "Basic Application Functionality", "file": "faq-system.spec.ts", "line": 25, "column": 8, "specs": [{"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13263, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:17.257Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-9b805d3f5cb639120996", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should detect FAQ section presence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 16890, "errors": [], "stdout": [{"text": "Found FAQ indicator: Preguntas Frecuentes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:17.273Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-2563e433e1d8afc72d00", "file": "faq-system.spec.ts", "line": 42, "column": 9}, {"title": "should handle page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 13188, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:17.270Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-e7e3f57f3f16f3e3053c", "file": "faq-system.spec.ts", "line": 89, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "passed", "duration": 12966, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:36.165Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-ed666bf58dd5251241c3", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should detect FAQ section presence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "passed", "duration": 17603, "errors": [], "stdout": [{"text": "Found FAQ indicator: Preguntas Frecuentes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:39.193Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-ec9cc4833303b8336539", "file": "faq-system.spec.ts", "line": 42, "column": 9}, {"title": "should handle page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 2, "status": "passed", "duration": 13541, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:48.475Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-c486eec00fa403e0a813", "file": "faq-system.spec.ts", "line": 89, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "passed", "duration": 11887, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:08.397Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-d411d72fcfca1c834297", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should detect FAQ section presence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 3, "status": "passed", "duration": 17212, "errors": [], "stdout": [{"text": "Found FAQ indicator: Preguntas Frecuentes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:09.850Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-b6e0a17320f884caa55f", "file": "faq-system.spec.ts", "line": 42, "column": 9}, {"title": "should handle page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 9744, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:12.630Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-4514d2016c9a84c4249a", "file": "faq-system.spec.ts", "line": 89, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 12163, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:29.660Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-1a1c6be02c3da06114f9", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should detect FAQ section presence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "passed", "duration": 19194, "errors": [], "stdout": [{"text": "Found FAQ indicator: Preguntas Frecuentes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:30.676Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-a623b669ed41ba67eb60", "file": "faq-system.spec.ts", "line": 42, "column": 9}, {"title": "should handle page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 12959, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:34.231Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-fe215abba4f268f591f4", "file": "faq-system.spec.ts", "line": 89, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 11030, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:55.139Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-d742afed10bdb98e2041", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should detect FAQ section presence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 3, "status": "passed", "duration": 18875, "errors": [], "stdout": [{"text": "Found FAQ indicator: Preguntas Frecuentes\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:57.507Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-ba68bb86d0ef51eb877d", "file": "faq-system.spec.ts", "line": 42, "column": 9}, {"title": "should handle page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "passed", "duration": 8736, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:03:01.175Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-27b87d5e5893be010c79", "file": "faq-system.spec.ts", "line": 89, "column": 9}]}, {"title": "Authentication and Protected Routes", "file": "faq-system.spec.ts", "line": 103, "column": 8, "specs": [{"title": "should handle protected route access", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 13996, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:17.358Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-19dff3fa55970e3a25a4", "file": "faq-system.spec.ts", "line": 104, "column": 9}, {"title": "should display login form when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 12819, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:31.952Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-dcfc2f8840eb30a980ee", "file": "faq-system.spec.ts", "line": 121, "column": 9}, {"title": "should handle protected route access", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 15385, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:48.812Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-66bc08aeac708918aff8", "file": "faq-system.spec.ts", "line": 104, "column": 9}, {"title": "should display login form when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "passed", "duration": 13625, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:51.414Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-f0fb0f538bd7d0cca272", "file": "faq-system.spec.ts", "line": 121, "column": 9}, {"title": "should handle protected route access", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "passed", "duration": 10738, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:17.534Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-fd29f21d4bdc407cbab2", "file": "faq-system.spec.ts", "line": 104, "column": 9}, {"title": "should display login form when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "passed", "duration": 10659, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:20.913Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-14e2c491137ca9951dd4", "file": "faq-system.spec.ts", "line": 121, "column": 9}, {"title": "should handle protected route access", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 0, "status": "passed", "duration": 14081, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:41.318Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-62ef685942deea8ab6f3", "file": "faq-system.spec.ts", "line": 104, "column": 9}, {"title": "should display login form when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 10837, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:43.401Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-cfaf1d069b43d5d61b30", "file": "faq-system.spec.ts", "line": 121, "column": 9}, {"title": "should handle protected route access", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 12215, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:03:03.831Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-5587d066821bbb7a8b8f", "file": "faq-system.spec.ts", "line": 104, "column": 9}, {"title": "should display login form when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 11213, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:03:06.400Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-20c066f3a32a52ac5da4", "file": "faq-system.spec.ts", "line": 121, "column": 9}]}, {"title": "Responsive Design", "file": "faq-system.spec.ts", "line": 138, "column": 8, "specs": [{"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 12786, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:32.082Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-463689ee1359165d3748", "file": "faq-system.spec.ts", "line": 139, "column": 9}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "passed", "duration": 12262, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:01:58.829Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-e7d7e8263269e23db754", "file": "faq-system.spec.ts", "line": 139, "column": 9}, {"title": "should work on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 15950, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    2 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n\u001b[2m    6 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    2 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n\u001b[2m    6 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts:153:33", "location": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}, "snippet": "\u001b[0m \u001b[90m 151 |\u001b[39m       \u001b[90m// Test search functionality on mobile\u001b[39m\n \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'certificado'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoHaveValue(\u001b[32m'certificado'\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 154 |\u001b[39m     })\n \u001b[90m 155 |\u001b[39m   })\n \u001b[90m 156 |\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    2 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n\u001b[2m    6 × locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m      - unexpected value \"\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 151 |\u001b[39m       \u001b[90m// Test search functionality on mobile\u001b[39m\n \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'certificado'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoHaveValue(\u001b[32m'certificado'\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 154 |\u001b[39m     })\n \u001b[90m 155 |\u001b[39m   })\n \u001b[90m 156 |\u001b[39m })\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts:153:33\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:22.626Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}}], "status": "unexpected"}], "id": "d43c6b69427cd64ffd78-188fb8dcb9e964bf20bb", "file": "faq-system.spec.ts", "line": 139, "column": 9}, {"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 11630, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:02:48.558Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-10772b02fe1ccdff13a7", "file": "faq-system.spec.ts", "line": 139, "column": 9}, {"title": "should work on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 15724, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m    7 × unexpected value \"\"\u001b[22m\n\u001b[2m      - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m    - unexpected value \"\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m    7 × unexpected value \"\"\u001b[22m\n\u001b[2m      - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m    - unexpected value \"\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts:153:33", "location": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}, "snippet": "\u001b[0m \u001b[90m 151 |\u001b[39m       \u001b[90m// Test search functionality on mobile\u001b[39m\n \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'certificado'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoHaveValue(\u001b[32m'certificado'\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 154 |\u001b[39m     })\n \u001b[90m 155 |\u001b[39m   })\n \u001b[90m 156 |\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveValue\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\nExpected string: \u001b[32m\"certificado\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveValue\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getByPlaceholder('¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"false\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed…/>\u001b[22m\n\u001b[2m    7 × unexpected value \"\"\u001b[22m\n\u001b[2m      - locator resolved to <input value=\"\" type=\"text\" role=\"combobox\" aria-expanded=\"true\" aria-haspopup=\"listbox\" aria-label=\"Búsqueda inteligente de trámites\" placeholder=\"¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia...\" class=\"flex h-10 w-full px-3 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-chia-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed …/>\u001b[22m\n\u001b[2m    - unexpected value \"\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 151 |\u001b[39m       \u001b[90m// Test search functionality on mobile\u001b[39m\n \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'certificado'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoHaveValue(\u001b[32m'certificado'\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 154 |\u001b[39m     })\n \u001b[90m 155 |\u001b[39m   })\n \u001b[90m 156 |\u001b[39m })\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts:153:33\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:03:10.212Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\test-results\\faq-system-FAQ-System-E2E--7fc44-uld-work-on-mobile-viewport-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\faq-system.spec.ts", "column": 33, "line": 153}}], "status": "unexpected"}], "id": "d43c6b69427cd64ffd78-928e1025869d66639f69", "file": "faq-system.spec.ts", "line": 139, "column": 9}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-01T21:01:09.465Z", "duration": 136585.66999999998, "expected": 28, "skipped": 0, "unexpected": 2, "flaky": 0}}