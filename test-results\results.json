{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\global-setup.ts", "globalTeardown": "C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\tests\\e2e\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Documents/augment-projects/chia-tramites/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "faq-system.spec.ts", "file": "faq-system.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "FAQ System E2E Tests - Simplified", "file": "faq-system.spec.ts", "line": 13, "column": 6, "specs": [], "suites": [{"title": "Basic Application Functionality", "file": "faq-system.spec.ts", "line": 25, "column": 8, "specs": [{"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 20795, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:52:56.051Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-9b805d3f5cb639120996", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 20135, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:52:55.948Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-ed666bf58dd5251241c3", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 17420, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:52:55.946Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-d411d72fcfca1c834297", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 21798, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:52:56.104Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-1a1c6be02c3da06114f9", "file": "faq-system.spec.ts", "line": 26, "column": 9}, {"title": "should load home page with main search bar", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 2, "status": "passed", "duration": 9051, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T21:53:19.927Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d43c6b69427cd64ffd78-d742afed10bdb98e2041", "file": "faq-system.spec.ts", "line": 26, "column": 9}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-01T21:52:38.801Z", "duration": 50532.473, "expected": 5, "skipped": 0, "unexpected": 0, "flaky": 0}}