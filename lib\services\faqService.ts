import tramitesData from '@/tramites_chia_optimo.json'
import opasData from '@/OPA-chia-optimo.json'

/**
 * Interfaz para una pregunta frecuente
 */
export interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
  relatedProcedures: string[]
  popularity: number
  lastUpdated: Date
}

/**
 * Interfaz para categorías de FAQ
 */
export interface FAQCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  count: number
}

/**
 * Opciones de búsqueda para FAQ
 */
export interface FAQSearchOptions {
  category?: string
  limit?: number
  includeRelated?: boolean
}

/**
 * Servicio para gestionar preguntas frecuentes
 * Implementa patrón Singleton para eficiencia
 */
class FAQService {
  private static instance: FAQService
  private faqs: Map<string, FAQItem> = new Map()
  private categories: Map<string, FAQCategory> = new Map()
  private initialized = false

  private constructor() {}

  static getInstance(): FAQService {
    if (!FAQService.instance) {
      FAQService.instance = new FAQService()
    }
    return FAQService.instance
  }

  /**
   * Inicializar el servicio con datos predefinidos
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return

    // Definir categorías principales
    const categories: FAQCategory[] = [
      {
        id: 'impuestos',
        name: 'Impuestos y Tributos',
        description: 'Preguntas sobre impuestos municipales, predial, industria y comercio',
        icon: 'Receipt',
        color: 'bg-blue-500',
        count: 0
      },
      {
        id: 'licencias',
        name: 'Licencias y Permisos',
        description: 'Construcción, funcionamiento, comerciales y ambientales',
        icon: 'FileCheck',
        color: 'bg-green-500',
        count: 0
      },
      {
        id: 'certificados',
        name: 'Certificados',
        description: 'Residencia, libertad y tradición, estratificación',
        icon: 'Award',
        color: 'bg-purple-500',
        count: 0
      },
      {
        id: 'servicios',
        name: 'Servicios Públicos',
        description: 'Alumbrado público, aseo, acueducto y alcantarillado',
        icon: 'Zap',
        color: 'bg-yellow-500',
        count: 0
      },
      {
        id: 'tramites',
        name: 'Trámites Generales',
        description: 'Procedimientos administrativos y documentación',
        icon: 'FileText',
        color: 'bg-indigo-500',
        count: 0
      },
      {
        id: 'pagos',
        name: 'Pagos y Facturación',
        description: 'Métodos de pago, facturación y paz y salvos',
        icon: 'CreditCard',
        color: 'bg-red-500',
        count: 0
      }
    ]

    // Cargar categorías
    categories.forEach(category => {
      this.categories.set(category.id, category)
    })

    // Generar FAQs basadas en datos reales
    await this.generateFAQsFromData()

    this.initialized = true
  }

  /**
   * Generar FAQs basadas en los datos de trámites y OPAs
   */
  private async generateFAQsFromData(): Promise<void> {
    const faqs: FAQItem[] = [
      // Impuestos y Tributos
      {
        id: 'impuesto-predial-que-es',
        question: '¿Qué es el impuesto predial y cómo se calcula?',
        answer: 'El impuesto predial unificado es un tributo municipal que grava la propiedad inmueble. Se calcula de acuerdo con el avalúo catastral del predio y el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta para consultas es de 1 hora.',
        category: 'impuestos',
        tags: ['impuesto predial', 'avalúo catastral', 'tributos', 'propiedad'],
        relatedProcedures: ['Impuesto predial unificado'],
        popularity: 95,
        lastUpdated: new Date()
      },
      {
        id: 'impuesto-industria-comercio',
        question: '¿Cómo funciona el impuesto de industria y comercio?',
        answer: 'El impuesto de industria y comercio y su complementario de avisos y tableros se calcula de acuerdo con los ingresos obtenidos en el año inmediatamente anterior, según el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta es de 1 día.',
        category: 'impuestos',
        tags: ['industria y comercio', 'avisos y tableros', 'ingresos', 'comercio'],
        relatedProcedures: ['Impuesto de industria y comercio y su complementario de avisos y tableros'],
        popularity: 85,
        lastUpdated: new Date()
      },
      {
        id: 'alumbrado-publico-tarifa',
        question: '¿Cuánto cuesta el impuesto de alumbrado público?',
        answer: 'Las tarifas del impuesto sobre el servicio de alumbrado público están establecidas en el Acuerdo 130 de 2017. Para predios usuarios de energía eléctrica domiciliaria es 0.5 por mil sobre el valor del impuesto predial. Para predios urbanizables no urbanizados aplican tarifas específicas.',
        category: 'impuestos',
        tags: ['alumbrado público', 'tarifas', 'energía eléctrica', 'predios'],
        relatedProcedures: ['Impuesto sobre el servicio de alumbrado público'],
        popularity: 70,
        lastUpdated: new Date()
      },

      // Licencias y Permisos
      {
        id: 'licencia-construccion-requisitos',
        question: '¿Qué requisitos necesito para obtener una licencia de construcción?',
        answer: 'Para obtener una licencia de construcción debe presentar los documentos técnicos requeridos ante la Secretaría de Planeación. El proceso tiene un costo de $419.000 y un tiempo de respuesta de 45 días hábiles. Consulte los requisitos específicos en el portal SUIT o GOV.CO.',
        category: 'licencias',
        tags: ['licencia construcción', 'planeación', 'obras civiles', 'requisitos'],
        relatedProcedures: ['Licencia de construcción'],
        popularity: 90,
        lastUpdated: new Date()
      },
      {
        id: 'licencia-funcionamiento-comercio',
        question: '¿Cómo obtengo la licencia de funcionamiento para mi negocio?',
        answer: 'La licencia de funcionamiento se tramita según el tipo de actividad comercial. Debe cumplir con los requisitos sanitarios, de seguridad y urbanísticos. Consulte con la dependencia correspondiente según su actividad específica.',
        category: 'licencias',
        tags: ['licencia funcionamiento', 'negocio', 'comercio', 'actividad comercial'],
        relatedProcedures: [],
        popularity: 88,
        lastUpdated: new Date()
      },

      // Certificados
      {
        id: 'certificado-residencia-como',
        question: '¿Cómo puedo obtener un certificado de residencia?',
        answer: 'El certificado de residencia se puede solicitar en la Secretaría General. Debe presentar documento de identidad y comprobantes de residencia en el municipio. Consulte los requisitos específicos y tiempos de respuesta en las oficinas municipales.',
        category: 'certificados',
        tags: ['certificado residencia', 'secretaría general', 'documento identidad'],
        relatedProcedures: [],
        popularity: 92,
        lastUpdated: new Date()
      },
      {
        id: 'certificado-libertad-tradicion',
        question: '¿Qué es el certificado de libertad y tradición?',
        answer: 'El certificado de libertad y tradición es un documento que certifica la propiedad inmobiliaria y su historial jurídico. Se tramita en la Secretaría General, no tiene costo y el tiempo de respuesta es de 1 día hábil.',
        category: 'certificados',
        tags: ['libertad y tradición', 'propiedad inmobiliaria', 'historial jurídico'],
        relatedProcedures: ['Certificado de libertad y tradición'],
        popularity: 80,
        lastUpdated: new Date()
      },

      // Servicios Públicos
      {
        id: 'servicios-publicos-consulta',
        question: '¿Dónde puedo consultar sobre servicios públicos?',
        answer: 'Para consultas sobre servicios públicos como acueducto, alcantarillado y aseo, puede dirigirse a la dependencia correspondiente o consultar en línea. Cada servicio tiene procedimientos específicos de facturación y atención.',
        category: 'servicios',
        tags: ['servicios públicos', 'acueducto', 'alcantarillado', 'aseo'],
        relatedProcedures: [],
        popularity: 75,
        lastUpdated: new Date()
      },

      // Pagos y Facturación
      {
        id: 'metodos-pago-disponibles',
        question: '¿Qué métodos de pago están disponibles?',
        answer: 'El municipio acepta diversos métodos de pago para trámites y servicios: efectivo en oficinas, transferencias bancarias, PSE y otros medios electrónicos. Consulte las opciones específicas para cada trámite.',
        category: 'pagos',
        tags: ['métodos pago', 'efectivo', 'transferencias', 'PSE', 'electrónicos'],
        relatedProcedures: [],
        popularity: 85,
        lastUpdated: new Date()
      },
      {
        id: 'paz-salvo-como-obtener',
        question: '¿Cómo obtengo un paz y salvo municipal?',
        answer: 'El paz y salvo municipal certifica que está al día con sus obligaciones tributarias. Se puede solicitar en la Secretaría de Hacienda presentando documento de identidad y comprobante de pago de impuestos al día.',
        category: 'pagos',
        tags: ['paz y salvo', 'obligaciones tributarias', 'secretaría hacienda'],
        relatedProcedures: [],
        popularity: 78,
        lastUpdated: new Date()
      }
    ]

    // Cargar FAQs y actualizar contadores de categorías
    faqs.forEach(faq => {
      this.faqs.set(faq.id, faq)
      const category = this.categories.get(faq.category)
      if (category) {
        category.count++
        this.categories.set(faq.category, category)
      }
    })
  }

  /**
   * Obtener todas las categorías
   */
  async getCategories(): Promise<FAQCategory[]> {
    await this.initialize()
    return Array.from(this.categories.values()).sort((a, b) => b.count - a.count)
  }

  /**
   * Obtener FAQs por categoría
   */
  async getFAQsByCategory(categoryId: string, limit?: number): Promise<FAQItem[]> {
    await this.initialize()
    const faqs = Array.from(this.faqs.values())
      .filter(faq => faq.category === categoryId)
      .sort((a, b) => b.popularity - a.popularity)
    
    return limit ? faqs.slice(0, limit) : faqs
  }

  /**
   * Buscar FAQs por texto
   */
  async searchFAQs(query: string, options: FAQSearchOptions = {}): Promise<FAQItem[]> {
    await this.initialize()
    const { category, limit = 10, includeRelated = true } = options

    if (!query.trim()) {
      return []
    }

    const searchTerm = query.toLowerCase().trim()
    let faqs = Array.from(this.faqs.values())

    // Filtrar por categoría si se especifica
    if (category) {
      faqs = faqs.filter(faq => faq.category === category)
    }

    // Buscar en preguntas, respuestas y tags
    const results = faqs.filter(faq => {
      const questionMatch = faq.question.toLowerCase().includes(searchTerm)
      const answerMatch = faq.answer.toLowerCase().includes(searchTerm)
      const tagMatch = faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      const procedureMatch = includeRelated && 
        faq.relatedProcedures.some(proc => proc.toLowerCase().includes(searchTerm))

      return questionMatch || answerMatch || tagMatch || procedureMatch
    })

    // Ordenar por relevancia (popularidad y coincidencias)
    results.sort((a, b) => {
      const aScore = this.calculateRelevanceScore(a, searchTerm)
      const bScore = this.calculateRelevanceScore(b, searchTerm)
      return bScore - aScore
    })

    return results.slice(0, limit)
  }

  /**
   * Calcular puntuación de relevancia
   */
  private calculateRelevanceScore(faq: FAQItem, searchTerm: string): number {
    let score = 0
    const term = searchTerm.toLowerCase()

    // Coincidencia exacta en pregunta (peso alto)
    if (faq.question.toLowerCase().includes(term)) {
      score += 100
    }

    // Coincidencia en respuesta (peso medio)
    if (faq.answer.toLowerCase().includes(term)) {
      score += 50
    }

    // Coincidencia en tags (peso medio)
    faq.tags.forEach(tag => {
      if (tag.toLowerCase().includes(term)) {
        score += 30
      }
    })

    // Popularidad (peso bajo)
    score += faq.popularity * 0.1

    return score
  }

  /**
   * Obtener FAQs más populares
   */
  async getPopularFAQs(limit: number = 5): Promise<FAQItem[]> {
    await this.initialize()
    return Array.from(this.faqs.values())
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, limit)
  }

  /**
   * Obtener FAQ por ID
   */
  async getFAQById(id: string): Promise<FAQItem | null> {
    await this.initialize()
    return this.faqs.get(id) || null
  }

  /**
   * Obtener estadísticas del FAQ
   */
  async getFAQStats(): Promise<{
    totalFAQs: number
    totalCategories: number
    averagePopularity: number
    mostPopularCategory: string
  }> {
    await this.initialize()
    const faqs = Array.from(this.faqs.values())
    const categories = Array.from(this.categories.values())
    
    const averagePopularity = faqs.reduce((sum, faq) => sum + faq.popularity, 0) / faqs.length
    const mostPopularCategory = categories.reduce((prev, current) => 
      current.count > prev.count ? current : prev
    ).name

    return {
      totalFAQs: faqs.length,
      totalCategories: categories.length,
      averagePopularity: Math.round(averagePopularity),
      mostPopularCategory
    }
  }
}

export default FAQService.getInstance()
