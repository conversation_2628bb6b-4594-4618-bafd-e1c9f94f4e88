import { supabase } from '@/lib/supabase/client'
import type { Database } from '@/lib/database.types'
import faqAnalytics from './faqAnalytics'

// Tipos de base de datos
type FAQRow = Database['public']['Tables']['faqs']['Row']
type FAQCategoryRow = Database['public']['Tables']['faq_categories']['Row']

/**
 * Interfaz para una pregunta frecuente (adaptada para base de datos)
 */
export interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  categoryId: string | null
  tags: string[]
  relatedProcedures: string[]
  popularity: number
  viewCount: number
  helpfulVotes: number
  unhelpfulVotes: number
  lastUpdated: Date
}

/**
 * Interfaz para categorías de FAQ (adaptada para base de datos)
 */
export interface FAQCategory {
  id: string
  name: string
  description: string | null
  icon: string | null
  color: string | null
  displayOrder: number | null
  count: number
}

/**
 * Opciones de búsqueda para FAQ
 */
export interface FAQSearchOptions {
  category?: string
  limit?: number
  includeRelated?: boolean
}

/**
 * Servicio para gestionar preguntas frecuentes
 * Ahora conectado con Supabase para datos dinámicos
 */
class FAQService {
  private static instance: FAQService
  private categoriesCache: Map<string, FAQCategory> = new Map()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutos
  private lastCacheUpdate = 0

  private constructor() {}

  static getInstance(): FAQService {
    if (!FAQService.instance) {
      FAQService.instance = new FAQService()
    }
    return FAQService.instance
  }

  /**
   * Convertir datos de base de datos a interfaz FAQItem
   */
  private mapFAQFromDB(faqRow: FAQRow, categoryName?: string): FAQItem {
    return {
      id: faqRow.id,
      question: faqRow.question,
      answer: faqRow.answer,
      category: categoryName || '',
      categoryId: faqRow.category_id,
      tags: faqRow.tags || [],
      relatedProcedures: faqRow.related_procedures || [],
      popularity: faqRow.popularity || 0,
      viewCount: faqRow.view_count || 0,
      helpfulVotes: faqRow.helpful_votes || 0,
      unhelpfulVotes: faqRow.unhelpful_votes || 0,
      lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || '')
    }
  }

  /**
   * Convertir datos de base de datos a interfaz FAQCategory
   */
  private mapCategoryFromDB(categoryRow: FAQCategoryRow, count: number = 0): FAQCategory {
    return {
      id: categoryRow.id,
      name: categoryRow.name,
      description: categoryRow.description,
      icon: categoryRow.icon,
      color: categoryRow.color,
      displayOrder: categoryRow.display_order,
      count
    }
  }

  /**
   * Obtener todas las categorías desde Supabase
   */
  async getCategories(): Promise<FAQCategory[]> {
    try {
      // Verificar cache
      const now = Date.now()
      if (now - this.lastCacheUpdate < this.cacheExpiry && this.categoriesCache.size > 0) {
        return Array.from(this.categoriesCache.values()).sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0))
      }

      // Obtener categorías con conteo de FAQs
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('faq_categories')
        .select(`
          *,
          faqs!inner(count)
        `)
        .eq('is_active', true)
        .order('display_order')

      if (categoriesError) {
        console.error('Error fetching FAQ categories:', categoriesError)
        return []
      }

      // Obtener conteo real de FAQs por categoría
      const { data: faqCounts, error: countError } = await supabase
        .from('faqs')
        .select('category_id')
        .eq('is_active', true)

      if (countError) {
        console.error('Error fetching FAQ counts:', countError)
      }

      // Crear mapa de conteos
      const countMap = new Map<string, number>()
      faqCounts?.forEach(faq => {
        if (faq.category_id) {
          countMap.set(faq.category_id, (countMap.get(faq.category_id) || 0) + 1)
        }
      })

      // Mapear y cachear categorías
      const categories = categoriesData?.map(cat =>
        this.mapCategoryFromDB(cat, countMap.get(cat.id) || 0)
      ) || []

      // Actualizar cache
      this.categoriesCache.clear()
      categories.forEach(cat => this.categoriesCache.set(cat.id, cat))
      this.lastCacheUpdate = now

      return categories.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0))
    } catch (error) {
      console.error('Error in getCategories:', error)
      return []
    }
  }

  /**
   * Obtener FAQs por categoría desde Supabase
   */
  async getFAQsByCategory(categoryId: string, limit?: number): Promise<FAQItem[]> {
    try {
      let query = supabase
        .from('faqs')
        .select(`
          *,
          faq_categories!inner(name)
        `)
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('popularity', { ascending: false })

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching FAQs by category:', error)
        return []
      }

      return data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_categories?.name)
      ) || []
    } catch (error) {
      console.error('Error in getFAQsByCategory:', error)
      return []
    }
  }

  /**
   * Buscar FAQs por texto usando Supabase
   */
  async searchFAQs(query: string, options: FAQSearchOptions = {}): Promise<FAQItem[]> {
    const startTime = Date.now()
    const { category, limit = 10, includeRelated = true } = options

    if (!query.trim()) {
      return []
    }

    try {
      const searchTerm = query.toLowerCase().trim()

      // Construir query base
      let supabaseQuery = supabase
        .from('faqs')
        .select(`
          *,
          faq_categories!inner(name)
        `)
        .eq('is_active', true)

      // Filtrar por categoría si se especifica
      if (category) {
        supabaseQuery = supabaseQuery.eq('category_id', category)
      }

      // Usar búsqueda de texto completo
      const { data, error } = await supabaseQuery
        .or(`question.ilike.%${searchTerm}%,answer.ilike.%${searchTerm}%`)
        .order('popularity', { ascending: false })
        .limit(limit * 2) // Obtener más para filtrar después

      if (error) {
        console.error('Error searching FAQs:', error)
        return []
      }

      // Filtrar y ordenar resultados
      let results = data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_categories?.name)
      ) || []

      // Filtrar por tags y procedimientos relacionados si includeRelated es true
      if (includeRelated) {
        results = results.filter(faq => {
          const questionMatch = faq.question.toLowerCase().includes(searchTerm)
          const answerMatch = faq.answer.toLowerCase().includes(searchTerm)
          const tagMatch = faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
          const procedureMatch = faq.relatedProcedures.some(proc => proc.toLowerCase().includes(searchTerm))

          return questionMatch || answerMatch || tagMatch || procedureMatch
        })
      }

      // Ordenar por relevancia
      results.sort((a, b) => {
        const aScore = this.calculateRelevanceScore(a, searchTerm)
        const bScore = this.calculateRelevanceScore(b, searchTerm)
        return bScore - aScore
      })

      const finalResults = results.slice(0, limit)
      const responseTime = Date.now() - startTime

      // Registrar analytics
      if (finalResults.length === 0) {
        faqAnalytics.trackNoResults(query, category)
      } else {
        faqAnalytics.trackSearch(query, finalResults.length, responseTime, category)
      }

      return finalResults
    } catch (error) {
      console.error('Error in searchFAQs:', error)
      return []
    }
  }

  /**
   * Calcular puntuación de relevancia
   */
  private calculateRelevanceScore(faq: FAQItem, searchTerm: string): number {
    let score = 0
    const term = searchTerm.toLowerCase()

    // Coincidencia exacta en pregunta (peso alto)
    if (faq.question.toLowerCase().includes(term)) {
      score += 100
    }

    // Coincidencia en respuesta (peso medio)
    if (faq.answer.toLowerCase().includes(term)) {
      score += 50
    }

    // Coincidencia en tags (peso medio)
    faq.tags.forEach(tag => {
      if (tag.toLowerCase().includes(term)) {
        score += 30
      }
    })

    // Popularidad (peso bajo)
    score += faq.popularity * 0.1

    return score
  }

  /**
   * Obtener FAQs más populares desde Supabase
   */
  async getPopularFAQs(limit: number = 5): Promise<FAQItem[]> {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select(`
          *,
          faq_categories!inner(name)
        `)
        .eq('is_active', true)
        .order('popularity', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error fetching popular FAQs:', error)
        return []
      }

      return data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_categories?.name)
      ) || []
    } catch (error) {
      console.error('Error in getPopularFAQs:', error)
      return []
    }
  }

  /**
   * Obtener FAQ por ID desde Supabase
   */
  async getFAQById(id: string): Promise<FAQItem | null> {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select(`
          *,
          faq_categories!inner(name)
        `)
        .eq('id', id)
        .eq('is_active', true)
        .single()

      if (error) {
        console.error('Error fetching FAQ by ID:', error)
        return null
      }

      const faq = this.mapFAQFromDB(data, data.faq_categories?.name)

      // Registrar visualización y actualizar contador
      faqAnalytics.trackFAQView(faq.id, faq.question)

      // Incrementar view_count en la base de datos
      await supabase
        .from('faqs')
        .update({ view_count: (data.view_count || 0) + 1 })
        .eq('id', id)

      return faq
    } catch (error) {
      console.error('Error in getFAQById:', error)
      return null
    }
  }

  /**
   * Obtener estadísticas del FAQ desde Supabase
   */
  async getFAQStats(): Promise<{
    totalFAQs: number
    totalCategories: number
    averagePopularity: number
    mostPopularCategory: string
  }> {
    try {
      // Obtener estadísticas de FAQs
      const { data: faqStats, error: faqError } = await supabase
        .from('faqs')
        .select('popularity')
        .eq('is_active', true)

      if (faqError) {
        console.error('Error fetching FAQ stats:', faqError)
        return { totalFAQs: 0, totalCategories: 0, averagePopularity: 0, mostPopularCategory: '' }
      }

      // Obtener estadísticas de categorías
      const { data: categoryStats, error: categoryError } = await supabase
        .from('faq_categories')
        .select(`
          name,
          faqs!inner(count)
        `)
        .eq('is_active', true)

      if (categoryError) {
        console.error('Error fetching category stats:', categoryError)
      }

      // Calcular estadísticas
      const totalFAQs = faqStats?.length || 0
      const totalCategories = categoryStats?.length || 0
      const averagePopularity = totalFAQs > 0
        ? Math.round(faqStats.reduce((sum, faq) => sum + (faq.popularity || 0), 0) / totalFAQs)
        : 0

      // Obtener categoría más popular
      const categories = await this.getCategories()
      const mostPopularCategory = categories.length > 0
        ? categories.reduce((prev, current) => current.count > prev.count ? current : prev).name
        : ''

      return {
        totalFAQs,
        totalCategories,
        averagePopularity,
        mostPopularCategory
      }
    } catch (error) {
      console.error('Error in getFAQStats:', error)
      return { totalFAQs: 0, totalCategories: 0, averagePopularity: 0, mostPopularCategory: '' }
    }
  }
}

export default FAQService.getInstance()
