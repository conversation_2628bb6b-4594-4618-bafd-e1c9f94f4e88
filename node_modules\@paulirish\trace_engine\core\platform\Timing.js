// Copyright 2023 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
export function secondsToMilliSeconds(x) {
    return (x * 1000);
}
export function milliSecondsToSeconds(x) {
    return (x / 1000);
}
export function microSecondsToMilliSeconds(x) {
    return (x / 1000);
}
//# sourceMappingURL=Timing.js.map