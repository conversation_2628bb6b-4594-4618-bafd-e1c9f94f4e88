{"version": 3, "file": "FirefoxLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/FirefoxLauncher.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,4CAAoB;AACpB,0CAAoD;AACpD,4CAAoB;AACpB,gDAAwB;AAExB,kDAAgF;AAEhF,+CAA6C;AAC7C,iDAAyC;AAMzC,6DAAyE;AAEzE,wCAAgC;AAEhC;;GAEG;AACH,MAAa,eAAgB,SAAQ,oCAAe;IAClD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAsC,EAAE;QAExC,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,cAAc,EACd,IAAI,GAAG,KAAK,EACZ,iBAAiB,GAAG,EAAE,EACtB,aAAa,GAAG,IAAI,GACrB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE;YACtB,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;SACrD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YAC3C,gBAAgB,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;SACH;aAAM;YACL,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAChC;QAED,IACE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF;YACA,IAAI,IAAI,EAAE;gBACR,IAAA,kBAAM,EACJ,aAAa,KAAK,IAAI,EACtB,2EAA2E,CAC5E,CAAC;aACH;YACD,gBAAgB,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;SACxE;QAED,IAAI,WAA+B,CAAC;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,gEAAgE;QAChE,gEAAgE;QAChE,MAAM,eAAe,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvD,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;YAC1B,WAAW,GAAG,gBAAgB,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC/C,MAAM,IAAI,KAAK,CAAC,iCAAiC,WAAW,GAAG,CAAC,CAAC;aAClE;YAED,+DAA+D;YAC/D,6BAA6B;YAC7B,iBAAiB,GAAG,KAAK,CAAC;SAC3B;aAAM;YACL,WAAW,GAAG,MAAM,IAAA,kBAAO,EAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACnD,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;QAED,MAAM,IAAA,wBAAa,EAAC,kBAAiB,CAAC,OAAO,EAAE;YAC7C,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,iBAAiB;SAC/B,CAAC,CAAC;QAEH,IAAI,iBAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,cAAc,EAAE;YACrD,IAAA,kBAAM,EACJ,cAAc,EACd,gEAAgE,CACjE,CAAC;YACF,iBAAiB,GAAG,cAAc,CAAC;SACpC;aAAM;YACL,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;SAC3C;QAED,OAAO;YACL,iBAAiB;YACjB,WAAW;YACX,IAAI,EAAE,gBAAgB;YACtB,cAAc,EAAE,iBAAiB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,WAAmB,EACnB,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI;gBACF,MAAM,IAAA,UAAE,EAAC,WAAW,CAAC,CAAC;aACvB;YAAC,OAAO,KAAK,EAAE;gBACd,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;aACb;SACF;aAAM;YACL,IAAI;gBACF,8DAA8D;gBAC9D,8DAA8D;gBAC9D,MAAM,IAAA,iBAAM,EAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;gBAEhD,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;gBACrE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;oBAClC,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBACrD,MAAM,IAAA,iBAAM,EAAC,SAAS,CAAC,CAAC;oBACxB,MAAM,IAAA,iBAAM,EAAC,eAAe,EAAE,SAAS,CAAC,CAAC;iBAC1C;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;aACnB;SACF;IACH,CAAC;IAEQ,cAAc;QACrB,+DAA+D;QAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,KAAK,QAAQ,EAAE;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;gBACzD,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAoB;aAC1C,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc,EAAE,CAAC;YACvD,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE;gBACrB,IAAI,CAAC,qBAAqB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;aAChD;SACF;QACD,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACtC,CAAC;IAEQ,WAAW,CAAC,UAAwC,EAAE;QAC7D,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GAAG,IAAI,GACnB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,CAAC,aAAa,CAAC,CAAC;QAEzC,QAAQ,YAAE,CAAC,QAAQ,EAAE,EAAE;YACrB,KAAK,QAAQ;gBACX,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC5C,MAAM;SACT;QACD,IAAI,WAAW,EAAE;YACf,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;QACD,IAAI,QAAQ,EAAE;YACZ,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACrC;QACD,IAAI,QAAQ,EAAE;YACZ,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACrC;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF;YACA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACtC;QACD,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF;AAnLD,0CAmLC"}