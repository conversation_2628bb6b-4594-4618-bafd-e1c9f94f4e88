{"version": 3, "file": "CPUProfileDataModel.js", "sourceRoot": "", "sources": ["../../../../../../front_end/models/cpu_profile/CPUProfileDataModel.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,iCAAiC,CAAC;AAG5D,OAAO,EAAC,WAAW,EAAE,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAEpE,MAAM,OAAO,cAAe,SAAQ,WAAW;IACpC,EAAE,CAAS;IACX,IAAI,CAAS;IACtB,gEAAgE;IAChE,kEAAkE;IAClE,qEAAqE;IACrE,+DAA+D;IAC/D,yBAAyB;IACzB,aAAa,CAAiD;IACrD,WAAW,CAAc;IAElC,YAAY,IAAmC,EAAE,gBAAwB,CAAC,kBAAkB;QAC1F,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAK;YACjB,mEAAmE;YACnE,mBAAmB;YACnB,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC;YAClC,mEAAmE;YACnE,mBAAmB;YACnB,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;YAC1B,mEAAmE;YACnE,mBAAmB;YACnB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;YAChB,mEAAmE;YACnE,mBAAmB;YACnB,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YAClC,mEAAmE;YACnE,mBAAmB;YACnB,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;SACR,CAAC;QACnD,KAAK,CAAC,SAAS,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC;QACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,oFAAoF;QACpF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IACpG,CAAC;CACF;AAED,MAAM,OAAO,mBAAoB,SAAQ,gBAAgB;IACvD,gBAAgB,CAA4B;IAC5C,cAAc,CAA4B;IAC1C,UAAU,CAAW;IACrB,OAAO,CAAqB;IAC5B,KAAK,CAAY;IACjB,aAAa,CAAS;IACtB,WAAW,CAAiB;IAC5B;;;;OAIG;IACH,eAAe,CAA4B;IAC3C,MAAM,CAAe;IACrB,WAAW,CAAe;IAC1B,QAAQ,CAAe;IACvB,gBAAgB,CAAY;IAC5B,sBAAsB,CAAY;IAClC,YAAY,OAAwB;QAClC,KAAK,EAAE,CAAC;QACR,0BAA0B;QAC1B,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAChD,IAAI,cAAc,EAAE,CAAC;YACnB,6EAA6E;YAC7E,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACjD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC7C,0BAA0B;YAC1B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YACrC,IAAI,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,qFAAqF;YACrF,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACjD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAE/B,4DAA4D;QAC5D,gEAAgE;QAChE,iEAAiE;QACjE,gEAAgE;QAChE,kEAAkE;QAClE,6DAA6D;QAC7D,WAAW;QACX,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,kCAAkC,CAAC,OAAkC;QAC3E,0BAA0B;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACnC,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAoC,EAAE,CAAC;QAClD,0BAA0B;QAC1B,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,0BAA0B;QAC1B,OAAO,OAAO,CAAC,IAAI,CAAC;QACpB,SAAS,gBAAgB,CAAC,IAAmC;YAC3D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjB,0BAA0B;YAC1B,IAAI,CAAC,QAAQ,GAAI,IAAI,CAAC,QAA4C,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACzF,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,OAAkC;QAC1D,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC;QACzC,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACnD,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1C,UAAU,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;QACnC,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;;;OAQG;IACK,oBAAoB,CAAC,KAAsC;QACjE,SAAS,wBAAwB,CAAC,KAAsC;YACtE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACtB,OAAO;YACT,CAAC;YACD,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,0BAA0B;gBAC1B,MAAM,UAAU,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrD,0BAA0B;gBAC1B,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,0BAA0B;oBAC1B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,0BAA0B;oBAC1B,UAAU,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,wBAAwB,CAAC,KAAsC,EAAE,OAA2B;YACnG,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC5C,OAAO;YACT,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;YACxB,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzC,SAAS;gBACX,CAAC;gBACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;QAED,4DAA4D;QAC5D,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAyC,CAAC;QAC1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC;QAED,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChF,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;QACtF,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,uEAAuE;QACvE,+DAA+D;QAC/D,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QAEjC,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,OAAO,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,IAAI,UAAU,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;YACzC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC/B,SAAS;YACX,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACzB,UAAU,CAAC,QAAQ,GAAG,EAAE,CAAC;YAC3B,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAC9D,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,UAAU,GAAG,UAAU,CAAC;YAExB,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;YACxD,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,UAA4B,CAAC,CAAC,CAAC;YACzG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACrG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAW,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;OAGG;IACK,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAC5D,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7D,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QACD,IAAI,UAAU,GAAa,IAAI,CAAC,UAAU,CAAC;QAC3C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,+DAA+D;YAC/D,aAAa;YACb,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC/C,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAChF,qEAAqE;YACrE,UAAU,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,QAAQ,CAAC;YAClD,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,6CAA6C;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;YAC9C,qEAAqE;YACrE,MAAM,aAAa,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,mBAAmB,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;IACjE,CAAC;IAED;;;OAGG;IACK,gBAAgB;QACtB,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACrG,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,KAAK,qBAAqB,EAAE,CAAC;gBAChD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACrB,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW,EAAE,CAAC;gBAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAC1B,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,uEAAuE;QACvE,uEAAuE;QACvE,8BAA8B;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QACD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,UAAU,GAAW,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,MAAM,GAAW,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC;YACxE,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC1C,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnF,OAAO,CAAC,KAAK,CAAC,uCAAuC,UAAU,IAAI,UAAU,EAAE,CAAC,CAAC;gBACjF,SAAS;YACX,CAAC;YACD,IAAI,MAAM,KAAK,aAAa,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAClF,UAAU,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClD,OAAO,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;YACpC,CAAC;YACD,UAAU,GAAG,MAAM,CAAC;YACpB,MAAM,GAAG,UAAU,CAAC;QACtB,CAAC;QACD,SAAS,UAAU,CAAC,IAAiB;YACnC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACzC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,SAAS,YAAY,CAAC,MAAc;YAClC,OAAO,MAAM,KAAK,aAAa,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,UAAU,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,YAAY,CACR,iBAAgF,EAChF,kBAAgH,EAChH,SAAkB,EAAE,QAAiB;QACvC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC;QAC3B,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,UAAU,GACZ,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC1G,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,UAAU,GAAkB,EAAE,CAAC;QACrC,IAAI,MAAM,GAAW,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QACzC,IAAI,UAAU,CAAC;QACf,IAAI,YAAY,GAAqB,IAAI,CAAC;QAE1C,uCAAuC;QACvC,yDAAyD;QACzD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;QAChD,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,IAAI,CAAC,sBAAsB,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC;QACD,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAE1D,IAAI,IAAI,CAAC;QACT,IAAI,WAAW,CAAC;QAChB,KAAK,WAAW,GAAG,UAAU,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,EAAE,EAAE,CAAC;YACzE,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;YACrC,IAAI,UAAU,IAAI,QAAQ,EAAE,CAAC;gBAC3B,MAAM;YACR,CAAC;YACD,MAAM,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;YAChC,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;gBAClB,SAAS;YACX,CAAC;YACD,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,QAAQ,GAAqB,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;YAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,SAAS;YACX,CAAC;YAED,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC9B,uFAAuF;gBACvF,YAAY,GAAG,QAAQ,CAAC;gBACxB,iBAAiB,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC9D,eAAe,CAAC,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC;gBACzC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,EAAE,CAAC;gBACZ,SAAS;YACX,CAAC;YACD,IAAI,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,YAAY,EAAE,CAAC;gBAClD,kBAAkB;gBAClB,MAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;gBACpC,qBAAqB,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;gBAChD,kBAAkB,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAChH,EAAE,QAAQ,CAAC;gBACX,QAAQ,GAAG,YAAY,CAAC;gBACxB,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC;gBACrB,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YAED,6DAA6D;YAC7D,kEAAkE;YAClE,4CAA4C;YAC5C,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC3C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;YAED,kEAAkE;YAClE,+DAA+D;YAC/D,kEAAkE;YAClE,8DAA8D;YAC9D,2BAA2B;YAC3B,EAAE;YACF,eAAe;YACf,EAAE;YACF,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,WAAW;YACX,kBAAkB;YAClB,yBAAyB;YACzB,kBAAkB;YAClB,EAAE;YACF,8DAA8D;YAC9D,gDAAgD;YAChD,OAAO,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;gBACpC,qBAAqB,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;gBAChD,kBAAkB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1G,EAAE,QAAQ,CAAC;gBACX,uDAAuD;gBACvD,gEAAgE;gBAChE,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC;oBAC1C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACtB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;gBACrB,CAAC;gBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,gDAAgD;YAChD,OAAO,UAAU,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;gBACrC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,MAAM;gBACR,CAAC;gBACD,IAAI,GAAG,WAAW,CAAC;gBACnB,iBAAiB,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC9D,eAAe,CAAC,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC;gBACzC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,GAAG,EAAE,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;QAC5D,IAAI,IAAI,IAAI,YAAY,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;YAC5D,MAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;YACpC,qBAAqB,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;YAChD,kBAAkB,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC9G,EAAE,QAAQ,CAAC;YACX,MAAM,GAAG,YAAY,CAAC,EAAE,CAAC;QAC3B,CAAC;QACD,KAAK,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9E,MAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;YACpC,qBAAqB,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;YAChD,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClG,EAAE,QAAQ,CAAC;QACb,CAAC;IACH,CAAC;IACD;;OAEG;IACH,WAAW,CAAC,KAAa;QACvB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC;IAC/E,CAAC;IACD;;OAEG;IACH,QAAQ,CAAC,MAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;CACF", "sourcesContent": ["// Copyright 2014 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../core/platform/platform.js';\nimport type * as Protocol from '../../generated/protocol.js';\n\nimport {ProfileNode, ProfileTreeModel} from './ProfileTreeModel.js';\n\nexport class CPUProfileNode extends ProfileNode {\n  override id: number;\n  override self: number;\n  // Position ticks are available in profile nodes coming from CDP\n  // profiles and not in those coming from tracing. They are used to\n  // calculate the line level execution time shown in the Sources panel\n  // after recording a profile. For trace CPU profiles we use the\n  // `lines` array instead.\n  positionTicks: Protocol.Profiler.PositionTickInfo[]|undefined;\n  override deoptReason: string|null;\n\n  constructor(node: Protocol.Profiler.ProfileNode, samplingInterval: number /* milliseconds */) {\n    const callFrame = node.callFrame || ({\n                        // TODO(crbug.com/1172300) Ignored during the jsdoc to ts migration\n                        // @ts-expect-error\n                        functionName: node['functionName'],\n                        // TODO(crbug.com/1172300) Ignored during the jsdoc to ts migration\n                        // @ts-expect-error\n                        scriptId: node['scriptId'],\n                        // TODO(crbug.com/1172300) Ignored during the jsdoc to ts migration\n                        // @ts-expect-error\n                        url: node['url'],\n                        // TODO(crbug.com/1172300) Ignored during the jsdoc to ts migration\n                        // @ts-expect-error\n                        lineNumber: node['lineNumber'] - 1,\n                        // TODO(crbug.com/1172300) Ignored during the jsdoc to ts migration\n                        // @ts-expect-error\n                        columnNumber: node['columnNumber'] - 1,\n                      } as Protocol.Runtime.CallFrame);\n    super(callFrame);\n    this.id = node.id;\n    this.self = (node.hitCount || 0) * samplingInterval;\n    this.positionTicks = node.positionTicks;\n    // Compatibility: legacy backends could provide \"no reason\" for optimized functions.\n    this.deoptReason = node.deoptReason && node.deoptReason !== 'no reason' ? node.deoptReason : null;\n  }\n}\n\nexport class CPUProfileDataModel extends ProfileTreeModel {\n  profileStartTime: number /* milliseconds */;\n  profileEndTime: number /* milliseconds */;\n  timestamps: number[];\n  samples: number[]|undefined;\n  lines?: number[];\n  totalHitCount: number;\n  profileHead: CPUProfileNode;\n  /**\n   * A cache for the nodes we have parsed.\n   * Note: \"Parsed\" nodes are different from the \"Protocol\" nodes, the\n   * latter being the raw data we receive from the backend.\n   */\n  #idToParsedNode!: Map<number, ProfileNode>;\n  gcNode?: ProfileNode;\n  programNode?: ProfileNode;\n  idleNode?: ProfileNode;\n  #stackStartTimes?: number[];\n  #stackChildrenDuration?: number[];\n  constructor(profile: ExtendedProfile) {\n    super();\n    // @ts-ignore Legacy types\n    const isLegacyFormat = Boolean(profile['head']);\n    if (isLegacyFormat) {\n      // Legacy format contains raw timestamps and start/stop times are in seconds.\n      this.profileStartTime = profile.startTime * 1000;\n      this.profileEndTime = profile.endTime * 1000;\n      // @ts-ignore Legacy types\n      this.timestamps = profile.timestamps;\n      this.compatibilityConversionHeadToNodes(profile);\n    } else {\n      // Current format encodes timestamps as deltas. Start/stop times are in microseconds.\n      this.profileStartTime = profile.startTime / 1000;\n      this.profileEndTime = profile.endTime / 1000;\n      this.timestamps = this.convertTimeDeltas(profile);\n    }\n    this.samples = profile.samples;\n\n    // Lines are available only in profiles coming from tracing.\n    // Elements in the lines array have a 1 to 1 correspondance with\n    // samples, by array position. They can be 1 or 0 and indicate if\n    // there is line data for a given sample, i.e. if a given sample\n    // needs to be included to calculate the line level execution time\n    // data, which we show in the sources panel after recording a\n    // profile.\n    this.lines = profile.lines;\n    this.totalHitCount = 0;\n    this.profileHead = this.translateProfileTree(profile.nodes);\n    this.initialize(this.profileHead);\n    this.extractMetaNodes();\n    if (this.samples?.length) {\n      this.sortSamples();\n      this.normalizeTimestamps();\n      this.fixMissingSamples();\n    }\n  }\n\n  private compatibilityConversionHeadToNodes(profile: Protocol.Profiler.Profile): void {\n    // @ts-ignore Legacy types\n    if (!profile.head || profile.nodes) {\n      return;\n    }\n    const nodes: Protocol.Profiler.ProfileNode[] = [];\n    // @ts-ignore Legacy types\n    convertNodesTree(profile.head);\n    profile.nodes = nodes;\n    // @ts-ignore Legacy types\n    delete profile.head;\n    function convertNodesTree(node: Protocol.Profiler.ProfileNode): number {\n      nodes.push(node);\n      // @ts-ignore Legacy types\n      node.children = (node.children as Protocol.Profiler.ProfileNode[]).map(convertNodesTree);\n      return node.id;\n    }\n  }\n\n  /**\n   * Calculate timestamps using timeDeltas. Some CPU profile formats,\n   * like the ones contained in traces have timeDeltas instead of\n   * timestamps.\n   */\n  private convertTimeDeltas(profile: Protocol.Profiler.Profile): number[] {\n    if (!profile.timeDeltas) {\n      return [];\n    }\n    let lastTimeMicroSec = profile.startTime;\n    const timestamps = new Array(profile.timeDeltas.length);\n    for (let i = 0; i < profile.timeDeltas.length; ++i) {\n      lastTimeMicroSec += profile.timeDeltas[i];\n      timestamps[i] = lastTimeMicroSec;\n    }\n    return timestamps;\n  }\n\n  /**\n   * Creates a Tree of CPUProfileNodes using the Protocol.Profiler.ProfileNodes.\n   * As the tree is built, samples of native code (prefixed with \"native \") are\n   * filtered out. Samples of filtered nodes are replaced with the parent of the\n   * node being filtered.\n   *\n   * This function supports legacy and new definitions of the CDP Profiler.Profile\n   * type.\n   */\n  private translateProfileTree(nodes: Protocol.Profiler.ProfileNode[]): CPUProfileNode {\n    function buildChildrenFromParents(nodes: Protocol.Profiler.ProfileNode[]): void {\n      if (nodes[0].children) {\n        return;\n      }\n      nodes[0].children = [];\n      for (let i = 1; i < nodes.length; ++i) {\n        const node = nodes[i];\n        // @ts-ignore Legacy types\n        const parentNode = protocolNodeById.get(node.parent);\n        // @ts-ignore Legacy types\n        if (parentNode.children) {\n          // @ts-ignore Legacy types\n          parentNode.children.push(node.id);\n        } else {\n          // @ts-ignore Legacy types\n          parentNode.children = [node.id];\n        }\n      }\n    }\n\n    /**\n     * Calculate how many times each node was sampled in the profile, if\n     * not available in the profile data.\n     */\n    function buildHitCountFromSamples(nodes: Protocol.Profiler.ProfileNode[], samples: number[]|undefined): void {\n      // If hit count is available, this profile has the new format, so\n      // no need to continue.`\n      if (typeof (nodes[0].hitCount) === 'number') {\n        return;\n      }\n      if (!samples) {\n        throw new Error('Error: Neither hitCount nor samples are present in profile.');\n      }\n      for (let i = 0; i < nodes.length; ++i) {\n        nodes[i].hitCount = 0;\n      }\n      for (let i = 0; i < samples.length; ++i) {\n        const node = protocolNodeById.get(samples[i]);\n        if (!node || node.hitCount === undefined) {\n          continue;\n        }\n        node.hitCount++;\n      }\n    }\n\n    // A cache for the raw nodes received from the traces / CDP.\n    const protocolNodeById = new Map<number, Protocol.Profiler.ProfileNode>();\n    for (let i = 0; i < nodes.length; ++i) {\n      const node = nodes[i];\n      protocolNodeById.set(node.id, node);\n    }\n\n    buildHitCountFromSamples(nodes, this.samples);\n    buildChildrenFromParents(nodes);\n    this.totalHitCount = nodes.reduce((acc, node) => acc + (node.hitCount || 0), 0);\n    const sampleTime = (this.profileEndTime - this.profileStartTime) / this.totalHitCount;\n    const root = nodes[0];\n    // If a node is filtered out, its samples are replaced with its parent,\n    // so we keep track of the which id to use in the samples data.\n    const idToUseForRemovedNode = new Map<number, number>([[root.id, root.id]]);\n    this.#idToParsedNode = new Map();\n\n    const resultRoot = new CPUProfileNode(root, sampleTime);\n    this.#idToParsedNode.set(root.id, resultRoot);\n    if (!root.children) {\n      throw new Error('Missing children for root');\n    }\n    const parentNodeStack = root.children.map(() => resultRoot);\n    const sourceNodeStack = root.children.map(id => protocolNodeById.get(id));\n    while (sourceNodeStack.length) {\n      let parentNode = parentNodeStack.pop();\n      const sourceNode = sourceNodeStack.pop();\n      if (!sourceNode || !parentNode) {\n        continue;\n      }\n      if (!sourceNode.children) {\n        sourceNode.children = [];\n      }\n      const targetNode = new CPUProfileNode(sourceNode, sampleTime);\n      parentNode.children.push(targetNode);\n      parentNode = targetNode;\n\n      idToUseForRemovedNode.set(sourceNode.id, parentNode.id);\n      parentNodeStack.push.apply(parentNodeStack, sourceNode.children.map(() => parentNode as CPUProfileNode));\n      sourceNodeStack.push.apply(sourceNodeStack, sourceNode.children.map(id => protocolNodeById.get(id)));\n      this.#idToParsedNode.set(sourceNode.id, targetNode);\n    }\n    if (this.samples) {\n      this.samples = this.samples.map(id => idToUseForRemovedNode.get(id) as number);\n    }\n    return resultRoot;\n  }\n\n  /**\n   * Sorts the samples array using the timestamps array (there is a one\n   * to one matching by index between the two).\n   */\n  private sortSamples(): void {\n    if (!this.timestamps || !this.samples) {\n      return;\n    }\n\n    const timestamps = this.timestamps;\n    const samples = this.samples;\n    const orderedIndices = timestamps.map((_x, index) => index);\n    orderedIndices.sort((a, b) => timestamps[a] - timestamps[b]);\n\n    this.timestamps = [];\n    this.samples = [];\n\n    for (let i = 0; i < orderedIndices.length; i++) {\n      const orderedIndex = orderedIndices[i];\n      this.timestamps.push(timestamps[orderedIndex]);\n      this.samples.push(samples[orderedIndex]);\n    }\n  }\n\n  /**\n   * Fills in timestamps and/or time deltas from legacy profiles where\n   * they could be missing.\n   */\n  private normalizeTimestamps(): void {\n    if (!this.samples) {\n      return;\n    }\n    let timestamps: number[] = this.timestamps;\n    if (!timestamps) {\n      // Support loading CPU profiles that are missing timestamps and\n      // timedeltas\n      const profileStartTime = this.profileStartTime;\n      const interval = (this.profileEndTime - profileStartTime) / this.samples.length;\n      // Add an extra timestamp used to calculate the last sample duration.\n      timestamps = new Array(this.samples.length + 1);\n      for (let i = 0; i < timestamps.length; ++i) {\n        timestamps[i] = profileStartTime + i * interval;\n      }\n      this.timestamps = timestamps;\n      return;\n    }\n\n    // Convert samples from micro to milliseconds\n    for (let i = 0; i < timestamps.length; ++i) {\n      timestamps[i] /= 1000;\n    }\n    if (this.samples.length === timestamps.length) {\n      // Add an extra timestamp used to calculate the last sample duration.\n      const lastTimestamp = timestamps.at(-1) || 0;\n      const averageIntervalTime = (lastTimestamp - timestamps[0]) / (timestamps.length - 1);\n      this.timestamps.push(lastTimestamp + averageIntervalTime);\n    }\n    this.profileStartTime = timestamps.at(0) || this.profileStartTime;\n    this.profileEndTime = timestamps.at(-1) || this.profileEndTime;\n  }\n\n  /**\n   * Some nodes do not refer to JS samples but to V8 system tasks, AKA\n   * \"meta\" nodes. This function extracts those nodes from the profile.\n   */\n  private extractMetaNodes(): void {\n    const topLevelNodes = this.profileHead.children;\n    for (let i = 0; i < topLevelNodes.length && !(this.gcNode && this.programNode && this.idleNode); i++) {\n      const node = topLevelNodes[i];\n      if (node.functionName === '(garbage collector)') {\n        this.gcNode = node;\n      } else if (node.functionName === '(program)') {\n        this.programNode = node;\n      } else if (node.functionName === '(idle)') {\n        this.idleNode = node;\n      }\n    }\n  }\n\n  private fixMissingSamples(): void {\n    // Sometimes the V8 sampler is not able to parse the JS stack and returns\n    // a (program) sample instead. The issue leads to call frames being split\n    // apart when they shouldn't.\n    // Here's a workaround for that. When there's a single (program) sample\n    // between two call stacks sharing the same bottom node, it is replaced\n    // with the preceeding sample.\n    const samples = this.samples;\n    if (!samples) {\n      return;\n    }\n    const samplesCount = samples.length;\n    if (!this.programNode || samplesCount < 3) {\n      return;\n    }\n    const idToNode = this.#idToParsedNode;\n    const programNodeId = this.programNode.id;\n    const gcNodeId = this.gcNode ? this.gcNode.id : -1;\n    const idleNodeId = this.idleNode ? this.idleNode.id : -1;\n    let prevNodeId: number = samples[0];\n    let nodeId: number = samples[1];\n    for (let sampleIndex = 1; sampleIndex < samplesCount - 1; sampleIndex++) {\n      const nextNodeId = samples[sampleIndex + 1];\n      const prevNode = idToNode.get(prevNodeId);\n      const nextNode = idToNode.get(nextNodeId);\n      if (prevNodeId === undefined || nextNodeId === undefined || !prevNode || !nextNode) {\n        console.error(`Unexpectedly found undefined nodes: ${prevNodeId} ${nextNodeId}`);\n        continue;\n      }\n      if (nodeId === programNodeId && !isSystemNode(prevNodeId) && !isSystemNode(nextNodeId) &&\n          bottomNode(prevNode) === bottomNode(nextNode)) {\n        samples[sampleIndex] = prevNodeId;\n      }\n      prevNodeId = nodeId;\n      nodeId = nextNodeId;\n    }\n    function bottomNode(node: ProfileNode): ProfileNode {\n      while (node.parent && node.parent.parent) {\n        node = node.parent;\n      }\n      return node;\n    }\n    function isSystemNode(nodeId: number): boolean {\n      return nodeId === programNodeId || nodeId === gcNodeId || nodeId === idleNodeId;\n    }\n  }\n\n  /**\n   * Traverses the call tree derived from the samples calling back when a call is opened\n   * and when it's closed\n   */\n  forEachFrame(\n      openFrameCallback: (depth: number, node: ProfileNode, timestamp: number) => void,\n      closeFrameCallback: (depth: number, node: ProfileNode, timestamp: number, dur: number, selfTime: number) => void,\n      startTime?: number, stopTime?: number): void {\n    if (!this.profileHead || !this.samples) {\n      return;\n    }\n\n    startTime = startTime || 0;\n    stopTime = stopTime || Infinity;\n    const samples = this.samples;\n    const timestamps = this.timestamps;\n    const idToNode = this.#idToParsedNode;\n    const gcNode = this.gcNode;\n    const samplesCount = samples.length;\n    const startIndex =\n        Platform.ArrayUtilities.lowerBound(timestamps, startTime, Platform.ArrayUtilities.DEFAULT_COMPARATOR);\n    let stackTop = 0;\n    const stackNodes: ProfileNode[] = [];\n    let prevId: number = this.profileHead.id;\n    let sampleTime;\n    let gcParentNode: ProfileNode|null = null;\n\n    // Extra slots for gc being put on top,\n    // and one at the bottom to allow safe stackTop-1 access.\n    const stackDepth = this.maxDepth + 3;\n    if (!this.#stackStartTimes) {\n      this.#stackStartTimes = new Array(stackDepth);\n    }\n    const stackStartTimes = this.#stackStartTimes;\n    if (!this.#stackChildrenDuration) {\n      this.#stackChildrenDuration = new Array(stackDepth);\n    }\n    const stackChildrenDuration = this.#stackChildrenDuration;\n\n    let node;\n    let sampleIndex;\n    for (sampleIndex = startIndex; sampleIndex < samplesCount; sampleIndex++) {\n      sampleTime = timestamps[sampleIndex];\n      if (sampleTime >= stopTime) {\n        break;\n      }\n      const id = samples[sampleIndex];\n      if (id === prevId) {\n        continue;\n      }\n      node = idToNode.get(id);\n      let prevNode: ProfileNode|null = idToNode.get(prevId) || null;\n      if (!prevNode) {\n        continue;\n      }\n\n      if (gcNode && node === gcNode) {\n        // GC samples have no stack, so we just put GC node on top of the last recorded sample.\n        gcParentNode = prevNode;\n        openFrameCallback(gcParentNode.depth + 1, gcNode, sampleTime);\n        stackStartTimes[++stackTop] = sampleTime;\n        stackChildrenDuration[stackTop] = 0;\n        prevId = id;\n        continue;\n      }\n      if (gcNode && prevNode === gcNode && gcParentNode) {\n        // end of GC frame\n        const start = stackStartTimes[stackTop];\n        const duration = sampleTime - start;\n        stackChildrenDuration[stackTop - 1] += duration;\n        closeFrameCallback(gcParentNode.depth + 1, gcNode, start, duration, duration - stackChildrenDuration[stackTop]);\n        --stackTop;\n        prevNode = gcParentNode;\n        prevId = prevNode.id;\n        gcParentNode = null;\n      }\n\n      // If the depth of this node is greater than the depth of the\n      // previous one, new calls happened in between and we need to open\n      // them, so track all of them in stackNodes.\n      while (node && node.depth > prevNode.depth) {\n        stackNodes.push(node);\n        node = node.parent;\n      }\n\n      // If `prevNode` differs from `node`, the current sample was taken\n      // after a change in the call stack, meaning that frames in the\n      // path of `prevNode` that differ from those in the path of `node`\n      // can be closed. So go down to the lowest common ancestor and\n      // close current intervals.\n      //\n      // For example:\n      //\n      // prevNode  node\n      //    |       |\n      //    v       v\n      // [---D--]\n      // [---C--][--E--]\n      // [------B------] <- LCA\n      // [------A------]\n      //\n      // Because a sample was taken with A, B and E in the stack, it\n      // means C and D finished and we can close them.\n      while (prevNode && prevNode !== node) {\n        const start = stackStartTimes[stackTop];\n        const duration = sampleTime - start;\n        stackChildrenDuration[stackTop - 1] += duration;\n        closeFrameCallback(prevNode.depth, prevNode, start, duration, duration - stackChildrenDuration[stackTop]);\n        --stackTop;\n        // Track calls to open after previous calls were closed\n        // In the example above, this would add E to the tracking stack.\n        if (node && node.depth === prevNode.depth) {\n          stackNodes.push(node);\n          node = node.parent;\n        }\n        prevNode = prevNode.parent;\n      }\n\n      // Go up the nodes stack and open new intervals.\n      while (stackNodes.length) {\n        const currentNode = stackNodes.pop();\n        if (!currentNode) {\n          break;\n        }\n        node = currentNode;\n        openFrameCallback(currentNode.depth, currentNode, sampleTime);\n        stackStartTimes[++stackTop] = sampleTime;\n        stackChildrenDuration[stackTop] = 0;\n      }\n\n      prevId = id;\n    }\n\n    // Close remaining intervals.\n    sampleTime = timestamps[sampleIndex] || this.profileEndTime;\n    if (node && gcParentNode && idToNode.get(prevId) === gcNode) {\n      const start = stackStartTimes[stackTop];\n      const duration = sampleTime - start;\n      stackChildrenDuration[stackTop - 1] += duration;\n      closeFrameCallback(gcParentNode.depth + 1, node, start, duration, duration - stackChildrenDuration[stackTop]);\n      --stackTop;\n      prevId = gcParentNode.id;\n    }\n    for (let node = idToNode.get(prevId); node && node.parent; node = node.parent) {\n      const start = stackStartTimes[stackTop];\n      const duration = sampleTime - start;\n      stackChildrenDuration[stackTop - 1] += duration;\n      closeFrameCallback(node.depth, node, start, duration, duration - stackChildrenDuration[stackTop]);\n      --stackTop;\n    }\n  }\n  /**\n   * Returns the node that corresponds to a given index of a sample.\n   */\n  nodeByIndex(index: number): ProfileNode|null {\n    return this.samples && this.#idToParsedNode.get(this.samples[index]) || null;\n  }\n  /**\n   * Returns the node that corresponds to a given node id.\n   */\n  nodeById(nodeId: number): ProfileNode|null {\n    return this.#idToParsedNode.get(nodeId) || null;\n  }\n\n  nodes(): ProfileNode[]|null {\n    if (!this.#idToParsedNode) {\n      return null;\n    }\n    return [...this.#idToParsedNode.values()];\n  }\n}\n\n// Format used by profiles coming from traces.\nexport type ExtendedProfileNode = Protocol.Profiler.ProfileNode&{parent?: number};\nexport type ExtendedProfile =\n    Protocol.Profiler.Profile&{nodes: Protocol.Profiler.ProfileNode[] | ExtendedProfileNode[], lines?: number[]};\n"]}