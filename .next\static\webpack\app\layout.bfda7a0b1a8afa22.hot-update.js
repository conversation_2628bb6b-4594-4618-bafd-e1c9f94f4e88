"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/performance.tsx":
/*!*****************************!*\
  !*** ./lib/performance.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: function() { return /* binding */ PerformanceMonitor; },\n/* harmony export */   createLazyComponent: function() { return /* binding */ createLazyComponent; },\n/* harmony export */   fetchWithCache: function() { return /* binding */ fetchWithCache; },\n/* harmony export */   getOptimizedImageUrl: function() { return /* binding */ getOptimizedImageUrl; },\n/* harmony export */   lazyImport: function() { return /* binding */ lazyImport; },\n/* harmony export */   performanceCache: function() { return /* binding */ performanceCache; },\n/* harmony export */   prefetchRoute: function() { return /* binding */ prefetchRoute; },\n/* harmony export */   preloadCriticalResources: function() { return /* binding */ preloadCriticalResources; },\n/* harmony export */   useDebounce: function() { return /* binding */ useDebounce; },\n/* harmony export */   useIntersectionObserver: function() { return /* binding */ useIntersectionObserver; },\n/* harmony export */   useMemoryMonitor: function() { return /* binding */ useMemoryMonitor; },\n/* harmony export */   useNetworkStatus: function() { return /* binding */ useNetworkStatus; },\n/* harmony export */   useThrottle: function() { return /* binding */ useThrottle; },\n/* harmony export */   useVirtualScroll: function() { return /* binding */ useVirtualScroll; },\n/* harmony export */   withPerformanceTracking: function() { return /* binding */ withPerformanceTracking; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ performanceCache,useDebounce,useThrottle,useIntersectionObserver,useVirtualScroll,PerformanceMonitor,getOptimizedImageUrl,lazyImport,useMemoryMonitor,useNetworkStatus,prefetchRoute,preloadCriticalResources,withPerformanceTracking,fetchWithCache,createLazyComponent auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n/**\n * Performance optimization utilities\n * Includes caching, memoization, debouncing, and performance monitoring\n */ \n// Cache implementation for client-side data\nclass PerformanceCache {\n    set(key, data) {\n        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5 * 60 * 1000;\n        // Remove oldest entries if cache is full\n        if (this.cache.size >= this.maxSize) {\n            const oldestKey = this.cache.keys().next().value;\n            this.cache.delete(oldestKey);\n        }\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            ttl\n        });\n    }\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return null;\n        }\n        // Check if entry has expired\n        if (Date.now() - entry.timestamp > entry.ttl) {\n            this.cache.delete(key);\n            return null;\n        }\n        return entry.data;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    delete(key) {\n        this.cache.delete(key);\n    }\n    size() {\n        return this.cache.size;\n    }\n    constructor(){\n        this.cache = new Map();\n        this.maxSize = 100;\n    }\n}\n// Global cache instance\nconst performanceCache = new PerformanceCache();\n// Debounce hook for search and input optimization\nfunction useDebounce(value, delay) {\n    _s();\n    const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            setDebouncedValue(value);\n        }, delay);\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        delay\n    ]);\n    return debouncedValue;\n}\n_s(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");\n// Throttle hook for scroll and resize events\nfunction useThrottle(value, limit) {\n    _s1();\n    const [throttledValue, setThrottledValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    const lastRan = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            if (Date.now() - lastRan.current >= limit) {\n                setThrottledValue(value);\n                lastRan.current = Date.now();\n            }\n        }, limit - (Date.now() - lastRan.current));\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        limit\n    ]);\n    return throttledValue;\n}\n_s1(useThrottle, \"imraTRBCOzuFTuCf1VZinwbuH38=\");\n// Intersection Observer hook for lazy loading\nfunction useIntersectionObserver(elementRef) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    _s2();\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            setIsIntersecting(entry.isIntersecting);\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\",\n            ...options\n        });\n        observer.observe(element);\n        return ()=>{\n            observer.unobserve(element);\n        };\n    }, [\n        elementRef,\n        options\n    ]);\n    return isIntersecting;\n}\n_s2(useIntersectionObserver, \"AlYpBYKL+Qmn8I+76+SVWywhgtA=\");\n// Virtual scrolling hook for large lists\nfunction useVirtualScroll(items, itemHeight, containerHeight) {\n    _s3();\n    const [scrollTop, setScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const visibleItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startIndex = Math.floor(scrollTop / itemHeight);\n        const endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + 1, items.length);\n        return {\n            startIndex,\n            endIndex,\n            items: items.slice(startIndex, endIndex),\n            totalHeight: items.length * itemHeight,\n            offsetY: startIndex * itemHeight\n        };\n    }, [\n        items,\n        itemHeight,\n        containerHeight,\n        scrollTop\n    ]);\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        setScrollTop(e.currentTarget.scrollTop);\n    }, []);\n    return {\n        ...visibleItems,\n        handleScroll\n    };\n}\n_s3(useVirtualScroll, \"fIaCs5RcEdd8G3v5HUUlzD5Ljjs=\");\n// Performance monitoring utilities\nclass PerformanceMonitor {\n    static mark(name) {\n        this.marks.set(name, performance.now());\n    }\n    static measure(name, startMark) {\n        const startTime = this.marks.get(startMark);\n        if (!startTime) {\n            console.warn('Start mark \"'.concat(startMark, '\" not found'));\n            return 0;\n        }\n        const duration = performance.now() - startTime;\n        if (true) {\n            console.log(\"Performance: \".concat(name, \" took \").concat(duration.toFixed(2), \"ms\"));\n        }\n        return duration;\n    }\n    static clearMarks() {\n        this.marks.clear();\n    }\n}\nPerformanceMonitor.marks = new Map();\n// Image optimization utilities\nfunction getOptimizedImageUrl(src, width, height) {\n    let quality = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 75;\n    // For Supabase Storage images\n    if (src.includes(\"supabase.co\")) {\n        const url = new URL(src);\n        url.searchParams.set(\"width\", width.toString());\n        if (height) {\n            url.searchParams.set(\"height\", height.toString());\n        }\n        url.searchParams.set(\"quality\", quality.toString());\n        url.searchParams.set(\"format\", \"webp\");\n        return url.toString();\n    }\n    // For Next.js Image optimization\n    return src;\n}\n// Bundle size optimization - dynamic imports\nconst lazyImport = (importFn)=>{\n    return importFn;\n};\n// Memory usage monitoring\nfunction useMemoryMonitor() {\n    _s4();\n    const [memoryInfo, setMemoryInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (\"memory\" in performance) {\n            const updateMemoryInfo = ()=>{\n                setMemoryInfo(performance.memory);\n            };\n            updateMemoryInfo();\n            const interval = setInterval(updateMemoryInfo, 5000) // Update every 5 seconds\n            ;\n            return ()=>clearInterval(interval);\n        }\n    }, []);\n    return memoryInfo;\n}\n_s4(useMemoryMonitor, \"8bLXzGuLq1jMZ1KKGlSuP2acmqs=\");\n// Network status monitoring\nfunction useNetworkStatus() {\n    _s5();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(typeof navigator !== \"undefined\" ? navigator.onLine : true);\n    const [connectionType, setConnectionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unknown\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Check connection type if available\n        if (\"connection\" in navigator) {\n            const connection = navigator.connection;\n            setConnectionType(connection.effectiveType || \"unknown\");\n            const handleConnectionChange = ()=>{\n                setConnectionType(connection.effectiveType || \"unknown\");\n            };\n            connection.addEventListener(\"change\", handleConnectionChange);\n            return ()=>{\n                window.removeEventListener(\"online\", handleOnline);\n                window.removeEventListener(\"offline\", handleOffline);\n                connection.removeEventListener(\"change\", handleConnectionChange);\n            };\n        }\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, []);\n    return {\n        isOnline,\n        connectionType\n    };\n}\n_s5(useNetworkStatus, \"N4s19/VPUor5/GslotaznQKGH6U=\");\n// Prefetch utilities for better navigation performance\nfunction prefetchRoute(href) {\n    if (true) {\n        const link = document.createElement(\"link\");\n        link.rel = \"prefetch\";\n        link.href = href;\n        document.head.appendChild(link);\n    }\n}\n// Critical resource preloading\nfunction preloadCriticalResources() {\n    if (true) {\n        // Preload critical fonts\n        const fontLink = document.createElement(\"link\");\n        fontLink.rel = \"preload\";\n        fontLink.href = \"/fonts/inter-var.woff2\";\n        fontLink.as = \"font\";\n        fontLink.type = \"font/woff2\";\n        fontLink.crossOrigin = \"anonymous\";\n        document.head.appendChild(fontLink);\n        // Preload critical images\n        const logoImg = new Image();\n        logoImg.src = \"/images/chia-logo.png\";\n    }\n}\n// Error boundary with performance tracking\nfunction withPerformanceTracking(Component, componentName) {\n    var _s = $RefreshSig$();\n    return _s(function PerformanceTrackedComponent(props) {\n        _s();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            PerformanceMonitor.mark(\"\".concat(componentName, \"-mount-start\"));\n            return ()=>{\n                PerformanceMonitor.measure(\"\".concat(componentName, \"-mount-duration\"), \"\".concat(componentName, \"-mount-start\"));\n            };\n        }, []);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n            lineNumber: 330,\n            columnNumber: 12\n        }, this);\n    }, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n}\n// Optimized data fetching with caching\nasync function fetchWithCache(key, fetchFn, ttl) {\n    // Check cache first\n    const cached = performanceCache.get(key);\n    if (cached) {\n        return cached;\n    }\n    // Fetch and cache\n    const data = await fetchFn();\n    performanceCache.set(key, data, ttl);\n    return data;\n}\n// Component lazy loading with error boundaries\nfunction createLazyComponent(importFn, fallback) {\n    const LazyComponent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(importFn);\n    return function LazyWrapper(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n            fallback: fallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"fallback\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 362,\n                columnNumber: 44\n            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 362,\n                columnNumber: 59\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this);\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/performance.tsx\n"));

/***/ })

});