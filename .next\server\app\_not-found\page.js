/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/error/ErrorBoundary.tsx */ \"(ssr)/./components/error/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/accessibility.tsx */ \"(ssr)/./lib/accessibility.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/mobile.tsx */ \"(ssr)/./lib/mobile.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/error/ErrorBoundary.tsx":
/*!********************************************!*\
  !*** ./components/error/ErrorBoundary.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_performance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/performance */ \"(ssr)/./lib/performance.tsx\");\n/* harmony import */ var _lib_accessibility__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/accessibility */ \"(ssr)/./lib/accessibility.tsx\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler auto */ \n\n\n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.retryCount = 0;\n        this.maxRetries = 3;\n        this.logError = (error, errorInfo)=>{\n            const errorData = {\n                message: error.message,\n                stack: error.stack,\n                componentStack: errorInfo.componentStack,\n                errorId: this.state.errorId,\n                timestamp: new Date().toISOString(),\n                userAgent: navigator.userAgent,\n                url: window.location.href,\n                level: this.props.level || \"component\"\n            };\n            // Log to console in development\n            if (true) {\n                console.error(\"Error Boundary caught an error:\", errorData);\n            }\n            // In production, send to monitoring service\n            if (false) {}\n        };\n        this.sendErrorToMonitoring = async (errorData)=>{\n            try {\n                // Example: Send to monitoring endpoint\n                await fetch(\"/api/errors\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(errorData)\n                });\n            } catch (err) {\n                console.error(\"Failed to send error to monitoring:\", err);\n            }\n        };\n        this.handleRetry = ()=>{\n            if (this.retryCount < this.maxRetries) {\n                this.retryCount++;\n                this.setState({\n                    hasError: false,\n                    error: null,\n                    errorInfo: null,\n                    errorId: \"\"\n                });\n                // Track retry attempt\n                _lib_performance__WEBPACK_IMPORTED_MODULE_4__.PerformanceMonitor.mark(\"error-boundary-retry\");\n                // Announce retry to screen readers\n                (0,_lib_accessibility__WEBPACK_IMPORTED_MODULE_5__.announceToScreenReader)(\"Reintentando cargar el contenido...\");\n            } else {\n                // Max retries reached, suggest page reload\n                (0,_lib_accessibility__WEBPACK_IMPORTED_MODULE_5__.announceToScreenReader)(\"Se ha alcanzado el m\\xe1ximo de reintentos. Por favor, recarga la p\\xe1gina.\", \"assertive\");\n            }\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.handleContactSupport = ()=>{\n            // Open support chat or contact form\n            window.location.href = \"/chat\";\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: \"\"\n        };\n    }\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error,\n            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Log error for monitoring\n        this.logError(error, errorInfo);\n        // Update state with error info\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Call custom error handler\n        this.props.onError?.(error, errorInfo);\n        // Announce error to screen readers\n        (0,_lib_accessibility__WEBPACK_IMPORTED_MODULE_5__.announceToScreenReader)(\"Ha ocurrido un error en la aplicaci\\xf3n. Por favor, intenta recargar la p\\xe1gina.\", \"assertive\");\n        // Track performance impact\n        _lib_performance__WEBPACK_IMPORTED_MODULE_4__.PerformanceMonitor.mark(\"error-boundary-triggered\");\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default error UI based on level\n            return this.renderErrorUI();\n        }\n        return this.props.children;\n    }\n    renderErrorUI() {\n        const { level = \"component\", showDetails = false } = this.props;\n        const { error, errorInfo, errorId } = this.state;\n        // Critical errors get full page treatment\n        if (level === \"critical\" || level === \"page\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-xl text-gray-900\",\n                                    children: \"Algo sali\\xf3 mal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Ha ocurrido un error inesperado. Nuestro equipo ha sido notificado autom\\xe1ticamente.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 bg-gray-100 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"ID del Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        errorId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-2\",\n                                    children: [\n                                        this.retryCount < this.maxRetries ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: this.handleRetry,\n                                            className: \"flex-1\",\n                                            variant: \"default\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Reintentar (\",\n                                                this.maxRetries - this.retryCount,\n                                                \" intentos restantes)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: this.handleReload,\n                                            className: \"flex-1\",\n                                            variant: \"default\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Recargar p\\xe1gina\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: this.handleGoHome,\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Ir al inicio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleContactSupport,\n                                    variant: \"ghost\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Contactar soporte\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                showDetails && \"development\" === \"development\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                            className: \"cursor-pointer text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Detalles t\\xe9cnicos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs bg-gray-100 p-3 rounded-md overflow-auto max-h-40\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Error:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        error?.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Stack:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"whitespace-pre-wrap mt-1\",\n                                                            children: error?.stack\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Component Stack:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"whitespace-pre-wrap mt-1\",\n                                                            children: errorInfo.componentStack\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this);\n        }\n        // Component-level errors get inline treatment\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border border-red-200 bg-red-50 rounded-lg p-4 my-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-red-800 mb-1\",\n                                children: \"Error en el componente\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-700 mb-3\",\n                                children: \"Este componente no se pudo cargar correctamente.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: this.retryCount < this.maxRetries ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleRetry,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"border-red-300 text-red-700 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Reintentar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleReload,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"border-red-300 text-red-700 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Recargar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            showDetails && \"development\" === \"development\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-xs text-red-600\",\n                                        children: \"Ver detalles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-600 mt-1 font-mono\",\n                                        children: error?.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n}\n// HOC for wrapping components with error boundary\nfunction withErrorBoundary(Component, errorBoundaryProps) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            ...errorBoundaryProps,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\error\\\\ErrorBoundary.tsx\",\n            lineNumber: 314,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Hook for error reporting\nfunction useErrorHandler() {\n    const reportError = (error, context)=>{\n        // Create a synthetic error boundary trigger\n        throw new Error(`${context ? `[${context}] ` : \"\"}${error.message}`);\n    };\n    return {\n        reportError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/error/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-chia-blue-600 text-white hover:bg-chia-blue-700\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600\",\n            outline: \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n            secondary: \"bg-chia-green-100 text-chia-green-800 hover:bg-chia-green-200\",\n            ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n            link: \"text-chia-blue-600 underline-offset-4 hover:underline\",\n            success: \"bg-chia-green-600 text-white hover:bg-chia-green-700\",\n            warning: \"bg-yellow-500 text-white hover:bg-yellow-600\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-gray-500\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/accessibility.tsx":
/*!*******************************!*\
  !*** ./lib/accessibility.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink),\n/* harmony export */   announceToScreenReader: () => (/* binding */ announceToScreenReader),\n/* harmony export */   calculateContrastRatio: () => (/* binding */ calculateContrastRatio),\n/* harmony export */   getButtonProps: () => (/* binding */ getButtonProps),\n/* harmony export */   getFormFieldProps: () => (/* binding */ getFormFieldProps),\n/* harmony export */   getLinkProps: () => (/* binding */ getLinkProps),\n/* harmony export */   getModalProps: () => (/* binding */ getModalProps),\n/* harmony export */   getTableProps: () => (/* binding */ getTableProps),\n/* harmony export */   meetsContrastRequirement: () => (/* binding */ meetsContrastRequirement),\n/* harmony export */   useAriaLiveRegion: () => (/* binding */ useAriaLiveRegion),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement),\n/* harmony export */   useHighContrast: () => (/* binding */ useHighContrast),\n/* harmony export */   useKeyboardNavigation: () => (/* binding */ useKeyboardNavigation),\n/* harmony export */   useReducedMotion: () => (/* binding */ useReducedMotion)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ calculateContrastRatio,meetsContrastRequirement,useKeyboardNavigation,announceToScreenReader,useFocusManagement,SkipLink,useAriaLiveRegion,useReducedMotion,useHighContrast,getFormFieldProps,getTableProps,getModalProps,getButtonProps,getLinkProps auto */ \n/**\n * Accessibility utilities for WCAG 2.1 AA compliance\n * Includes keyboard navigation, screen reader support, and color contrast utilities\n */ \n// WCAG 2.1 AA color contrast ratios\nconst CONTRAST_RATIOS = {\n    AA_NORMAL: 4.5,\n    AA_LARGE: 3,\n    AAA_NORMAL: 7,\n    AAA_LARGE: 4.5\n};\n// Color contrast calculation\nfunction calculateContrastRatio(color1, color2) {\n    const getLuminance = (color)=>{\n        // Convert hex to RGB\n        const hex = color.replace(\"#\", \"\");\n        const r = parseInt(hex.substr(0, 2), 16) / 255;\n        const g = parseInt(hex.substr(2, 2), 16) / 255;\n        const b = parseInt(hex.substr(4, 2), 16) / 255;\n        // Calculate relative luminance\n        const getRGB = (c)=>{\n            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n        };\n        return 0.2126 * getRGB(r) + 0.7152 * getRGB(g) + 0.0722 * getRGB(b);\n    };\n    const lum1 = getLuminance(color1);\n    const lum2 = getLuminance(color2);\n    const brightest = Math.max(lum1, lum2);\n    const darkest = Math.min(lum1, lum2);\n    return (brightest + 0.05) / (darkest + 0.05);\n}\n// Check if color combination meets WCAG standards\nfunction meetsContrastRequirement(foreground, background, level = \"AA\", isLargeText = false) {\n    const ratio = calculateContrastRatio(foreground, background);\n    if (level === \"AA\") {\n        return ratio >= (isLargeText ? CONTRAST_RATIOS.AA_LARGE : CONTRAST_RATIOS.AA_NORMAL);\n    } else {\n        return ratio >= (isLargeText ? CONTRAST_RATIOS.AAA_LARGE : CONTRAST_RATIOS.AAA_NORMAL);\n    }\n}\n// Keyboard navigation hook\nfunction useKeyboardNavigation(containerRef, options = {}) {\n    const { focusableSelector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])', loop = true, autoFocus = false } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const container = containerRef.current;\n        if (!container) return;\n        const focusableElements = container.querySelectorAll(focusableSelector);\n        if (autoFocus && focusableElements.length > 0) {\n            focusableElements[0].focus();\n        }\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Tab\") {\n                const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);\n                if (e.shiftKey) {\n                    // Shift + Tab (previous)\n                    if (currentIndex <= 0) {\n                        if (loop) {\n                            e.preventDefault();\n                            focusableElements[focusableElements.length - 1].focus();\n                        }\n                    }\n                } else {\n                    // Tab (next)\n                    if (currentIndex >= focusableElements.length - 1) {\n                        if (loop) {\n                            e.preventDefault();\n                            focusableElements[0].focus();\n                        }\n                    }\n                }\n            }\n            // Arrow key navigation for lists and grids\n            if ([\n                \"ArrowUp\",\n                \"ArrowDown\",\n                \"ArrowLeft\",\n                \"ArrowRight\"\n            ].includes(e.key)) {\n                const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);\n                let nextIndex = currentIndex;\n                switch(e.key){\n                    case \"ArrowDown\":\n                    case \"ArrowRight\":\n                        nextIndex = currentIndex + 1;\n                        break;\n                    case \"ArrowUp\":\n                    case \"ArrowLeft\":\n                        nextIndex = currentIndex - 1;\n                        break;\n                }\n                if (nextIndex >= 0 && nextIndex < focusableElements.length) {\n                    e.preventDefault();\n                    focusableElements[nextIndex].focus();\n                } else if (loop) {\n                    e.preventDefault();\n                    if (nextIndex < 0) {\n                        focusableElements[focusableElements.length - 1].focus();\n                    } else {\n                        focusableElements[0].focus();\n                    }\n                }\n            }\n            // Home/End navigation\n            if (e.key === \"Home\") {\n                e.preventDefault();\n                focusableElements[0].focus();\n            } else if (e.key === \"End\") {\n                e.preventDefault();\n                focusableElements[focusableElements.length - 1].focus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>container.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        containerRef,\n        focusableSelector,\n        loop,\n        autoFocus\n    ]);\n}\n// Screen reader announcements\nfunction announceToScreenReader(message, priority = \"polite\") {\n    const announcement = document.createElement(\"div\");\n    announcement.setAttribute(\"aria-live\", priority);\n    announcement.setAttribute(\"aria-atomic\", \"true\");\n    announcement.className = \"sr-only\";\n    announcement.textContent = message;\n    document.body.appendChild(announcement);\n    // Remove after announcement\n    setTimeout(()=>{\n        document.body.removeChild(announcement);\n    }, 1000);\n}\n// Focus management hook\nfunction useFocusManagement() {\n    const previousFocusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const saveFocus = ()=>{\n        previousFocusRef.current = document.activeElement;\n    };\n    const restoreFocus = ()=>{\n        if (previousFocusRef.current) {\n            previousFocusRef.current.focus();\n        }\n    };\n    const trapFocus = (containerRef)=>{\n        const container = containerRef.current;\n        if (!container) return;\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Tab\") {\n                if (e.shiftKey) {\n                    if (document.activeElement === firstElement) {\n                        e.preventDefault();\n                        lastElement.focus();\n                    }\n                } else {\n                    if (document.activeElement === lastElement) {\n                        e.preventDefault();\n                        firstElement.focus();\n                    }\n                }\n            }\n            if (e.key === \"Escape\") {\n                restoreFocus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        firstElement?.focus();\n        return ()=>{\n            container.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    };\n    return {\n        saveFocus,\n        restoreFocus,\n        trapFocus\n    };\n}\n// Skip link component for keyboard navigation\nfunction SkipLink({ href, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-md focus:shadow-lg\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\accessibility.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n// ARIA live region hook for dynamic content updates\nfunction useAriaLiveRegion() {\n    const [liveRegion, setLiveRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const region = document.createElement(\"div\");\n        region.setAttribute(\"aria-live\", \"polite\");\n        region.setAttribute(\"aria-atomic\", \"true\");\n        region.className = \"sr-only\";\n        document.body.appendChild(region);\n        setLiveRegion(region);\n        return ()=>{\n            if (document.body.contains(region)) {\n                document.body.removeChild(region);\n            }\n        };\n    }, []);\n    const announce = (message)=>{\n        if (liveRegion) {\n            liveRegion.textContent = message;\n        }\n    };\n    return announce;\n}\n// Reduced motion detection\nfunction useReducedMotion() {\n    const [prefersReducedMotion, setPrefersReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n        setPrefersReducedMotion(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setPrefersReducedMotion(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, []);\n    return prefersReducedMotion;\n}\n// High contrast mode detection\nfunction useHighContrast() {\n    const [prefersHighContrast, setPrefersHighContrast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mediaQuery = window.matchMedia(\"(prefers-contrast: high)\");\n        setPrefersHighContrast(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setPrefersHighContrast(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, []);\n    return prefersHighContrast;\n}\n// Form accessibility utilities\nfunction getFormFieldProps(id, label, error, description, required) {\n    const describedBy = [];\n    if (description) describedBy.push(`${id}-description`);\n    if (error) describedBy.push(`${id}-error`);\n    return {\n        field: {\n            id,\n            \"aria-describedby\": describedBy.length > 0 ? describedBy.join(\" \") : undefined,\n            \"aria-invalid\": error ? \"true\" : undefined,\n            \"aria-required\": required ? \"true\" : undefined\n        },\n        label: {\n            htmlFor: id,\n            children: label + (required ? \" *\" : \"\")\n        },\n        description: description ? {\n            id: `${id}-description`,\n            children: description\n        } : null,\n        error: error ? {\n            id: `${id}-error`,\n            role: \"alert\",\n            \"aria-live\": \"polite\",\n            children: error\n        } : null\n    };\n}\n// Table accessibility utilities\nfunction getTableProps(caption, summary) {\n    return {\n        table: {\n            role: \"table\",\n            \"aria-label\": caption\n        },\n        caption: {\n            children: caption,\n            className: \"sr-only\"\n        },\n        summary: summary ? {\n            children: summary,\n            className: \"sr-only\"\n        } : null\n    };\n}\n// Modal accessibility utilities\nfunction getModalProps(titleId, descriptionId, onClose) {\n    return {\n        modal: {\n            role: \"dialog\",\n            \"aria-modal\": \"true\",\n            \"aria-labelledby\": titleId,\n            \"aria-describedby\": descriptionId,\n            onKeyDown: (e)=>{\n                if (e.key === \"Escape\" && onClose) {\n                    onClose();\n                }\n            }\n        },\n        title: {\n            id: titleId\n        },\n        description: descriptionId ? {\n            id: descriptionId\n        } : null,\n        closeButton: {\n            \"aria-label\": \"Cerrar modal\",\n            onClick: onClose\n        }\n    };\n}\n// Button accessibility utilities\nfunction getButtonProps(label, options = {}) {\n    const { expanded, controls, pressed, disabled } = options;\n    return {\n        \"aria-label\": label,\n        \"aria-expanded\": expanded !== undefined ? expanded : undefined,\n        \"aria-controls\": controls,\n        \"aria-pressed\": pressed !== undefined ? pressed : undefined,\n        disabled,\n        type: \"button\"\n    };\n}\n// Link accessibility utilities\nfunction getLinkProps(text, options = {}) {\n    const { external, download, current } = options;\n    return {\n        \"aria-label\": text + (external ? \" (abre en nueva ventana)\" : \"\") + (download ? \" (descarga archivo)\" : \"\"),\n        \"aria-current\": current ? \"page\" : undefined,\n        target: external ? \"_blank\" : undefined,\n        rel: external ? \"noopener noreferrer\" : undefined,\n        download: download ? true : undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/accessibility.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/mobile.tsx":
/*!************************!*\
  !*** ./lib/mobile.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   MobileOptimized: () => (/* binding */ MobileOptimized),\n/* harmony export */   getMobileImageSizes: () => (/* binding */ getMobileImageSizes),\n/* harmony export */   getResponsiveGridCols: () => (/* binding */ getResponsiveGridCols),\n/* harmony export */   useDeviceDetection: () => (/* binding */ useDeviceDetection),\n/* harmony export */   useMobilePerformance: () => (/* binding */ useMobilePerformance),\n/* harmony export */   useMobileScroll: () => (/* binding */ useMobileScroll),\n/* harmony export */   useResponsiveValue: () => (/* binding */ useResponsiveValue),\n/* harmony export */   useSafeAreaInsets: () => (/* binding */ useSafeAreaInsets),\n/* harmony export */   useTouchGestures: () => (/* binding */ useTouchGestures),\n/* harmony export */   useViewportHeight: () => (/* binding */ useViewportHeight)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BREAKPOINTS,useDeviceDetection,useResponsiveValue,useTouchGestures,useViewportHeight,useSafeAreaInsets,useMobileScroll,useMobilePerformance,MobileOptimized,getResponsiveGridCols,getMobileImageSizes auto */ \n/**\n * Mobile optimization utilities\n * Includes responsive design helpers, touch interactions, and mobile-specific optimizations\n */ \n// Breakpoint definitions\nconst BREAKPOINTS = {\n    xs: 0,\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    \"2xl\": 1536\n};\n// Device detection\nfunction useDeviceDetection() {\n    const [deviceInfo, setDeviceInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isMobile: false,\n        isTablet: false,\n        isDesktop: false,\n        isTouchDevice: false,\n        orientation: \"portrait\",\n        screenSize: \"md\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDeviceInfo = ()=>{\n            const width = window.innerWidth;\n            const height = window.innerHeight;\n            const isTouchDevice = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n            // Determine screen size\n            let screenSize = \"xs\";\n            for (const [breakpoint, minWidth] of Object.entries(BREAKPOINTS).reverse()){\n                if (width >= minWidth) {\n                    screenSize = breakpoint;\n                    break;\n                }\n            }\n            // Device type detection\n            const isMobile = width < BREAKPOINTS.md;\n            const isTablet = width >= BREAKPOINTS.md && width < BREAKPOINTS.lg && isTouchDevice;\n            const isDesktop = width >= BREAKPOINTS.lg;\n            // Orientation\n            const orientation = width > height ? \"landscape\" : \"portrait\";\n            setDeviceInfo({\n                isMobile,\n                isTablet,\n                isDesktop,\n                isTouchDevice,\n                orientation,\n                screenSize\n            });\n        };\n        updateDeviceInfo();\n        window.addEventListener(\"resize\", updateDeviceInfo);\n        window.addEventListener(\"orientationchange\", updateDeviceInfo);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateDeviceInfo);\n            window.removeEventListener(\"orientationchange\", updateDeviceInfo);\n        };\n    }, []);\n    return deviceInfo;\n}\n// Responsive value hook\nfunction useResponsiveValue(values, defaultValue) {\n    const { screenSize } = useDeviceDetection();\n    // Find the appropriate value for current screen size\n    const breakpointOrder = [\n        \"2xl\",\n        \"xl\",\n        \"lg\",\n        \"md\",\n        \"sm\",\n        \"xs\"\n    ];\n    const currentIndex = breakpointOrder.indexOf(screenSize);\n    for(let i = currentIndex; i < breakpointOrder.length; i++){\n        const breakpoint = breakpointOrder[i];\n        if (values[breakpoint] !== undefined) {\n            return values[breakpoint];\n        }\n    }\n    return defaultValue;\n}\n// Touch gesture detection\nfunction useTouchGestures(elementRef) {\n    const [gestures, setGestures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isSwipeLeft: false,\n        isSwipeRight: false,\n        isSwipeUp: false,\n        isSwipeDown: false,\n        isPinching: false,\n        scale: 1\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        let startX = 0;\n        let startY = 0;\n        let startDistance = 0;\n        const handleTouchStart = (e)=>{\n            if (e.touches.length === 1) {\n                startX = e.touches[0].clientX;\n                startY = e.touches[0].clientY;\n            } else if (e.touches.length === 2) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                startDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n            }\n        };\n        const handleTouchMove = (e)=>{\n            if (e.touches.length === 2) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                const currentDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                const scale = currentDistance / startDistance;\n                setGestures((prev)=>({\n                        ...prev,\n                        isPinching: true,\n                        scale\n                    }));\n            }\n        };\n        const handleTouchEnd = (e)=>{\n            if (e.changedTouches.length === 1) {\n                const endX = e.changedTouches[0].clientX;\n                const endY = e.changedTouches[0].clientY;\n                const deltaX = endX - startX;\n                const deltaY = endY - startY;\n                const minSwipeDistance = 50;\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {\n                    if (deltaX > 0) {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeRight: true\n                            }));\n                    } else {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeLeft: true\n                            }));\n                    }\n                } else if (Math.abs(deltaY) > minSwipeDistance) {\n                    if (deltaY > 0) {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeDown: true\n                            }));\n                    } else {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeUp: true\n                            }));\n                    }\n                }\n                // Reset gestures after a short delay\n                setTimeout(()=>{\n                    setGestures({\n                        isSwipeLeft: false,\n                        isSwipeRight: false,\n                        isSwipeUp: false,\n                        isSwipeDown: false,\n                        isPinching: false,\n                        scale: 1\n                    });\n                }, 100);\n            }\n        };\n        element.addEventListener(\"touchstart\", handleTouchStart, {\n            passive: true\n        });\n        element.addEventListener(\"touchmove\", handleTouchMove, {\n            passive: true\n        });\n        element.addEventListener(\"touchend\", handleTouchEnd, {\n            passive: true\n        });\n        return ()=>{\n            element.removeEventListener(\"touchstart\", handleTouchStart);\n            element.removeEventListener(\"touchmove\", handleTouchMove);\n            element.removeEventListener(\"touchend\", handleTouchEnd);\n        };\n    }, [\n        elementRef\n    ]);\n    return gestures;\n}\n// Viewport height fix for mobile browsers\nfunction useViewportHeight() {\n    const [vh, setVh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateVh = ()=>{\n            const vh = window.innerHeight * 0.01;\n            setVh(vh);\n            document.documentElement.style.setProperty(\"--vh\", `${vh}px`);\n        };\n        updateVh();\n        window.addEventListener(\"resize\", updateVh);\n        window.addEventListener(\"orientationchange\", updateVh);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateVh);\n            window.removeEventListener(\"orientationchange\", updateVh);\n        };\n    }, []);\n    return vh;\n}\n// Safe area insets for devices with notches\nfunction useSafeAreaInsets() {\n    const [insets, setInsets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateInsets = ()=>{\n            const computedStyle = getComputedStyle(document.documentElement);\n            setInsets({\n                top: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-top)\") || \"0\"),\n                right: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-right)\") || \"0\"),\n                bottom: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-bottom)\") || \"0\"),\n                left: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-left)\") || \"0\")\n            });\n        };\n        updateInsets();\n        window.addEventListener(\"orientationchange\", updateInsets);\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", updateInsets);\n        };\n    }, []);\n    return insets;\n}\n// Mobile-optimized scroll behavior\nfunction useMobileScroll(options = {}) {\n    const { threshold = 10, onScrollUp, onScrollDown } = options;\n    const [scrollDirection, setScrollDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let lastScrollY = window.scrollY;\n        let ticking = false;\n        const updateScrollDirection = ()=>{\n            const currentScrollY = window.scrollY;\n            const difference = Math.abs(currentScrollY - lastScrollY);\n            if (difference > threshold) {\n                const direction = currentScrollY > lastScrollY ? \"down\" : \"up\";\n                setScrollDirection(direction);\n                setScrollY(currentScrollY);\n                if (direction === \"up\" && onScrollUp) {\n                    onScrollUp();\n                } else if (direction === \"down\" && onScrollDown) {\n                    onScrollDown();\n                }\n                lastScrollY = currentScrollY;\n            }\n            ticking = false;\n        };\n        const onScroll = ()=>{\n            if (!ticking) {\n                requestAnimationFrame(updateScrollDirection);\n                ticking = true;\n            }\n        };\n        window.addEventListener(\"scroll\", onScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", onScroll);\n    }, [\n        threshold,\n        onScrollUp,\n        onScrollDown\n    ]);\n    return {\n        scrollDirection,\n        scrollY\n    };\n}\n// Performance optimization for mobile\nfunction useMobilePerformance() {\n    const [isLowEndDevice, setIsLowEndDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Detect low-end devices\n        const checkDeviceCapabilities = ()=>{\n            const memory = navigator.deviceMemory;\n            const cores = navigator.hardwareConcurrency;\n            const connection = navigator.connection;\n            let isLowEnd = false;\n            // Memory check (less than 4GB)\n            if (memory && memory < 4) {\n                isLowEnd = true;\n            }\n            // CPU cores check (less than 4 cores)\n            if (cores && cores < 4) {\n                isLowEnd = true;\n            }\n            // Network check (slow connection)\n            if (connection && (connection.effectiveType === \"slow-2g\" || connection.effectiveType === \"2g\")) {\n                isLowEnd = true;\n            }\n            setIsLowEndDevice(isLowEnd);\n        };\n        checkDeviceCapabilities();\n    }, []);\n    return {\n        isLowEndDevice\n    };\n}\n// Mobile-friendly component wrapper\nfunction MobileOptimized({ children, fallback, enableTouchOptimizations = true }) {\n    const { isMobile, isTouchDevice } = useDeviceDetection();\n    const { isLowEndDevice } = useMobilePerformance();\n    useViewportHeight();\n    // Apply mobile-specific optimizations\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && enableTouchOptimizations) {\n            // Disable hover effects on touch devices\n            document.body.classList.add(\"touch-device\");\n            // Optimize scrolling\n            document.body.style.webkitOverflowScrolling = \"touch\";\n            // Prevent zoom on input focus\n            const viewport = document.querySelector(\"meta[name=viewport]\");\n            if (viewport) {\n                viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");\n            }\n        }\n        return ()=>{\n            if (isMobile) {\n                document.body.classList.remove(\"touch-device\");\n            }\n        };\n    }, [\n        isMobile,\n        enableTouchOptimizations\n    ]);\n    // Show fallback for low-end devices if provided\n    if (isLowEndDevice && fallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Responsive grid utility\nfunction getResponsiveGridCols(screenSize) {\n    const gridCols = {\n        xs: 1,\n        sm: 2,\n        md: 2,\n        lg: 3,\n        xl: 4,\n        \"2xl\": 4\n    };\n    return gridCols[screenSize] || 1;\n}\n// Mobile-optimized image loading\nfunction getMobileImageSizes(screenSize) {\n    const sizes = {\n        xs: \"100vw\",\n        sm: \"50vw\",\n        md: \"33vw\",\n        lg: \"25vw\",\n        xl: \"20vw\",\n        \"2xl\": \"16vw\"\n    };\n    return sizes[screenSize] || \"100vw\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/mobile.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/performance.tsx":
/*!*****************************!*\
  !*** ./lib/performance.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),\n/* harmony export */   createLazyComponent: () => (/* binding */ createLazyComponent),\n/* harmony export */   fetchWithCache: () => (/* binding */ fetchWithCache),\n/* harmony export */   getOptimizedImageUrl: () => (/* binding */ getOptimizedImageUrl),\n/* harmony export */   lazyImport: () => (/* binding */ lazyImport),\n/* harmony export */   performanceCache: () => (/* binding */ performanceCache),\n/* harmony export */   prefetchRoute: () => (/* binding */ prefetchRoute),\n/* harmony export */   preloadCriticalResources: () => (/* binding */ preloadCriticalResources),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useMemoryMonitor: () => (/* binding */ useMemoryMonitor),\n/* harmony export */   useNetworkStatus: () => (/* binding */ useNetworkStatus),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useVirtualScroll: () => (/* binding */ useVirtualScroll),\n/* harmony export */   withPerformanceTracking: () => (/* binding */ withPerformanceTracking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ performanceCache,useDebounce,useThrottle,useIntersectionObserver,useVirtualScroll,PerformanceMonitor,getOptimizedImageUrl,lazyImport,useMemoryMonitor,useNetworkStatus,prefetchRoute,preloadCriticalResources,withPerformanceTracking,fetchWithCache,createLazyComponent auto */ \n/**\n * Performance optimization utilities\n * Includes caching, memoization, debouncing, and performance monitoring\n */ \n// Cache implementation for client-side data\nclass PerformanceCache {\n    set(key, data, ttl = 5 * 60 * 1000) {\n        // Remove oldest entries if cache is full\n        if (this.cache.size >= this.maxSize) {\n            const oldestKey = this.cache.keys().next().value;\n            this.cache.delete(oldestKey);\n        }\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            ttl\n        });\n    }\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return null;\n        }\n        // Check if entry has expired\n        if (Date.now() - entry.timestamp > entry.ttl) {\n            this.cache.delete(key);\n            return null;\n        }\n        return entry.data;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    delete(key) {\n        this.cache.delete(key);\n    }\n    size() {\n        return this.cache.size;\n    }\n    constructor(){\n        this.cache = new Map();\n        this.maxSize = 100;\n    }\n}\n// Global cache instance\nconst performanceCache = new PerformanceCache();\n// Debounce hook for search and input optimization\nfunction useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            setDebouncedValue(value);\n        }, delay);\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        delay\n    ]);\n    return debouncedValue;\n}\n// Throttle hook for scroll and resize events\nfunction useThrottle(value, limit) {\n    const [throttledValue, setThrottledValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    const lastRan = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            if (Date.now() - lastRan.current >= limit) {\n                setThrottledValue(value);\n                lastRan.current = Date.now();\n            }\n        }, limit - (Date.now() - lastRan.current));\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        limit\n    ]);\n    return throttledValue;\n}\n// Intersection Observer hook for lazy loading\nfunction useIntersectionObserver(elementRef, options = {}) {\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        const observer = new IntersectionObserver(([entry])=>{\n            setIsIntersecting(entry.isIntersecting);\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\",\n            ...options\n        });\n        observer.observe(element);\n        return ()=>{\n            observer.unobserve(element);\n        };\n    }, [\n        elementRef,\n        options\n    ]);\n    return isIntersecting;\n}\n// Virtual scrolling hook for large lists\nfunction useVirtualScroll(items, itemHeight, containerHeight) {\n    const [scrollTop, setScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const visibleItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startIndex = Math.floor(scrollTop / itemHeight);\n        const endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + 1, items.length);\n        return {\n            startIndex,\n            endIndex,\n            items: items.slice(startIndex, endIndex),\n            totalHeight: items.length * itemHeight,\n            offsetY: startIndex * itemHeight\n        };\n    }, [\n        items,\n        itemHeight,\n        containerHeight,\n        scrollTop\n    ]);\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        setScrollTop(e.currentTarget.scrollTop);\n    }, []);\n    return {\n        ...visibleItems,\n        handleScroll\n    };\n}\n// Performance monitoring utilities\nclass PerformanceMonitor {\n    static{\n        this.marks = new Map();\n    }\n    static mark(name) {\n        this.marks.set(name, performance.now());\n    }\n    static measure(name, startMark) {\n        const startTime = this.marks.get(startMark);\n        if (!startTime) {\n            console.warn(`Start mark \"${startMark}\" not found`);\n            return 0;\n        }\n        const duration = performance.now() - startTime;\n        if (true) {\n            console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);\n        }\n        return duration;\n    }\n    static clearMarks() {\n        this.marks.clear();\n    }\n}\n// Image optimization utilities\nfunction getOptimizedImageUrl(src, width, height, quality = 75) {\n    // For Supabase Storage images\n    if (src.includes(\"supabase.co\")) {\n        const url = new URL(src);\n        url.searchParams.set(\"width\", width.toString());\n        if (height) {\n            url.searchParams.set(\"height\", height.toString());\n        }\n        url.searchParams.set(\"quality\", quality.toString());\n        url.searchParams.set(\"format\", \"webp\");\n        return url.toString();\n    }\n    // For Next.js Image optimization\n    return src;\n}\n// Bundle size optimization - dynamic imports\nconst lazyImport = (importFn)=>{\n    return importFn;\n};\n// Memory usage monitoring\nfunction useMemoryMonitor() {\n    const [memoryInfo, setMemoryInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (\"memory\" in performance) {\n            const updateMemoryInfo = ()=>{\n                setMemoryInfo(performance.memory);\n            };\n            updateMemoryInfo();\n            const interval = setInterval(updateMemoryInfo, 5000) // Update every 5 seconds\n            ;\n            return ()=>clearInterval(interval);\n        }\n    }, []);\n    return memoryInfo;\n}\n// Network status monitoring\nfunction useNetworkStatus() {\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(typeof navigator !== \"undefined\" ? navigator.onLine : true);\n    const [connectionType, setConnectionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unknown\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Check connection type if available\n        if (\"connection\" in navigator) {\n            const connection = navigator.connection;\n            setConnectionType(connection.effectiveType || \"unknown\");\n            const handleConnectionChange = ()=>{\n                setConnectionType(connection.effectiveType || \"unknown\");\n            };\n            connection.addEventListener(\"change\", handleConnectionChange);\n            return ()=>{\n                window.removeEventListener(\"online\", handleOnline);\n                window.removeEventListener(\"offline\", handleOffline);\n                connection.removeEventListener(\"change\", handleConnectionChange);\n            };\n        }\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, []);\n    return {\n        isOnline,\n        connectionType\n    };\n}\n// Prefetch utilities for better navigation performance\nfunction prefetchRoute(href) {\n    if (false) {}\n}\n// Critical resource preloading\nfunction preloadCriticalResources() {\n    if (false) {}\n}\n// Error boundary with performance tracking\nfunction withPerformanceTracking(Component, componentName) {\n    return function PerformanceTrackedComponent(props) {\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            PerformanceMonitor.mark(`${componentName}-mount-start`);\n            return ()=>{\n                PerformanceMonitor.measure(`${componentName}-mount-duration`, `${componentName}-mount-start`);\n            };\n        }, []);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n            lineNumber: 330,\n            columnNumber: 12\n        }, this);\n    };\n}\n// Optimized data fetching with caching\nasync function fetchWithCache(key, fetchFn, ttl) {\n    // Check cache first\n    const cached = performanceCache.get(key);\n    if (cached) {\n        return cached;\n    }\n    // Fetch and cache\n    const data = await fetchFn();\n    performanceCache.set(key, data, ttl);\n    return data;\n}\n// Component lazy loading with error boundaries\nfunction createLazyComponent(importFn, fallback) {\n    const LazyComponent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(importFn);\n    return function LazyWrapper(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n            fallback: fallback ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"fallback\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 362,\n                columnNumber: 44\n            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 362,\n                columnNumber: 59\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\performance.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/performance.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCedula: () => (/* binding */ formatCedula),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateCedula: () => (/* binding */ validateCedula),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format a date to a localized string\n */ function formatDate(date, options) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        ...options\n    };\n    return dateObj.toLocaleDateString(\"es-CO\", defaultOptions);\n}\n/**\n * Format a date to a relative time string (e.g., \"hace 2 horas\")\n */ function formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"hace unos segundos\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `hace ${diffInMinutes} minuto${diffInMinutes > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `hace ${diffInHours} hora${diffInHours > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) {\n        return `hace ${diffInDays} día${diffInDays > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n        return `hace ${diffInWeeks} semana${diffInWeeks > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `hace ${diffInMonths} mes${diffInMonths > 1 ? \"es\" : \"\"}`;\n    }\n    const diffInYears = Math.floor(diffInDays / 365);\n    return `hace ${diffInYears} año${diffInYears > 1 ? \"s\" : \"\"}`;\n}\n/**\n * Format currency in Colombian pesos\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat(\"es-CO\", {\n        style: \"currency\",\n        currency: \"COP\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\n/**\n * Capitalize first letter of a string\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\n/**\n * Generate a random ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Validate Colombian document number (Cédula)\n */ function validateCedula(cedula) {\n    // Remove any non-numeric characters\n    const cleanCedula = cedula.replace(/\\D/g, \"\");\n    // Check if it's a valid length (6-10 digits)\n    if (cleanCedula.length < 6 || cleanCedula.length > 10) {\n        return false;\n    }\n    // Basic validation - all digits should not be the same\n    if (/^(\\d)\\1+$/.test(cleanCedula)) {\n        return false;\n    }\n    return true;\n}\n/**\n * Format Colombian document number\n */ function formatCedula(cedula) {\n    const cleanCedula = cedula.replace(/\\D/g, \"\");\n    return cleanCedula.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\");\n}\n/**\n * Validate email format\n */ function validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate Colombian phone number\n */ function validatePhone(phone) {\n    // Remove any non-numeric characters\n    const cleanPhone = phone.replace(/\\D/g, \"\");\n    // Colombian phone numbers are typically 10 digits (mobile) or 7 digits (landline)\n    return cleanPhone.length === 10 || cleanPhone.length === 7;\n}\n/**\n * Format Colombian phone number\n */ function formatPhone(phone) {\n    const cleanPhone = phone.replace(/\\D/g, \"\");\n    if (cleanPhone.length === 10) {\n        // Mobile format: ************\n        return cleanPhone.replace(/(\\d{3})(\\d{3})(\\d{4})/, \"$1 $2 $3\");\n    } else if (cleanPhone.length === 7) {\n        // Landline format: 123 4567\n        return cleanPhone.replace(/(\\d{3})(\\d{4})/, \"$1 $2\");\n    }\n    return phone;\n}\n/**\n * Truncate text to a specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * Sleep function for delays\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Check if a string is a valid URL\n */ function isValidUrl(string) {\n    try {\n        new URL(string);\n        return true;\n    } catch (_) {\n        return false;\n    }\n}\n/**\n * Get initials from a full name\n */ function getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0).toUpperCase()).slice(0, 2).join(\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3641c1dc23a8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGlhLXRyYW1pdGVzLWFpLy4vYXBwL2dsb2JhbHMuY3NzPzBlZGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNjQxYzFkYzIzYThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true,\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/error/ErrorBoundary */ \"(rsc)/./components/error/ErrorBoundary.tsx\");\n/* harmony import */ var _lib_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/accessibility */ \"(rsc)/./lib/accessibility.tsx\");\n/* harmony import */ var _lib_mobile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/mobile */ \"(rsc)/./lib/mobile.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Sistema de Atenci\\xf3n Ciudadana - Municipio de Ch\\xeda\",\n        template: \"%s | Municipio de Ch\\xeda\"\n    },\n    description: \"Plataforma digital para tr\\xe1mites y servicios municipales del Municipio de Ch\\xeda, Cundinamarca. Accede a servicios gubernamentales de forma r\\xe1pida y segura.\",\n    keywords: \"Ch\\xeda, municipio, tr\\xe1mites, servicios, ciudadanos, gobierno digital, OPAs, procedimientos, documentos\",\n    authors: [\n        {\n            name: \"Municipio de Ch\\xeda\",\n            url: \"https://chia.gov.co\"\n        }\n    ],\n    creator: \"Municipio de Ch\\xeda\",\n    publisher: \"Municipio de Ch\\xeda\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    openGraph: {\n        title: \"Sistema de Atenci\\xf3n Ciudadana - Municipio de Ch\\xeda\",\n        description: \"Plataforma digital para tr\\xe1mites y servicios municipales\",\n        type: \"website\",\n        locale: \"es_CO\",\n        siteName: \"Municipio de Ch\\xeda\",\n        url: \"https://tramites.chia.gov.co\",\n        images: [\n            {\n                url: \"/images/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"Sistema de Atenci\\xf3n Ciudadana - Municipio de Ch\\xeda\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Sistema de Atenci\\xf3n Ciudadana - Municipio de Ch\\xeda\",\n        description: \"Plataforma digital para tr\\xe1mites y servicios municipales\",\n        images: [\n            \"/images/twitter-image.png\"\n        ],\n        creator: \"@ChiaOficial\"\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/favicon-16x16.png\",\n                sizes: \"16x16\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/favicon-32x32.png\",\n                sizes: \"32x32\",\n                type: \"image/png\"\n            }\n        ],\n        apple: [\n            {\n                url: \"/apple-touch-icon.png\",\n                sizes: \"180x180\",\n                type: \"image/png\"\n            }\n        ],\n        other: [\n            {\n                rel: \"mask-icon\",\n                url: \"/safari-pinned-tab.svg\",\n                color: \"#2563eb\"\n            }\n        ]\n    },\n    manifest: \"/manifest.json\",\n    metadataBase: new URL(\"https://tramites.chia.gov.co\"),\n    alternates: {\n        canonical: \"/\",\n        languages: {\n            \"es-CO\": \"/\"\n        }\n    },\n    category: \"government\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://supabase.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Frame-Options\",\n                        content: \"DENY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Accept-CH\",\n                        content: \"DPR, Viewport-Width, Width\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} font-sans antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_accessibility__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {\n                        href: \"#main-content\",\n                        children: \"Saltar al contenido principal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_error_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n                        level: \"critical\",\n                        showDetails: \"development\" === \"development\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_mobile__WEBPACK_IMPORTED_MODULE_4__.MobileOptimized, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                id: \"main-content\",\n                                role: \"main\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\layout.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/error/ErrorBoundary.tsx":
/*!********************************************!*\
  !*** ./components/error/ErrorBoundary.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useErrorHandler: () => (/* binding */ e2),
/* harmony export */   withErrorBoundary: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\components\error\ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\components\error\ErrorBoundary.tsx#withErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\components\error\ErrorBoundary.tsx#useErrorHandler`);


/***/ }),

/***/ "(rsc)/./lib/accessibility.tsx":
/*!*******************************!*\
  !*** ./lib/accessibility.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e5),
/* harmony export */   announceToScreenReader: () => (/* binding */ e3),
/* harmony export */   calculateContrastRatio: () => (/* binding */ e0),
/* harmony export */   getButtonProps: () => (/* binding */ e12),
/* harmony export */   getFormFieldProps: () => (/* binding */ e9),
/* harmony export */   getLinkProps: () => (/* binding */ e13),
/* harmony export */   getModalProps: () => (/* binding */ e11),
/* harmony export */   getTableProps: () => (/* binding */ e10),
/* harmony export */   meetsContrastRequirement: () => (/* binding */ e1),
/* harmony export */   useAriaLiveRegion: () => (/* binding */ e6),
/* harmony export */   useFocusManagement: () => (/* binding */ e4),
/* harmony export */   useHighContrast: () => (/* binding */ e8),
/* harmony export */   useKeyboardNavigation: () => (/* binding */ e2),
/* harmony export */   useReducedMotion: () => (/* binding */ e7)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#calculateContrastRatio`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#meetsContrastRequirement`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#useKeyboardNavigation`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#announceToScreenReader`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#useFocusManagement`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#SkipLink`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#useAriaLiveRegion`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#useReducedMotion`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#useHighContrast`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#getFormFieldProps`);

const e10 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#getTableProps`);

const e11 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#getModalProps`);

const e12 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#getButtonProps`);

const e13 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\accessibility.tsx#getLinkProps`);


/***/ }),

/***/ "(rsc)/./lib/mobile.tsx":
/*!************************!*\
  !*** ./lib/mobile.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BREAKPOINTS: () => (/* binding */ e0),
/* harmony export */   MobileOptimized: () => (/* binding */ e8),
/* harmony export */   getMobileImageSizes: () => (/* binding */ e10),
/* harmony export */   getResponsiveGridCols: () => (/* binding */ e9),
/* harmony export */   useDeviceDetection: () => (/* binding */ e1),
/* harmony export */   useMobilePerformance: () => (/* binding */ e7),
/* harmony export */   useMobileScroll: () => (/* binding */ e6),
/* harmony export */   useResponsiveValue: () => (/* binding */ e2),
/* harmony export */   useSafeAreaInsets: () => (/* binding */ e5),
/* harmony export */   useTouchGestures: () => (/* binding */ e3),
/* harmony export */   useViewportHeight: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#BREAKPOINTS`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useDeviceDetection`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useResponsiveValue`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useTouchGestures`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useViewportHeight`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useSafeAreaInsets`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useMobileScroll`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#useMobilePerformance`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#MobileOptimized`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#getResponsiveGridCols`);

const e10 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chia-tramites\lib\mobile.tsx#getMobileImageSizes`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-tramites&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();