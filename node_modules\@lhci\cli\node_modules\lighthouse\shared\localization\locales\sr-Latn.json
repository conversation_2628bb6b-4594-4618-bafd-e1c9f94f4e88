{"core/audits/accessibility/accesskeys.js | description": {"message": "Tasteri za pristup omogućavaju korisnicima da brzo fokusiraju deo stranice. Da bi navigacija radila ispra<PERSON>no, svaki taster za pristup mora da bude jedinstven. [Saznajte više o tasterima za pristup](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> za `[accesskey]` nisu jedinstvene"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Vrednosti za `[accesskey]` su jedinstvene"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Svaki ARIA element `role` podr<PERSON><PERSON> određeni podskup atributa `aria-*`. Ako se ovi elementi ne podudaraju, atributi `aria-*` će biti nevažeći. [Saz<PERSON><PERSON>te kako da se ARIA atributi podudaraju sa ulogama](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributi `[aria-*]` se ne podudaraju sa svojim ulogama"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributi `[aria-*]` se podudaraju sa svojim ulogama"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Kada element nema pristupa<PERSON>an naziv, čitači ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte kako da učinite elemente komandi pristupačnijim](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementi `button`, `link` i `menuitem` nemaju nazive prilagođene funkcijama pristupačnosti."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementi `button`, `link` i `menuitem` imaju nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Tehnologije za pomoć osobama sa <PERSON>tom, poput čita<PERSON>, ne rade dos<PERSON>no kada se `aria-hidden=\"true\"` podesi za `<body>` dokumenta. [Saznajte kako `aria-hidden` utiče na sadržaj dokumenta](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Element `[aria-hidden=\"true\"]` je prisutan za `<body>` dokumenta"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Element `[aria-hidden=\"true\"]` nije prisutan za `<body>` dokumenta"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Elementi po opadajućem redosledu koji mogu da se fokusiraju u okviru elementa `[aria-hidden=\"true\"]` sprečavaju te interaktivne elemente da budu dostupni korisnicima tehnologija za pomoć osobama sa invaliditetom, poput čitača ekrana. [<PERSON><PERSON><PERSON><PERSON><PERSON> kako `aria-hidden` utiče na elemente koji mogu da se fokusiraju](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementi `[aria-hidden=\"true\"]` sadrže elemente po opadajućem redosledu koji mogu da se fokusiraju"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elementi `[aria-hidden=\"true\"]` ne obuhvataju elemente po opadajućem redosledu koji mogu da se fokusiraju"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Kada polje za unos nema naziv prilagođen funkciji pristupačnosti, čitači ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte više o oznakama polja za unos](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA polja za unos nemaju nazive prilagođene funkciji pristupačnosti"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA polja za unos imaju nazive prilagođene funkciji pristupačnosti"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Kada element merača nema pristupa<PERSON>an naziv, č<PERSON><PERSON>i ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte kako da imenujete `meter` elemente](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA elementi `meter` nemaju nazive prilagođene funkcijama pristupačnosti."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA elementi `meter` imaju nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Kada `progressbar` nema naziv prilagođen funkciji pristupačnosti, čitači ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte kako da označite elemente `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA elementi `progressbar` nemaju nazive prilagođene funkcijama pristupačnosti."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA elementi `progressbar` imaju nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Pojedine ARIA uloge imaju obavezne atribute koji status elementa opisuju čitačima ekrana. [Saznajte više o ulogama i obaveznim atributima](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` nemaju sve obavezne atribute `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` imaju sve obavezne atribute `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Pojedine nadređene ARIA uloge moraju da obuhvataju određene podređene uloge da bi pravilno obavljale namenjene funkcije pristupačnosti. [Saznajte više o ulogama i obaveznim podređenim elementima](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementima sa ARIA ulogom `[role]` koji zahtevaju da podređeni elementi sadrže konkretni element `[role]` nedostaju neki ili svi ti potrebni podređeni elementi."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementi sa <PERSON> ulogom `[role]` koji zahteva<PERSON> da podređeni elementi sadrže konkretni element `[role]` imaju sve potrebne podređene elemente."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Pojedine podređene ARIA uloge moraju da budu obuhvaćene određenim nadređenim ulogama da bi pravilno obavljale namenjene funkcije pristupačnosti. [Saznajte više o ARIA ulogama i obaveznom nadređenom elementu](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` nisu o<PERSON>ene svojim obaveznim nadređenim elementom"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` su obuhvaćene svojim obaveznim nadređenim elementom"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Vrednosti ARIA uloga moraju da budu važ<PERSON>́e da bi pravilno obavljale namenjene funkcije pristupačnosti. [Saznajte više o va<PERSON><PERSON>́im ARIA ulogama](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> za `[role]` nisu va<PERSON><PERSON>e"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> za `[role]` su važ<PERSON>́e"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Kada polje za prekidač nema naziv prilagođen funkciji pristupačnosti, čitači ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte više o uključivanju ili isključivanju polja](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA polja prekidača nemaju nazive prilagođene funkciji pristupačnosti"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA polja prekidača imaju nazive prilagođene funkciji pristupačnosti"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Kada element objašnjenja nema pristupačan naziv, č<PERSON><PERSON>i ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte kako da imenujete `tooltip` elemente](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA elementi `tooltip` nemaju nazive prilagođene funkcijama pristupačnosti."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA elementi `tooltip` imaju nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Kada `treeitem` nema naziv prilagođen funkciji pristupačnosti, čitači ekrana ga najavljuju pomoću generičkog naziva, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte više o označavanju elemenata `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA elementi `treeitem` nemaju nazive prilagođene funkcijama pristupačnosti."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA elementi `treeitem` imaju nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Tehnologije za pomoć osobama sa invaliditetom, poput č<PERSON>, ne mogu da protumače ARIA atribute sa nevažećim vrednostima. [Saznajte više o važećim vrednostima za ARIA atribute](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Vrednosti atributa `[aria-*]` nisu va<PERSON><PERSON>e"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Vrednosti atributa `[aria-*]` su važeće"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Tehnologije za pomoć osobama sa invaliditetom, poput č<PERSON>, ne mogu da protumače ARIA atribute sa nevažećim nazivima. [Saznajte više o važećim ARIA atributima](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributi `[aria-*]` nisu važ<PERSON>́i ili su pogrešno napisani"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributi `[aria-*]` su važeći i nisu pogrešno napisani"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementi koji nisu pro<PERSON> proveru"}, "core/audits/accessibility/button-name.js | description": {"message": "Kada dugme nema naziv prilagođen funkciji pristupačnosti, č<PERSON><PERSON>i ekrana ga najavljuju kao „dugme“, pa korisnici koji se oslanjaju na čitače ekrana ne mogu da ga koriste. [Saznajte kako da učinite dugmad pristupačnijom](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Dugmad nema nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/button-name.js | title": {"message": "Dugmad ima nazive prilagođene funkcijama pristupačnosti"}, "core/audits/accessibility/bypass.js | description": {"message": "Kada se dodaju načini za zaobilaženje sadržaja koji se ponavlja, korisnici tastature mogu efikasnije da se kreću po stranici. [Saznajte više o zaobilaž<PERSON><PERSON> blokova](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Stranica ne obuhvata naslov, link za preskakanje niti region orijentira"}, "core/audits/accessibility/bypass.js | title": {"message": "Stranica obuhvata naslov, link za preskakanje ili region orijentira"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Mnogi korisnici veoma teško čitaju tekst sa malim kontrastom ili uopšte ne mogu da ga čitaju. [Saznajte kako da obezbedite dovoljno veliki kontrast boja](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Boje u pozadini i u prvom planu nemaju zadovoljavajući odnos kontrasta."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Boje u pozadini i u prvom planu imaju zadovoljavajući odnos kontrasta"}, "core/audits/accessibility/definition-list.js | description": {"message": "Kada liste definicija nisu pravilno <PERSON>, čitači ekrana mogu da pružaju zbunjujući ili netačan izlaz. [Saznajte kako da napravite pravilnu strukturu lista definicija](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` ne sadrži samo pravilno poređane grupe `<dt>` i `<dd>`, elemente `<script>`, `<template>` ili `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` sad<PERSON><PERSON><PERSON> samo pravilno poređane grupe `<dt>` i `<dd>`, elemente `<script>`, `<template>` ili `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Stavke liste definicija (`<dt>` i `<dd>`) moraju da budu upakovane u nadređeni element`<dl>` da bi čitači ekrana mogli da ih pravilno čitaju. [Saznajte kako da napravite pravilnu strukturu lista definicija](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Stavke liste definicija su upakovane u elemente `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Stavke liste definicija su upakovane u elemente`<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Naslov korisnicima čitača ekrana pruža pregled stranice, a korisnici pretraživača se na njega oslanjaju da bi utvrdili da li je stranica relevantna za njihovu pretragu. [Saznajte više o naslovima dokumenata](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument nema element `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument ima element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Da biste se uverili da su vidljivi za tehnologije za pomoć osobama sa invaliditetom, svi elementi koji mogu da se fokusiraju moraju da imaju jedinstveni `id`. [Saznaj<PERSON> kako da rešite duplirane `id`-ove](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atributi `[id]` na aktivnim elementima koji mogu da se fokusiraju nisu jedinstveni"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atributi `[id]` na aktivnim elementima koji mogu da se fokusiraju su jedinstveni"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Vrednost ARIA ID-a mora da bude jedinstvena da bi se sprečilo da tehnologije za pomoć osobama sa invaliditetom propuste druge instance. [Saz<PERSON>jte kako da otklonite duplikate ARIA ID-ova](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ID-ovi nisu jedinstveni"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ID-ovi su jedinstveni"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Tehnologije za pomoć osobama sa invaliditetom, poput čitača ekrana koji koriste prvu, pos<PERSON>n<PERSON> ili sve oznake, mogu da čitaju polja obrasca sa više oznaka na način koji zbunjuje korisnike. [Saznajte kako da koristite oznake obrazaca](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Polja obrasca imaju više oznaka"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nijedno polje obrasca nema više oznaka"}, "core/audits/accessibility/frame-title.js | description": {"message": "Korisnici čitača ekrana očekuju od naslova okvira da im opišu sadržaj okvira. [Saznajte više o naslovima okvira](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementi `<frame>` ili `<iframe>` nema<PERSON> naslov"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementi `<frame>` ili `<iframe>` imaju naslov"}, "core/audits/accessibility/heading-order.js | description": {"message": "Naslovi sa pravilnim redosledom koji ne preskaču nivoe prenose semantičku strukturu stranice, pa je čine lakšom za kretanje i razumevanje pri korišćenju tehnologija za pomoć osobama sa invaliditetom. [Saznajte više o redosledu naslova](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elementi naslova se ne prikazuju opadajućim redosledom"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elementi naslova se prikazuju opadajućim redosledom"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ako za stranicu nije naveden atribut `lang`, čita<PERSON> ekrana pretpostavlja da je stranica na podrazumevanom jeziku koji je korisnik odabrao tokom podešavanja čitača ekrana. Ako stranica zapravo nije na podrazumevanom jeziku, čitač ekrana možda neće pravilno čitati tekst sa stranice. [Saznajte više o atributu `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nema atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ima atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Navođenjem važećeg koda [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) omogućava se da čitač ekrana pravilno čita tekst. [Saznajte kako da koristite atribut `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` nema važeću vrednost za svoj atribut `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ima važ<PERSON>́u vrednost za svoj atribut `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informativni elementi treba da sadrže kratki, opisni alternativni tekst. Dekorativni elementi mogu da se zanemare praznim atributom alt. [Saznajte više o atributu `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementi slike nemaju atribute `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elementi slika imaju atribute `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Kada se slika koristi kao dugme `<input>`, navođenje alternativnog teksta može da pomogne korisnicima da razumeju svrhu dugmeta. [Saznajte više o alternativnom tekstu slike za unos](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementi `<input type=\"image\">` ne sadr<PERSON>e tekst `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` sad<PERSON><PERSON><PERSON> te<PERSON> `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Oznake omogućavaju da tehnologije za pomoć osobama sa <PERSON>itetom, poput čita<PERSON> ekrana, pravilno najavljuju kontrole obrazaca. [Saznajte više o oznakama elemenata obrasca](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Elementi obrazaca nemaju povezane oznake"}, "core/audits/accessibility/label.js | title": {"message": "Elementi obrazaca imaju povezane oznake"}, "core/audits/accessibility/link-name.js | description": {"message": "Tekst linka (i alternativni tekst za slike kada se koristi za linkove) koji je prepoznatljiv, jedinstven i može da se fokusira olakšava kretanje za korisnike čitača ekrana. [Saznajte kako da učinite linkove dostupnim.](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON> linkova ne može da se prepozna"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON> linkova može da se prepozna"}, "core/audits/accessibility/list.js | description": {"message": "Čitači ekrana čitaju liste na poseban način. Pravilna struktura liste olakšava razumevanje čitača ekrana. [Saznajte više o pravilnoj strukturi liste](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Liste ne sadrže isključivo elemente `<li>` i elemente koji podržavaju skripte (`<script>` i`<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Liste sadrže isključivo elemente `<li>` i elemente koji podržavaju skripte (`<script>` i `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Čitači ekrana zahtevaju da stavke liste (`<li>`) budu obuhvaćene nadređenim elementima `<ul>`, `<ol>` ili `<menu>` da bi mogle da se pravilno čitaju. [Saznajte više o pravilnoj strukturi liste](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> liste (`<li>`) nisu obuhvaćene nadređenim elementima`<ul>`, `<ol>` ili `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> liste (`<li>`) su obuhvaćene nadređenim elementima `<ul>`, `<ol>` ili `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Korisnici ne očekuju da se stranica automatski osvežava i time se fokus premešta na početak stranice. To može da frustira ili zbunjuje korisnike. [Saznajte više o osvežavanju metaoznake](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument koristi metaoznaku `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument ne koristi metaoznaku `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Onemogućavanje zumiranja predstavlja problem za slabovide korisnike koji se oslanjaju na uvećavanje prikaza ekrana da bi mogli da vide sadržaj veb-stranice. [Saznajte više o metaoznaci oblasti prikaza](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` se koristi u elementu `<meta name=\"viewport\">` ili je vrednost atributa `[maximum-scale]` manja od 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` se ne koristi u elementu `<meta name=\"viewport\">`, a vrednost atributa `[maximum-scale]` nije manja od 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Čitači ekrana ne mogu da prevode sadržaj koji nije tekst. Dodavanje alternativnog teksta elementima `<object>` omogućava da čitači ekrana lakše prenesu značenje korisnicima. [Saznajte više o alternativnom tekstu za elemente `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementi `<object>` nemaju alternativni tekst"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` imaju alternativni tekst"}, "core/audits/accessibility/tabindex.js | description": {"message": "Vrednost veća od 0 označava eksplicitni raspored navigacije. Iako je tehnički ispravno, to često frustrira korisnike koji se oslanjaju na tehnologije za pomoć osobama sa invaliditetom. [Saznajte više o atributu `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Neki elementi imaju vrednost za `[tabindex]` koja je veća od 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Vrednost nijednog elementa `[tabindex]` nije veća od 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Čitači ekrana imaju funkcije koje olakšavaju kretanje kroz tabele. Ako se pobrinete da se ćelije `<td>` koje koriste atribut `[headers]` odnose samo na druge ćelije u istoj tabeli, možete da poboljšate doživljaj za korisnike čitača ekrana. [Saznajte više o atributu `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ćelije u elementu `<table>` koje koriste atribut `[headers]` odnose se na element `id` koji se ne nalazi u istoj tabeli."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Ćelije u elementu `<table>` koje koriste atribut `[headers]` odnose se na ćelije tabele u istoj tabeli."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čitači ekrana imaju funkcije koje olakšavaju kretanje kroz tabele. Ako se pobrinete da se naslovi tabela uvek odnose na neku grupu ćelija, možete da poboljšate doživljaj za korisnike čitača ekrana. [Saznajte više o zaglavljima tabela](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` i elementi sa atributom`[role=\"columnheader\"/\"rowheader\"]` nema<PERSON> c<PERSON>elije sa podacima koje opisuju."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` i elementi sa atributom `[role=\"columnheader\"/\"rowheader\"]` imaju c<PERSON>elije sa podacima koje opisuju."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Navođenjem važećeg koda [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) u elementima omogućava se da čitač ekrana pravilno čita tekst. [Saznajte kako da koristite atribut `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Vrednost atributa `[lang]` nije va<PERSON>a"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributi `[lang]` imaju va<PERSON><PERSON><PERSON>u vrednost"}, "core/audits/accessibility/video-caption.js | description": {"message": "<PERSON>da je dostupan titl za video, gluvi korisnici i oni sa oštećenjem sluha lakše mogu da pristupaju informacijama koje video obuhvata. [Saznajte više o titlovima video snimaka](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` ne obuhvataju element `<track>` sa atributom `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` sadrže element `<track>` sa atributom `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Aktuelna vrednost"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Predloženi token"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` poma<PERSON>e korisnicima da brže šalju obrasce. Da bi korisnicima bilo lakše, ne bi bilo loše da podesite atribut `autocomplete` na važeću vrednost i time omogućite ovu stavku. [Saz<PERSON><PERSON>te više o atributu `autocomplete` u obrascima](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Elementi `<input>` nemaju ispravne atribute `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Zahteva r<PERSON> pregled"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Pregledajte redosled tokena"}, "core/audits/autocomplete.js | title": {"message": "Elementi `<input>` ispravno koriste `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "<PERSON><PERSON><PERSON> za `autocomplete`: „{token}“ nije važec<PERSON>i za {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Pregledajte redosled tokena: „{tokens}“ u elementu {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON><PERSON> ne<PERSON> da se preduzme"}, "core/audits/bf-cache.js | description": {"message": "Mnoge navigacije se obavljaju vraćanjem na prethodnu stranicu ili ponovnim prosleđivanjem. Keširanje cele stranice (bfcache) može da ubrza ove povratne navigacije. [Saznajte više o bfcache-u](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 razlog neuspeha}one{# razlog neuspeha}few{# razloga neuspeha}other{# razloga neuspeha}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Razlog neuspeha"}, "core/audits/bf-cache.js | failureTitle": {"message": "Stranica je sprečila vraćanje keširanja cele stranice"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Ne može ništa da se preduzme"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Podrška za pregledač na čekanju"}, "core/audits/bf-cache.js | title": {"message": "Stranica nije sprečila vraćanje keširanja cele stranice"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Dodaci za Chrome su negativno uticali na brzinu učitavanja ove stranice. Probajte da proverite stranicu u režimu bez arhiviranja ili sa Chrome profila bez dodataka."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON> skripta"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Raščlanjivanje skripta"}, "core/audits/bootup-time.js | columnTotal": {"message": "Ukupno CPU vreme"}, "core/audits/bootup-time.js | description": {"message": "Preporučujemo vam da smanjite vreme potrebno za raščlanjivanje, kompajliranje i izvršavanje JS fajlova. Prikazivanje manjih JS resursa će vam možda pomoći u tome. [Saznajte kako da smanjite vreme izvršavanja JavaScript-a](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Smanjite vreme izvršavanja JavaScript datoteka"}, "core/audits/bootup-time.js | title": {"message": "Vreme izvršavanja JavaScript-a"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Uklonite velike, duplirane JavaScript module iz paketa da biste smanjili nepotrebnu potrošnju podataka tokom mrežnih aktivnosti. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Uklonite duplirane module iz JavaScript paketa"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veliki GIF-ovi nisu korisni za prikazivanje animiranog sadržaja. Preporučujemo vam da umesto GIF-ova koristite MPEG4/WebM video snimke za animacije i PNG/WebP za statične slike da biste uštedeli mrežne podatke. [Saznajte više o efikasnim video formatima](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Koristite video formate za animirani sadržaj"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polifili i transformacije omogućavaju da stare verzije pregledača koriste nove funkcije JavaScript-a. <PERSON>, mnogi nisu neophodni za moderne pregledače. Za JavaScript pakete koristite modernu strategiju primene skripte pomoću otkrivanja funkcija modulom/bez modula da biste smanjili količinu koda koji se šalje modernim pregledačima, pritom zadržavajući podršku za stare verzije pregledača. [Saznajte kako se koristi moderni JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Izbegavajte prikazivanje zastarelog JavaScript-a modernim pregledačima"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formati slika kao što su WebP i AVIF često pružaju bolju kompresiju od PNG-a ili JPEG-a, što omogućava brža preuzimanja i manju potrošnju podataka. [Saznajte više o modernim formatima slika](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Prikazujte slike u formatima sledeće generacije"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Preporučujemo vam da odložite učitavanje slika van ekrana i skrivenih slika dok se svi veoma važni resursi ne učitaju kako biste smanjili vreme do početka interakcije. [Saznajte kako da odložite slike van ekrana](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odložite slike van ekrana"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursi blokiraju prvo prikazivanje stranice. Preporučujemo vam da prikazujete sve važne JS/CSS fajlove u tekstu i da odložite sve JS fajlove/stilove koji nisu toliko važni. [Saz<PERSON>jte kako da eliminišete resurse koji blokiraju prikazivanje](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Eliminišite resurse koji blokiraju prikazivanje"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Velike mrežne resurse korisnici moraju da plate stvarnim novcem i oni su veoma povezani sa dugim vremenima učitavanja. [Saznajte kako da smanjite veličine resursa](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Ukupna veličina je bila {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Izbegavajte ogromne mrežne resurse"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Izbegava ogromne mrežne resurse"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Umanjivanjem CSS fajlova možete da smanjite veličine mrežnih resursa. [Saz<PERSON>j<PERSON> kako da umanjite CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Umanjite CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Umanjivanje JavaScript fajlova može da smanji veličine resursa i vreme raščlanjivanja skripta. [Saznajte kako da umanjite JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Umanjite JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Smanjite nekorišćena pravila iz opisa stilova i odložite CSS koji se ne koristi za sadržaj iznad preloma da biste smanjili potrošnju bajtova tokom mrežnih aktivnosti. [Saznajte kako da smanjite CSS koji se ne koristi](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Smanjite nekorišćeni CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Smanjite nekorišćeni JavaScript i odložite učitavanje skripti dok ne budu potrebne da biste smanjili potrošnju bajtova tokom mrežnih aktivnosti. [Saznajte kako da smanjite JavaScript koji se ne koristi](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Smanjite nekorišćeni JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dugo trajanje keša može da ubrza ponovne posete stranici. [Saznajte više o efikasnim smernicama za keš](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je 1 resurs}one{Pronađen je # resurs}few{Pronađena su # resursa}other{<PERSON>nađeno je # resursa}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Prikazujte statične elemente sa efikasnim smernicama keša"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Koristi efikasne smernice keša na statičnim elementima"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizovane slike se učitavaju brže i troše manje mobilnih podataka. [Saznajte kako da efikasno kodirate slike](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efikasno kodirajte slike"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Stvarne dimenzije"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Prikazane dimenzije"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Slike su veće od prikazane veličine"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Slike su odgovarajuće za prikazanu veličinu"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Prikazujte slike odgovarajuće veličine da biste uštedeli mobilne podatke i poboljšali vreme učitavanja. [Saznajte kako da podesite veličinu slika](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Odre<PERSON><PERSON> odgo<PERSON>́u veličinu slika"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Resurse zasnovane na tekstu treba da prikazujete u komprimovanom formatu (gzip, deflate ili brotli) da biste smanjili ukupnu količinu potrošenih mrežnih podataka. [Saznajte više o komprimovanju teksta](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Omogućite kompresiju teksta"}, "core/audits/content-width.js | description": {"message": "Ako se širina sadržaja aplikacije ne podudara sa širinom oblasti prikaza, aplikacija možda nije optimizovana za ekrane na mobilnim uređajima. [Saznajte kako da odredite veličinu sadržaja za oblast prikaza](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Veličina oblasti prikaza od {innerWidth} piksela se ne podudara sa veličinom prozora od {outerWidth} piksela."}, "core/audits/content-width.js | failureTitle": {"message": "Sadržaj nije odgovarajuće veličine za oblast prikaza"}, "core/audits/content-width.js | title": {"message": "Sad<PERSON><PERSON><PERSON> je odgovarajuće veličine za oblast prikaza"}, "core/audits/critical-request-chains.js | description": {"message": "Lanci veoma važnih zahteva u nastavku vam prikazuju koji resursi se učitavaju sa visokim prioritetom. Preporučujemo vam da smanjite dužinu lanaca, da smanjite veličinu preuzimanja za resurse ili da odložite preuzimanje resursa koji nisu neophodni radi bržeg učitavanja stranice. [Saznajte kako da izbegnete pravljenje lanaca kritičnih zahteva](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>nađen je 1 lanac}one{Pronađen je # lanac}few{Pronađena su # lanca}other{<PERSON>nađeno je # lanaca}}"}, "core/audits/critical-request-chains.js | title": {"message": "Izbegavajte pravljenje lanaca kritičnih zahteva"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Ozbiljnost"}, "core/audits/csp-xss.js | description": {"message": "Snažne smernice za bezbednos<PERSON> (CSP) znatno smanjuju rizik od napada skriptovanjem dinamički generisanih veb-stranica (XSS). [Saznajte kako da koristite CSP da biste sprečili XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sin<PERSON>ks<PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Stranica sadrži CSP definisan u <meta> oznaci. Razmotrite premeštanje CSP-a u HTTP zaglavlje ili definisanje drugog strogog CSP-a u HTTP zaglavlju."}, "core/audits/csp-xss.js | noCsp": {"message": "Nije pronađen nijedan CSP u režimu primene"}, "core/audits/csp-xss.js | title": {"message": "Uverite se da je CSP efikasan u borbi protiv XSS napada"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Zastarelo/upozorenje"}, "core/audits/deprecations.js | columnLine": {"message": "Red"}, "core/audits/deprecations.js | description": {"message": "Zastareli API-ji će na kraju biti uklonjeni iz pregledača. [Saznajte više o zastarelim API-jima](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Otkriveno je 1 upozorenje}one{Otkriveno je # upozorenje}few{Otkrivena su # upozorenja}other{Otkriveno je # upozorenja}}"}, "core/audits/deprecations.js | failureTitle": {"message": "<PERSON>risti zastarele API-je"}, "core/audits/deprecations.js | title": {"message": "Izbegava zastarele API-je"}, "core/audits/dobetterweb/charset.js | description": {"message": "Utvrđivanje kodiranja znakova je obavezno. To može da se uradi pomoću oznake `<meta>` u prva 1024 bajta HTML-a ili u HTTP zaglavlju odgovora Content-Type. [Saznajte više o deklarisanju kodiranja znakova](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Charset nije utvrđen ili se utvrđuje prekasno u HTML-u"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> definisan charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Navođenjem doctype-a sprečava se prelazak na arhajski režim pregleda<PERSON>a. [Saznajte više o deklaraciji doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Naziv za doctype mora da bude string `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Doku<PERSON> sadr<PERSON>i `doctype` koji pokrec<PERSON>e `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument mora da sadrži doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Očekivani publicId c<PERSON>e biti prazan string"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Očekivani systemId će biti prazan string"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Doku<PERSON> sadr<PERSON>i `doctype` koji pokre<PERSON>e `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stranici nedostaje HTML doctype, pa se aktivira arhajski režim"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Stranica ima HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vrednost"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Veliki DOM će povec<PERSON>ati potrošnju memorije, iza<PERSON><PERSON><PERSON> du<PERSON> [izra<PERSON><PERSON><PERSON><PERSON> stilova](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) i dovesti do skupih [preoblikovanja izgleda](https://developers.google.com/speed/articles/reflow). [Saznajte kako da izbegnete prekomernu veličinu DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}one{# element}few{# elementa}other{# elemenata}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Izbegavajte preveliku veličinu DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimalna dubina DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Ukupan broj DOM elemenata"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maks<PERSON>lan broj podređenih elemenata"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Izbegava preveliku veličinu DOM-a"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Korisnici nemaju poverenja u sajtove koji traže njihovu lokaciju bez konteksta ili ih takvi sajtovi zbunjuju. Preporučujemo vam da umesto toga povežete zahtev sa radnjom koju obavlja korisnik. [Saznajte više o dozvoli za geolociranje](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Traži dozvolu za geolociranje pri učitavanju stranice"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Izbegavajte traženje dozvole za geolociranje pri učitavanju stranice"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON> <PERSON>a"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problemi evidentirani u oknu `Issues` u Chrome alatkama za programere ukazuju na nerešene probleme. Oni mogu da budu rezultat neuspelih mrežnih zahteva, nedovoljnih bezbednosnih kontrola i drugih problema u vezi sa pregledačem. Otvorite okno Problemi u Chrome alatkama za programere da biste pronašli više detalja o svakom problemu."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problemi su evidentirani u oknu `Issues` u Chrome alatkama za programere"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokiraju smernice za zahteve iz drugih izvora"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Oglasi koriste mnogo resursa"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Nema problema u oknu `Issues` u Chrome alatkama za programere"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verzija"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Sve korisničke JavaScript biblioteke otkrivene na ovoj stranici. [Saznajte više o ovoj dijagnostičkom nadzoru otkrivanja JavaScript biblioteke](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Otkrivene su JavaScript biblioteke"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON> korisnici imaju spore veze, spoljne skripte koje se dinamički ubacuju pomoću atributa `document.write()` mogu da odlože učitavanje stranice za desetine sekundi. [Saznajte kako da izbegnete document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Izbegnite `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Izbegava atribut `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Korisnici nemaju poverenja u sajtove koji traže dozvolu za slanje obaveštenja bez konteksta ili ih takvi sajtovi zbunjuju. Preporučujemo vam da umesto toga povežete zahtev sa pokretima korisnika. [Saznajte više o odgovornom dobijanju dozvole za obaveštenja](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Traži dozvolu za obaveštenja pri učitavanju stranice"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Izbegavajte traženje dozvole za obaveštenja pri učitavanju stranice"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ima brojne prednosti u odnosu na HTTP/1.1, uključujući binarna zaglavlja i multipleksiranje. [Saznajte više o protokolu HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 zahtev nije isporučen preko protokola HTTP/2}one{# zahtev nije isporučen preko protokola HTTP/2}few{# zahteva nisu isporučena preko protokola HTTP/2}other{# zahteva nije isporučeno preko protokola HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Koristite HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Preporučujemo vam da pasivne obrađivače događaja označite kao `passive` da biste poboljšali rezultate pomeranja. [Saznajte više o usvajanju pasivnih osluškivača događaja](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ne koristite pasivne obrađivače da biste poboljšali učinak pomeranja"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Koristite pasivne obrađivače da biste poboljšali učinak pomeranja"}, "core/audits/errors-in-console.js | description": {"message": "Greške evidentirane u konzoli ukazuju na nerešene probleme. One su rezultat neuspelih mrežnih zahteva i drugih problema u vezi sa pregledačem. [Saznajte više o ovim greškama u dijagnostičkom nadzoru konzole](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Greške pregledača su evidentirane u konzoli"}, "core/audits/errors-in-console.js | title": {"message": "Nijedna greška pregledača nije evidentirana u konzoli"}, "core/audits/font-display.js | description": {"message": "Iskoristite CSS funkciju `font-display` da bi tekst bio vidljiv korisnicima dok se veb-fontovi učitavaju. [Saznajte više o funkciji `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Pobrinite se da tekst ostane vidljiv tokom učitavanja veb-fontova"}, "core/audits/font-display.js | title": {"message": "Sav tekst ostaje vidljiv tokom učitavanja veb-fontova"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse nije uspeo da automatski proveri vrednost `font-display` za polazni URL {fontOrigin}.}one{Lighthouse nije uspeo da automatski proveri `font-display` vrednosti za polazni URL {fontOrigin}.}few{Lighthouse nije uspeo da automatski proveri `font-display` vrednosti za polazni URL {fontOrigin}.}other{Lighthouse nije uspeo da automatski proveri `font-display` vrednosti za polazni URL {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Razmera (stvarna)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Razmera (prikazana)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Dimenzije prikaza slike treba da se podudaraju sa prirodnom razmerom. [Saznajte više o razmeri slike](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Prikazuje slike sa pogrešnom razmerom"}, "core/audits/image-aspect-ratio.js | title": {"message": "Prikazuje slike sa tačnom razmerom"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Prikazan<PERSON> ve<PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Očekivana veličina"}, "core/audits/image-size-responsive.js | description": {"message": "Prirodne dimenzije slike treba da budu proporcionalne razmeri veličine ekrana i piksela da bi slike bile što jasnije. [Saznajte kako da pružate prilagodljive slike](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Prikazuje slike sa niskom rezolucijom"}, "core/audits/image-size-responsive.js | title": {"message": "Prikazuje slike sa odgovarajućom rezolucijom"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikacija je već instalirana"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Preuzimanje obavezne ikone iz manifesta nije uspelo"}, "core/audits/installable-manifest.js | columnValue": {"message": "Razlog neuspeha"}, "core/audits/installable-manifest.js | description": {"message": "Serviser je tehnologija koja omogućava aplikaciji da koristi mnoge funkcije progresivnih veb-aplikacija, poput oflajn rada, dodavanja na početni ekran i iskačućih obaveštenja. Uz odgovarajuće primene servisera i manifesta pregledači mogu proaktivno da traže od korisnika da dodaju aplikaciju na početni ekran, što može da dovede do većeg angažovanja. [Saznajte više o uslovima za instaliranje za manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 razlog}one{# razlog}few{# razloga}other{# razloga}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifest veb-aplikacije ili serviser ne zadovoljava uslove za instaliranje"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL aplikacije u Play prodavnici i ID u Play prodavnici se ne podudaraju"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Stranica se učitava u prozoru bez arhiviranja"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Svojstvo „display“ u manifestu mora da bude „standalone“, „fullscreen“ ili „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest sadr<PERSON>i polje „display_override“, a prvi podržani način prikazivanja mora da bude „standalone“, „fullscreen“ ili „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Preuzimanje ili raščlanjivanje manifesta nije uspelo ili je on prazan"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL manifesta je promenjen tokom preuzimanja manifesta."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest ne sadrži polje „name“ ili „short_name“"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest ne sadrži odgovarajuću ikonu – obavezan je PNG, SVG ili WebP format koji ima najmanje {value0} pik<PERSON><PERSON>, atribut za veličine mora da bude podešen, a atribut za svrhu, ako je podešen, mora da obuhvata „Bilo koji“."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nijedna od navedenih ikona nije u obliku kvadrata od najmanje {value0} piksela u PNG, SVG ili WebP formatu, sa atributom svrhe koji nije podešen ili je podešen na „Bilo koji“"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Preuzeta ikona je bila prazna ili oštećena"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "<PERSON>je naveden ID u Play prodavnici"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Stranica ne sadrži <link> URL manifesta"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "<PERSON><PERSON> otkriven nijedan odgovarajući serviser. Možda ćete morati ponovo da učitate stranicu ili da proverite da li opseg servisera za aktuelnu stranicu obuhvata opseg i početni URL iz manifesta."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Provera servisera bez polja „start_url“ u manifestu nije uspela"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID greške u vezi sa mogućnošću instaliranja „{errorId}“ nije prepoznat"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Stranica se ne prikazuje iz bezbednog izvora"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Stranica se ne učitava u glavnom okviru"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Stranica ne radi oflajn"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA je deinstalirana i provere nestabilnosti se resetuju."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Navedena platforma za aplikacije nije podržana na Android-u"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest navodi prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Stavka prefer_related_applications je podržana samo u Chrome-u beta i na Stabilnim kanalima na Android-u."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse nije uspeo da utvrdi da li je postojao serviser. Probajte sa novijom verzijom Chrome-a."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Šema URL-a manifesta ({scheme}) nije podržana na Android-u."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Početni URL manifesta nije važeći"}, "core/audits/installable-manifest.js | title": {"message": "Manifest veb-aplikacije i serviser zadovoljavaju uslove za instaliranje"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL u manifestu sadrži korisničko ime, lozinku ili port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Stranica ne radi oflajn. Smatraće se da stranica ne može da se instalira posle Chrome-a 93, čija stabilna verzija izlazi u avgustu 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Dozvoljen"}, "core/audits/is-on-https.js | blocked": {"message": "Blokiran"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nebezbedan URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Svi sajtovi treba da budu zaštićeni HTTPS-om, čak i oni koji ne obrađuju osetljive podatke. To obuhvata izbegavanje [mešov<PERSON>g sadržaja](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), odnosno učitavanje nekih resursa preko HTTP-a iako se početni zahtev prikazuje preko HTTPS-a. HTTPS sprečava uljeze da neovlašćeno pristupaju komunikaciji između aplikacije i korisnika ili da je pasivno slušaju. On je preduslov za HTTP/2 i API-je brojnih novih veb-platformi. [Saznajte više o protokolu HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je 1 nebezbed<PERSON> zahtev}one{Pronađen je # nebezbedan zahtev}few{Pronađena su # nebezbedna zahteva}other{Pronađeno je # nebez<PERSON><PERSON>h zahteva}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Ne koristi HTTPS"}, "core/audits/is-on-https.js | title": {"message": "<PERSON><PERSON>i <PERSON>"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatski nadograđen na HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Dozvoljen uz upozorenje"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Ovo je najveći element sadržaja koji je prikazan u oblasti prikaza. [Saznajte više o elementu sa najvećim prikazivanjem sadržaja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element sa najvećim prikazivanjem sadržaja"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS doprinos"}, "core/audits/layout-shift-elements.js | description": {"message": "Ovi DOM elementi najviše doprinose CLS-u stranice. [Saznajte kako da poboljšate CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Izbegavajte velike promene rasporeda"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Slike iznad preloma koje se sporo učitavaju prikazuju se kasnije u ciklusu stranice, što može da izazove kašnjenje najvećeg prikazivanja sadržaja. [Saznajte više o optimalnom sporom učitavanju](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Slika najvećeg prikazivanja sadržaja je sporo učitana"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Slika najvećeg prikazivanja sadržaja nije sporo učitana"}, "core/audits/long-tasks.js | description": {"message": "Navodi najduže zadatke u glavnoj niti, što je korisno za identifikovanje onih koji najviše doprinose kašnjenju prikaza. [Saznajte kako da izbegnete duge zadatke u glavnoj niti](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je # dugi zadatak}one{Pronađen je # dugi zadatak}few{Pronađena su # duga zadatka}other{Pronađeno je # dugih zadataka}}"}, "core/audits/long-tasks.js | title": {"message": "Izbegavajte duge zadatke u glavnoj niti"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Preporučujemo vam da smanjite vreme potrebno za raščlanjivanje, kompajliranje i izvršavanje JS fajlova. Prikazivanje manjih JS resursa će vam možda pomoći u tome. [Saz<PERSON><PERSON><PERSON> kako da smanjite rad glavne niti](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> rad glavne niti"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Smanjuje rad glavne niti"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Da bi imali što više koris<PERSON>, sajtovi treba da rade u svim značajnijim pregledačima. [Saznajte više o kompatibilnosti sa više pregledača](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Sajt radi u različitim veb-pregledačima"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Uverite se da do pojedinačnih stranica vode precizni linkovi preko URL-ova i da su URL-ovi jedinstveni u svrhu deljenja na društvenim medijima. [Saznajte više o pružanju preciznih linkova](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Svaka stranica ima URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Korisnici treba da imaju utisak da su prelazi brzi dok dodiruju stavke, čak i na sporoj mreži. Ovaj doživljaj je ključan za utisak koji će korisnik imati o učinku. [Saz<PERSON>j<PERSON> više o prelascima sa stranice na stranicu](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Prelasci sa stranice na stranicu ne deluju kao da se blokiraju zbog mreže"}, "core/audits/maskable-icon.js | description": {"message": "Ikona koja može da se maskira omogućava da slika prekrije ceo oblik bez vodoravnog oivičenja kada se aplikacija instalira na uređaju. [Saznajte više o ikonama manifesta koje mogu da se maskiraju](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest ne sadrži ikonu koja može da se maskira"}, "core/audits/maskable-icon.js | title": {"message": "Manifest sadr<PERSON>i ikonu koja može da se maskira"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulativni pomak sadržaja stranice meri kretanje vidljivih elemenata unutar oblasti prikaza. [Saznajte više o pokazatelju Kumulativni pomak sadržaja stranice](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interakcija do sledećeg prikazivanja meri brzinu odgovora stranice, koliko dugo stranici treba da vidljivo odgovori na unos korisnika. [Saznajte više o pokazatelju Interakcija do sledećeg prikazivanja](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvo prikazivanje sadržaja označava vreme kada se prikazuju prvi tekst ili slika. [Saznajte više o pokazatelju Prvo prikazivanje sadržaja](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Prvo značajno prikazivanje označava vreme kada primarni sadržaj stranice postaje vidljiv. [Saznajte više o pokazatelju Prvo značajno prikazivanje](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Vreme do početka interakcije je količina vremena koja je potrebna da bi stranica postala potpuno interaktivna. [Saznajte više o pokazatelju Vreme do početka interakcije](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Najveće prikazivanje sadržaja označava trenutak u kojem se prikazuju najveći tekst ili slika. [Saznajte više o pokazatelju Najveće prikazivanje sadržaja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Maksimalno potencijalno kašnjenje prvog prikaza koje može da se desi korisnicima je trajanje najdužeg zadatka. [Saznajte više o pokazatelju Maksimalno potencijalno kašnjenje prvog prikaza](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Indeks brzine prikazuje koliko brzo sadržaj stranice postaje vidljiv za korisnike. [Saznajte više o pokazatelju Indeks brzine](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Zbir svih perioda između FCP-a i vremena do početka interakcije, kada zadatak traje duže od 50 ms, izraženo u milisekundama. [Saznajte više o pokazatelju Ukupno vreme blokiranja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Trajanja povratnog puta (RTT) mreže znatno utiču na učinak. Ako je trajanje povratnog puta do početne lokacije veliko, to zna<PERSON>i da bi serveri koji su bliži korisniku mogli da poboljšaju učinak. [Saznajte više o trajanju povratnog puta](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Traj<PERSON><PERSON> povratnog puta mreže"}, "core/audits/network-server-latency.js | description": {"message": "Kašnjenja servera mogu da utiču na učinak veba. Ako je kašnjenje servera za početnu lokaciju veliko, to zna<PERSON>i da je server preopterećen ili da ima slab pozadinski učinak. [Saznajte više o vremenu odgovora servera](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Pozadinska kašnjenja servera"}, "core/audits/no-unload-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> `unload` se ne pokreće pouzdano i ako se on obrađuje, to može da spreči optimizacije pregledača poput keša za kretanje unazad i unapred. Bo<PERSON><PERSON> da koristite događaje `pagehide` ili `visibilitychange`. [Saznajte više o uklanjanju osluškivača događaja](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Regis<PERSON><PERSON>je obrađivača događaja `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Izbegava obrađivače događaja `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animacije koje nisu kompozitne mogu da budu problematične i da povećavaju kumulativni pomak sadržaja stranice. [Saznajte kako da izbegnete animacije koje nisu kompozitne](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je # animiran element}one{Pronađen je # animiran element}few{Pronađena su # animirana elementa}other{Pronađeno je # animiranih elemenata}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Svojstvo koje se odnosi na filtere može da pomera piksele"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON>j ima drugu animaciju koja je nekompatibilna"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efekat ima kompozitni režim koji nije „replace“"}, "core/audits/non-composited-animations.js | title": {"message": "Izbegavajte animacije koje nisu kompozitne"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Svojstvo vezano za transformaciju zavisi od veličine okvira"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepodržano CSS svojstvo: {properties}}one{Nepodržana CSS svojstva: {properties}}few{Nepodržana CSS svojstva: {properties}}other{Nepodržana CSS svojstva: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekat ima nepodržane parametre vremena"}, "core/audits/performance-budget.js | description": {"message": "Zahteve za količinu i veličinu mreže održavajte ispod granica određenih ciljevima za učinak. [Saznajte više o ograničenjima učinka](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 zahtev}one{# zahtev}few{# zahteva}other{# zahteva}}"}, "core/audits/performance-budget.js | title": {"message": "Cilj za učinak"}, "core/audits/preload-fonts.js | description": {"message": "Predučitajte `optional` fontove da bi novi posetioci mogli da ih koriste. [Saznajte više o predučitavanju fontova](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Fontovi koji koriste `font-display: optional` nisu predučitani"}, "core/audits/preload-fonts.js | title": {"message": "Fontovi koji koriste `font-display: optional` su predučitani"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ako se LCP element dinamički dodaje na stranicu, treba da predučitate sliku da biste poboljšali LCP (najveće prikazivanje sadržaja). [Saznajte više o učitavanju LCP elemenata](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Predučitajte sliku sa najvećim prikazivanjem sadržaja"}, "core/audits/redirects.js | description": {"message": "Preusmeravanja dovode do dodatnih kašnjenja pre učitavanja stranice. [Saznajte kako da izbegnete preusmeravanja stranice](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Izbegavajte višestruka preusmeravanja stranice"}, "core/audits/resource-summary.js | description": {"message": "Da biste podesili ciljeve za količinu i veličinu resursa stranice, dodajte datoteku budget.json file. [Saznajte više o ograničenjima učinka](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 zahtev • {byteCount, number, bytes} KiB}one{# zahtev • {byteCount, number, bytes} Ki<PERSON>}few{# zahteva • {byteCount, number, bytes} Ki<PERSON>}other{# zahteva • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Omogući da broj zahteva i veličine prenosa budu mali"}, "core/audits/seo/canonical.js | description": {"message": "Kanonički linkovi predlažu koji URL treba da se prikaže u rezultatima pretrage. [Saznajte više o kanonič<PERSON> linkovima](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON><PERSON> URL-ova ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Nevažeći URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Usmerava na drugu `hreflang` lokaciju ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Nije apsolutni URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Usmerava na osnovni URL domena (početnu stranicu), umesto ekvivalentne stranice sadržaja"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nema važeću vrednost `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument ima važeći atribut `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON> koji ne može da se popiše"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Pretraživači mogu da koriste `href` atribute na linkovima za popisivanje veb-sajtova. Uverite se da `href` atribut elemenata sidra vodi na odgovarajuće odredište kako bi više stranica sa sajta moglo da se otkrije. [Saznajte kako da omogućite da se linkovi popisuju](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Linkovi ne mogu da se popišu"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Linkovi mogu da se popišu"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Dodatan nečitljiv tekst"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Procenat teksta na stranici"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "V<PERSON><PERSON>ine fontova ispod 12 piksela su premale da bi bile čitljive i zbog njih korisnici na mobilnim uređajima moraju da „zumiraju prstima“ kako bi mogli da čitaju sadržaj. Potrudite se da >60% teksta stranice bude ≥12 piksela. [Saznajte više o čitljivim veličinama fontova](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} čitljivog teksta"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst nije čitljiv jer ne postoji metaoznaka oblasti prikaza koja je optimizovana za ekrane na mobilnim uređajima."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokument ne koristi čitljive veličine fontova"}, "core/audits/seo/font-size.js | legibleText": {"message": "Čitljiv tekst"}, "core/audits/seo/font-size.js | title": {"message": "Dokument koristi čitljive velič<PERSON>"}, "core/audits/seo/hreflang.js | description": {"message": "Linkovi hreflang obaveštavaju pretraživače koju verziju stranice treba da navedu u rezultatima pretrage za dati jezik ili region. [Saznajte više o linkovima `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nema važeći atribut `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativna href vrednost"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument ima važeći atribut `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Neočekivani kôd jezika"}, "core/audits/seo/http-status-code.js | description": {"message": "Stranice sa neuspešnim HTTP kodovima statusa možda neće biti pravilno indeksirane. [Saznajte više o HTTP kodovima statusa](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Stranica ima neuspešan HTTP kôd statusa"}, "core/audits/seo/http-status-code.js | title": {"message": "Stranica ima uspešan HTTP kôd statusa"}, "core/audits/seo/is-crawlable.js | description": {"message": "Pretraživači ne mogu da uvrste stranice u rezultate pretrage ako nemaju dozvolu da ih popisuju. [Saznajte više o direktivama popisivača](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indeksiranje stranice je blokirano"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indeksiranje stranice nije blokirano"}, "core/audits/seo/link-text.js | description": {"message": "Opisni tekst u linkovima pomaže pretraživačima da razumeju sadržaj. [Saznajte kako da učinite linkove pristupačnijim](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je 1 link}one{Pronađen je # link}few{<PERSON>nađena su # linka}other{<PERSON>nađeno je # linkova}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linkovi nemaju opisni tekst"}, "core/audits/seo/link-text.js | title": {"message": "Linkovi imaju opisni tekst"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Pokrenite [alatku za testiranje strukturiranih podataka](https://search.google.com/structured-data/testing-tool/) i [alatku za analiziranje strukturiranih podataka](http://linter.structured-data.org/) da biste procenili strukturirane podatke. [Saznajte više o strukturiranim podacima](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturirani podaci su važeći"}, "core/audits/seo/meta-description.js | description": {"message": "Metaopisi mogu da budu uvršteni u rezultate pretrage da bi pružili sažeti rezime sadržaja stranice. [Saznajte više o metaopisu](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Polje za tekst opisa je prazno."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nema metaopis"}, "core/audits/seo/meta-description.js | title": {"message": "Dokument ima metaopis"}, "core/audits/seo/plugins.js | description": {"message": "Pretraživači ne mogu da indeksiraju sadržaj dodatnih komponenata, a mnogi uređaji ograničavaju dodatne komponente ili ih ne podržavaju. [Saznajte više o izbegavanju dodatnih komponenti](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument koristi dodatne komponente"}, "core/audits/seo/plugins.js | title": {"message": "Dokument izbegava dodatne komponente"}, "core/audits/seo/robots-txt.js | description": {"message": "Ako fajl robots.txt nije pravilno napravljen, popisiva<PERSON>i možda neće moći da razumeju kako želite da se veb-sajt popiše ili indeksira. [Saznajte više o fajlu robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Zahtev za datoteku robots.txt vratio je HTTP status: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Pronađena je 1 greška}one{Pronađena je # greška}few{Pronađene su # greške}other{Pronađeno je # grešaka}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nije uspeo da preuzme datoteku robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Datoteka robots.txt nije važeća"}, "core/audits/seo/robots-txt.js | title": {"message": "Datoteka robots.txt je važeća"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktivni elementi poput dugmadi i linkova treba da budu dovoljno veliki (48×48 piksela) i da imaju dovoljno prostora oko sebe da bi bilo lako da se dodirnu bez preklapanja sa drugim elementima. [Saznajte više o ciljevima za dodir](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ciljeva dodirivanja ima odgovarajuću veličinu"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Ciljevi dodirivanja su premali jer ne postoji metaoznaka oblasti prikaza koja je optimizovana za ekrane na mobilnim uređajima"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Ciljevi dodirivanja nemaju odgovarajuću veličinu"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "<PERSON><PERSON><PERSON> koji se preklapa"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "Ciljevi dodirivanja imaju odgovaraj<PERSON>́u veličinu"}, "core/audits/server-response-time.js | description": {"message": "Vreme odgovora servera za glavni dokument treba da bude kratko jer svi drugi zahtevi zavise od njega. [Saznajte više o pokazatelju Vreme do prvog bajta](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Osnovnom dokumentu je trebalo {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Skratite početno vreme odgovora servera"}, "core/audits/server-response-time.js | title": {"message": "Početno vreme odgovora servera je bilo kratko"}, "core/audits/service-worker.js | description": {"message": "Serviser je tehnologija koja omogućava aplikaciji da koristi mnoge funkcije progresivnih veb-aplikacija, poput oflajn rada, dodavanja na početni ekran i iskačućih obaveštenja. [Saznajte više o serviserima](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Ovu stranicu kontroli<PERSON>e serviser, ali nije pronađen nijedan `start_url` jer nije uspelo raščlanjivanje manifesta kao važeće JSON datoteke"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "<PERSON><PERSON> stranicu kontroli<PERSON> serviser, ali `start_url` ({startUrl}) ne spada u opseg servisera ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> stranicu kontrol<PERSON> serviser, ali nije pronađen nijedan `start_url` jer nijedan manifest nije preuzet."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON><PERSON><PERSON> izvor ima jedan ili više servisera, ali stranica ({pageUrl}) nije u opsegu."}, "core/audits/service-worker.js | failureTitle": {"message": "Ne registruje serviser koji kontroliše stranicu i `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registruje serviser koji kontroliše stranicu i `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Tematski uvodni ekran obezbeđuje kvalitetan doživljaj kada korisnici pokreću aplikaciju sa početnih ekrana. [Saznajte više o uvodnim ekranima](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "<PERSON>je konfigu<PERSON> za prilagođeni uvodni ekran"}, "core/audits/splash-screen.js | title": {"message": "Konfigurisano za prilagođeni uvodni ekran"}, "core/audits/themed-omnibox.js | description": {"message": "Traka za adresu pregledača može da ima temu koja odgovara sajtu. [Saznajte više o tome kako se tematizuje traka za adresu](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON>e podešava boju teme za traku za adresu."}, "core/audits/themed-omnibox.js | title": {"message": "Podešava boju teme za traku za adresu."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (uspeh klijenata)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (d<PERSON><PERSON>tvene mreže)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Proizvod"}, "core/audits/third-party-facades.js | description": {"message": "Neka ugrađivanja trećih strana mogu sporo da se učitavaju. Predlažemo da ih zamenite fasadom dok ne budu potrebna. [Saznajte kako da odložite treće strane pomoću fasade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Dostupna je # alternativna fasada}one{Dostupna je # alternativna fasada}few{Dostupne su # alternativne fasade}other{Dostupno je # alternativnih fasada}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Neki resursi trećih strana mogu odloženo da se učitavaju pomoću fasade"}, "core/audits/third-party-facades.js | title": {"message": "Odloženo učitavajte resurse trećih strana pomoću fasada"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Nezavisni dobavljač"}, "core/audits/third-party-summary.js | description": {"message": "Kôd nezavisnog dobavljača može značajno da utiče na učinak učitavanja. Ograničite broj suvišnih nezavisnih dobavljača usluge i probajte da učitate kôd nezavisnog dobavljača kada stranica primarno završi sa učitavanjem. [Saznajte kako da smanjite uticaj treće strane](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON>d nezavisnog dobavljača je blokirao glavnu nit {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Smanjite uticaj koda nezavisnog dobavljača"}, "core/audits/third-party-summary.js | title": {"message": "Smanjite korišćenje sadržaja treće strane"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Pokazatelj"}, "core/audits/timing-budget.js | description": {"message": "Podesite budžet za brzinu učitavanja da biste lakše pratili učinak sajta. Sajtovi sa dobrim učinkom se učitavaju za kratko vreme i brzo reaguju na događaje unosa korisnika. [Saznajte više o ograničenjima učinka](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Budžet za brzinu učitavanja"}, "core/audits/unsized-images.js | description": {"message": "Podesite eksplicitnu širinu i visinu u elementima slika radi smanjenja prelaza izgleda i poboljšanja kumulativnog pomaka sadržaja stranice. [Saznajte kako da podesite dimenzije slike](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Elementi slika nemaju eksplicitne: `width` i `height`"}, "core/audits/unsized-images.js | title": {"message": "Elementi slika imaju eksplicitne: `width` i `height`"}, "core/audits/user-timings.js | columnType": {"message": "Tip"}, "core/audits/user-timings.js | description": {"message": "Preporučujemo vam da opremite aplikaciju API-jem za vreme korisnika da biste izmerili učinak aplikacije u realnom svetu tokom ključnih korisničkih doživljaja. [Saznajte više o oznakama Vreme korisnika](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 vreme korisnika}one{# vreme korisnika}few{# vremena korisnika}other{# vremena korisnika}}"}, "core/audits/user-timings.js | title": {"message": "Oznake i mere Vremena korisnika"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Povezivanje unapred `<link rel=preconnect>` je pro<PERSON><PERSON><PERSON>a „{securityOrigin}“, ali ga pregledač nije upotrebio. Proverite da li pravilno koristite atribut `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Razmislite o tome da dodate savete za resurse `preconnect` ili `dns-prefetch` kako biste uspostavili rane veze sa važnim izvorima trećih strana. [Saznajte kako da se unapred povežete sa obaveznim izvorima](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Povežite se unapred sa potrebnim izvorima"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Pronađeno je više od 2 povezivanja `<link rel=preconnect>`. Ona treba da se koriste retko i samo do najvažnijih izvora."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Povezivanje unapred `<link rel=preconnect>` je prona<PERSON><PERSON> za „{securityOrigin}“, ali ga pregledač nije upotrebio. Koristite `preconnect` samo za važne izvore koje će stranica sigurno zahtevati."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Predučitavanje `<link>` je pro<PERSON><PERSON><PERSON> za „{preloadURL}“, ali ga pregledač nije upotrebio. Proverite da li pravilno koristite atribut `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Preporučuje<PERSON> vam da koristite `<link rel=preload>` kako biste kasnije tokom učitavanja stranice dali prioritet preuzimanju resursa koji se trenutno traže. [Saznajte kako da unapred učitate ključne zahteve](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Unapred učitajte najvažnije zahteve"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL mape"}, "core/audits/valid-source-maps.js | description": {"message": "Izvorne mape prevode umanjen kôd u originalni izvorni kôd. To pomaže programerima pri otklanjanju grešaka u proizvodnji. <PERSON><PERSON> toga, Lighthouse sadrži dodatni uvid. Ne bi bilo loše da primenite izvorne mape da biste mogli da koristite te prednosti. [Saznajte više o izvornim mapama](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Nedostaju izvorne mape za veliki JavaScript prve strane"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Velikom JavaScript fajlu nedostaje izvorna mapa"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Upozorenje: nedostaje 1 stavka u atributu `.sourcesContent`}one{Upozorenje: nedostaje # stavka u atributu `.sourcesContent`}few{Upozorenje: nedostaju # stavke u atributu `.sourcesContent`}other{Upozorenje: nedostaje # stavki u atributu `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Stranica ima važeće izvorne mape"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` optimizuje aplikaciju za veličine ekrana mobilnih telefona, ali i sprečava [kašnjenje od 300 milisekundi pri unosu korisnika](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Saznajte više o korišćenju metaoznake oblasti prikaza](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON> prona<PERSON> `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Nema oznaku `<meta name=\"viewport\">` sa oznakama `width` ili `initial-scale`"}, "core/audits/viewport.js | title": {"message": "<PERSON><PERSON> ozna<PERSON> `<meta name=\"viewport\">` sa oznakom `width` ili `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "To je rad na blokiranju niti koje se odvija tokom merenja interakcije do sledećeg prikazivanja. [Saznajte više o pokazatelju Interakcija do sledećeg prikazivanja](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms je potroš<PERSON> na događaj „{interactionType}“"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Umanjite rad tokom ključne interakcije"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Kašnjenje unosa"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Kašnjenje pre<PERSON>cije"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Vreme obrade"}, "core/audits/work-during-interaction.js | title": {"message": "Smanjuje rad tokom ključne interakcije"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "To su prilike da poboljšate korišćenje ARIA uloga u aplikaciji, čime može da poboljša doživljaj korisnika tehnologije za pomoć osobama sa invaliditetom, kao što je čitač ekrana."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "To su prilike da pružite alternativni sadržaj za audio i video datoteke. To može da poboljša doživljaj za korisnike sa oštećenim sluhom ili vidom."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk i video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ove stavke ističu uobičajene najbolje prakse u vezi sa pristupačnošću."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Najbolje prakse"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ove provere ističu prilike za [poboljšanje pristupačnosti veb-aplikacije](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatski može da se otkrije samo jedan podskup problema sa pristupačnošću, pa preporučujemo da obavljate i ručno testiranje."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ove stavke obrađuju oblasti koje alatka za automatizovano testiranje ne može da obuhvati. Saznajte više u vodiču o [sprovođenju pregleda pristupačnosti](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Pristupačnost"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "To su prilike da poboljšate čitljivost sadržaja."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "To su prilike da poboljšate tumačenje svog sadržaja za korisnike na različitim jezicima."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizacija i lokalizacija"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "To su prilike da poboljšate semantiku kontrola u aplikaciji. To može da poboljša doživljaj korisnika tehnologije za pomoć osobama sa invaliditetom, kao što je čitač ekrana."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nazivi i oznake"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ovo su prilike da poboljšate kretanje po tastaturi u aplikaciji."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigacija"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ovo su prilike za poboljšanje doživljaja pri čitanju podataka iz tabela ili lista pomoću tehnologije za pomoć osobama sa invaliditetom, poput čitača ekrana."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele i liste"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilnost pregledača"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Najbolje prakse"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Opšte"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Poverenje i bezbednost"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Korisnički doživljaj"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Ciljevima za učinak određuju se standardi za učinak sajta."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Više informacija o učinku aplikacije. Ovi brojevi ne [utiču direktno](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na ocenu učinka."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Dijagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najvažniji aspekt učinka je brzina kojom se pikseli prikazuju na ekranu. Ključni pokazatelji: Prvo prikazivanje <PERSON>aja, Prvo značajno prikazivanje"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Poboljšanja prvog prikazivanja"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON> predlozi mogu da vam pomognu da se stranica učitava brže. Ne [utiču direktno](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na ocenu učinka."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "Po<PERSON>zatelji"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Poboljšajte opšti doživljaj učitavanja da bi stranica počela da se odaziva i da bi bila spremna za korišćenje u najkraćem mogućem roku. Ključni pokazatelji: Vreme početka interakcije, Indeks brzine"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Opšta poboljšanja"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON>inak"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON><PERSON> proverama se proveravaju aspekti progresivne veb-aplikacije. [Saznajte šta čini dobru progresivnu veb-aplikaciju](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ove provere zahteva o<PERSON> [Kontrolna lista za progresivne veb-aplikacije](https://web.dev/pwa-checklist/), ali ih Lighthouse ne sprovodi automatski. One ne utiču na vaš rezultat, ali je važno da ih ručno potvrdite."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "<PERSON>ž<PERSON> da se instalira"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizovano za PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Zahvaljujući ovim proverama stranica će sigurno biti u skladu sa osnovnim savetima za optimizaciju za pretraživače. Ima mnogo dodatnih faktora koje Lighthouse ovde ne uzima u obzir, a koji mogu da utiču na rangiranje u pretrazi, uključujući učinak u [Osnovnim pokazateljima za veb](https://web.dev/learn-core-web-vitals/). [Saznajte više o osnovama Google pretrage](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Pokrećite ove dodatne validatore na sajtu da biste proverili dodatne najbolje prakse optimizacije za pretraživače."}, "core/config/default-config.js | seoCategoryTitle": {"message": "Optimizacija za pretraživače"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatirajte HTML sadržaj na način koji omogućava popisivačima da bolje razumeju sadržaj aplikacije."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Najbolje prakse za sadržaj"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Da bi se aplikacija pojavila u rezultatima pretrage, popisivači treba da imaju pristup do nje."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Popisivanje i indeksiranje"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Uverite se da su stranice prilagođene mobilnim uređajima da korisnici ne bi morali da umanjuju ili uvećavaju prikaz kako bi čitali stranice sa sadržajem. [Saznajte kako da prilagodite stranice mobilnim uređajima](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Prilagođeno mobilnim uređajima"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Izgleda da testirani uređaj ima sporiji procesor nego što  Lighthouse očekuje. To može negativno da utiče na ocenu učinka. Saznajte više o [kalibrisanju odgovarajućeg množioca u vezi sa usporavanjem procesora](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Stranica se možda ne učitava na očekivan način zato što je probni URL ({requested}) preusmeren na {final}. Probajte direktno da testirate drugi URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Učitavanje stranice je bilo presporo da bi se dovršilo u okviru vremenskog ograničenja. Rezultati mogu da budu nepotpuni."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Vremensko ograničenje za brisanje keša pregledača je isteklo. Probajte ponovo da proverite ovu stranicu i prijavite grešku ako se <PERSON> nastavi."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Na ovoj lokaciji možda ima sačuvanih podataka koji utiču na efikasnost učitavanja: {locations}. Proverite tu stranicu u prozoru bez arhiviranja da biste sprečili da ti resursi utiču na rezultate.}one{Na ovim lokacijama možda ima sačuvanih podataka koji utiču na efikasnost učitavanja: {locations}. Proverite tu stranicu u prozoru bez arhiviranja da biste sprečili da ti resursi utiču na rezultate.}few{Na ovim lokacijama možda ima sačuvanih podataka koji utiču na efikasnost učitavanja: {locations}. Proverite tu stranicu u prozoru bez arhiviranja da biste sprečili da ti resursi utiču na rezultate.}other{Na ovim lokacijama možda ima sačuvanih podataka koji utiču na efikasnost učitavanja: {locations}. Proverite tu stranicu u prozoru bez arhiviranja da biste sprečili da ti resursi utiču na rezultate.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Vremensko ograničenje za brisanje izvornih podataka je isteklo. Probajte ponovo da proverite ovu stranicu i prijavite grešku ako se <PERSON> nastavi."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Samo stranice učitane preko GET zahteva ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Samo stranice sa kodom statusa 2XX mogu da se keširaju."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome je otkrio pokušaj izvršavanja JavaScript-a dok je bio u kešu."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Stranice koje su zahtevale AppBanner trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Oznake su onemogućile keširanje cele stranice. Posetite chrome://flags/#back-forward-cache da biste ga omogućili lokalno na uređaju."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Komandna linija je onemogućila keširanje cele stranice."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Keširanje cele stranice je onemogućeno zbog nedovoljno memorije."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Delegat ne podržava keširanje cele stranice."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Funkcija za prikazivanje unapred je onemogućila keširanje cele stranice."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Ova stranica ne može da se kešira jer ima instancu BroadcastChannel sa registrovanim slušaocima."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Stranice sa zaglavljem cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON> je <PERSON><PERSON> obrisan."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Stranica je uklonjena iz keša da bi se dozvolilo keširanje druge stranice."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Stranice koje imaju dodatne komponente trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Stranice koje koriste FileChooser API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Stranice koje koriste API za pristup sistemu fajlova trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Stranice koje koriste dispečer za medijski uređaj ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Reprodukcija iz medija plejera je bila u toku pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Stranice koje koriste MediaSession API i podešavaju status reprodukcije ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Stranice koje koriste MediaSession API i podešavaju obrađivače radnji ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Čitač ekrana je onemogućio keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Stranice koje koriste SecurityHandler trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Stranice koje koriste Serial API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Stranice koje koriste WebAuthetication API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Stranice koje koriste WebBluetooth API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Stranice koje koriste WebUSB API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Stranice koje koriste predviđeni obrađivač ili radni zadatak trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokument nije dovršio učitavanje pre napuštanja dokumenta."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "App Banner je bio aktivan pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome menadžer lozinki je bio aktivan pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM destilacija je bila u toku pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer je bio aktivan pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Keširanje cele stranice je onemogućeno jer su dodaci koristili API za razmenu poruka."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Dodaci sa trajnom vezom treba da zatvore vezu pre keširanja cele stranice."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Dodaci sa trajnom vezom su pokušali da šalju poruke okvirima u keširanju cele stranice."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Keširanje cele stranice je onemogućeno zbog dodataka."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Modalni dijalog kao što je ponovno slanje obrasca ili dijalog za HTTP lozinku je prikazan pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Oflajn stranica je prikazana pri napuštanju."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Traka za intervenciju u vezi sa nedostatkom memorije je bila prisutna pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Došlo je do slanja zahteva za dozvole pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Otkriven je blokator iskačućih prozora pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Prikazani su detalji o Bezbednom pregledanju pri napuštanju stranice."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezbedno pregledanje je otkrilo da ova stranica sadrži zloupotrebu i blokiralo je iskačući prozor."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Serviser je aktiviran dok je stranica bila u procesu keširanja cele stranice."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Keširanje cele stranice je onemogućeno zbog greške u dokumentu."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Stranice koje koriste FencedFrames ne mogu da se skladište u kešu cele stranice."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Stranica je uklonjena iz keša da bi se dozvolilo keširanje druge stranice."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Stranice koje su odobrile pristup za strimovanje medija trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Stranice koje koriste portale trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Stranice koje koriste IdleManager trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Stranice koje imaju otvorenu IndexedDB vezu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "<PERSON><PERSON><PERSON> se <PERSON>-ji koji ne ispunja<PERSON>ju uslove."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Stranice na kojima se StyleSheet umeće pomoću dodataka trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Stranice na kojima se StyleSheet umeće pomoću dodataka trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Interna greška."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Keširanje cele stranice je onemogućeno zbog zahteva za održavanje linka."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Stranice koje koriste zaključavanje tastature trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | loading": {"message": "Stranica nije dovršila učitavanje pre napuštanja stranice."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> č<PERSON> glavni resurs ima cache-control:no-cache ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> č<PERSON> glavni resurs ima cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Kretanje je otkazano pre nego što je stranica mogla da bude vraćena iz keša cele stranice."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Stranica je uklonjena iz keša jer je aktivna mrežna veza primila previše podataka. Chrome ograničava količinu podataka koju stranica može da primi dok je keširana."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stranice koje imaju preuzimanje() ili XHR u toku ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Stranica je uklonjena iz keširanja cele stranice jer je aktivan mrežni zahtev obuhvatao preusmeravanje."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Stranica je uklonjena iz keša jer je mrežna veza bila predugo otvorena. Chrome ograničava vreme koje stranica ima za primanje podataka dok je keširana."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Stranice koje nemaju ispravno zaglavlje odgovora ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Kretanje se desilo u okviru koji nije glavni okvir."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Stranice sa aktivnim indeksiranim DB transakcijama trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Stranice sa aktivnim zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Stranice sa aktivnim zahtevom za preuzimanje mreže trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Stranice sa aktivnim zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Stranice sa aktivnim XHR zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Stranice koje koriste PaymentManager trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Stranice koje koriste funkciju Slika u slici trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | portal": {"message": "Stranice koje koriste portale trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | printing": {"message": "Stranice koje prikazuju korisnički interfejs za štampanje trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Stranica je otvorena pomoću metoda `window.open()`, a druga kartica sadrži referencu na nju ili je stranica otvorila prozor."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Otkazao je proces renderovanja za stranicu u kešu cele stranice."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Proces renderovanja za stranicu u kešu cele stranice je prekinut."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Stranice koje zahtevaju dozvole za snimanje audio sadržaja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Stranice koje zahtevaju dozvole za senzore trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Stranice koje zahtevaju sinhronizaciju u pozadini ili dozvole za preuzimanje trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Stranice koje zahtevaju dozvole za MIDI trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Stranice koje zahtevaju dozvole za obaveštenja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Stranice koje zahtevaju pristup memorijskom prostoru trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Stranice koje zahtevaju dozvole za snimanje videa trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Samo stranice čija šema URL-a je HTTP ili HTTPS mogu da se keširaju."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON> je preuzeo serviser dok je keširanje cele stranice u toku."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Serviser je pokušao da pošalje stranicu koja je u procesu keširanja cele stranice atributu `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Registracija za ServiceWorker je opozvana dok je bilo u toku keširanje cele stranice."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Stranica je uklonjena iz keširanja cele stranice zbog aktivacije servisera."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome je restartovao i obrisao unose keširanja cele stranice."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Stranice koje koriste SharedWorker trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Stranice koje koriste SpeechRecognizer trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Stranice koje koriste SpeechSynthesis trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "iframe na stranici je započeo kretanje koje se nije završilo."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-cache ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Stranica je premašila maksimalno vreme za keširanje cele stranice i istekla je."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Isteklo je vreme da stranica pristupi keširanju cele stranice (verovatno zbog obrađivača sakrivanja stranice koji su dugo bili pokrenuti)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Stranica ima unload obrađivač u glavnom okviru."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Stranica ima unload obrađivač u podokviru."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Pregledač je promenio zaglavlje zamene korisničkog agenta."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Stranice koje su odobrile pristup za snimanje video ili audio sadržaja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Stranice koje koriste WebDatabase trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Stranice koje koriste WebHID trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Stranice koje koriste WebLocks trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Stranice koje koriste WebNfc trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Stranice koje koriste WebOTPService trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Stranice sa WebRTC-om ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Stranice koje koriste WebShare trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Keširanje celih stranica sa WebSocket-om nije moguće."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Stranice sa WebTransport-om ne mogu da pristupe keširanju cele stranice."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Stranice koje koriste WebXR trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Možete da dodate https: i http: URL šeme (koje ignorišu pregledači koji podržavaju strict-dynamic) radi kompatibilnosti unazad sa starijim pregledačima."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Direktiva disown-opener je zastarela od smernica CSP3. Koristite zaglavlje za Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Direktiva referrer je zastarela od smernica CSP2. Koristite zaglavlje za Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Direktiva reflected-xss je zastarela od smernica CSP2. Koristite zaglavlje za X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Base-uri koji nedostaje omogućava umetnutim <PERSON> <base> da podese osnovni URL za sve relativne URL-ove (npr. skripte) do domena koji kontroliše napadač. Savetujemo vam da podesite base-uri na „none“ ili „self“."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Ako direktiva object-src nedostaje, dozvoljeno je umetanje dodatnih komponenti koje izvršavaju nebezbedne skripte. Savetujemo vam da podesite direktivu object-src na „none“ ako možete."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Direktiva script-src nedostaje. To može da omogući izvršavanje nebezbednih skripti."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Da li ste zaboravili tačku i zaraz? Izgleda da je {keyword} direkiva, a ne ključna reč."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Jednokratni ključevi moraju da koriste base64 charset."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Jednokratni ključevi moraju da imaju bar 8 znakova."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Izbegavajte da koristite obične URL šeme ({keyword}) u ovoj direktivi. Obične URL šeme dozvoljavaju da izvor skripti bude nebezbedan domen."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Izbegavajte da koristite obične džokerske znake ({keyword}) u ovoj direktivi. Obični džokerski znaci omogućavaju da izvor skripti bude nebezbedan domen."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Odredište za izveštaje se konfiguriše samo preko direktive report-to. Ova direktiva je podržana samo u pregledačima zasnovanim na Chromium-u, pa se preporučuje da koristite direktivu report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nijedan CSP ne konfiguriše odredište za izveštaje. Time se otežava održavanje CSP-a tokom vremena i praćenje otkazivanja."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Liste dozvoljenih hostova često mogu da se zaobiđu. Savetujemo vam da koristite CSP jednokratne ključeve ili <PERSON>, uz „strict-dynamic“ ako je potrebno."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Nepoznata CSP direktiva."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON><PERSON> da je {keyword} nevažeća ključ<PERSON> reč."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Direktiva unsafe-inline omogućava izvršavanje nebezbednih skripti i obrađivača događaja na stranici. Savetujemo vam da koristite CSP jednokratne ključeve ili heševe da biste pojedinačno dozvoljavali skripte."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Savetujemo vam da dodate direktivu unsafe-inline (koju ignorišu pregledači koji podržavaju jednokratne ključeve/hešove) radi kompatibilnosti unazad sa starijim pregledačima."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Ovlašćenje neće biti pokriveno simbolom džokerskog znaka (*) u okviru CORS upravljanja atributom `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Zahtevi za resurse čiji URL-ovi su sadržali uklonjene znakove za razmak (`(n|r|t)`) i znakove „manje-od“ (`<`) su blokirani. Uklonite nove redove i kodirajte znakove „manje-od“ iz izvora kao što su vrednosti atributa elemenata da bi se učitali ti resursi."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "Atribut `chrome.loadTimes()` je zastareo, pa koristite standardizovani API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "Atribut `chrome.loadTimes()` je zastareo, pa koristite standardizovani API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` je zastareli tip, pa koristite standardizovani API: `nextHopProtocol` za Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "<PERSON><PERSON>čići koji sadrže znak `(0|r|n)` će biti od<PERSON>je<PERSON>, a ne skraćeni."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Popuštanje smernica za isto poreklo podešavanjem atributa `document.domain` je zastarelo i biće podrazumevano onemogućeno. Ovo upozorenje o zastarevanju se odnosi na pristup različitog porekla koji je omogućen podešavanjem atributa `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Pokretanje {PH1} iz iframe-ova različitog porekla je zastarelo i ukloniće se u budućnosti."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Atribut `disableRemotePlayback` treba da se koristi da bi se onemogućila podrazumevana integracija za prebacivanje umesto korišćenja birača `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} je z<PERSON><PERSON><PERSON> tip. Umesto njega koristite {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Ovo je primer prevedene poruke o problemu sa zastarevanjem."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Popuštanje smernica za isto poreklo podešavanjem atributa `document.domain` je zastarelo i biće podrazumevano onemogućeno. Da biste nastavili da koristite ovu funkciju onemogućite grupe agenata sa ključevima prema poreklu tako što ćete poslati zaglavlje atributa `Origin-Agent-Cluster: ?0` zajedno sa HTTP odgovorom za dokument i okvire. Pogledajte https://developer.chrome.com/blog/immutable-document-domain/ za više detalja."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Atribut `Event.path` je zastareo i ukloniće se. Umesto njega koristite `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Zaglavlje `Expect-CT` je zastarelo i biće uklonjeno. Chrome zahteva transparentnost sertifikata za sve javno pouzdane sertifikate izdate posle 30. aprila 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Više detalja potražite na stranici za status funkcije."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` i`watchPosition()` vi<PERSON>e ne rade sa nebezbednim poreklom. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` i`watchPosition()` su zastareli ili nebezbedni izvori. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` vi<PERSON>e ne radi sa nebezbednim poreklom. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` je zastareli tip. Umesto njega koristite `RTCPeerConnectionIceErrorEvent.address` ili `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Poreklo prodavca i proizvoljni podaci iz događaja servisera `canmakepayment` su zastareli i biće uklonjeni: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Veb-sajt je zatražio podizvor sa mreže kojoj je mogao da pristupi samo zbog privilegovane mrežne pozicije svojih korisnika. Ovi zahtevi izlažu uređaje i servere koji nisu javni internetu, čime se povećava rizik od napada falsifikovanjem zahteva sa drugih sajtova (CSRF) i/ili curenja informacija. Da bi ublažio ove rizike, Chrome zastareva zahteve ka podizvorima koji nisu javni kada se pokrenu iz nebezbednog konteksta i počeće da ih blokira."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS ne može da se učita sa `file:` URL-ova ako se ne završavaju ekstenzijom fajla `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Korišćenje atributa `SourceBuffer.abort()` da bi se otkazalo uklanjanje asinhronog opsega za `remove()` je zastarelo zbog promene specifikacije. Podrška će se ukloniti u budućnosti. Umesto njega treba da slušate događaj `updateend`. `abort()` ima za cilj samo da otkaže dodavanje asinhronih medija ili da resetuje stanje raščlanjivača."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Podešavanje atributa `MediaSource.duration` ispod najviše vremenske oznake prezentacije bilo kojih baferovanih kodiranih okvira je zastarelo zbog promene specifikacije. Podrška za implicitno uklanjanje skraćenog baferovanog medijskog sadržaja će se ukloniti u budućnosti. Umesto toga treba da izvršite eksplicitni `remove(newDuration, oldDuration)` na sve `sourceBuffers`, gde je `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "<PERSON>va izmena će stupiti na snagu sa ciljem {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI će zatražiti dozvolu za korišćenje čak i ako SysEx nije naveden u atributu `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "API za obaveštenja više ne sme da se koristi iz nezbezbednog porekla. Razmislite o prebacivanju aplikacije na bezbedno poreklo, kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Dozvola za API za obaveštenja više ne može da se traži od iframe-a različitog porekla. Razmislite o tome da zatražite dozvolu od okvira najvišeg nivoa ili da otvorite novi prozor."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Vaš partner <PERSON><PERSON><PERSON> (D)TLS verziju. Proverite sa partnerom da biste ispravili ovo."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL u nebezbednim kontekstima je zastareo i uskoro će biti uklonjen. Koristite memorijski prostor na vebu ili indeksiranu bazu podataka."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON> nave<PERSON>e `overflow: visible` na oznakama img, video i canvas, one mogu da dovedu do pravljenja vizuelnog sadržaja van granica elementa. Pogledajte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` je zastareli tip. Bolje da koristite jednokratno instaliranje za obrađivače plaćanja."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest` poziv je zaobišao direktivu `connect-src` za smernice za bezbednost sadržaja (CSP). Ovo zaobilaženje je zastarelo. Dodajte identifikator načina plaćanja iz API-ja `PaymentRequest` (u polju `supportedMethods`) u direktivu `connect-src` za CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` je z<PERSON><PERSON> tip. Koristite standardizovani `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Atribut `<source src>` sa nadređenim elementom `<picture>` je neva<PERSON>, pa se ignoriše. Umesto njega koristite `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` je zastareli tip. Koristite standardizovani `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Blokirani su zahtevi za podresurse čiji URL-ovi sadrže ugrađene akreditive (npr. `**********************/`)."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Ograničenje `DtlsSrtpKeyAgreement` je uklonjeno. Naveli ste vrednost `false` za ovo ograni<PERSON>, što se tumači kao pokušaj korišćenja uklonjenog metoda `SDES key negotiation`. <PERSON>va funkcija je uklonjena, pa koristite uslugu koja podržava `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Ograničenje `DtlsSrtpKeyAgreement` je uklonjeno. Naveli ste vrednost `true` za ovo ograni<PERSON>je, što nije imalo efekta, ali možete da uklonite ovo ograničenje radi preglednosti."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Otkriven je atribut `Complex Plan B SDP`. Dijalekt za `Session Description Protocol` više nije podržan. Umesto njega koristite `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, koji se koristi pri izradi atributa `RTCPeerConnection` uz `{sdpSemantics:plan-b}` je zastarela nestandardna verzija atributa `Session Description Protocol` koji je trajno izbrisan sa veb-platforme. I dalje je dostupan pri izradi uz `IS_FUCHSIA`, ali <PERSON><PERSON><PERSON><PERSON> da ga izbrišemo čim to bude moguće. Obustavite zavisnost od njega. Pogledajte https://crbug.com/1302249 za status."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Opcija `rtcpMuxPolicy` je zastarela i biće uklonjena."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` c<PERSON>e zahtevati izolaciju od pristupa iz drugih izvora. Pogledajte https://developer.chrome.com/blog/enabling-shared-array-buffer/ za više detalja."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Atribut `speechSynthesis.speak()` bez aktivacije korisnika je zastareo i ukloniće se."}, "core/lib/deprecations-strings.js | title": {"message": "Koristi se zastarela funkcija"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Dodaci treba da omoguće izolaciju od pristupa iz drugih izvora da biste i dalje koristil<PERSON> `SharedArrayBuffer`. Pogledajte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} zavisi od prodavca. Koristite standardni {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "JSON za odgovor ne podržava UTF-16 u atributu `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhroni `XMLHttpRequest` u glavnoj niti je zastareo zbog negativnog uticaja na doživljaj krajnjeg korisnika. Dodatnu pomoć potražite na https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` je zastareli tip. Koristite `isSessionSupported()` i proverite rešenu logičku vrednost."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Period blokiranja glavne niti"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Vreme preživljavanja keša"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Opis"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementi koji nisu pro<PERSON> proveru"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Lokacija"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Premašuje cilj"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Veličina resursa"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tip resursa"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Veličina"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Vreme početka"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Provedeno vreme"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Velič<PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potencijalna ušteda"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potencijalna ušteda"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potencijalna ušteda od {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Pronađen je 1 element}one{Pronađen je # element}few{Pronađena su # elementa}other{Pronađeno je # elemenata}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potencijalna ušteda od {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Prvo značajno prikazivanje"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Slika"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interakcija do sledećeg prikazivanja"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Visoka"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Srednja"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maks. potencijalno kašnjenje prvog prikaza"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Drugo"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON> resursi"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Skripta"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sek"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Opis stila"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Nezavisni resursi"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Ukupno"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Došlo je do greške pri evidentiranju traga tokom učitavanja stranice. Ponovo pokrenite Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Vremensko ograničenje čekanja na inicijalnu vezu za protokol programa za otklanjanje grešaka."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome nije prikupio nijedan snimak ekrana tokom učitavanja stranice. Uverite se da je sadržaj vidljiv na stranici, pa probajte da ponovo pokrenete Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS serveri nisu mogli da razreše navedeni domen."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> za obavezni resurs {artifactName} je naišao na grešku: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Došlo je do interne greške u Chrome-u. Ponovo pokrenite Chrome i probajte da ponovo pokrenete Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Prik<PERSON><PERSON><PERSON><PERSON> za obavezni resurs {artifactName} se nije pokrenuo."}, "core/lib/lh-error.js | noFcp": {"message": "Stranica nije prikazala nikakav sadržaj. Uverite se da je prozor pregledača u prvom planu tokom učitavanja i probajte ponovo. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Na stranici se nije prikazao sadržaj koji se kvalifikuje kao najveće prikazivanje sadržaja (LCP). Uverite se da stranica ima važeći LCP element i probajte ponovo. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Navedena stranica nije HTML (prikazuje se kao {mimeType} MIME tipa)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ova verzija Chrome-a je prestara da bi podržavala „{featureName}“. Koristite noviju verziju da biste videli kompletne rezultate."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nije uspeo da pouzdano učita stranicu koju ste zahtevali. Uverite se da testirate odgovarajući URL i da server pravilno odgovara na sve zahteve."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nije uspeo da pouzdano učita URL koji ste zahtevali jer je stranica prestala da reaguje."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL koji ste naveli nema važeći bezbednosni sertifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome je sprečio učitavanje stranice sa tranzitivnim oglasom. Uverite se da testirate odgovarajući URL i da server pravilno odgovara na sve zahteve."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nije uspeo da pouzdano učita stranicu koju ste zahtevali. Uverite se da testirate odgovarajući URL i da server pravilno odgovara na sve zahteve. (Detalji: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nije uspeo da pouzdano učita stranicu koju ste zahtevali. Uverite se da testirate odgovarajući URL i da server pravilno odgovara na sve zahteve. (Kôd statusa: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Učitavanje stranice je trajalo predugo. Pratite prilike u izveštaju da biste skratili vreme učitavanja stranice, pa ponovo pokrenite Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Od<PERSON><PERSON> protokola DevTools se čeka duže od dodeljenog perioda. (Metod: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Preuzimanje sadržaja resursa traje duže od dodeljenog perioda."}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON>zgleda da je URL koji ste naveli nevažeći."}, "core/lib/navigation-error.js | warningXhtml": {"message": "MIME tip stranice je XHTML: Lighthouse ne podržava izričito ovaj tip dokumenta"}, "core/user-flow.js | defaultFlowName": {"message": "Koris<PERSON>čki tok ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Izveštaj o navigaciji ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Izveš<PERSON>j sa pregledom ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Iz<PERSON><PERSON><PERSON>j za period ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Svi izveštaji"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kategorije"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Pristupačnost"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Najbolje prakse"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON>inak"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresivna veb-aplikacija"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "Optimizacija za pretraživače"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Računar"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Razumevanje izveštaja o toku za Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Koristite izveštaje o navigaciji za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Koristite izveštaje sa pregledom za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Koristite izveštaje za period za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Preuzmite Lighthouse ocenu učinka."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Izmerite pokazatelje učinka za učitavanje stranice, kao što su najveće prikazivanje sadržaja i indeks brzine."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Pristupite mogućnostima progresivnih veb-aplikacija."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Pronađite probleme sa pristupačnošću u aplikacijama sa jednom stranicom ili kompleksnim formama."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Procenite najbolje prakse za menije i elemente korisničkog interfejsa sakrivene iza interakcije."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Izmerite vreme izvršavanja prelaza izgleda i JavaScript-a za seriju interakcija."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Otkrijte prilike za učinak da biste poboljšali doživljaj za dugoročne stranice i aplikacije sa jednom stranicom."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Najveći uticaj"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativna provera}one{{numInformative} informativna provera}few{{numInformative} informativne provere}other{{numInformative} informativnih provera}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Učitavanje stranice"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Izveštaji o navigaciji analiziraju učitavanje pojedinačne stranice, potpuno isto kao originalni Lighthouse izveštaji."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Izveštaj o navigaciji"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} izveštaj o navigaciji}one{{numNavigation} izveštaj o navigaciji}few{{numNavigation} izveštaja o navigaciji}other{{numNavigation} izveštaja o navigaciji}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} provera koja može da se prođe}one{{numPassableAudits} provera koja može da se prođe}few{{numPassableAudits} provere koje mogu da se prođu}other{{numPassableAudits} provera koje mogu da se prođu}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{<PERSON>š<PERSON> ste {numPassed} proveru}one{Prošli ste{numPassed} proveru}few{Prošli ste{numPassed} provere}other{<PERSON>šli ste{numPassed} provera}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Prosek"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Greška"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Slabo"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Dobro"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Sačuvaj"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Snimljeno stanje stranice"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Izveštaji sa pregledom analiziraju stranicu u posebnom stanju, obično posle interakcije sa korisnicima."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Izveštaj sa pregledom"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} izve<PERSON><PERSON><PERSON> sa pregledom}one{{numSnapshot} izve<PERSON><PERSON>j sa pregledom}few{{numSnapshot} izve<PERSON><PERSON><PERSON> sa pregledom}other{{numSnapshot} izve<PERSON><PERSON><PERSON> sa pregledom}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Korisničke interakcije"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Izveštaji za period analiziraju nasumični period, koji obično sadrži interakcije korisnika."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Izveštaj za period"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} izveštaj za period}one{{numTimespan} izveštaj za period}few{{numTimespan} izveš<PERSON>ja za period}other{{numTimespan} izveštaja za period}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Izveštaj o korisničkom toku za Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Kada je u pitanju animirani sadr<PERSON>, koris<PERSON> [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) da biste smanjili korišćenje procesora kada sadržaj nije na ekranu."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Razmislite o tome da prikažete sve komponente [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) u WebP formatima uz navođenje prikladnog rezervnog rešenja za druge pregledače. [Saznaj<PERSON> više](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Uverite se da koristite oznake [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) za slike kako bi se one automatski odloženo učitavale. [Saznajte više](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Ko<PERSON><PERSON> alat<PERSON> kao <PERSON> j<PERSON> [AMP optimizator](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) da biste [prikazivali AMP rasporede na serveru](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Pogledajte [dokumentaciju za AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) da biste se uverili da su svi stilovi podržani."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponenta [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) podržava atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) da bi naznačila koje slikovne elemente da koristi na osnovu veličine ekrana. [Saznajte više](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Razmislite o tome da koristite virtuelno pomeranje pomoću Component Dev Kit-a (CDK) ako se prikazuju veoma velike liste. [Saznajte više](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Primenite [razdvajanje koda na nivou rute](https://web.dev/route-level-code-splitting-in-angular/) da biste smanjili veličinu JavaScript paketa. Takođe razmislite o tome da unapred keširate elemente pomoću [servise<PERSON>](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON>ko koristite Angular CLI, uverite se da su verzije generisane u proizvodnom režimu. [Saznajte više](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Ako koristite Angular CLI, uvrstite mape izvora u proizvodnu verziju da biste pregledali pakete. [Saznajte više](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Učitajte rute unapred da biste ubrzali navigaciju. [Saznajte više](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Razmislite o tome da koristite uslužni program `BreakpointObserver` u Component Dev Kit-u (CDK) da biste upravljali prelomnim tačkama slika. [Saznajte više](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Predlažemo da otpremite GIF u uslugu koja će ga kodirati za ugradnju kao HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Navedite `@font-display` kad definišete prilagođene fontove u temi."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Predlažemo da konfigurišete [WebP formate slika pomoću stila Konverzija slike](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) na sajtu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instalirajte [Drupal modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) koji može odloženo da učitava slike. Takvi moduli pružaju mogućnost odlaganja svih slika van ekrana da bi se poboljšao učinak."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Predlažemo da koristite modul za ugradnju ključnog CSS-a i JavaScipt-a ili potencijalno asinhrono učitavanje elemenata putem JavaScript-a, poput modula [Napredno CSS/JS grupisanje](https://www.drupal.org/project/advagg). Imajte na umu da optimizacije koje pruža ovaj modul mogu da oštete sajt, pa ćete verovatno morati da unesete neke promene u kôd."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Teme, moduli i specifikacije servera doprinose vremenu odgovora servera. Predlažemo da pronađete optimizovaniju temu, pažljivo izaberete modul za optimizaciju i/ili nadogradite server. Serveri za hostovanje treba da koriste keširanje PHP opkoda, keširanje memorije radi smanjenja vremena odgovaranja na upit iz baze podataka, poput Redis-a ili Memcached-a, kao i optimizovanu logiku aplikacije radi brže pripreme stranica."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Predlažemo da koristite [prilagodljive stilove slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) kako biste smanjili veličinu slika koje se učitavaju na stranici. Ako koristite Views (prikazi) da biste prikazali više stavki sadržaja na stranici, razmislite o primeni numerisanja stranica da biste ograničili broj stavki sadržaja koje se prikazuju na određenoj stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Uverite se da ste omogućili Aggregate CSS files (Grupisanje CSS datoteka) na stranici Administration (Administracija) » Configuration (Konfiguracija) » Development (Razvoj). Možete i da konfigurišete naprednije opcije grupisanja putem [dodatnih modula](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kako biste ubrzali sajt nadovezivanjem, umanjivanjem i komprimovanjem CSS stilova."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Uverite se da ste omogućili Aggregate JavaScript files (Grupisanje JavaScript datoteka) na stranici Administration (Administracija) » Configuration (Konfiguracija) » Development (Razvoj). Možete i da konfigurišete naprednije opcije grupisanja putem [dodatnih modula](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kako biste ubrzali sajt nadovezivanjem, umanjivanjem i komprimovanjem JavaScript elemenata."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Predlažemo da uklonite CSS pravila koja se ne koriste i priložite samo neopohdne Drupal biblioteke relevantnoj stranici ili komponenti na stranici. Detalje potražite na [linku za Drupal dokumentaciju](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Da biste identifikovali priložene biblioteke koje dodaju suvišan CSS, probajte da pokrenete proveru [pokrivenosti koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatki Chrome DevTools. Spornu temu ili modul možete da identifikujete u URL-u opisa stila kad je na Drupal sajtu onemogućeno CSS grupisanje. Potražite teme ili module koji na listi imaju mnogo opisa stila sa dosta crvenih elemenata u pokrivenosti koda. Tema ili modul treba da stave opis stila na listu samo ako se on stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Predlažemo da uklonite JavaScript elemente koji se ne koriste i priložite samo neophodne Drupal biblioteke relevantnoj stranici ili komponenti na stranici. Detalje potražite na [linku za Drupal dokumentaciju](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Da biste identifikovali priložene biblioteke koje dodaju suvišan JavaScript, probajte da pokrenete [prosek upotrebe koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatki Chrome DevTools. Spornu temu ili modul možete da identifikujete u URL-u skripte kad je na Drupal sajtu onemogućeno JavaScript grupisanje. Potražite teme ili module koji na listi imaju mnogo skripti sa dosta crvenih elemenata u proseku upotrebe koda. Tema ili modul treba da stave skriptu na listu samo ako se ona stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Podesite Browser and proxy cache maximum age (Maksimalna starost keša pregledača i proksija) na stranici Administration (Administracija) » Configuration (Konfiguracija) » Development (Razvoj). Pročitajte više o [Drupal kešu i optimizaciji za bolje performanse](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Predlažemo da koristite [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) koji automatski optimizuje i smanjuje veličinu slika koje se otpremaju preko sajta bez gubitka kvaliteta. Uverite se i da koristite izvorne [stilove prilagodljivih slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) koje pruža Drupal (dostupni u Drupal-u 8 i novijim verzijama) za sve slike koje se renderuju na sajtu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Savete za resurse za povezivanje unapred ili predučitavanje DNS-a možete da dodate instaliranjem i konfigurisanjem [modula](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) koji pruža infrastrukturu za savete za resurse korisničkog agenta."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Uverite se da koristite izvorne [stilove prilagodljivih slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) koje pruža Drupal (dostupni u Drupal-u 8 i novijim verzijama). Koristite stilove prilagodljivih slika pri renderovanju polja za slike preko režima prikaza, prikaza ili slika otpremljenih putem WYSIWYG uređivača."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Optimize Fonts` da biste automatski iskoristili CSS funkciju `font-display` kako bi tekst bio vidljiv korisnicima dok se veb-fontovi učitavaju."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Next-Gen Formats` da biste konvertovali slike u WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `<PERSON><PERSON> Images` da biste odložili učitavanje slika van ekrana dok ne budu potrebne."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Critical CSS` i `Script Delay` da biste odložili nekritični JS ili CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Koristite [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) da biste keširali sadržaj na svetskoj mreži i poboljšali vreme do prvog bajta."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Minify CSS` da biste automatski umanjili CSS kako bi se smanjile veličine mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Minify Javascript` da biste automatski umanjili JS radi smanjenja veličine mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Remove Unused CSS` radi lakšeg rešavanja ovog problema. Time se identifikuju CSS klase koje se stvarno koriste na svakoj stranici vašeg sajta i uklanjaju se sve druge da bi veličina datoteke ostala mala."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Efficient Static Cache Policy` da biste podesili preporučene vrednosti u zaglavlju keširanja za statične elemente."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Next-Gen Formats` da biste konvertovali slike u WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Pre-Connect Origins` da biste automatski dodali savete za `preconnect` resurse radi uspostavljanja ranih veza sa važnim izvorima trećih strana."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Preload Fonts` i `Preload Background Images` da biste dodali linkove za `preload` kako biste dali prednost preuzimanju resursa koji se trenutno traže kasnije tokom učitavanja stranice."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Resize Images` da biste promenili veličinu slika tako da odgovara uređaju i time smanjili veličine mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Predlažemo da otpremite GIF u uslugu koja će ga kodirati za ugradnju kao HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Preporu<PERSON><PERSON><PERSON><PERSON> da koristite [dodatnu komponentu](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ili uslugu koja automatski konvertuje otpremljene slike u optimalne formate."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instalirajte [Jo<PERSON><PERSON> dodatnu komponentu za odlo<PERSON>eno učitavanje](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) koja omogućava da odložite sve slike van ekrana ili pređite na šablon koji pruža tu funkciju. Počev od verzije Joomla 4.0, sve nove slike će [automatski](https://github.com/joomla/joomla-cms/pull/30748) dobiti atribut `loading` od osnovnih funkcija."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Mnoge Joomla dodatne komponente mogu da vam pomognu da [ugradite ključne elemente](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ili [odložite manje važne resurse](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Imajte na umu da optimizacije koje pružaju te dodatne komponente mogu da oštete funkcije šablona ili dodatnih komponenti, pa morate detaljno da ih testirate."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, dodaci i specifikacije servera doprinose vremenu odgovora servera. Predlažemo da pronađete optimizovaniji šablon, pažljivo izaberete dodatak za optimizaciju i/ili nadogradite server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Predlažemo da prikažete odlomke u kategorijama članaka (npr. preko linka „pročitajte više“), smanjite broj članaka koji se prikazuju na određenoj stranici, razdvojite dugačke postove na više stranica ili koristite dodatne komponente za odloženo učitavanje komentara."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> do<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) mogu da ubrzaju sajt nadovezivanjem, umanjivanjem i komprimovanjem CSS stilova. Postoje i šabloni koji pružaju tu funkciju."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Mnogi [<PERSON><PERSON><PERSON> do<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) mogu da ubrzaju sajt nadovezivanjem, umanjivanjem i komprimovanjem skripti. Postoje i šabloni koji pružaju tu funkciju."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Predlažemo da izostavite ili zamenite [<PERSON><PERSON><PERSON> dodatke](https://extensions.joomla.org/) koji na stranici učitavaju CSS koji se ne koristi. Da biste identifikovali dodatke koji dodaju suvišan CSS, probajte da pokrenete proveru [pokrivenosti koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatki Chrome DevTools. Spornu temu ili dodatnu komponentu možete da identifikujete u URL-u opisa stila. Potražite dodatne komponente koje na listi imaju mnogo opisa stilova sa dosta crvenih elemenata u pokrivenosti koda. Dodatna komponenta treba da stavi opis stila na listu samo ako se on stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Predlažemo da izostavite ili zamenite [Jo<PERSON><PERSON> dodatke](https://extensions.joomla.org/) koji na stranici učitavaju JavaScript koji se ne koristi. Da biste identifikovali dodatne komponente koje dodaju suvišan JS, probajte da pokrenete proveru [pokrivenosti koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatki Chrome DevTools. Sporni dodatak možete da identifikujete u URL-u skripte. Potražite dodatke koji na listi imaju mnogo skripti sa dosta crvenih elemenata u pokrivenosti koda. Dodatak treba da stavi skriptu na listu samo ako se ona stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Pročitajte više o [keširanju pregledača u sistemu Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Predla<PERSON><PERSON><PERSON> da koristite [dodatnu komponentu za optimizaciju slika](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) koja komprimuje slike bez gubitka kvaliteta."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Predla<PERSON>em<PERSON> da koristite [dodatne komponente za prilagodljive slike](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) kako biste koristili prilagodljive slike u sadržaju."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Možete da omogućite komprimovanje teksta tako što ćete omogućiti Gzip komprimovanje stranica u sistemu Joomla (System (Sistem) > Global configuration (Globalna konfiguracija) > Server (Server))."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "<PERSON>ko ne pravite pakete JavaScript elemenata, razmislite o tome da koristite [bejler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Onemogućite ugrađene [funkcije pravljenja paketa i umanjivanja u JavaScript-u](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) na platformi Magento i razmotrite korišćenje [bejlera](https://github.com/magento/baler/) umesto toga."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Naznačite oznaku `@font-display` kada [definišete prilagođene fontove](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Razmislite o tome da potražite mnoštvo dodataka trećih strana na [Magento Marketplace-u](https://marketplace.magento.com/catalogsearch/result/?q=webp) da biste iskoristili novije formate slika."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Razmislite o tome da izmenite šablone proizvoda i kataloga da biste iskoristili funkciju [odlo<PERSON><PERSON>g učitavanja](https://web.dev/native-lazy-loading) na veb-platformi."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Koristite [Varnish integraciju](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) platforme Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Omogućite opciju „Umanji CSS datoteke“ u podešavanjima za programere u prodavnici. [Saznajte više](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [Terser](https://www.npmjs.com/package/terser) da biste umanjili sve JavaScript elemente iz primene statičnog sadržaja i onemogućili ugrađenu funkciju umanjivanja."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Onemogućite ugrađeno [pravljenje JavaScript paketa](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) na platformi Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Razmislite o tome da potražite mnoštvo dodataka trećih strana na [Magento Marketplace-u](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) da biste optimizovali slike."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Savete za resurse za povezivanje unapred ili pripremu učitavanja DNS-a možete da dodate ako[izmenite izgled teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> `<link rel=preload>` mogu da se dodaju [izmenom izgleda teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image`, a ne `<img>`, da biste automatski optimizovali format slika. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image`, a ne `<img>`, da biste automatski sporo učitavali slike. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image` i postavite „priority“ na „true“ da biste unapred učitali LCP sliku. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> komponentu `next/script` da biste odložili učitavanje skripta treće strane koje nisu toliko važne. [Saznajte više](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image` da biste se uverili da su slike uvek odgovarajuće veličine. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Preporučujemo vam da podesite `PurgeCSS` u konfiguraciji komponente `Next.js` da biste uklonili neiskorišćena pravila iz opisa stilova. [Saznajte više](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Ko<PERSON>ite `Webpack Bundle Analyzer` da biste otkrili neiskorišćeni JavaScript kôd. [Saznajte više](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Preporučuje<PERSON> vam da koristite `Next.js Analytics` za merenje učinka aplikacije u stvarnom svetu. [Saznajte više](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurišite keširanje za nepromenljive elemente i `Server-side Rendered` (SSR) stranice. [Saznajte više](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image`, a ne `<img>`, da biste podesili kvalitet slike. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image` da biste podesili odgo<PERSON> `sizes`. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Omogućite komprimovanje na Next.js serveru. [Saznaj<PERSON> viš<PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> komponentu `nuxt/image` i podesite `format=\"webp\"`. [Saznajte više](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Ko<PERSON><PERSON> komponentu `nuxt/image` i podesite `loading=\"lazy\"` za slike van ekrana. [Saznajte više](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponentu `nuxt/image` i navedite `preload` za LCP sliku. [Saznajte više](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON><PERSON><PERSON> komponentu `nuxt/image` i navedite eksplicitne atribute `width` i `height`. [Saznaj<PERSON> više](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Ko<PERSON><PERSON> komponentu `nuxt/image` i podesite odgovarajući `quality`. [Saznajte više](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponentu `nuxt/image` i podesite odgovarajuće `sizes`. [Saznajte više](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Zamenite animirane GIF-ove video sadržajem](https://web.dev/replace-gifs-with-videos/) da bi se veb-stranice brže uč<PERSON>vale, a predlažemo i da koristite moderne formate fajlova, kao <PERSON>to je [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ili [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), da biste poboljšali efikasnost komprimovanja za više od 30% u odnosu na aktuelni najsavremeniji video kodek, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Predlažemo da koristite [dodatnu komponentu](https://octobercms.com/plugins?search=image) ili uslugu koja automatski konvertuje otpremljene slike u optimalne formate. Veličina [WebP slika bez gubitaka](https://developers.google.com/speed/webp) je za 26% manja od veličine PNG slika i za 25–34% manja od veličine odgovarajućih JPEG slika ekvivalentnog SSIM indeksa kvaliteta. Još jedan format slike sledeće generacije koji treba da uzmete u obzir je [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Predlažemo da instalirate [dodatnu komponentu za odloženo učitavanje slika](https://octobercms.com/plugins?search=lazy) koja pruža mogućnost odlaganja svih slika van ekrana ili prelaska na temu koja pruža tu funkciju. Predlažemo i da koristite [dodatnu komponentu za AMP stranice](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "<PERSON><PERSON> mnogo dodatnih komponenti koje vam pomažu da [umetnete kritične elemente](https://octobercms.com/plugins?search=css). Te dodatne komponente mogu da oštete druge dodatne komponente, pa morate detaljno da ih testirate."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Te<PERSON>, dodatne komponente i specifikacije servera doprinose vremenu odgovora servera. Predlažemo da pronađete optimizovaniju temu, pažljivo izaberete dodatnu komponentu za optimizaciju i/ili nadogradite server. Sistem za upravljanje sadržajem October omogućava programerima i da koriste stavku [`Queues`](https://octobercms.com/docs/services/queues) da bi odložili obradu nekog zadatka koji oduzima mnogo vremena, poput slanja imejla. To drastično ubrzava veb-zahteve."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Predlažemo da prikažete odlomke na listama postova (na primer, pomoc<PERSON>u dugmeta `show more`), smanjite broj postova koji se prikazuju na određenoj veb-stranici, razdvojite dugačke postove na više veb-stranica ili koristite dodatnu komponentu za odloženo učitavanje komentara."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON><PERSON> mno<PERSON> [dodat<PERSON>h komponenti](https://octobercms.com/plugins?search=css) koje mogu da ubrzaju veb-sajt nadovezivanjem, umanjivanjem i komprimovanjem stilova. <PERSON>ko koristite prevođenje i povezivanje da biste unapred obavili ovo umanjivanje, to može da ubrza programiranje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON><PERSON> mno<PERSON> [dodat<PERSON>h komponenti](https://octobercms.com/plugins?search=javascript) koje mogu da ubrzaju veb-sajt nadovezivanjem, umanjivanjem i komprimovanjem skripti. Ako koristite prevođenje i povezivanje da biste unapred obavili ovo umanjivanje, to može da ubrza programiranje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Predlažemo da pregledate [dodatne komponente](https://octobercms.com/plugins) koje na veb-sajtu učitavaju CSS koji se ne koristi. Da biste identifikovali dodatne komponente koje dodaju nepotrebni CSS, pokrenite [prosek upotrebe koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u Chrome alatkama za programere. Identifikujte odgovornu temu ili dodatnu komponentu na osnovu URL-a opisa stila. Potražite dodatne komponente koje imaju mnogo opisa stilova sa dosta crvenih elemenata u proseku upotrebe koda. Dodatna komponenta treba da doda opis stila samo ako se on stvarno koristi na veb-stranici."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Predla<PERSON>emo da pregledate [dodatne komponente](https://octobercms.com/plugins?search=javascript) koje na veb-stranici učitavaju JavaScript koji se ne koristi. Da biste identifikovali dodatne komponente koje dodaju nepotrebni JavaScript, pokrenite [prosek upotrebe koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u Chrome alatkama za programere. Identifikujte odgovornu temu ili dodatnu komponentu na osnovu URL-a skripte. Potražite dodatne komponente koje imaju mnogo skripti sa dosta crvenih elemenata u proseku upotrebe koda. Dodatna komponenta treba da doda skriptu samo ako se ona stvarno koristi na veb-stranici."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Pročitajte više o [sprečavanju nepotrebnih mrežnih zahteva pomoću HTTP keša](https://web.dev/http-cache/#caching-checklist). Ima mnogo [dodatnih komponenti](https://octobercms.com/plugins?search=Caching) koje mogu da se koriste za ubrzavanje keširanja."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Predlažemo da koristite [dodatnu komponentu za optimizaciju slika](https://octobercms.com/plugins?search=image) koja komprimuje slike bez gubitka kvaliteta."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Otpremajte slike direktno u menadžeru medija da biste se uverili da su dostupne obavezne veličine slika. Predlažemo da koristite [filter za promenu veličine](https://octobercms.com/docs/markup/filter-resize) ili [dodatnu komponentu za promenu veličine slika](https://octobercms.com/plugins?search=image) da biste se uverili da se koriste optimalne veličine slika."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Omogućite komprimovanje teksta u konfiguraciji veb-servera."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Predlažemo da koristite biblioteku „sa prozorima“ kao što je `react-window` kako biste smanjili broj napravljenih DOM čvorova ako na stranici renderujete mnogo elemenata koji se ponavljaju. [Saznajte više](https://web.dev/virtualize-long-lists-react-window/). <PERSON>to tako, ako koristite kopču `Effect` za poboljšanje učinka vremena izvršavanja, smanjite nepotrebna ponovna renderovanja pomoću komponenti [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ili [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) i [preskočite efekte](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) samo dok se određene zavisnosti ne promene."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON> koristite React ruter, sman<PERSON>e kori<PERSON>enje komponente `<Redirect>` za [navigacije pomoću rute](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ako prikazujete bilo kakve React komponente na serveru, razmislite o tome da koristite `renderToPipeableStream()` ili `renderToStaticNodeStream()` kako biste omogućili klijentu da prima i popunjava različite delove oznake umesto svih delova odjednom. [Saznajte više](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ako sistem za prevođenje i povezivanje automatski umanjuje CSS fajlove, uverite se da primenjujete proizvodnu verziju aplikacije. To možete da proverite pomoću dodatka React Developer Tools. [Saznajte više](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ako sistem za prevođenje i povezivanje automatski umanjuje JS fajlove, uverite se da primenjujete proizvodnu verziju aplikacije. To možete da proverite pomoću dodatka React Developer Tools. [Saznajte više](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON> ne prikazujete na serveru, [razdvojite JavaScript pakete](https://web.dev/code-splitting-suspense/) pomoću oznake `React.lazy()`. U suprotnom, razdvojite kôd pomoću biblioteke treće strane poput [komponenata koje mogu da se uč<PERSON>ju](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Koristite React DevTools Profiler, koji koristi API Profiler, da biste merili performanse prikazivanja komponenata. [Saznajte više.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Predlažemo da otpremite GIF u uslugu koja će ga kodirati za ugradnju kao HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Predlažemo da koristite dodatnu komponentu [ Performance Lab](https://wordpress.org/plugins/performance-lab/) za automatsko konvertovanje otpremljenih JPEG slika u WebP uvek kada je to podržano."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instalirajte [WordPress dodatnu komponentu za lako učitavanje](https://wordpress.org/plugins/search/lazy+load/) koja omogućava da odložite sve slike van ekrana ili da pređete na temu koja pruža tu funkciju. Preporučujemo i da koristite [dodatnu komponentu za AMP stranice](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Neke WordPress dodatne komponente mogu da vam pomognu da [umetnete kritične elemente](https://wordpress.org/plugins/search/critical+css/) ili [odložite manje važne resurse](https://wordpress.org/plugins/search/defer+css+javascript/). Imajte na umu da optimizacije koje pružaju ove dodatne komponente mogu da oštete funkcije ili teme dodatnih komponenti, pa ćete verovatno morati da unosite promene u kôd."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Te<PERSON>, dodatne komponente i specifikacije servera doprinose vremenu odgovora servera. Preporučujemo da pronađete optimizovaniju temu, pažljivo izaberete dodatnu komponentu za optimizaciju i/ili nadogradite server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Preporučujemo da prikažete odlomke u listama postova (na primer, preko još <PERSON>), smanjite broj postova koji se prikazuju na određenoj stranici, razdvojite dugačke postove na više strancia ili koristite dodatnu komponentu za lako učitavanje komentara."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Neke [WordPress dodatne komponente](https://wordpress.org/plugins/search/minify+css/) mogu da ubrzaju sajt tako što povezuju, umanjuju i komprimuju stilove. Ovo umanjivanje možete da obavite i unapred pomoću procesa dizajniranja ako je moguće."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Neke [WordPress dodatne komponente](https://wordpress.org/plugins/search/minify+javascript/) mogu da ubrzaju sajt tako što povezuju, umanjuju i komprimuju skripte. Ovo umanjivanje možete da obavite i unapred pomoću procesa dizajniranja ako je moguće."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Preporučujemo da umanjite ili promenite broj [WordPress dodatnih komponenti](https://wordpress.org/plugins/) koje na stranici učitavaju CSS koji se ne koristi. Da biste identifikovali dodatne komponente koje dodaju suvišan CSS, probajte da pokrenete [pokrivenost koda](https://developer.chrome.com/docs/devtools/coverage/) u alatki Chrome DevTools. Možete da identifikujete odgovornu temu/dodatnu komponentu u URL-u stilske stranice. Potražite dodatne komponente koje na listi imaju mnogo stilskih stranica sa dosta crvenila u pokrivenosti koda. Dodatna komponenta treba da stavi stilsku stranicu na listu samo ako se stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Preporučujemo da umanjite ili promenite broj [WordPress dodatnih komponenti](https://wordpress.org/plugins/) koje na stranici učitavaju JavaScript koji se ne koristi. Da biste identifikovali dodatne komponente koje dodaju suvišan JS, probajte da pokrenete [pokrivenost koda](https://developer.chrome.com/docs/devtools/coverage/) u alatki Chrome DevTools. Možete da identifikujete odgovornu temu/dodatnu komponentu u URL-u skripte. Potražite dodatne komponente koje na listi imaju mnogo skripti sa dosta crvenila u pokrivenosti koda. Dodatna komponenta treba da stavi skriptu na listu samo ako se stvarno koristi na stranici."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Pročitajte više o [keširanju pregledača u WordPress-u](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Preporuč<PERSON><PERSON><PERSON> da koristite [WordPress dodatnu komponentu za optimizaciju slika](https://wordpress.org/plugins/search/optimize+images/) koja komprimuje slike bez gubitka kvaliteta."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Otpremajte slike direktno pomoću [biblioteke medija](https://wordpress.org/support/article/media-library-screen/) da biste se uverili da su dostupne obavezne veličine slika, pa ih umetnite u biblioteku medija ili koristite vidžet stranice da biste se uverili da se koriste optimalne veličine slika (uključujući one za prelomne tačke koje se odazivaju). Izbegavajte korišćenje slika `Full Size` ako dimenzije nisu adekvatne za njihovo korišćenje. [Saznajte više](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Možete da omogućite komprimovanje teksta u konfiguraciji veb-servera."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Omogućite Imagify na kartici Optimizacija slika u WP Rocket-u da biste konvertovali slike u WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Omogućite [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) u WP Rocket-u da biste popravili ovu preporuku. Ova funkcija odlaže učitavanje slika dok posetilac ne skroluje nadole na stranici i mora da ih vidi."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Omogućite [Ukloni nekorišćeni CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) i [Učitaj odloženi JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) u WP Rocket-u da biste odgovorili na ovu preporuku. Ove funkcije će optimizovati CSS i JavaScript datoteke tako da ne blokiraju prikazivanje stranice."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Omogućite [Umanjite CSS datoteke](https://docs.wp-rocket.me/article/1350-css-minify-combine) u WP Rocket-u da biste rešili ovaj problem. Svi prostori i komentari u CSS datotekama sajta biće uklonjeni kako bi se veličina datoteke smanjila i kako bi se ubrzalo preuzimanje."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Omogućite [Umanji JavaScript datoteke](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) u WP Rocket-u da biste rešili ovaj problem. Prazni prostori i komentari će biti uklonjeni iz JavaScript datoteka radi smanjivanja veličine i bržeg preuzimanja."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Omogućite [Ukloni nekorišćeni CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) u „WP Rocket-u“ da biste rešili ovaj problem. Smanjuje veličinu stranice uklanjanjem svih CSS-ova i opisa stilova koji se ne koriste, a zadržava samo CSS koji se koristi za svaku stranicu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Da biste rešili ovaj problem, omogućite [Odloži izvršavanje JavaScript-a](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) u WP Rocket-u. To će poboljšati učitavanje stranice odlaganjem izvršavanja skripti do interakcije korisnika. Ako sajt sadrži iframe-ove, možete da koristite i [LazyLoad za iframe-ove i video snimke](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) WP Rocket-a, kao i [Zameni YouTube iframe slikom pregleda](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Omogućite Imagify na kartici Optimizacija slika u WP Rocket-u i pokrenite grupnu optimizaciju da biste komprimovali slike."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Koristite [Predučitavaj DNS zahteve](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) u WP Rocket-u da biste dodali „dns-fetch“ i ubrzali vezu sa spoljnim domenima. Uz to, WP Rocket automatski dodaje „povezivanje unapred“ u [domen Google fontova](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) i CNAME elemente dodate preko funkcije [Omogući CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Da biste rešili ovaj problem sa fontovima, omogućite [Uklonite nekorišćeni CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) u WP Rocket-u. Najvažniji fontovi sajta će se unapred učitati sa prioritetom."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Pogledaj<PERSON> kalkulator."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON><PERSON> p<PERSON>z"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Početna navigacija"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maksimalno kašnjenje kritične putanje:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Uključi/isključi tamnu temu"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Proširi dijalog za štampanje"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Štampaj rezime"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Saču<PERSON>j kao G<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Sačuvaj kao HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Sačuvaj kao JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Otvori u prikazivaču"}, "report/renderer/report-utils.js | errorLabel": {"message": "Greška!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Prijavljivanje greške: nema informacija o proveri"}, "report/renderer/report-utils.js | expandView": {"message": "Proširi prikaz"}, "report/renderer/report-utils.js | footerIssue": {"message": "Prijavite problem"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Podaci o eksperimentalnim funkcijama"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analiza aktuelne stranice emulirane pomoću mobilne mreže. Vrednosti predstavljaju procene i mogu da variraju."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Dodatne stavke za ručnu proveru"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Mogućnost"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Procenje<PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Provere sa zadovoljavajućom ocenom"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Početno učitavanje stranice"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Prilagođ<PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emulirani računar"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Bez emulacije"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe verzija"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "<PERSON><PERSON><PERSON><PERSON> procesor/memorija"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Ograničavanje procesora"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ograničavanje mreže"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulacija ekrana"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Korisnički agent (mreža)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Jedno učitavanje stranice"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ovi podaci su dobijeni na osnovu jednog učitavanja stranice, nasuprot podacima iz polja koji rezimiraju mnoge sesije."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ograničavanje poput spore 4G veze"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Nepoznato"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Prikaži revizije relevantne za:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Skupi fragment"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Proširi fragment"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Prikaži nezavisne resurse"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Pruža okruženje"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Bilo je izvesnih problema koji su uticali na ovo pokretanje Lighthouse-a:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Vrednosti predstavljaju procene i mogu da variraju. [Ocena učinka sa izračunava](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) direktno na osnovu tih pokazatelja."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Prikaži originalni trag"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Prikaži trag"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Provere sa zadovoljavajućom ocenom koje sadrže upozorenja"}, "report/renderer/report-utils.js | warningHeader": {"message": "Upozorenja: "}, "treemap/app/src/util.js | allLabel": {"message": "Sve"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Sve skripte"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Pokrivenost"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> moduli"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bajtovi resursa"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Uključi/isključi tabelu"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Neiskorišc<PERSON><PERSON> b<PERSON>i"}}