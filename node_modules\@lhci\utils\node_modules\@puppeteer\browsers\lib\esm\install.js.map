{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../src/install.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAC,UAAU,EAAC,MAAM,IAAI,CAAC;AAC9B,OAAO,EAAC,KAAK,EAAE,MAAM,EAAC,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAGL,YAAY,GACb,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAC,KAAK,EAAmB,MAAM,YAAY,CAAC;AACnD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,qBAAqB,EAAC,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAC,aAAa,EAAC,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAC,YAAY,EAAE,eAAe,EAAC,MAAM,eAAe,CAAC;AAE5D,MAAM,YAAY,GAAG,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAEzD,MAAM,KAAK,GAAG,IAAI,GAAG,EAA4B,CAAC;AAClD,SAAS,SAAS,CAAC,KAAa;IAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,qCAAqC;IAC1G,YAAY,CAAC,gBAAgB,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC;AACvD,CAAC;AAkDD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAuB;;IAEvB,MAAA,OAAO,CAAC,QAAQ,oCAAhB,OAAO,CAAC,QAAQ,GAAK,qBAAqB,EAAE,EAAC;IAC7C,MAAA,OAAO,CAAC,MAAM,oCAAd,OAAO,CAAC,MAAM,GAAK,IAAI,EAAC;IACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACrB,MAAM,IAAI,KAAK,CACb,uDAAuD,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;KACH;IACD,MAAM,GAAG,GAAG,cAAc,CACxB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,MAAM,CAAC,QAAQ,EAAE,uCAAuC,GAAG,GAAG,CAAC,CAAC;IAChE,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IACrD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAM,KAAK,CAAC,WAAW,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;KAC7C;IAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACnB,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;YAC3B,OAAO;gBACL,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC;SACH;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,CAAC;QACtB,MAAM,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACvE,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;KACH;IAED,MAAM,UAAU,GAAG,SAAS,CAAC,eAAe,CAC1C,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;KACH;IACD,IAAI;QACF,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,IAAI;YACF,SAAS,CAAC,UAAU,CAAC,CAAC;YACtB,MAAM,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;SACxE;gBAAS;YACR,YAAY,CAAC,UAAU,CAAC,CAAC;SAC1B;QAED,YAAY,CAAC,cAAc,WAAW,OAAO,UAAU,EAAE,CAAC,CAAC;QAC3D,IAAI;YACF,SAAS,CAAC,SAAS,CAAC,CAAC;YACrB,MAAM,aAAa,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;SAC9C;gBAAS;YACR,YAAY,CAAC,SAAS,CAAC,CAAC;SACzB;KACF;YAAS;QACR,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;YAC3B,MAAM,MAAM,CAAC,WAAW,CAAC,CAAC;SAC3B;KACF;IACD,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;KACzB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,OAAuB;;IACvD,MAAA,OAAO,CAAC,QAAQ,oCAAhB,OAAO,CAAC,QAAQ,GAAK,qBAAqB,EAAE,EAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACrB,MAAM,IAAI,KAAK,CACb,uDAAuD,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;KACH;IACD,OAAO,MAAM,eAAe,CAC1B,cAAc,CACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CACF,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,OAAgB,EAChB,QAAyB,EACzB,OAAe,EACf,OAAgB;IAEhB,OAAO,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,CAAC"}