{"version": 3, "file": "PuppeteerNode.js", "sourceRoot": "", "sources": ["../../../../src/node/PuppeteerNode.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,OAAO,EAAC,IAAI,EAAC,MAAM,MAAM,CAAC;AAM1B,OAAO,EAGL,SAAS,GACV,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAC,mBAAmB,EAAC,MAAM,iBAAiB,CAAC;AAEpD,OAAO,EAAC,cAAc,EAAwB,MAAM,qBAAqB,CAAC;AAC1E,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAmBrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAM,OAAO,aAAc,SAAQ,SAAS;IAc1C;;OAEG;IACH,YACE,QAE2B;QAE3B,MAAM,EAAC,aAAa,EAAE,GAAG,cAAc,EAAC,GAAG,QAAQ,CAAC;QACpD,KAAK,CAAC,cAAc,CAAC,CAAC;;QAtBxB,2CAA6B;QAC7B,qDAA+B;QAO/B;;WAEG;QACH,kBAAa,GAAkB,EAAE,CAAC;QAYhC,IAAI,aAAa,EAAE;YACjB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;SACpC;QACD,QAAQ,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;YACzC,KAAK,SAAS;gBACZ,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,OAAO,CAAC;gBAC1D,MAAM;YACR;gBACE,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,QAAQ,CAAC;gBAC7C,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;gBAC3D,MAAM;SACT;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACM,OAAO,CAAC,OAAuB;QACtC,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,MAAM,CAAC,UAAkC,EAAE;QACzC,MAAM,EAAC,OAAO,GAAG,IAAI,CAAC,cAAc,EAAC,GAAG,OAAO,CAAC;QAChD,uBAAA,IAAI,sCAAwB,OAAO,MAAA,CAAC;QACpC,OAAO,uBAAA,IAAI,6DAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IA2BD;;OAEG;IACH,cAAc,CAAC,OAA8B;QAC3C,OAAO,uBAAA,IAAI,6DAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;;QACjB,OAAO,CACL,MAAA,MAAA,MAAA,uBAAA,IAAI,gCAAW,0CAAE,wBAAwB,EAAE,mCAC3C,IAAI,CAAC,aAAa,CAAC,eAAe,mCAClC,IAAI,CAAC,sBAAuB,CAC7B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,IAAI,mBAAmB;;QACrB,OAAO,CACL,MAAA,IAAI,CAAC,aAAa,CAAC,YAAY,mCAC/B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,cAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;;QACrB,OAAO,MAAA,uBAAA,IAAI,0CAAqB,mCAAI,IAAI,CAAC,cAAc,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACH,IAAI,cAAc;;QAChB,OAAO,MAAA,IAAI,CAAC,aAAa,CAAC,cAAc,mCAAI,QAAQ,CAAC;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,6DAAU,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,UAAwC,EAAE;QACpD,OAAO,uBAAA,IAAI,6DAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;OASG;IACH,oBAAoB,CAClB,UAA0C,EAAE;;QAE5C,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,YAAY,EAAE;YACjC,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;SAC7B;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;QACD,IACE,CAAC,CAAC,mBAAmB,IAAI,OAAO,CAAC;aACjC,MAAA,IAAI,CAAC,aAAa,CAAC,WAAW,0CAAE,qBAAqB,CAAA,EACrD;YACA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAClC;QACD,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YAC3D,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;SAChD;QACD,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;YAChE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;SACrD;QACD,OAAO,IAAI,cAAc,CAAC,OAAgC,CAAC,CAAC;IAC9D,CAAC;CACF;;IA3HG,IACE,uBAAA,IAAI,gCAAW;QACf,uBAAA,IAAI,gCAAW,CAAC,OAAO,KAAK,IAAI,CAAC,mBAAmB,EACpD;QACA,OAAO,uBAAA,IAAI,gCAAW,CAAC;KACxB;IACD,QAAQ,IAAI,CAAC,mBAAmB,EAAE;QAChC,KAAK,QAAQ;YACX,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;YAC3D,uBAAA,IAAI,4BAAc,IAAI,cAAc,CAAC,IAAI,CAAC,MAAA,CAAC;YAC3C,MAAM;QACR,KAAK,SAAS;YACZ,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,OAAO,CAAC;YAC1D,uBAAA,IAAI,4BAAc,IAAI,eAAe,CAAC,IAAI,CAAC,MAAA,CAAC;YAC5C,MAAM;QACR;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,uBAAA,IAAI,0CAAqB,EAAE,CAAC,CAAC;KACpE;IACD,OAAO,uBAAA,IAAI,gCAAW,CAAC;AACzB,CAAC"}