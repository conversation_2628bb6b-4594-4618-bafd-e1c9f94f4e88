"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/award.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Award; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Award = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Award\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"1vp47v\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\",\n            key: \"em7aur\"\n        }\n    ]\n]);\n //# sourceMappingURL=award.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronDown\", [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsY0FBY0MsZ0VBQWdCQSxDQUFDLGVBQWU7SUFDbEQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBZ0JDLEtBQUs7UUFBQTtLQUFVO0NBQzlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvY2hldnJvbi1kb3duLnRzP2NmMWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignQ2hldnJvbkRvd24nLCBbXG4gIFsncGF0aCcsIHsgZDogJ202IDkgNiA2IDYtNicsIGtleTogJ3FydW5zbCcgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvbkRvd247XG4iXSwibmFtZXMiOlsiQ2hldnJvbkRvd24iLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronUp\", [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFBLE1BQU1BLFlBQVlDLGdFQUFnQkEsQ0FBQyxhQUFhO0lBQUM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBa0JDLEtBQUs7UUFBUztLQUFFO0NBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9jaGV2cm9uLXVwLnRzPzVmY2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uVXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1UZ2dNVFV0TmkwMkxUWWdOaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi11cFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25VcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25VcCcsIFtbJ3BhdGgnLCB7IGQ6ICdtMTggMTUtNi02LTYgNicsIGtleTogJzE1M3VkeicgfV1dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblVwO1xuIl0sIm5hbWVzIjpbIkNoZXZyb25VcCIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/credit-card.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreditCard; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CreditCard = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CreditCard\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"14\",\n            x: \"2\",\n            y: \"5\",\n            rx: \"2\",\n            key: \"ynyp8z\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"2\",\n            x2: \"22\",\n            y1: \"10\",\n            y2: \"10\",\n            key: \"1b3vmo\"\n        }\n    ]\n]);\n //# sourceMappingURL=credit-card.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-check.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileCheck; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileCheck\", [\n    [\n        \"path\",\n        {\n            d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n            key: \"1nnpy2\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"14 2 14 8 20 8\",\n            key: \"1ew0cm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 15 2 2 4-4\",\n            key: \"1grp1n\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZmlsZS1jaGVjay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLFlBQVlDLGdFQUFnQkEsQ0FBQyxhQUFhO0lBQzlDO1FBQ0U7UUFDQTtZQUFFQyxHQUFHO1lBQXlFQyxLQUFLO1FBQVM7S0FDOUY7SUFDQTtRQUFDO1FBQVk7WUFBRUMsUUFBUTtZQUFrQkQsS0FBSztRQUFBO0tBQVU7SUFDeEQ7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBaUJDLEtBQUs7UUFBQTtLQUFVO0NBQy9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvZmlsZS1jaGVjay50cz9lNDZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgRmlsZUNoZWNrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVFF1TlNBeVNEWmhNaUF5SURBZ01DQXdMVElnTW5ZeE5tRXlJRElnTUNBd0lEQWdNaUF5YURFeVlUSWdNaUF3SURBZ01DQXlMVEpXTnk0MVRERTBMalVnTW5vaUlDOCtDaUFnUEhCdmJIbHNhVzVsSUhCdmFXNTBjejBpTVRRZ01pQXhOQ0E0SURJd0lEZ2lJQzgrQ2lBZ1BIQmhkR2dnWkQwaWJUa2dNVFVnTWlBeUlEUXROQ0lnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvZmlsZS1jaGVja1xuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEZpbGVDaGVjayA9IGNyZWF0ZUx1Y2lkZUljb24oJ0ZpbGVDaGVjaycsIFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7IGQ6ICdNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnonLCBrZXk6ICcxbm5weTInIH0sXG4gIF0sXG4gIFsncG9seWxpbmUnLCB7IHBvaW50czogJzE0IDIgMTQgOCAyMCA4Jywga2V5OiAnMWV3MGNtJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTkgMTUgMiAyIDQtNCcsIGtleTogJzFncnAxbicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgRmlsZUNoZWNrO1xuIl0sIm5hbWVzIjpbIkZpbGVDaGVjayIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5IiwicG9pbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/help-circle.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HelpCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst HelpCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"HelpCircle\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\",\n            key: \"1u773s\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n]);\n //# sourceMappingURL=help-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/receipt.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Receipt; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Receipt = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Receipt\", [\n    [\n        \"path\",\n        {\n            d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1-2-1Z\",\n            key: \"wqdwcb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8\",\n            key: \"1h4pet\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17V7\",\n            key: \"pyj7ub\"\n        }\n    ]\n]);\n //# sourceMappingURL=receipt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Zap; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"polygon\",\n        {\n            points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n            key: \"45s27k\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvemFwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsTUFBTUMsZ0VBQWdCQSxDQUFDLE9BQU87SUFDbEM7UUFBQztRQUFXO1lBQUVDLFFBQVE7WUFBMENDLEtBQUs7UUFBQTtLQUFVO0NBQ2hGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvemFwLnRzPzkxMjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBaYXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHOXNlV2R2YmlCd2IybHVkSE05SWpFeklESWdNeUF4TkNBeE1pQXhOQ0F4TVNBeU1pQXlNU0F4TUNBeE1pQXhNQ0F4TXlBeUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3phcFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFphcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ1phcCcsIFtcbiAgWydwb2x5Z29uJywgeyBwb2ludHM6ICcxMyAyIDMgMTQgMTIgMTQgMTEgMjIgMjEgMTAgMTIgMTAgMTMgMicsIGtleTogJzQ1czI3aycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgWmFwO1xuIl0sIm5hbWVzIjpbIlphcCIsImNyZWF0ZUx1Y2lkZUljb24iLCJwb2ludHMiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_search_IntelligentSearchBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/search/IntelligentSearchBar */ \"(app-pages-browser)/./components/search/IntelligentSearchBar.tsx\");\n/* harmony import */ var _components_dependencies_DependencyGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dependencies/DependencyGrid */ \"(app-pages-browser)/./components/dependencies/DependencyGrid.tsx\");\n/* harmony import */ var _components_faq_FAQSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/faq/FAQSection */ \"(app-pages-browser)/./components/faq/FAQSection.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Clock,FileText,MessageSquare,Shield,Smartphone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction HomePage() {\n    // Manejar búsqueda desde el hero\n    const handleSearch = (query, results)=>{\n        console.log(\"B\\xfasqueda realizada:\", query, results);\n    // En producción, esto podría redirigir a una página de resultados\n    };\n    // Manejar selección de resultado\n    const handleResultSelect = (result)=>{\n        console.log(\"Resultado seleccionado:\", result);\n    // En producción, esto redirigiría al detalle del trámite/OPA\n    };\n    // Manejar selección de dependencia\n    const handleDependencySelect = (dependency)=>{\n        console.log(\"Dependencia seleccionada:\", dependency);\n    // En producción, esto redirigiría a la página de la dependencia\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-chia-blue-50 to-chia-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-chia-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-chia-blue-900\",\n                                                children: \"Municipio de Ch\\xeda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-chia-blue-600\",\n                                                children: \"Sistema de Atenci\\xf3n Ciudadana\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            children: \"Iniciar Sesi\\xf3n\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-chia-blue-600 hover:bg-chia-blue-700\",\n                                            children: \"Registrarse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"Servicios Municipales\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-chia-blue-600\",\n                                        children: \"Digitales e Inteligentes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Accede a todos los tr\\xe1mites y servicios del Municipio de Ch\\xeda desde un solo lugar. Con inteligencia artificial, seguimiento en tiempo real y atenci\\xf3n 24/7.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_IntelligentSearchBar__WEBPACK_IMPORTED_MODULE_4__.IntelligentSearchBar, {\n                                    onSearch: handleSearch,\n                                    onResultSelect: handleResultSelect,\n                                    placeholder: \"\\xbfQu\\xe9 tr\\xe1mite necesitas? Ej: licencia construcci\\xf3n, certificado residencia...\",\n                                    className: \"w-full\",\n                                    showFilters: false,\n                                    maxResults: 6\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-chia-blue-600 hover:bg-chia-blue-700\",\n                                            children: [\n                                                \"Comenzar Ahora\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/procedimientos\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            children: \"Ver Tr\\xe1mites Disponibles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Explora por Dependencias\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Navega por las diferentes secretar\\xedas y dependencias municipales para encontrar los tr\\xe1mites y servicios que necesitas de manera r\\xe1pida y organizada.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dependencies_DependencyGrid__WEBPACK_IMPORTED_MODULE_5__.DependencyGrid, {\n                            onDependencySelect: handleDependencySelect,\n                            showSearch: true,\n                            showStats: true,\n                            maxItems: 6,\n                            className: \"mb-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/dependencias\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    children: [\n                                        \"Ver Todas las Dependencias\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"ml-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_faq_FAQSection__WEBPACK_IMPORTED_MODULE_6__.FAQSection, {\n                        title: \"Preguntas Frecuentes\",\n                        description: \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales de Ch\\xeda\",\n                        initialLimit: 8,\n                        showSearch: true,\n                        showCategoryFilter: true,\n                        showStats: true,\n                        className: \"mb-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"\\xbfPor qu\\xe9 elegir nuestro sistema?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"Innovaci\\xf3n y eficiencia al servicio de la ciudadan\\xeda\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-chia-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-6 w-6 text-chia-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Asistente IA Inteligente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Chatbot con inteligencia artificial que te gu\\xeda paso a paso en tus tr\\xe1mites\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-chia-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6 text-chia-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Disponible 24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Realiza tus tr\\xe1mites en cualquier momento, desde cualquier lugar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Seguimiento en Tiempo Real\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Conoce el estado de tus tr\\xe1mites con actualizaciones autom\\xe1ticas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Seguridad Garantizada\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Tus datos est\\xe1n protegidos con los m\\xe1s altos est\\xe1ndares de seguridad\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-6 w-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Multiplataforma\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Accede desde tu computador, tablet o celular con la misma experiencia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-6 w-6 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Atenci\\xf3n Personalizada\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Soporte especializado para resolver todas tus dudas y consultas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Servicios Disponibles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"M\\xe1s de 100 tr\\xe1mites y 700 OPAs disponibles en l\\xednea\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-3 h-6 w-6 text-chia-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Tr\\xe1mites Municipales\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Gestiona todos tus tr\\xe1mites oficiales de manera digital\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Licencias de construcci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Certificados y constancias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Permisos comerciales\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Y muchos m\\xe1s...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"mr-3 h-6 w-6 text-chia-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"OPAs (Otras Prestaciones)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Servicios adicionales para mejorar tu calidad de vida\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Programas sociales\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Servicios de salud\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Actividades culturales\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"M\\xe1s de 700 servicios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-chia-blue-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-4\",\n                            children: \"\\xbfListo para comenzar?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-chia-blue-100 mb-8\",\n                            children: \"\\xdanete a miles de ciudadanos que ya disfrutan de nuestros servicios digitales\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/auth/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"lg\",\n                                className: \"bg-white text-chia-blue-600 hover:bg-gray-100\",\n                                children: [\n                                    \"Crear Cuenta Gratuita\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Clock_FileText_MessageSquare_Shield_Smartphone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"ml-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-chia-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: \"C\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: \"Municipio de Ch\\xeda\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Sistema de Atenci\\xf3n Ciudadana con tecnolog\\xeda de vanguardia para servir mejor a nuestra comunidad.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Carrera 11 # 17-25\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Ch\\xeda, Cundinamarca\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tel\\xe9fono: (*************\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Email: <EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Horarios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Lunes a Viernes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"8:00 AM - 5:00 PM\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-chia-blue-400 font-medium\",\n                                                    children: \"Servicios en l\\xednea 24/7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Municipio de Ch\\xeda. Todos los derechos reservados.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/faq/FAQSection.tsx":
/*!***************************************!*\
  !*** ./components/faq/FAQSection.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQSection: function() { return /* binding */ FAQSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/faqService */ \"(app-pages-browser)/./lib/services/faqService.ts\");\n/* __next_internal_client_entry_do_not_use__ FAQSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * Mapeo de iconos para categorías\n */ const categoryIcons = {\n    Receipt: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    FileCheck: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Award: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Zap: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    FileText: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    CreditCard: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    HelpCircle: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n};\n/**\n * Componente principal de la sección FAQ\n */ function FAQSection(param) {\n    let { title = \"Preguntas Frecuentes\", description = \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales\", initialLimit = 10, showSearch = true, showCategoryFilter = true, showStats = true, className = \"\" } = param;\n    _s();\n    // Estados\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedFAQs, setExpandedFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAllFAQs, setShowAllFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalFAQs: 0,\n        totalCategories: 0,\n        averagePopularity: 0,\n        mostPopularCategory: \"\"\n    });\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    // Realizar búsqueda cuando cambie la query o categoría\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        performSearch();\n    }, [\n        searchQuery,\n        selectedCategory\n    ]);\n    /**\n   * Cargar datos iniciales\n   */ const loadInitialData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [categoriesData, popularFAQs, statsData] = await Promise.all([\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getCategories(),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(initialLimit),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQStats()\n            ]);\n            setCategories(categoriesData);\n            setFaqs(popularFAQs);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading FAQ data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Realizar búsqueda de FAQs\n   */ const performSearch = async ()=>{\n        try {\n            if (searchQuery.trim()) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].searchFAQs(searchQuery, {\n                    category: selectedCategory || undefined,\n                    limit: showAllFAQs ? 50 : initialLimit\n                });\n                setFaqs(results);\n            } else if (selectedCategory) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQsByCategory(selectedCategory, showAllFAQs ? undefined : initialLimit);\n                setFaqs(results);\n            } else {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(showAllFAQs ? 50 : initialLimit);\n                setFaqs(results);\n            }\n        } catch (error) {\n            console.error(\"Error searching FAQs:\", error);\n        }\n    };\n    /**\n   * Alternar expansión de FAQ\n   */ const toggleFAQExpansion = (faqId)=>{\n        const newExpanded = new Set(expandedFAQs);\n        if (newExpanded.has(faqId)) {\n            newExpanded.delete(faqId);\n        } else {\n            newExpanded.add(faqId);\n        }\n        setExpandedFAQs(newExpanded);\n    };\n    /**\n   * Limpiar filtros\n   */ const clearFilters = ()=>{\n        setSearchQuery(\"\");\n        setSelectedCategory(\"\");\n        setShowAllFAQs(false);\n    };\n    /**\n   * Obtener icono de categoría\n   */ const getCategoryIcon = (iconName)=>{\n        const IconComponent = categoryIcons[iconName] || _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        return IconComponent;\n    };\n    // FAQs filtrados y limitados\n    const displayedFAQs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return showAllFAQs ? faqs : faqs.slice(0, initialLimit);\n    }, [\n        faqs,\n        showAllFAQs,\n        initialLimit\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Cargando preguntas frecuentes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-8 w-8 text-chia-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalFAQs,\n                                    \" preguntas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalCategories,\n                                    \" categor\\xedas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"M\\xe1s consultada: \",\n                                    stats.mostPopularCategory\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            (showSearch || showCategoryFilter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Buscar en preguntas frecuentes...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 17\n                            }, this),\n                            showCategoryFilter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filtrar por categor\\xeda:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: selectedCategory === \"\" ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSelectedCategory(\"\"),\n                                                className: \"h-8\",\n                                                children: \"Todas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, this),\n                                            categories.map((category)=>{\n                                                const IconComponent = getCategoryIcon(category.icon);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedCategory(category.id),\n                                                    className: \"h-8 space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"ml-1 h-4 text-xs\",\n                                                            children: category.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Limpiar filtros\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedFAQs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No se encontraron preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: searchQuery ? 'No hay resultados para \"'.concat(searchQuery, '\"') : \"No hay preguntas disponibles en esta categor\\xeda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: clearFilters,\n                                children: \"Ver todas las preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        displayedFAQs.map((faq)=>{\n                            const isExpanded = expandedFAQs.has(faq.id);\n                            const category = categories.find((cat)=>cat.id === faq.category);\n                            const IconComponent = category ? getCategoryIcon(category.icon) : _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"transition-all duration-200 hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"cursor-pointer\",\n                                        onClick: ()=>toggleFAQExpansion(faq.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-4 w-4 text-chia-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: category === null || category === void 0 ? void 0 : category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        faq.popularity,\n                                                                        \"% popular\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-left text-lg leading-tight\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"ml-4 flex-shrink-0\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 19\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 25\n                                                }, this),\n                                                faq.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: faq.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 27\n                                                }, this),\n                                                faq.relatedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Tr\\xe1mites relacionados:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: faq.relatedProcedures.map((procedure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-chia-blue-600\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        procedure\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 33\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, faq.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        !showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAllFAQs(true),\n                                className: \"px-8\",\n                                children: [\n                                    \"Ver m\\xe1s preguntas (\",\n                                    faqs.length - initialLimit,\n                                    \" restantes)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 15\n                        }, this),\n                        showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>setShowAllFAQs(false),\n                                className: \"px-8\",\n                                children: \"Ver menos preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQSection, \"oXoLY62DY6oxGQfx6srpVxVrg6w=\");\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/faq/FAQSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/**\n * Servicio para gestionar preguntas frecuentes\n * Implementa patrón Singleton para eficiencia\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Inicializar el servicio con datos predefinidos\n   */ async initialize() {\n        if (this.initialized) return;\n        // Definir categorías principales\n        const categories = [\n            {\n                id: \"impuestos\",\n                name: \"Impuestos y Tributos\",\n                description: \"Preguntas sobre impuestos municipales, predial, industria y comercio\",\n                icon: \"Receipt\",\n                color: \"bg-blue-500\",\n                count: 0\n            },\n            {\n                id: \"licencias\",\n                name: \"Licencias y Permisos\",\n                description: \"Construcci\\xf3n, funcionamiento, comerciales y ambientales\",\n                icon: \"FileCheck\",\n                color: \"bg-green-500\",\n                count: 0\n            },\n            {\n                id: \"certificados\",\n                name: \"Certificados\",\n                description: \"Residencia, libertad y tradici\\xf3n, estratificaci\\xf3n\",\n                icon: \"Award\",\n                color: \"bg-purple-500\",\n                count: 0\n            },\n            {\n                id: \"servicios\",\n                name: \"Servicios P\\xfablicos\",\n                description: \"Alumbrado p\\xfablico, aseo, acueducto y alcantarillado\",\n                icon: \"Zap\",\n                color: \"bg-yellow-500\",\n                count: 0\n            },\n            {\n                id: \"tramites\",\n                name: \"Tr\\xe1mites Generales\",\n                description: \"Procedimientos administrativos y documentaci\\xf3n\",\n                icon: \"FileText\",\n                color: \"bg-indigo-500\",\n                count: 0\n            },\n            {\n                id: \"pagos\",\n                name: \"Pagos y Facturaci\\xf3n\",\n                description: \"M\\xe9todos de pago, facturaci\\xf3n y paz y salvos\",\n                icon: \"CreditCard\",\n                color: \"bg-red-500\",\n                count: 0\n            }\n        ];\n        // Cargar categorías\n        categories.forEach((category)=>{\n            this.categories.set(category.id, category);\n        });\n        // Generar FAQs basadas en datos reales\n        await this.generateFAQsFromData();\n        this.initialized = true;\n    }\n    /**\n   * Generar FAQs basadas en los datos de trámites y OPAs\n   */ async generateFAQsFromData() {\n        const faqs = [\n            // Impuestos y Tributos\n            {\n                id: \"impuesto-predial-que-es\",\n                question: \"\\xbfQu\\xe9 es el impuesto predial y c\\xf3mo se calcula?\",\n                answer: \"El impuesto predial unificado es un tributo municipal que grava la propiedad inmueble. Se calcula de acuerdo con el aval\\xfao catastral del predio y el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta para consultas es de 1 hora.\",\n                category: \"impuestos\",\n                tags: [\n                    \"impuesto predial\",\n                    \"aval\\xfao catastral\",\n                    \"tributos\",\n                    \"propiedad\"\n                ],\n                relatedProcedures: [\n                    \"Impuesto predial unificado\"\n                ],\n                popularity: 95,\n                lastUpdated: new Date()\n            },\n            {\n                id: \"impuesto-industria-comercio\",\n                question: \"\\xbfC\\xf3mo funciona el impuesto de industria y comercio?\",\n                answer: \"El impuesto de industria y comercio y su complementario de avisos y tableros se calcula de acuerdo con los ingresos obtenidos en el a\\xf1o inmediatamente anterior, seg\\xfan el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta es de 1 d\\xeda.\",\n                category: \"impuestos\",\n                tags: [\n                    \"industria y comercio\",\n                    \"avisos y tableros\",\n                    \"ingresos\",\n                    \"comercio\"\n                ],\n                relatedProcedures: [\n                    \"Impuesto de industria y comercio y su complementario de avisos y tableros\"\n                ],\n                popularity: 85,\n                lastUpdated: new Date()\n            },\n            {\n                id: \"alumbrado-publico-tarifa\",\n                question: \"\\xbfCu\\xe1nto cuesta el impuesto de alumbrado p\\xfablico?\",\n                answer: \"Las tarifas del impuesto sobre el servicio de alumbrado p\\xfablico est\\xe1n establecidas en el Acuerdo 130 de 2017. Para predios usuarios de energ\\xeda el\\xe9ctrica domiciliaria es 0.5 por mil sobre el valor del impuesto predial. Para predios urbanizables no urbanizados aplican tarifas espec\\xedficas.\",\n                category: \"impuestos\",\n                tags: [\n                    \"alumbrado p\\xfablico\",\n                    \"tarifas\",\n                    \"energ\\xeda el\\xe9ctrica\",\n                    \"predios\"\n                ],\n                relatedProcedures: [\n                    \"Impuesto sobre el servicio de alumbrado p\\xfablico\"\n                ],\n                popularity: 70,\n                lastUpdated: new Date()\n            },\n            // Licencias y Permisos\n            {\n                id: \"licencia-construccion-requisitos\",\n                question: \"\\xbfQu\\xe9 requisitos necesito para obtener una licencia de construcci\\xf3n?\",\n                answer: \"Para obtener una licencia de construcci\\xf3n debe presentar los documentos t\\xe9cnicos requeridos ante la Secretar\\xeda de Planeaci\\xf3n. El proceso tiene un costo de $419.000 y un tiempo de respuesta de 45 d\\xedas h\\xe1biles. Consulte los requisitos espec\\xedficos en el portal SUIT o GOV.CO.\",\n                category: \"licencias\",\n                tags: [\n                    \"licencia construcci\\xf3n\",\n                    \"planeaci\\xf3n\",\n                    \"obras civiles\",\n                    \"requisitos\"\n                ],\n                relatedProcedures: [\n                    \"Licencia de construcci\\xf3n\"\n                ],\n                popularity: 90,\n                lastUpdated: new Date()\n            },\n            {\n                id: \"licencia-funcionamiento-comercio\",\n                question: \"\\xbfC\\xf3mo obtengo la licencia de funcionamiento para mi negocio?\",\n                answer: \"La licencia de funcionamiento se tramita seg\\xfan el tipo de actividad comercial. Debe cumplir con los requisitos sanitarios, de seguridad y urban\\xedsticos. Consulte con la dependencia correspondiente seg\\xfan su actividad espec\\xedfica.\",\n                category: \"licencias\",\n                tags: [\n                    \"licencia funcionamiento\",\n                    \"negocio\",\n                    \"comercio\",\n                    \"actividad comercial\"\n                ],\n                relatedProcedures: [],\n                popularity: 88,\n                lastUpdated: new Date()\n            },\n            // Certificados\n            {\n                id: \"certificado-residencia-como\",\n                question: \"\\xbfC\\xf3mo puedo obtener un certificado de residencia?\",\n                answer: \"El certificado de residencia se puede solicitar en la Secretar\\xeda General. Debe presentar documento de identidad y comprobantes de residencia en el municipio. Consulte los requisitos espec\\xedficos y tiempos de respuesta en las oficinas municipales.\",\n                category: \"certificados\",\n                tags: [\n                    \"certificado residencia\",\n                    \"secretar\\xeda general\",\n                    \"documento identidad\"\n                ],\n                relatedProcedures: [],\n                popularity: 92,\n                lastUpdated: new Date()\n            },\n            {\n                id: \"certificado-libertad-tradicion\",\n                question: \"\\xbfQu\\xe9 es el certificado de libertad y tradici\\xf3n?\",\n                answer: \"El certificado de libertad y tradici\\xf3n es un documento que certifica la propiedad inmobiliaria y su historial jur\\xeddico. Se tramita en la Secretar\\xeda General, no tiene costo y el tiempo de respuesta es de 1 d\\xeda h\\xe1bil.\",\n                category: \"certificados\",\n                tags: [\n                    \"libertad y tradici\\xf3n\",\n                    \"propiedad inmobiliaria\",\n                    \"historial jur\\xeddico\"\n                ],\n                relatedProcedures: [\n                    \"Certificado de libertad y tradici\\xf3n\"\n                ],\n                popularity: 80,\n                lastUpdated: new Date()\n            },\n            // Servicios Públicos\n            {\n                id: \"servicios-publicos-consulta\",\n                question: \"\\xbfD\\xf3nde puedo consultar sobre servicios p\\xfablicos?\",\n                answer: \"Para consultas sobre servicios p\\xfablicos como acueducto, alcantarillado y aseo, puede dirigirse a la dependencia correspondiente o consultar en l\\xednea. Cada servicio tiene procedimientos espec\\xedficos de facturaci\\xf3n y atenci\\xf3n.\",\n                category: \"servicios\",\n                tags: [\n                    \"servicios p\\xfablicos\",\n                    \"acueducto\",\n                    \"alcantarillado\",\n                    \"aseo\"\n                ],\n                relatedProcedures: [],\n                popularity: 75,\n                lastUpdated: new Date()\n            },\n            // Pagos y Facturación\n            {\n                id: \"metodos-pago-disponibles\",\n                question: \"\\xbfQu\\xe9 m\\xe9todos de pago est\\xe1n disponibles?\",\n                answer: \"El municipio acepta diversos m\\xe9todos de pago para tr\\xe1mites y servicios: efectivo en oficinas, transferencias bancarias, PSE y otros medios electr\\xf3nicos. Consulte las opciones espec\\xedficas para cada tr\\xe1mite.\",\n                category: \"pagos\",\n                tags: [\n                    \"m\\xe9todos pago\",\n                    \"efectivo\",\n                    \"transferencias\",\n                    \"PSE\",\n                    \"electr\\xf3nicos\"\n                ],\n                relatedProcedures: [],\n                popularity: 85,\n                lastUpdated: new Date()\n            },\n            {\n                id: \"paz-salvo-como-obtener\",\n                question: \"\\xbfC\\xf3mo obtengo un paz y salvo municipal?\",\n                answer: \"El paz y salvo municipal certifica que est\\xe1 al d\\xeda con sus obligaciones tributarias. Se puede solicitar en la Secretar\\xeda de Hacienda presentando documento de identidad y comprobante de pago de impuestos al d\\xeda.\",\n                category: \"pagos\",\n                tags: [\n                    \"paz y salvo\",\n                    \"obligaciones tributarias\",\n                    \"secretar\\xeda hacienda\"\n                ],\n                relatedProcedures: [],\n                popularity: 78,\n                lastUpdated: new Date()\n            }\n        ];\n        // Cargar FAQs y actualizar contadores de categorías\n        faqs.forEach((faq)=>{\n            this.faqs.set(faq.id, faq);\n            const category = this.categories.get(faq.category);\n            if (category) {\n                category.count++;\n                this.categories.set(faq.category, category);\n            }\n        });\n    }\n    /**\n   * Obtener todas las categorías\n   */ async getCategories() {\n        await this.initialize();\n        return Array.from(this.categories.values()).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Obtener FAQs por categoría\n   */ async getFAQsByCategory(categoryId, limit) {\n        await this.initialize();\n        const faqs = Array.from(this.faqs.values()).filter((faq)=>faq.category === categoryId).sort((a, b)=>b.popularity - a.popularity);\n        return limit ? faqs.slice(0, limit) : faqs;\n    }\n    /**\n   * Buscar FAQs por texto\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        await this.initialize();\n        const { category, limit = 10, includeRelated = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        const searchTerm = query.toLowerCase().trim();\n        let faqs = Array.from(this.faqs.values());\n        // Filtrar por categoría si se especifica\n        if (category) {\n            faqs = faqs.filter((faq)=>faq.category === category);\n        }\n        // Buscar en preguntas, respuestas y tags\n        const results = faqs.filter((faq)=>{\n            const questionMatch = faq.question.toLowerCase().includes(searchTerm);\n            const answerMatch = faq.answer.toLowerCase().includes(searchTerm);\n            const tagMatch = faq.tags.some((tag)=>tag.toLowerCase().includes(searchTerm));\n            const procedureMatch = includeRelated && faq.relatedProcedures.some((proc)=>proc.toLowerCase().includes(searchTerm));\n            return questionMatch || answerMatch || tagMatch || procedureMatch;\n        });\n        // Ordenar por relevancia (popularidad y coincidencias)\n        results.sort((a, b)=>{\n            const aScore = this.calculateRelevanceScore(a, searchTerm);\n            const bScore = this.calculateRelevanceScore(b, searchTerm);\n            return bScore - aScore;\n        });\n        return results.slice(0, limit);\n    }\n    /**\n   * Calcular puntuación de relevancia\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en tags (peso medio)\n        faq.tags.forEach((tag)=>{\n            if (tag.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularity * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        await this.initialize();\n        return Array.from(this.faqs.values()).sort((a, b)=>b.popularity - a.popularity).slice(0, limit);\n    }\n    /**\n   * Obtener FAQ por ID\n   */ async getFAQById(id) {\n        await this.initialize();\n        return this.faqs.get(id) || null;\n    }\n    /**\n   * Obtener estadísticas del FAQ\n   */ async getFAQStats() {\n        await this.initialize();\n        const faqs = Array.from(this.faqs.values());\n        const categories = Array.from(this.categories.values());\n        const averagePopularity = faqs.reduce((sum, faq)=>sum + faq.popularity, 0) / faqs.length;\n        const mostPopularCategory = categories.reduce((prev, current)=>current.count > prev.count ? current : prev).name;\n        return {\n            totalFAQs: faqs.length,\n            totalCategories: categories.length,\n            averagePopularity: Math.round(averagePopularity),\n            mostPopularCategory\n        };\n    }\n    constructor(){\n        this.faqs = new Map();\n        this.categories = new Map();\n        this.initialized = false;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});