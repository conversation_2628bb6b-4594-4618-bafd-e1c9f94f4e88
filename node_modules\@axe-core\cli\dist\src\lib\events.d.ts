import type { AxeResults } from 'axe-core';
import type { EventParams } from '../types';
declare const _default: ({ silentMode, timer, cliReporter, verbose }: EventParams) => {
    startTimer: (message: string) => void;
    endTimer: (message: string) => void;
    waitingMessage: (loadDelayTime: number) => void;
    onTestStart: (url: string) => void;
    onTestComplete: (results: AxeResults) => void;
};
export default _default;
