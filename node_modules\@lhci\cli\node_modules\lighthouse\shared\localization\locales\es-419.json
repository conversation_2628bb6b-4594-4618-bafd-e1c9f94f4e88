{"core/audits/accessibility/accesskeys.js | description": {"message": "Las claves de acceso permiten a los usuarios dirigirse rápidamente a una parte concreta de la página. Para facilitar una navegación correcta, las claves de acceso deben ser únicas. [Obtén más información sobre las claves de acceso](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Los valores de `[accesskey]` no son únicos"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Los valores de `[accesskey]` son únicos"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> `role` de ARIA admite un subconjunto específico de atributos de `aria-*`. Si no coinciden estos valores, los atributos de `aria-*` no serán válidos. [Obtén más información para hacer coincidir los atributos de ARIA con sus roles](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no coinciden con sus roles"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Los atributos `[aria-*]` coinciden con sus roles"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Si un elemento no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén información para hacer que los elementos de comando sean más accesibles](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Los elementos `button`, `link` y `menuitem` no tienen nombres aptos para la accesibilidad."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Los elementos `button`, `link` y `menuitem` tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Si se configura `aria-hidden=\"true\"` en el documento `<body>`, las tecnologías de accesibilidad, como los lectores de pantalla, funcionarán de forma inconsistente. [Obtén información sobre cómo `aria-hidden` afecta el cuerpo del documento](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` aparece en el documento `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` no aparece en el documento `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Los objetos descendentes enfocables dentro de un elemento de `[aria-hidden=\"true\"]` impiden que esos elementos interactivos estén disponibles para los usuarios de tecnologías de accesibilidad, como los lectores de pantalla. [Obtén información sobre cómo`aria-hidden` afecta a los elementos enfocables](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Los elementos de `[aria-hidden=\"true\"]` contienen objetos descendentes enfocables"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Los elementos de `[aria-hidden=\"true\"]` no contienen objetos descendentes enfocables"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Si un campo de entrada no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información sobre las etiquetas de los campos de entrada](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Los campos de entrada de ARIA no tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Los campos de entrada de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Si un elemento del medidor no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información para nombrar elementos `meter` ](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Los elementos `meter` de ARIA no tienen nombres aptos para la accesibilidad."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Los elementos `meter` de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Si un elemento de `progressbar` no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén información para etiquetar elementos `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Los elementos `progressbar` de ARIA no tienen nombres aptos para la accesibilidad."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Los elementos `progressbar` de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Algunos roles de ARIA incluyen atributos obligatorios que describen el estado del elemento a los lectores de pantalla. [Obtén más información sobre los roles y los atributos requeridos](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Los elementos `[role]` no tienen todos los atributos `[aria-*]` necesarios"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Los elementos `[role]` tienen todos los atributos `[aria-*]` necesarios"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Algunos roles superiores de ARIA deben contener roles secundarios específicos para llevar a cabo las funciones de accesibilidad correspondientes. [Obtén más información sobre los roles y los elementos secundarios requeridos](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Los elementos ARIA con un atributo `[role]` deben incluir elementos secundarios con un `[role]` específico. Faltan algunos o todos los elementos secundarios necesarios."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Los elementos ARIA con un atributo `[role]` deben incluir elementos secundarios con un `[role]` específico. Se detectaron todos los elementos secundarios necesarios."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Algunos roles secundarios de ARIA deben incluirse dentro de roles principales específicos para llevar a cabo de manera adecuada las funciones de accesibilidad correspondientes. [Obtén más información sobre los roles de ARIA y el elemento superior requerido](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Los elementos `[role]` no se incluyen en los elementos principales necesarios"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Los elementos `[role]` se incluyen en los elementos principales correspondientes"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Los roles de ARIA deben tener valores válidos para realizar las funciones de accesibilidad correspondientes. [Obtén más información sobre los roles de ARIA válidos](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Los valores de `[role]` no son v<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Los valores de `[role]` son v<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Si un campo de activación no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información sobre los campos de activación](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Los campos de activación de ARIA no tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Los campos de activación de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Si un elemento de información sobre la herramienta no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información para nombrar elementos `tooltip` ](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Los elementos `tooltip` de ARIA no tienen nombres aptos para la accesibilidad."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Los elementos `tooltip` de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Si un elemento de `treeitem` no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta con un nombre genérico, por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información para etiquetar elementos `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Los elementos `treeitem` de ARIA no tienen nombres aptos para la accesibilidad."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Los elementos `treeitem` de ARIA tienen nombres aptos para la accesibilidad"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Las tecnologías de accesibilidad, como los lectores de pantalla, no pueden interpretar atributos de ARIA con valores no válidos. [Obtén más información sobre los valores válidos para los atributos de ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Los atributos `[aria-*]` no tienen valores válidos"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Los atributos `[aria-*]` tienen valores válidos"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Las tecnologías de accesibilidad, como los lectores de pantalla, no pueden interpretar los atributos de ARIA con nombres no válidos. [Obtén más información sobre los atributos de ARIA válidos](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no son válidos o no están bien escritos"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Los atributos `[aria-*]` son válidos y están bien escritos"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos con errores"}, "core/audits/accessibility/button-name.js | description": {"message": "Si un botón no tiene un nombre de accesibilidad, los lectores de pantalla lo leerán en voz alta como \"botón\", por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén información para que los botones sean más accesibles](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Los botones no tienen nombres accesibles"}, "core/audits/accessibility/button-name.js | title": {"message": "Los botones tienen nombres accesibles"}, "core/audits/accessibility/bypass.js | description": {"message": "Incluir maneras de omitir el contenido repetitivo permite a los usuarios que usan un teclado navegar por la página con mayor eficiencia. [Obtén más información sobre los bloqueos de omisiones](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "La página no contiene ningún título, vínculo de omisión ni región de punto de referencia"}, "core/audits/accessibility/bypass.js | title": {"message": "La página contiene un título, un vínculo de omisión o una región de punto de referencia"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Los textos con poco contraste resultan difíciles o imposibles de leer para muchos usuarios. [Obtén información para proporcionar suficiente contraste de color](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Los colores de fondo y de primer plano no tienen una relación de contraste adecuada."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Los colores de fondo y de primer plano tienen una relación de contraste adecuada"}, "core/audits/accessibility/definition-list.js | description": {"message": "Si las listas de definiciones no están bien marcadas, es posible que los lectores de pantalla las lean de forma confusa o imprecisa. [Obtén más información para estructurar las listas de definiciones correctamente](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Los elementos `<dl>` no contienen solo elementos `<script>`, `<template>` o `<div>`, o grupos de `<dt>` y `<dd>` ordenados correctamente."}, "core/audits/accessibility/definition-list.js | title": {"message": "Los elementos `<dl>` contienen solo elementos `<script>`, `<template>` o `<div>`, o grupos de `<dt>` y `<dd>` ordenados correctamente."}, "core/audits/accessibility/dlitem.js | description": {"message": "Los elementos de la lista de definiciones (`<dt>` y `<dd>`) deben incluirse en un elemento `<dl>` principal para garantizar que los lectores de pantalla los lean correctamente. [Obtén más información para estructurar las listas de definiciones correctamente](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Los elementos de la lista de definiciones no se incluyen en los elementos `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Los elementos de la lista de definiciones se incluyen en los elementos `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "El título les brinda a los usuarios de lectores de pantalla una descripción general de la página. Por su parte, los usuarios de motores de búsqueda lo usan mucho para determinar si una página es relevante para su búsqueda. [Obtén más información sobre los títulos de documentos](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "El documento no tiene un elemento `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "El documento tiene un elemento `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Todos los elementos enfocables deben tener un valor de `id` único para que las tecnologías de accesibilidad puedan detectarlos. [Obtén información para corregir `id` duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Los atributos de `[id]` de los elementos enfocables y activos no son únicos"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Los atributos de `[id]` de los elementos enfocables y activos son únicos"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "El valor de un ID de ARIA debe ser único para impedir que las tecnologías de accesibilidad omitan otras instancias. [Obtén información para corregir los IDs de ARIA duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Los ID de ARIA no son únicos"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Los ID de ARIA son únicos"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Las tecnologías de accesibilidad, como los lectores de pantalla, pueden tener dificultades para anunciar los campos de formulario con varias etiquetas, ya que usan la primera etiqueta, la última o todas. [Obtén más información para usar etiquetas de formularios](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Los campos de formulario tienen varias etiquetas"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Ningún campo del formulario tiene varias etiquetas"}, "core/audits/accessibility/frame-title.js | description": {"message": "Los usuarios de lectores de pantalla necesitan que los marcos tengan títulos para que se describa su contenido. [Obtén más información sobre los títulos de los marcos](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Los elementos `<frame>` o `<iframe>` no tienen título"}, "core/audits/accessibility/frame-title.js | title": {"message": "Los elementos `<frame>` o `<iframe>` tienen un título"}, "core/audits/accessibility/heading-order.js | description": {"message": "Los encabezados ordenados correctamente que no omiten niveles proporcionan la estructura semántica de la página, lo que facilita la navegación y comprensión cuando se usan tecnologías de accesibilidad. [Obtén más información sobre el orden de encabezados](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Los elementos de encabezado no están ordenados en una secuencia descendente"}, "core/audits/accessibility/heading-order.js | title": {"message": "Los elementos de encabezado están ordenados en una secuencia descendente"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Si no se especifica ningún atributo `lang` para una página, los lectores de pantalla considerarán que la página está en el idioma predeterminado que el usuario eligió al configurar el lector de pantalla. Si el idioma de la página es diferente del predeterminado, es posible que el lector de pantalla no lea bien el texto de la página. [Obtén más información sobre el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "El elemento `<html>` no tiene un atributo `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "El elemento `<html>` tiene un atributo `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Especificar un [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido permite a los lectores de pantalla leer el texto en voz alta correctamente. [Obtén más información para usar el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "El elemento `<html>` no tiene un valor válido para el atributo `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "El elemento `<html>` tiene un valor válido para su atributo `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "El texto de los elementos informativos debe ser corto y descriptivo. Los elementos decorativos se pueden ignorar usando un atributo alt vacío. [Obtén más información sobre el atributo `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Los elementos de imagen no tienen ningún atributo `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Los elementos de imagen tienen atributos `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Cuando se usa una imagen como botón `<input>`, resulta útil proporcionar un texto alternativo para permitir que los usuarios de lectores de pantalla entiendan cuál es la función del botón. [Obtén información sobre el texto alternativo de la imagen de entrada](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Los elementos `<input type=\"image\">` no tienen texto `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Los elementos `<input type=\"image\">` tienen texto `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Las etiquetas garantizan que las tecnologías de accesibilidad, como los lectores de pantalla, lean los controles de los formularios de forma correcta. [Obtén más información sobre las etiquetas de elementos de formulario](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Los elementos de formulario no tienen ninguna etiqueta asociada"}, "core/audits/accessibility/label.js | title": {"message": "Los elementos de formulario tienen etiquetas asociadas"}, "core/audits/accessibility/link-name.js | description": {"message": "Usar textos de vínculo (y textos alternativos para las imágenes, si estas se usan como vínculos) que sean reconocibles y únicos, y que se puedan enfocar mejora la experiencia de navegación de los usuarios de lectores de pantalla. [Obtén información para mejorar la accesibilidad de los vínculos](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Los vínculos no tienen nombres reconocibles"}, "core/audits/accessibility/link-name.js | title": {"message": "Los vínculos tienen nombres reconocibles"}, "core/audits/accessibility/list.js | description": {"message": "Los lectores de pantalla leen las listas en voz alta de una forma concreta. Se recomienda utilizar una estructura de lista adecuada para que los lectores de pantalla puedan leer las listas de forma correcta. [Obtén más información sobre la estructura de listas adecuada](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Las listas no contienen solo elementos `<li>` y elementos que admiten secuencias de comandos (`<script>` y `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Las listas contienen solo elementos `<li>` y elementos que admiten secuencias de comando (`<script>` y `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Los lectores de pantalla requieren que los elementos de lista (`<li>`) se incluyan en un elemento `<ul>`, `<ol>` o `<menu>` superior para leerlos correctamente. [Obtén más información sobre la estructura de listas adecuada](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Los elementos de lista (`<li>`) no se encuentran dentro de elementos superiores `<ul>`, `<ol>` o `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Los elementos de lista (`<li>`) se incluyen en los elementos superiores `<ul>`, `<ol>` o `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Los usuarios no esperan que una página se actualice automáticamente. Cuando eso sucede, vuelve a mostrarse la parte superior de la página. Esto puede generar una experiencia frustrante o confusa. [Obtén más información sobre la metaetiqueta de actualización](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "El documento usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "El documento no usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Desactivar el zoom genera problemas para los usuarios con visión reducida, quienes necesitan ampliar la pantalla para ver correctamente el contenido de las páginas web. [Obtén más información sobre la metaetiqueta de la vista del puerto](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` se usa en el elemento `<meta name=\"viewport\">` o el atributo `[maximum-scale]` tiene un valor inferior a 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "No se usa `[user-scalable=\"no\"]` en el elemento `<meta name=\"viewport\">` y el atributo `[maximum-scale]` no tiene un valor inferior a 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Los lectores de pantalla no pueden traducir contenido que no sea texto. El agregado de texto alternativo a los elementos `<object>` ayuda a los lectores de pantalla a transmitir el significado correspondiente a los usuarios. [Obtén más información sobre el texto alternativo de elementos `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Los elementos `<object>` no tienen texto alternativo"}, "core/audits/accessibility/object-alt.js | title": {"message": "Los elementos `<object>` tienen texto alternativo"}, "core/audits/accessibility/tabindex.js | description": {"message": "Si el valor es superior a 0, el orden de navegación es explícito. Aunque técnicamente esta es una posibilidad válida, suele producir experiencias frustrantes para los usuarios que necesitan usar las tecnologías de accesibilidad. [Obtén más información sobre el atributo `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Algunos elementos tienen un valor de `[tabindex]` superior a 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "No hay ningún elemento con un valor de `[tabindex]` superior a 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Asegurarse de que las celdas `<td>` que usan el atributo `[headers]` solo hagan referencia a otras celdas de la misma tabla puede mejorar la experiencia de los usuarios de lectores de pantalla. [Obtén más información sobre el atributo `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Las celdas de un elemento `<table>` que usan el atributo `[headers]` hacen referencia a un elemento `id` que no se encuentra en la misma tabla."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Las celdas de un elemento `<table>` que usa el atributo `[headers]` hacen referencia a las celdas de esa misma tabla."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Asegurarse de que los encabezados de las tablas siempre hagan referencia a un conjunto específico de celdas puede mejorar la experiencia de los usuarios de lectores de pantalla. [Obtén más información sobre los encabezados de las tablas](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Los elementos `<th>` y los elementos con `[role=\"columnheader\"/\"rowheader\"]` no contienen las celdas de datos que describen."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Los elementos `<th>` y los elementos con `[role=\"columnheader\"/\"rowheader\"]` contienen celdas de datos que describen."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Especificar un [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido en los elementos ayuda a asegurar que los lectores de pantalla pronuncien bien el texto correspondiente. [Obtén más información para usar el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Los atributos `[lang]` no tienen un valor válido"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Los atributos `[lang]` tienen un valor válido"}, "core/audits/accessibility/video-caption.js | description": {"message": "Si un video tiene subtítulos, los usuarios sordos o con dificultades auditivas pueden acceder a la información más fácilmente. [Obtén más información sobre los subtítulos de videos](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Los elementos `<video>` no contienen un elemento `<track>` con `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Los elementos `<video>` contienen un elemento `<track>` con `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Valor actual"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Token de sugerencias"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` permite que los usuarios envíen formularios más rápido. Para reducir el trabajo del usuario, te recomendamos establecer un valor válido para el atributo `autocomplete` a fin de habilitar la función. [Más información sobre `autocomplete` en formularios](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Los elementos de `<input>` no tienen atributos de `autocomplete` correctos"}, "core/audits/autocomplete.js | manualReview": {"message": "Requiere revisión manual"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Revisa el orden de los tokens"}, "core/audits/autocomplete.js | title": {"message": "Los elementos de `<input>` usan la función de `autocomplete` correctamente"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Tokens de `autocomplete`: \"{token}\" no es válido en {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Revisa el orden de los tokens: \"{tokens}\" en {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Permite una acción"}, "core/audits/bf-cache.js | description": {"message": "Para navegar, a veces se regresa a la página anterior o a la siguiente. La memoria caché atrás/adelante (bfcache) puede acelerar estas navegaciones de retorno. [Más información sobre la bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo de la falla}other{# motivos de la falla}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivo de la falla"}, "core/audits/bf-cache.js | failureTitle": {"message": "Se impidió el restablecimiento de la memoria caché atrás/adelante de la página"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON> de falla"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "No permite una acción"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Compatibilidad con el navegador pendiente"}, "core/audits/bf-cache.js | title": {"message": "La página no impidió el restablecimiento de la memoria caché atrás/adelante"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Las extensiones de Chrome afectaron de forma negativa al rendimiento de carga de esta página. Prueba a auditarla en modo incógnito o desde un perfil de Chrome sin extensiones."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Evaluación de la secuencia de comandos"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Análisis de la secuencia de comandos"}, "core/audits/bootup-time.js | columnTotal": {"message": "Tiempo de CPU total"}, "core/audits/bootup-time.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de JS. Para ello, puedes entregar cargas útiles de JS más pequeñas. [Obtén información para reducir el tiempo de ejecución de JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduce el tiempo de ejecución de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Tiempo de ejecución de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Quita de los paquetes los módulos de JavaScript que sean grandes y estén duplicados para reducir la cantidad de bytes que consume innecesariamente la actividad de red. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Quita los módulos duplicados de los paquetes de JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Los GIF de gran tamaño no son eficientes para mostrar contenido animado. En su lugar, te recomendamos utilizar formatos de video MPEG4/WebM para animaciones y formatos PNG/WebP para imágenes estáticas a fin de ahorrar bytes de la red. [Más información sobre los formatos de video eficientes](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Usa formatos de video para incluir contenido animado"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "El complemento Polyfill y las transformaciones permiten que los navegadores heredados utilicen nuevas funciones de JavaScript. Sin embargo, la mayoría no es necesaria en los navegadores actualizados. Para tus paquetes de JavaScript, adopta una estrategia moderna de implementación de secuencias de comandos mediante la detección de funciones de módulo/no módulo a fin de reducir la cantidad de código que se envía a los navegadores actualizados, mientras se mantiene la compatibilidad con navegadores heredados. [Más información sobre cómo usar JavaScript actualizado](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Evita entregar instancias heredadas de JavaScript a navegadores modernos"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Los formatos de imagen como WebP y AVIF por lo general proporcionan una mejor compresión que PNG o JPEG, lo que se traduce en descargas más rápidas y un menor consumo de los datos. [Obtén más información sobre los formatos de imagen modernos](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Publica imágenes con formatos de próxima generación"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Te recomendamos que uses la carga diferida para las imágenes ocultas y fuera de pantalla una vez que hayan terminado de cargarse todos los recursos críticos a fin de reducir el tiempo de carga. [Obtén información para diferir las imágenes fuera de pantalla](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Posterga la carga de imágenes que no aparecen en pantalla"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Hay recursos que bloquean el primer procesamiento de imagen de la página. Te recomendamos entregar los elementos JS/CSS críticos intercalados y postergar todos los JS/estilos que no sean críticos. [Obtén información para eliminar los recursos que bloquean la renderización](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimina los recursos que bloqueen el renderizado"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Trabajar con cargas útiles de red de gran tamaño resulta oneroso para el usuario, además de aumentar considerablemente el tiempo de carga de las páginas. [Obtén información para reducir los tamaños de cargas útiles](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "El tamaño total era {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evita cargas útiles de red de gran tamaño"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita cargas útiles de red de gran tamaño"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Si reduces los archivos CSS, puedes achicar el tamaño de la carga útil de la red. [Obtén información para reducir CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Reduce el uso de CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Si reduces los archivos JavaScript, puedes achicar el tamaño de la carga útil y el tiempo de análisis de secuencias de comandos. [Obtén información sobre cómo reducir JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Reducir el uso de JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Quita las reglas sin usar de las hojas de estilo y retrasa el código CSS sin usar para el contenido de la mitad superior de la página, a fin de reducir los bytes que consume la actividad de red. [Obtén información para reducir el código CSS sin usar](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduce el código CSS sin usar"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reduce el código JavaScript sin usar y posterga la carga de las secuencias de comandos hasta que se necesiten, a fin de disminuir los bytes que consume la actividad de red. [Obtén información para reducir el JavaScript sin usar](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduce el código JavaScript sin usar"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "La duración en caché por un período prolongado puede acelerar la carga de la página cuando el usuario la visita de manera repetida. [Obtén más información sobre las políticas de caché eficientes](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 recurso}other{Se encontraron # recursos}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Publica elementos estáticos con una política de caché eficaz"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Usa una política de caché eficaz en recursos estáticos"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Las imágenes optimizadas se cargan más rápido y consumen menos datos móviles. [Obtén más información para codificar imágenes de manera eficiente](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifica las imágenes de forma eficaz"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensiones reales"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensiones que se muestran"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Las imágenes eran más grandes que su tamaño de visualización"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Las imágenes eran apropiadas para su tamaño de visualización"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Muestra imágenes con un tamaño adecuado para ahorrar datos móviles y reducir el tiempo de carga. [Obtén información para ajustar el tamaño de las imágenes](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Usa un tamaño adecuado para las imágenes"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Los recursos basados en texto se deberían publicar comprimidos (gzip, deflate o brotli) para minimizar el total de bytes de la red. [Obtén más información sobre la compresión de texto](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Habilita la compresión de texto"}, "core/audits/content-width.js | description": {"message": "Si el ancho del contenido de tu app no coincide con el del viewport, es posible que la app no esté optimizada para pantallas de dispositivos móviles. [Obtén información para ajustar el tamaño del contenido para el viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "El tamaño de viewport de {innerWidth} px no coincide con el tamaño de ventana de {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "El contenido no tiene el tamaño correcto para el viewport"}, "core/audits/content-width.js | title": {"message": "El contenido tiene el tamaño correcto para el viewport"}, "core/audits/critical-request-chains.js | description": {"message": "Las cadenas de solicitudes críticas que se muestran a continuación indican qué recursos son de alta prioridad en la carga. Te recomendamos que reduzcas la longitud de las cadenas, disminuyas el tamaño de los recursos para la descarga o postergues la descarga de recursos innecesarios para mejorar la carga de la página. [Obtén información para evitar encadenar solicitudes críticas](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 cadena}other{Se encontraron # cadenas}}"}, "core/audits/critical-request-chains.js | title": {"message": "Evita encadenar solicitudes críticas"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravedad"}, "core/audits/csp-xss.js | description": {"message": "Una Política de Seguridad del Contenido (CSP) potente reduce enormemente el riesgo de los ataques de secuencia de comandos entre sitios (XSS). [Obtén información para usar un CSP a fin de evitar XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxis"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "La página contiene una CSP definida por una etiqueta <meta>. Considera trasladar la CSP a un encabezado HTTP o definir otra CSP estricta en el encabezado."}, "core/audits/csp-xss.js | noCsp": {"message": "No se encontró ninguna CSP en el modo de aplicación forzosa"}, "core/audits/csp-xss.js | title": {"message": "Asegúrate de que la CSP sea eficaz contra los ataques XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Baja/advertencia"}, "core/audits/deprecations.js | columnLine": {"message": "Lín<PERSON>"}, "core/audits/deprecations.js | description": {"message": "Con el tiempo, se quitarán las APIs no disponibles del navegador. [Obtén más información sobre las APIs no disponibles](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 advertencia}other{Se encontraron # advertencias}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Usa API obsoletas"}, "core/audits/deprecations.js | title": {"message": "Evita las API obsoletas"}, "core/audits/dobetterweb/charset.js | description": {"message": "Se debe declarar la codificación de caracteres. Para eso, puede agregarse una etiqueta `<meta>` en los primeros 1024 bytes del HTML o en el encabezado de respuesta HTTP Content Type. [Obtén más información para declarar la codificación de caracteres](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Falta la declaración del charset o aparece demasiado tarde en el HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Define el charset adecuadamente"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Especificar un DOCTYPE evita que el navegador cambie al modo no estándar. [Obtén más información sobre la declaración DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "El nombre de DOCTYPE debe ser un string `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "El documento contiene un `doctype` que activa `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "El documento debe contener un DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Se esperaba que publicId fuera una string vacía"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Se esperaba que systemId fuera una string vacía"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "El documento contiene un `doctype` que activa `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "La página no tiene el DOCKTYPE de HTML; por lo tanto, activa el modo no estándar"}, "core/audits/dobetterweb/doctype.js | title": {"message": "La página tiene el DOCTYPE de HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estadística"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Los DOM de gran tamaño aumentarán el uso de la memoria, harán que los [cálculos de estilos](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) tarden más y generarán costosos [reprocesamientos del diseño](https://developers.google.com/speed/articles/reflow). [Obtén información para evitar un tamaño de DOM excesivo](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}other{# elementos}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evita un tamaño excesivo de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidad máxima de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Número máximo de elementos secundarios"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evita un tamaño excesivo de DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Los sitios que solicitan a los usuarios su ubicación sin contexto los confunden o los hacen desconfiar. Te recomendamos vincular la solicitud a una acción del usuario. [Obtén más información sobre el permiso de ubicación geográfica](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita el permiso de ubicación geográfica al cargar la página"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita solicitar el permiso de ubicación geográfica al cargar la página"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipo de problema"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Los problemas registrados en el panel `Issues` de Herramientas para desarrolladores de Chrome indican problemas sin resolver. <PERSON>ueden provenir de fallas de solicitudes de red, de controles de seguridad insuficientes y de otros problemas del navegador. En Herramientas para desarrolladores de Chrome, abre el panel Problemas para obtener más información sobre cada problema."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Se registraron los problemas en el panel `Issues` de Herramientas para desarrolladores de Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "La política de origen cruzado bloqueó el recurso"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Los anuncios están utilizando muchos recursos"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "No se encontraron problemas en el panel `Issues` de Herramientas para desarrolladores de Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versión"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Se detectaron todas las bibliotecas JavaScript de frontend de la página. [Obtén más información sobre esta auditoría de diagnóstico de detección de la biblioteca de JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Se detectaron bibliotecas JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "En el caso de usuarios con una conexión lenta, las secuencias de comandos externas que se incorporan dinámicamente a través de `document.write()` pueden retrasar la carga de la página decenas de segundos. [Obtén información para evitar document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "No usa `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Los sitios que solicitan a los usuarios permiso para enviar notificaciones sin contexto los confunden o los hacen desconfiar. Te recomendamos vincular la solicitud a los gestos del usuario. [Obtén más información para obtener permiso de forma responsable para enviar notificaciones](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita el permiso de notificaciones al cargar la página"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita solicitar el permiso de notificaciones al cargar la página"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ofrece más beneficios que HTTP/1.1, como encabezados binarios y multiplexado. [Obtén más información sobre HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitud no se entregó mediante HTTP/2}other{# solicitudes no se entregaron mediante HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Usa HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Te recomendamos marcar tus objetos de escucha de eventos táctiles y de la rueda del mouse como `passive` para mejorar el rendimiento de desplazamiento de tu página. [Obtén más información sobre la adopción de objetos de escucha de eventos pasivos](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "No usa objetos de escucha pasivos para mejorar el rendimiento del desplazamiento"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Usa objetos de escucha pasivos para mejorar el rendimiento del desplazamiento"}, "core/audits/errors-in-console.js | description": {"message": "Los errores registrados en la consola indican la existencia de problemas no resueltos. Es posible que se deban a problemas con solicitudes de red y otros relativos al navegador. [Más información sobre estos errores en la auditoría de diagnóstico de la consola](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Se registraron errores del navegador en la consola"}, "core/audits/errors-in-console.js | title": {"message": "No se registraron errores del navegador en la consola"}, "core/audits/font-display.js | description": {"message": "Utiliza la función `font-display` de CSS a fin de que los usuarios vean el texto mientras se carga la fuente para sitios web. [Obtén más información sobre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Asegúrate de que el texto permanezca visible mientras se carga la fuente web"}, "core/audits/font-display.js | title": {"message": "Todo el texto permanece visible mientras se carga la fuente para sitios web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse no pudo verificar automáticamente el valor de `font-display` de la URL de origen {fontOrigin}.}other{Lighthouse no pudo verificar automáticamente los valores de `font-display` de la URL de origen {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Relación de aspecto (real)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Relación de aspecto (visualizada)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Las dimensiones de visualización de las imágenes deben coincidir con la relación de aspecto natural. [Obtén más información sobre la relación de aspecto de la imagen](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Muestra imágenes con una relación de aspecto incorrecta"}, "core/audits/image-aspect-ratio.js | title": {"message": "Muestra imágenes con una relación de aspecto correcta"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Tamaño real"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Tamaño de visualización"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Tam<PERSON><PERSON> esperado"}, "core/audits/image-size-responsive.js | description": {"message": "Para maximizar la claridad de la imagen, sus dimensiones naturales deben ser proporcionales al tamaño de la pantalla y a la proporción de píxeles. [Obtén información para proporcionar imágenes responsivas](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Entrega imágenes con baja resolución"}, "core/audits/image-size-responsive.js | title": {"message": "Entrega imágenes con la resolución adecuada"}, "core/audits/installable-manifest.js | already-installed": {"message": "La app ya está instalada"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "No se pudo descargar un ícono requerido del manifiesto"}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivo de la falla"}, "core/audits/installable-manifest.js | description": {"message": "Service worker es la tecnología que permite que tu app use varias funciones de las apps web progresivas, como el modo sin conexión, el agregado a la pantalla principal y las notificaciones push. Si se implementan el service worker y el manifiesto de forma adecuada, los navegadores podrán solicitar de forma proactiva a los usuarios que agreguen tu app a la pantalla principal, lo que puede aumentar la interacción. [Obtén más información sobre los requisitos de instalación del manifiesto](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo}other{# motivos}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "El manifiesto de la app web o el service worker no cumplen los requisitos para la instalación"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "La URL de Play Store de la aplicación y el ID de Play Store no coinciden"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Se cargó la página en una ventana de incógnito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "La propiedad \"display\" del manifiesto debe ser una de estas opciones: \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "El manifiesto contiene el campo \"display_override\", y el primer modo de visualización admitido debe ser una de estas opciones: \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "No se pudo recuperar el manifiesto, está vacío o no se pudo analizar"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "La URL del manifiesto cambió mientras este se estaba recuperando."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "El manifiesto no contiene los campos \"name\" ni \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "El manifiesto no contiene un ícono adecuado. Se requiere un formato PNG, SVG o WebP de al menos {value0} px, se debe definir el atributo de tamaño y, si se establece el atributo de propósito, este deberá incluir \"cualquiera\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Ninguno de los íconos proporcionados es de al menos {value0} px cuadrados en formato PNG, SVG o WebP, con el atributo de propósito sin establecer o establecido en \"cualquiera\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "El ícono que se descargó estaba vacío o dañado"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "No se proporcionó un ID de Play Store"}, "core/audits/installable-manifest.js | no-manifest": {"message": "La página no tiene la URL <link> del manifiesto"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "No se detectó un service worker coincidente. Es posible que debas volver a cargar la página o revisar que el alcance del service worker de la página actual abarque el alcance y la URL de inicio del manifiesto."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "No se pudo verificar el service worker porque no se incluyó el campo \"start_url\" en el manifiesto"}, "core/audits/installable-manifest.js | noErrorId": {"message": "No se reconoce el ID \"{errorId}\" del error de instalación"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "El servidor de origen de la página no es seguro"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "La página no se cargó en el marco principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "La página no funciona sin conexión"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Se desisntaló AWP y se están restableciendo las verificaciones de instalabilidad."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "La plataforma de aplicaciones especificada no es compatible con Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "El manifiesto especifica prefer_related_applications como verdadero"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications solo es compatible en Chrome Beta y en canales estables de Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse no pudo determinar si había un service worker. Prueba con una nueva versión de Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "El esquema de URL del manifiesto ({scheme}) no es compatible en Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "La URL de inicio del manifiesto no es válida"}, "core/audits/installable-manifest.js | title": {"message": "El manifiesto de la app web y el service worker cumplen los requisitos para la instalación"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Una URL en el manifiesto contiene un nombre de usuario, una contraseña o un puerto"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "La página no funciona sin conexión. No se podrá instalar la página después de la versión estable de Chrome 93 (agosto de 2021)."}, "core/audits/is-on-https.js | allowed": {"message": "Permitida"}, "core/audits/is-on-https.js | blocked": {"message": "Bloqueada"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL no segura"}, "core/audits/is-on-https.js | columnResolution": {"message": "Resolución de la solicitud"}, "core/audits/is-on-https.js | description": {"message": "Todos los sitios deben estar protegidos con el protocolo HTTPS, incluso aquellos que no controlan datos sensibles. Esta acción implica evitar [contenido mixto](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), es decir, la carga de algunos recursos por HTTP a pesar de que la solicitud inicial fuera por HTTPS. Este protocolo evita que intrusos manipulen o escuchen de forma pasiva las comunicaciones entre tu app y los usuarios. Además, HTTPS es un requisito previo del protocolo HTTP/2 y muchas APIs nuevas de plataformas web. [Obtén más información sobre HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró una solicitud no segura}other{Se encontraron # solicitudes no seguras}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "No usa HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Usa HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Se actualizó automáticamente a HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Permitida con advertencia"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Este es el elemento con contenido más grande para el procesamiento de imagen en viewport. [Más información sobre el elemento del Procesamiento de imagen con contenido más grande](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elemento del Procesamiento de imagen con contenido más grande"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribución en CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Estos elementos de DOM contribuyen principalmente con el CLS de la página. [Más información para mejorar CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "No realices cambios grandes en el diseño"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Las imágenes de la mitad superior de la página que se cargan de forma diferida se renderizan más tarde en el ciclo de vida de la página, lo que puede retrasar el Procesamiento de imagen con contenido más grande. [Obtén más información sobre la carga diferida óptima](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "La imagen del procesamiento de imagen con contenido más grande se cargó de forma diferida"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "La imagen del procesamiento de imagen con contenido más grande no se cargó de forma diferida"}, "core/audits/long-tasks.js | description": {"message": "Se muestran las tareas más largas en el subproceso principal (permite identificar las tareas que más contribuyen al mayor retraso de entrada). [Más información para evitar tareas largas en el subproceso principal](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró # tarea larga}other{Se encontraron # tareas largas}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON>ta tareas largas en el subproceso principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoría"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de JS. Para ello, puedes entregar cargas útiles de JS más pequeñas. [Más información para minimizar el trabajo del subproceso principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimiza el trabajo del hilo principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiza el trabajo del hilo principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Para alcanzar la mayor cantidad de usuarios, los sitios tienen que funcionar en todos los navegadores principales. [Obtén información sobre la compatibilidad entre navegadores](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "El sitio funciona en diferentes navegadores"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Garantiza que las páginas individuales puedan vincularse directamente a través de la URL y que las URLs sean únicas para que se puedan compartir en redes sociales. [Obtén más información para proporcionar vínculos directos](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tiene una URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Las transiciones deben ser ágiles cuando presionas en diferentes lugares, incluso con conexiones de red lentas. Esta experiencia es la clave del rendimiento percibido por el usuario. [Obtén más información sobre las transiciones de la página](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "No parece que se bloqueen las transiciones de la página en la red"}, "core/audits/maskable-icon.js | description": {"message": "El uso de un ícono adaptable garantiza que la imagen cubrirá toda la forma sin que se aplique el formato letterbox cuando se instale la app en un dispositivo. [Obtén información sobre los íconos de manifiesto adaptables](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "El manifiesto no tiene un ícono enmascarable"}, "core/audits/maskable-icon.js | title": {"message": "El manifiesto tiene un ícono enmascarable"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "El Cambio de diseño acumulado mide el movimiento de los elementos visibles dentro del viewport. [Obtén más información sobre la métrica de Cambio de diseño acumulado](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "La Interacción a la siguiente pintura mide la capacidad de respuesta de la página, es decir, cuánto tarda en responder de manera visible a las entradas del usuario. [Obtén más información sobre la métrica de Interacción a la siguiente pintura](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "El primer procesamiento de imagen con contenido indica el momento en el que se visualiza en la pantalla el primer texto o imagen. [Obtén más información sobre la métrica de Primer procesamiento de imagen con contenido](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "La primera pintura significativa mide el momento en que se muestra el contenido principal de la página. [Obtén más información sobre la métrica de Primera pintura significativa](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "El tiempo de carga indica cuánto tarda una página en ser totalmente interactiva. [Obtén más información sobre la métrica de tiempo de carga](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "La métrica Procesamiento de imagen con contenido más grande indica el momento en que se pinta el texto o la imagen más grandes. [Más información sobre la métrica de Procesamiento de imagen con contenido más grande](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "El máximo retraso de primera entrada potencial que podrían experimentar los usuarios es la duración de la tarea más larga. [Obtén más información sobre la métrica de Máximo retraso de primera entrada potencial](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "El Índice de velocidad indica la rapidez con la que se puede ver el contenido de una página. [Obtén más información sobre la métrica de Índice de velocidad](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Suma todos los períodos entre FCP y el Tiempo de carga, cuando la tarea tarda más de 50 ms. El resultado se expresa en milisegundos. [Obtén más información sobre la métrica de Tiempo de bloqueo total](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Los tiempos de ida y vuelta (RTT) de la red afectan mucho el rendimiento. Los valores altos de RTT respecto de un origen son indicio de que usar servidores más cercanos al usuario podría mejorar el rendimiento. [Obtén más información acerca del tiempo de ida y vuelta](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Tiempos de ida y vuelta de la red"}, "core/audits/network-server-latency.js | description": {"message": "Las latencias del servidor pueden afectar el rendimiento de la Web. Un nivel alto de latencia del servidor en un origen indica que el servidor está sobrecargado o que su rendimiento de backend es bajo. [Obtén más información sobre el tiempo de respuesta del servidor](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latencias de backend del servidor"}, "core/audits/no-unload-listeners.js | description": {"message": "El evento `unload` no se ejecuta de forma confiable y su escucha puede impedir las optimizaciones del navegador, como la Memoria caché atrás/adelante. En su lugar, usa los eventos `pagehide` o `visibilitychange`. [Más información sobre la descarga de objetos de escucha de eventos](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registra un objeto de escucha de `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Evita los objetos de escucha de eventos de `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Es posible que las animaciones que no estén compuestas se vean entrecortadas y aumenten el valor de CLS. [Más información para evitar las animaciones no compuestas](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró # elemento animado}other{Se encontraron # elementos animados}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Es posible que la propiedad relacionada con el filtro esté moviendo píxeles"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "El destino tiene otra animación que no es compatible"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "El efecto tiene otro modo compuesto además de \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evita las animaciones no compuestas"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "La propiedad relacionada con la transformación depende del tamaño del recuadro"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Propiedad de CSS no compatible: {properties}}other{Propiedades de CSS no compatibles: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "El efecto tiene parámetros de tiempo no compatibles"}, "core/audits/performance-budget.js | description": {"message": "Asegúrate de que la cantidad y el tamaño de las solicitudes de red sean menores que los valores objetivo establecidos en el presupuesto de rendimiento. [Obtén más información sobre los presupuestos de rendimiento](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 solicitud}other{# solicitudes}}"}, "core/audits/performance-budget.js | title": {"message": "Estimación de rendimiento"}, "core/audits/preload-fonts.js | description": {"message": "Precarga fuentes `optional` para que los visitantes nuevos puedan usarlas. [Más información sobre la precarga de fuentes](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "No se precargaron las fuentes con `font-display: optional`"}, "core/audits/preload-fonts.js | title": {"message": "Se precargaron las fuentes con `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Si se agrega el elemento LCP de forma dinámica a la página, debes precargar la imagen a fin de mejorar LCP. [Obtén más información sobre la precarga de elementos de LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Precarga la imagen del procesamiento de imagen con contenido más grande"}, "core/audits/redirects.js | description": {"message": "Las redirecciones provocan retrasos adicionales antes de que la página se cargue. [Obtén información para evitar redireccionamientos de la página](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evita que haya varias redirecciones de página"}, "core/audits/resource-summary.js | description": {"message": "A fin de configurar presupuestos para la cantidad y el tamaño de los recursos de la página, agrega un archivo budget.json. [Obtén más información sobre los presupuestos de rendimiento](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 solicitud • {byteCount, number, bytes} KiB}other{# solicitudes • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Asegúrate de que la cantidad de solicitudes y los tamaños de transferencia sean reducidos"}, "core/audits/seo/canonical.js | description": {"message": "Los vínculos canónicos indican qué URL mostrar en los resultados de la búsqueda. [Obtén más información sobre los vínculos canónicos](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Varias URL en conflicto ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL no válida ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Hace referencia a otra ubicación de `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "No es una URL absoluta ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Apunta a la URL raíz del dominio (la página principal), en lugar de a una página de contenido equivalente"}, "core/audits/seo/canonical.js | failureTitle": {"message": "El documento no tiene un vínculo `rel=canonical` válido"}, "core/audits/seo/canonical.js | title": {"message": "El documento tiene un atributo `rel=canonical` válido"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "El vínculo no se puede rastrear"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Es posible que los motores de búsqueda utilicen atributos `href` en los vínculos para rastrear los sitios web. Asegúrate de que el atributo `href` de los elementos de anclaje esté vinculado a un destino adecuado, a fin de que se puedan descubrir más páginas del sitio. [Más información para que los vínculos sean rastreables](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Los vínculos no son rastreables"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Los vínculos son rastreables"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Texto ilegible adicional"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Tamaño de fuente"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% de texto de página"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Las fuentes con un tamaño inferior a 12 px son demasiado pequeñas y poco legibles, lo que obliga a los visitantes que acceden con dispositivos móviles a pellizcar la pantalla para ampliarla y poder leer el texto. Intenta que más del 60% del texto de la página tenga un tamaño igual o superior a 12 px. [Obtén más información sobre los tamaños de fuente legibles](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} del texto es legible"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "El texto es ilegible porque no hay metaetiquetas de la vista del puerto optimizadas para pantallas de dispositivos móviles."}, "core/audits/seo/font-size.js | failureTitle": {"message": "El documento no usa tamaños de fuente legibles"}, "core/audits/seo/font-size.js | legibleText": {"message": "Texto legible"}, "core/audits/seo/font-size.js | title": {"message": "El documento usa tamaños de fuente legibles"}, "core/audits/seo/hreflang.js | description": {"message": "Los vínculos de hreflang indican a los motores de búsqueda qué versión de una página deben incluir en los resultados de la búsqueda para una región o un idioma determinados. [Obtén más información sobre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "El documento no tiene un atributo `hreflang` válido"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valor de href relativo"}, "core/audits/seo/hreflang.js | title": {"message": "El documento tiene un atributo `hreflang` válido"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "El código de idioma es inesperado"}, "core/audits/seo/http-status-code.js | description": {"message": "Es posible que las páginas con códigos de estado HTTP de error no se indexen correctamente. [Obtén más información sobre los códigos de estado HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "El código de estado HTTP de la página no es válido"}, "core/audits/seo/http-status-code.js | title": {"message": "El código de estado HTTP de la página es válido"}, "core/audits/seo/is-crawlable.js | description": {"message": "Los motores de búsqueda no pueden incluir tus páginas en los resultados de la búsqueda si no tienen permiso para rastrearlas. [Obtén más información sobre las directivas del rastreador](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Se bloqueó la indexación de la página"}, "core/audits/seo/is-crawlable.js | title": {"message": "No se bloqueó la indexación de la página"}, "core/audits/seo/link-text.js | description": {"message": "El texto descriptivo de los vínculos ayuda a los motores de búsqueda a entender tu contenido. [Obtén información para que los vínculos sean más accesibles](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 vínculo}other{Se encontraron # vínculos}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Los vínculos no tienen texto descriptivo"}, "core/audits/seo/link-text.js | title": {"message": "Los vínculos tienen texto descriptivo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Ejecuta la [Herramienta de prueba de datos estructurados](https://search.google.com/structured-data/testing-tool/) y el [Linter de datos estructurados](http://linter.structured-data.org/) para validar los datos estructurados. [Obtén más información sobre los datos estructurados](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Los datos estructurados son v<PERSON><PERSON>os"}, "core/audits/seo/meta-description.js | description": {"message": "Se pueden incluir metadescripciones en los resultados de la búsqueda para resumir el contenido de la página. [Obtén más información sobre la metadescripción](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "El texto de la descripción está vacío."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "El documento no tiene una metadescripción"}, "core/audits/seo/meta-description.js | title": {"message": "El documento tiene una metadescripción"}, "core/audits/seo/plugins.js | description": {"message": "Los motores de búsqueda no pueden indexar el contenido de los complementos y muchos dispositivos limitan el uso de complementos o no los admiten. [Obtén más información para evitar los complementos](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "El documento usa complementos"}, "core/audits/seo/plugins.js | title": {"message": "Los documentos evitan el uso de complementos"}, "core/audits/seo/robots-txt.js | description": {"message": "Si el formato del archivo robots.txt no es correcto, es posible que los rastreadores no puedan interpretar cómo quieres que se rastree o indexe tu sitio web. [Obtén más información acerca de los archivos robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La solicitud de robots.txt mostró el siguiente estado de HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Se encontró 1 error}other{Se encontraron # errores}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse no pudo descargar un archivo robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt no es válido"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt es válido"}, "core/audits/seo/tap-targets.js | description": {"message": "Los elementos interactivos, como los botones y vínculos, deben ser suficientemente grandes (48 × 48 px) y tener alrededor el espacio necesario para que sea posible tocarlos con facilidad sin presionar otros elementos a la vez. [Obtén más información sobre los botones táctiles](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "El {decimalProportion, number, percent} de los elementos táctiles tiene un tamaño adecuado"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Los elementos táctiles son demasiado pequeños porque no hay metaetiquetas de la vista del puerto optimizadas para pantallas de dispositivos móviles"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "El tamaño de los elementos táctiles no es el adecuado"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Elementos superpuestos"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Elemento táctil"}, "core/audits/seo/tap-targets.js | title": {"message": "El tamaño de los elementos táctiles es el adecuado"}, "core/audits/server-response-time.js | description": {"message": "Es importante que el tiempo de respuesta del servidor para el documento principal sea breve, ya que afecta a las demás solicitudes. [Obtén más información sobre la métrica Tiempo hasta el primer byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "El documento raíz tardó {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reduce el tiempo de respuesta del servidor"}, "core/audits/server-response-time.js | title": {"message": "El tiempo de respuesta inicial del servidor fue breve"}, "core/audits/service-worker.js | description": {"message": "El service worker es la tecnología que permite que tu app use varias funciones de app web progresiva, como el modo sin conexión, el agregado a la pantalla principal y las notificaciones push. [Obtén más información sobre los service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Un service worker controla esta página, pero no se encontró ningún atributo `start_url` porque no se pudo analizar el archivo de manifiesto como un JSON válido"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Un service worker controla esta página, pero el atributo `start_url` ({startUrl}) está fuera del alcance del service worker ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Un service worker controla esta página, pero no se encontró el atributo `start_url` porque no se obtuvo ningún manifiesto."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Este origen tiene al menos un service worker, pero la página ({pageUrl}) no está dentro del alcance."}, "core/audits/service-worker.js | failureTitle": {"message": "No registra un service worker que controle la página y `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registra un service worker que controle la página y `start_url`"}, "core/audits/splash-screen.js | description": {"message": "El uso de una pantalla de presentación con un tema asegura que los usuarios tengan una experiencia de alta calidad al iniciar tu app desde sus pantallas principales. [Obtén más información sobre las pantallas de presentación](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "No se configuró para una pantalla de presentación personalizada"}, "core/audits/splash-screen.js | title": {"message": "Se configuró para una pantalla de presentación personalizada"}, "core/audits/themed-omnibox.js | description": {"message": "Se puede aplicar un tema a la barra de direcciones del navegador para que combine con tu sitio web. [Obtén más información sobre los temas de la barra de direcciones](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "No establece un color de tema para la barra de direcciones."}, "core/audits/themed-omnibox.js | title": {"message": "Establece un color de tema para la barra de direcciones."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (éxito de clientes)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (redes sociales)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Producto"}, "core/audits/third-party-facades.js | description": {"message": "Algunas incorporaciones de terceros pueden ser de carga diferida. Te recomendamos reemplazarlas con una fachada hasta que sean obligatorias. [Obtén información para diferir terceros con una fachada](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Hay # alternativa de fachada disponible}other{Hay # alternativas de fachada disponibles}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Algunos recursos de terceros pueden ser de carga diferida con una fachada"}, "core/audits/third-party-facades.js | title": {"message": "Recursos de terceros de carga diferida con fachadas"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Terceros"}, "core/audits/third-party-summary.js | description": {"message": "El código de terceros puede reducir en gran medida el rendimiento de carga. Limita la cantidad de proveedores externos redundantes y prueba cargar el código de terceros después de que haya finalizado la carga principal de tu página. [Obtén información para minimizar el impacto de terceros](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "El código de terceros bloqueó el subproceso principal por {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduce el impacto del código de terceros"}, "core/audits/third-party-summary.js | title": {"message": "Minimiza el uso del código de terceros"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Medición"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "M<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Establece un presupuesto de tiempo para controlar el rendimiento de tu sitio. Los sitios con buen rendimiento se cargan sin demora y responden rápido a los eventos de entrada del usuario. [Obtén más información sobre los presupuestos de rendimiento](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Estimación de tiempo"}, "core/audits/unsized-images.js | description": {"message": "Establece un atributo explícito de altura y ancho en los elementos de imagen para reducir los cambios de diseño y mejorar la métrica del CLS. [Más información para configurar las dimensiones de las imágenes](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Los elementos de imagen no tienen ningún atributo `width` ni `height` explícito"}, "core/audits/unsized-images.js | title": {"message": "Los elementos de imagen tienen atributos `width` y `height` explícitos"}, "core/audits/user-timings.js | columnType": {"message": "Tipo"}, "core/audits/user-timings.js | description": {"message": "Te recomendamos que incorpores la API de User Timing en tu app para calcular su rendimiento real durante las principales experiencias del usuario. [Obtén más información sobre las marcas de User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 tiempo de usuario}other{# tiempos de usuario}}"}, "core/audits/user-timings.js | title": {"message": "Medidas y marcas de Tiempos de usuario"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Se encontró una instancia de `<link rel=preconnect>` para \"{securityOrigin}\", pero el navegador no la usó. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Te recomendamos agregar sugerencias de recursos `preconnect` o `dns-prefetch` para establecer conexiones tempranas con orígenes externos importantes. [Obtén información para conectarte previamente a los orígenes requeridos](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Establece conexión previamente con los orígenes necesarios"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Se encontraron más de 2 conexiones de `<link rel=preconnect>`. Estos vínculos deben usarse con moderación y solo para los orígenes más importantes."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Se encontró una instancia de `<link rel=preconnect>` para \"{securityOrigin}\", pero el navegador no la usó. Usa `preconnect` solo para orígenes importantes que sepas que la página solicitará."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Se encontró una precarga de `<link>` para \"{preloadURL}\", pero el navegador no la usó. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "core/audits/uses-rel-preload.js | description": {"message": "Te recomendamos usar `<link rel=preload>` para dar prioridad a la recuperación de los recursos que, en este momento, se solicitan en una instancia posterior de la carga de la página. [Obtén información para precargar solicitudes de claves](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Carga previamente las solicitudes clave"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL de mapas"}, "core/audits/valid-source-maps.js | description": {"message": "Los mapas de orígenes traducen código reducido en el código fuente original. Esto ayuda a los desarrolladores a depurar el código durante la producción. Además, Lighthouse puede brindar más estadísticas. Te recomendamos implementar los mapas de orígenes para aprovechar estos beneficios. [Obtén más información sobre los mapas de origen](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Faltan mapas de orígenes para el archivo JavaScript grande propio"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Falta un archivo JavaScript grande del mapa de orígenes"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Advertencia: Falta 1 elemento en `.sourcesContent`}other{Advertencia: Faltan # elementos en `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "La página tiene mapas de orígenes válidos"}, "core/audits/viewport.js | description": {"message": "Una metaetiqueta `<meta name=\"viewport\">` no solo optimiza tu app para las pantallas de dispositivos móviles, sino que también impide [un retraso de 300 milisegundos en la entrada del usuario](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Obtén más información para usar la metaetiqueta de la vista del puerto](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "No se encontró ninguna etiqueta `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "No tiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Tiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Este es el trabajo de bloqueo de subprocesos que se produce durante la medición de la interacción a la siguiente pintura. [Obtén más información sobre la métrica de Interacción a la siguiente pintura](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "Se dedican {timeInMs, number, milliseconds} ms al evento \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Objetivo del evento"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimiza el trabajo durante la interacción de clave"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Retraso de entrada"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Retraso en la presentación"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tiempo de procesamiento"}, "core/audits/work-during-interaction.js | title": {"message": "Minimiza el trabajo durante la interacción de clave"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "A continuación, se indican consejos para optimizar el uso de ARIA en tu app, lo que puede mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Aquí tienes consejos para proporcionar contenido alternativo para audio y video. Así se puede mejorar la experiencia de los usuarios con dificultades auditivas o visuales."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio y video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Estos elementos destacan las prácticas recomendadas de accesibilidad más habituales."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Prácticas recomendadas"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Estas comprobaciones incluyen consejos para [mejorar la accesibilidad de tu app web](https://developer.chrome.com/docs/lighthouse/accessibility/). Solo algunos problemas de accesibilidad pueden detectarse de forma automática. Por eso, te recomendamos realizar también pruebas manuales."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Estos elementos abarcan áreas que las herramientas de prueba automáticas no contemplan. Obtén más información en nuestra guía sobre [cómo revisar los aspectos de accesibilidad](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accesibilidad"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "A continuación, se indican consejos para facilitar la lectura del contenido."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "A continuación, se indican consejos para que los usuarios con diversas configuraciones regionales puedan interpretar mejor el contenido de las páginas."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalización y localización"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "A continuación, se indican consejos para mejorar la semántica de los controles de tu app. Estos consejos pueden mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nombres y etiquetas"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Estas son oportunidades para mejorar la navegación con el teclado en tu app."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegación"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Aquí tienes sugerencias para mejorar la lectura de datos en tablas o listas con tecnologías de accesibilidad, como los lectores de pantalla."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tablas y listas"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilidad del navegador"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Recomendaciones"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "General"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Confianza y seguridad"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiencia del usuario"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Las estimaciones de rendimiento establecen estándares para el rendimiento de tu sitio."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Estimaciones"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Obtén más información sobre el rendimiento de tu app. Estos números no [afectan directamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) la medición del rendimiento."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "El aspecto más importante del rendimiento es la rapidez con la que se renderizan los píxeles en la pantalla. Métricas clave: primer procesamiento de imagen con contenido, primera pintura significativa"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Mejoras del primer procesamiento de imagen"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Estas sugerencias pueden hacer que tus páginas se carguen más rápido. No [afectan directamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) la medición del rendimiento."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Mejora la experiencia de carga general para que la página responda bien y se pueda usar lo antes posible. Métricas clave: Tiempo de carga, <PERSON><PERSON><PERSON> de velocidad"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Mejoras generales"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Rendimiento"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Estas comprobaciones validan los aspectos de una app web progresiva. [Descubre qué es lo que hace buena a una app web progresiva](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Estas comprobaciones son necesarias según el modelo de referencia [Lista de tareas para AWP](https://web.dev/pwa-checklist/), pero Lighthouse no las realiza automáticamente. Es importante que las verifiques a mano (aunque no afectan a la puntuación)."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "AWP"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalable"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizado para PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Estas comprobaciones aseguran que tu página esté siguiendo la sugerencia básica de optimización por motores de búsqueda. Existen muchos factores adicionales que Lighthouse no registra y que pueden afectar su clasificación en la búsqueda, como el rendimiento en las [Métricas web esenciales](https://web.dev/learn-core-web-vitals/). [Obtén más información sobre los conceptos básicos de la Búsqueda de Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Ejecuta estos validadores adicionales en tu sitio web para comprobar más prácticas recomendadas de SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Edita el código HTML de tu página web de forma que los rastreadores puedan entender mejor el contenido de tu app."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Prácticas recomendadas para el contenido"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para aparecer en los resultados de búsqueda, los rastreadores necesitan acceso a tu app."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreo e indexación"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Asegúrate de que tus páginas estén optimizadas para dispositivos móviles a fin de que los usuarios no tengan que pellizcar ni hacer zoom para leer las páginas de contenido. [Obtén más información para hacer que las páginas estén optimizadas para dispositivos móviles](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Optimizada para dispositivos móviles"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "El dispositivo probado parece tener una CPU más lenta de lo que espera Lighthouse, lo que puede afectar negativamente la puntuación del rendimiento. Obtén más información para [calibrar un multiplicador adecuado para ralentizar la CPU](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Es posible que la página no se cargue como se espera porque se redireccionó la URL de prueba ({requested}) a {final}. Como alternativa, prueba directamente la segunda URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "La página tardó demasiado en cargarse y no finalizó antes del límite de tiempo establecido. Es posible que los resultados estén incompletos."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Se agotó el tiempo de espera para vaciar la caché del navegador. Intenta volver a auditar esta página y, si el problema persiste, informa un error."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Es posible que los datos almacenados afecten el rendimiento de carga de esta ubicación: {locations}. Realiza una auditoría de esta página en una ventana de incógnito para evitar que estos recursos afecten el resultado.}other{Es posible que los datos almacenados afecten el rendimiento de carga de estas ubicaciones: {locations}. Realiza una auditoría de esta página en una ventana de incógnito para evitar que estos recursos afecten el resultado.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Se agotó el tiempo para borrar los datos de origen. Intenta volver a auditar esta página y, si el problema persiste, informa un error."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Solo las páginas cargadas a través de una solicitud GET son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Solo se pueden almacenar en caché las páginas que tengan el código de estado de 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome detectó un intento de ejecución de JavaScript mientras se encontraba en caché."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Las páginas que solicitaron un AppBanner no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Las funciones experimentales inhabilitaron la memoria caché atrás/adelante. Para habilitarla localmente en este dispositivo, visita chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "La línea de comandos inhabilitó la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Se inhabilitó la memoria caché atrás/adelante debido a que no hay memoria suficiente."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "El delegado no admite la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Se inhabilitó la memoria caché atrás/adelante para la renderización previa."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "La página no puede almacenarse en caché porque tiene una instancia de BroadcastChannel con objetos de escucha registrados."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Las páginas con el encabezado cache-control:no-store no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Se borró la caché de forma intencional."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Se quitó la página de la memoria caché para que otra página pueda almacenarse en caché."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Las páginas que tienen complementos no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Las páginas que utilizan la API de FileChooser no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Las páginas que utilizan la API de File System Access no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Las páginas que utilizan Media Device Dispatcher no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON> saliste, había un reproductor multimedia reproduciendo contenido."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Las páginas que utilizan la API de MediaSession y establecen un estado de reproducción no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Las páginas que utilizan la API de MediaSession y establecen controladores de acciones no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Se inhabilitó la memoria caché atrás/adelante por el lector de pantalla."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Las páginas que utilizan SecurityHandler no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Las páginas que utilizan la API de Serial no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Las páginas que utilizan la API de Web Authentication no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Las páginas que utilizan la API de WebBluetooth no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Las páginas que utilizan la API de WebUSB no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Las páginas que utilizan un worker dedicado o un worklet no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "<PERSON><PERSON>do saliste del documento, este no había terminado de cargarse."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON> saliste, el banner de la app estaba presente."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON>, el Administrador de contraseñas de Chrome estaba presente."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON><PERSON><PERSON> salist<PERSON>, el DOM se estaba filtrando."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON>, el lector del filtro de DOM estaba presente."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Se inhabilitó la memoria caché atrás/adelante debido a las extensiones que usan una API de mensajería."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Las extensiones con conexión de larga duración deben cerrar la conexión antes de ingresar la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Las extensiones con conexión de larga duración intentaron enviar mensajes a marcos en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Se inhabilitó la Memoria caché atrás/adelante debido a las extensiones."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON>, se mostró un diálogo modal de la página, como un reenvío de formulario o un diálogo de contraseña HTTP."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON> salist<PERSON>, se mostró la página sin conexión."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON>, la barra de intervención por falta de memoria estaba presente."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "<PERSON><PERSON><PERSON>, había solicitudes de permisos."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON>, el bloqueador de ventanas emergentes estaba presente."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON> salist<PERSON>, se mostraron los detalles de la Navegación segura."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "La Navegación segura considera que esta página es abusiva y bloqueó las ventanas emergentes."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Se activó un service worker mientras la página se encontraba almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "La memoria caché atrás/adelante se inhabilitó debido a un error del documento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "No se pueden restaurar en bfcache las páginas que usan FencedFrames."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Se quitó la página de la memoria caché para que otra página pueda almacenarse en caché."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Las páginas que otorgaron acceso a la transmisión multimedia no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Las páginas que utilizan portales no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Las páginas que utilizan IdleManager no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Las páginas que tienen una conexión IndexedDB abierta no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Se utilizaron APIs no aptas."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Las páginas en las que se insertó JavaScript mediante extensiones no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Las páginas en las que se insertó StyleSheet mediante extensiones no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Error interno."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Se inhabilitó la memoria caché atrás/adelante debido a una solicitud de keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Las páginas que utilizan el bloqueo del teclado no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON><PERSON>do saliste de la página, esta no había terminado de cargarse."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Las páginas cuyo recurso principal tiene el encabezado cache-control:no-cache no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Las páginas cuyo recurso principal tiene el encabezado cache-control:no-store no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Se canceló la navegación antes de que se pudiera restablecer la página de la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Se quitó la página de la memoria caché porque una conexión de red activa recibió demasiados datos. Chrome limita la cantidad de datos que puede recibir una página cuando esta se encuentra almacenada en caché."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Las páginas que tienen solicitudes fetch() o XHR en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Se quitó la página de la memoria caché atrás/adelante porque una solicitud de red activa requería un redireccionamiento."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Se quitó la página de la memoria caché porque hubo una conexión de red abierta durante mucho tiempo. Chrome limita el tiempo durante el cual una página puede recibir datos cuando está almacenada en caché."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Las páginas que no tienen un encabezado de respuesta válido no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "La navegación se llevó a cabo en un marco que no es el principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Las páginas con transacciones de base de datos indexadas en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Las páginas con una solicitud de red en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Las páginas con una solicitud de red fetch() en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Las páginas con una solicitud de red en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Las páginas con una solicitud de red XHR en curso no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Las páginas que utilizan PaymentManager no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Las páginas que utilizan la función Picture-in-Picture no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | portal": {"message": "Las páginas que utilizan portales no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | printing": {"message": "Las páginas que muestran la IU de impresión no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "La página se abrió mediante \"`window.open()`\" y otra pestaña tiene una referencia a este elemento, o bien la página abrió una ventana."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Falló el proceso de renderización de la página que se encuentra almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Se canceló el proceso de renderización de la página que se encuentra almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Las páginas que solicitaron permisos para capturar audio no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Las páginas que solicitaron permiso de acceso a sensores no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Las páginas que solicitaron permisos de recuperación o sincronización en segundo plano no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Las páginas que solicitaron permiso de acceso a MIDI no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Las páginas que solicitaron permisos de notificaciones no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Las páginas que solicitaron acceso al almacenamiento no son aptas para la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Las páginas que solicitaron permisos para capturar video no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Solo se pueden almacenar en caché las páginas cuyo esquema de URL sea HTTP o HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Un service worker reclamó la página mientras se encontraba almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker intentó enviar una propiedad `MessageEvent` a la página almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Se canceló el registro de ServiceWorker mientras una página estaba almacenada en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Como un service worker activó la página, se la quitó de la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Se reinició Chrome y se borraron las entradas de la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Las páginas que utilizan SharedWorker no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Las páginas que utilizan SpeechRecognizer no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Las páginas que utilizan SpeechSynthesis no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Un iframe en la página inició una navegación que no se completó."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Las páginas cuyo subrecurso tiene el encabezado cache-control:no-cache no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Las páginas cuyo subrecurso tiene el encabezado cache-control:no-store no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Caducó el tiempo máximo permitido en la memoria caché atrás/adelante para la página."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Se agotó el tiempo de espera para que la página se almacene en la memoria caché atrás/adelante (probablemente, debido a la ejecución prolongada de controladores de pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "La página tiene un controlador de descargas en el marco principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "La página tiene un controlador de descargas en un marco secundario."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "El navegador cambió el encabezado de anulación del usuario-agente."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Las páginas que permitieron la grabación de audio o video no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Las páginas que utilizan WebDatabase no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Las páginas que utilizan WebHID no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Las páginas que utilizan WebLocks no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Las páginas que utilizan Web NFC no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Las páginas que utilizan WebOTPService no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Las páginas con WebRTC no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Las páginas que utilizan WebShare no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Las páginas con WebSocket no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Las páginas con WebTransport no son aptas para el almacenamiento en la memoria caché atrás/adelante."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Las páginas que utilizan WebXR no son aptas para el almacenamiento en la memoria caché atrás/adelante actualmente."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Considera agregar esquemas de URL con https: y http: (ignorados por los navegadores que admiten \"strict-dynamic\") para que la política sea retrocompatible con los navegadores anteriores."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener de<PERSON><PERSON> de estar disponible desde CSP3. En su lugar, usa el encabezado Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer dejó de estar disponible desde CSP2. En su lugar, usa el encabezado Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss dejó de estar disponible desde CSP2. En su lugar, usa el encabezado X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "La falta de directiva base-uri permite que las etiquetas <base> insertadas configuren la URL básica para todas las URL relativas (p. ej., secuencia de comandos) a un dominio controlado del atacante. Considera configurar base-uri como \"none\" o \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "La falta de la directiva object-src permite incorporar complementos que ejecutan secuencias de comandos no seguras. Considera establecer la directiva object-src con el valor \"none\" si es posible."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Falta la directiva script-src. Esto puede permitir la ejecución de secuencias de comandos no seguras."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "¿Olvidaste el punto y coma? Al parecer, {keyword} es una directiva, no una palabra clave."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Los nonces deben usar el charset base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Los nonces deben tener al menos 8 caracteres."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Impide usar esquemas de URL sin formato ({keyword}) en esta directiva. Los esquemas de URL sin formato permiten obtener secuencias de comandos de un dominio no seguro."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Impide usar comodines sin formato ({keyword}) en esta directiva. Los comodines sin formato permiten obtener secuencias de comandos de un dominio no seguro."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "El destino del informe solo se configura mediante la directiva report-to. Esta directiva solo es compatible con los navegadores con Chromium, por lo que se recomienda que uses siempre una directiva report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Ninguna CSP configura un destino para informes. Esto dificulta el mantenimiento de la CSP con el tiempo y el monitoreo de cualquier falla."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Las listas de host permitidos a menudo se pueden omitir. Considera usar nonces o hashes de CSP, junto con \"strict-dynamic\" si es necesario."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "La directiva de la CSP es desconocida."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON>, {keyword} es una palabra clave no válida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inline\" permite la ejecución de controladores de eventos y secuencias de comando no seguras de la página. Considera usar nonces o hashes de la CSP para permitir secuencias de comandos por separado."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Considera agregar la palabra clave \"unsafe-inline\" (ignorada por los navegadores que admiten nonces y hashes) para que la política sea retrocompatible con los navegadores anteriores."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "El símbolo comodín (*) no cubrirá la autorización en el controlador `Access-Control-Allow-Headers` de CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Las solicitudes de recurso cuyas URLs contenían tanto caracteres `(n|r|t)` con espacio en blanco que se quitaron y caracteres inferiores (`<`) están bloqueadas. Quita las líneas nuevas y codifica caracteres inferiores en lugares como los valores del atributo del elemento a fin de cargar estos recursos."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` ya no está disponible, en su lugar, usa la API estandarizada: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` ya no está disponible. En su lugar, utiliza la API estandarizada: <PERSON><PERSON>."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` ya no está disponible. En su lugar, usa la API estandarizada: `nextHopProtocol` en Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Se rechazarán las cookies que incluyan un caracter `(0|r|n)` en lugar de truncarse."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "La función para relajar la política del mismo origen mediante la configuración de `document.domain` ya no está disponible y se inhabilitará de forma predeterminada. Esta advertencia de baja es para un acceso de origen cruzado que se habilitó mediante la configuración de `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "La función para activar {PH1} desde iframes de origen cruzado ya no está disponible y se quitará en el futuro."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Debe usarse el atributo `disableRemotePlayback` en lugar del selector `-internal-media-controls-overlay-cast-button` a fin de inhabilitar la integración predeterminada del botón para transmitir."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} ya no está disponible. En su lugar, utiliza {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Este es un ejemplo de un mensaje de errores de obsolescencia traducido."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "La función para relajar la política del mismo origen mediante la configuración de `document.domain` ya no está disponible y se inhabilitará de forma predeterminada. Para seguir utilizando esta función, envía un encabezado `Origin-Agent-Cluster: ?0` junto con la respuesta HTTP para el documento y los marcos a fin de inhabilitar los clústeres de agentes con clave de origen. Para obtener más información, consulta https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` ya no está disponible y se quitará. En su lugar, utiliza `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "El encabezado `Expect-CT` ya no está disponible y se quitará. Chrome requiere un Certificado de transparencia para todos los certificados de confianza pública emitidos después del 30 de abril de 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Consulta la página de estado de la función para obtener más información."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` y `watchPosition()` ya no funcionan en orígenes inseguros. Para utilizar esta función, considera cambiar tu app a un origen seguro, como HTTPS. Para obtener más información, consulta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` y `watchPosition()` ya no están disponibles en orígenes inseguros. Para utilizar esta función, considera cambiar tu app a un origen seguro, como HTTPS. Para obtener más información, consulta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ya no funciona en orígenes inseguros. Para utilizar esta función, considera cambiar tu app a un origen seguro, como HTTPS. Para obtener más información, consulta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` ya no está disponible. En su lugar, usa `RTCPeerConnectionIceErrorEvent.address` o `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "El origen del comercio y los datos arbitrarios del evento del service worker `canmakepayment` dejaron de estar disponibles y se quitarán: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "El sitio web solicitó un subrecurso de una red a la que solo podía acceder gracias a la posición con privilegios de la red de los usuarios. Estas solicitudes exponen dispositivos y servidores no públicos en Internet, lo que aumenta el riesgo de un ataque de solicitud falsa entre sitios (CSRF) o de filtración de datos. Para mitigar este problema, Chrome da de baja las solicitudes a subrecursos no públicos cuando provienen de contextos no seguros y comenzará a bloquearlas."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "No se puede cargar CSS desde las URLs de `file:` a no ser que terminen en una extensión de archivo `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "El uso de `SourceBuffer.abort()` a fin de anular la eliminación del rango asíncrono de `remove()` ya no está disponible debido a cambios de especificación. En el futuro, se quitará la compatibilidad. En su lugar, debes escuchar el evento `updateend`. `abort()` está pensado únicamente para anular el proceso de adjuntar contenido multimedia de forma asíncrona o para restablecer el estado del analizador."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "La función para configurar `MediaSource.duration` bajo la marca de tiempo de presentación más alta de cualquier marco que tenga códigos con búfer ya no está disponible debido a cambios de especificación. La compatibilidad para la eliminación implícita de contenido multimedia con búfer truncado se quitará en el futuro. En su lugar, debes realizar de forma explícita `remove(newDuration, oldDuration)` en todos los `sourceBuffers`, donde `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Este cambio tendrá efecto con la versión principal {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "MIDI web solicitará permiso de uso, incluso si no se especifica el valor de sysex en `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Es posible que la API de Notification deje de usarse en orígenes inseguros. Deberías cambiar tu app a un origen seguro, como HTTPS. Para obtener más información, consulta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Es posible que ya no se soliciten los permisos para la API de Notification desde un iframe de origen cruzado. Puedes solicitar permiso desde un marco de nivel superior, o bien abrir una nueva ventana."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Tu socio está negociando una versión de (D)TLS que ya no está disponible. Consúltalo con tu socio para solucionar este problema."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL en contextos no seguros dejó de estar disponible y se quitará pronto. Usa Web Storage o Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Si especificas `overflow: visible` en las etiquetas img, video y canvas, es posible que produzcan contenido visual fuera de los límites de los elementos. Consulta https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` ya no está disponible. En su lugar, usa la instalación inmediata (JIT) para los controladores de pago."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Tu llamada a `PaymentRequest` omitió la directiva `connect-src` de la Política de Seguridad del Contenido (CSP). Esta omisión dejó de estar disponible. Agrega el identificador de forma de pago de la API de `PaymentRequest` (en el campo `supportedMethods`) a la directiva `connect-src` de tu CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` ya no está disponible. En su lugar, usa `navigator.storage` estandarizado."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` con un `<picture>` superior no es válido, por lo que no se tendrá en cuenta. En su lugar, utiliza `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` ya no está disponible. En su lugar, usa `navigator.storage` estandarizado."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Las solicitudes de los subrecursos cuyas URLs contienen credenciales incorporadas (p. ej., `**********************/`) están bloqueadas."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Se quitó la restricción `DtlsSrtpKeyAgreement`. Especificaste un valor `false` para esta restricción, lo que se interpreta como un intento de usar el método `SDES key negotiation` que se quitó. Se quitó esta funcionalidad; en su lugar, usa un servicio compatible con `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Se quitó la restricción `DtlsSrtpKeyAgreement`. Especificaste un valor `true` para esta restricción, lo que no tuvo efecto, pero puedes quitarla a fin de lograr un orden mejor."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Se detectó `Complex Plan B SDP`. El dialecto de `Session Description Protocol` ya no es compatible. En su lugar, utiliza `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, que se usa para construir una `RTCPeerConnection` con `{sdpSemantics:plan-b}`, es una versión no estándar heredada de `Session Description Protocol` que se quitó de forma permanente de la plataforma web. Sigue disponible cuando se construye con `IS_FUCHSIA`, pero pensamos quitarlo lo más pronto posible. Deja de depender de él. Consulta el estado en https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "La opción `rtcpMuxPolicy` ya no está disponible y se quitará."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` requerirá aislamiento de origen cruzado. Para obtener más información, consulta https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` sin activación del usuario ya no está disponible y se quitará."}, "core/lib/deprecations-strings.js | title": {"message": "Se usó una función no disponible"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Las extensiones deben habilitar el aislamiento de origen cruzado a fin de seguir utilizando `SharedArrayBuffer`. Consulta https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} es específica de proveedores. En su lugar, usa la {PH2} estándar."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 no es compatible con el archivo json de respuesta de `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "El objeto `XMLHttpRequest` síncrono ya no está disponible en el subproceso principal porque produce efectos nocivos en la experiencia del usuario final. Para obtener ayuda, revisa https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` ya no está disponible. En su lugar, usa `isSessionSupported()` y revisa el valor booleano resuelto."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tiempo de bloqueo del subproceso principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL en caché"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Descripción"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Duración"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementos con errores"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Ubicación"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nombre"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Superior a la estimación"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Solicitudes"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Tamaño del recurso"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Fuente"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Hora de inicio"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tiempo de uso"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Tamaño de transferencia"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Ah<PERSON><PERSON> posibles"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Ah<PERSON><PERSON> posibles"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Ahorro posible de {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Se encontró 1 elemento}other{Se encontraron # elementos}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Ahorro posible en {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Primera pintura significativa"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Fuente"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagen"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interacción a la siguiente pintura"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alta"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Baja"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Media"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Máximo retraso de primera entrada posible"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Contenido multimedia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON>s recursos"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Secuencia de comandos"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Hoja de estilo"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Terceros"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Se produjo un error de registro de seguimiento durante la carga de la página. Vuelve a ejecutar Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Se agotó el tiempo de espera de la conexión inicial del protocolo del depurador."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome no recopiló ninguna captura de pantalla al cargar la página. Comprueba que haya contenido visible en la página y vuelve a ejecutar Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Los servidores DNS no pudieron resolver el dominio proporcionado."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "El recopilador {artifactName} obligatorio encontró un error: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Se produjo un error interno de Chrome. Reinicia Chrome y vuelve a ejecutar Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "No se ejecutó el recopilador necesario para {artifactName}."}, "core/lib/lh-error.js | noFcp": {"message": "No se mostró contenido en la página. Asegúrate de que la ventana del navegador esté en primer plano durante la carga y vuelve a intentarlo. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "La página no mostraba contenido que fuera calificado como Largest Contentful Paint (LCP). Asegúrate de que la página tenga un elemento LCP válido y vuelve a intentarlo. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "La página proporcionada no es HTML (se proporciona como el tipo de MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Esta versión de Chrome no admite \"{featureName}\" porque es demasiado antigua. Usa una versión más reciente para ver los resultados completos."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse no pudo cargar correctamente la URL que solicitaste porque la página dej<PERSON> de responder."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "La URL que proporcionaste no tiene un certificado de seguridad válido. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome evitó la carga de la página y mostró una pantalla intersticial. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes. (Detalles: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes. (Código de estado: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "La página tardó demasiado en cargarse. Sigue los consejos del informe para reducir el tiempo de carga de la página y vuelve a ejecutar Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Se superó el tiempo asignado para la respuesta de protocolo de DevTools. (Método: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Se superó el tiempo asignado para obtener el contenido de los recursos"}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON> parecer, la URL que proporcionaste no es válida."}, "core/lib/navigation-error.js | warningXhtml": {"message": "El tipo de MIME de la página es XHTML: Lighthouse no admite explícitamente este tipo de documento"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON>lujo de usuarios ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Informe de navegación ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Informe de un momento específico ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Informe del período ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Todos los informes"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorías"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accesibilidad"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Recomendaciones"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Rendimiento"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "App web progresiva"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Escritorio"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Explicación del informe de flujos de Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Explicación de flujos"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Utilizar los informes de navegación para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Utilizar los informes de instantáneas para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Utilizar los informes de períodos para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obtener una puntuación de rendimiento de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Obtener métricas de rendimiento de carga de páginas, como el Procesamiento de imagen con contenido más grande y el índice de velocidad."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Evaluar las capacidades de las apps web progresivas."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Encontrar problemas de accesibilidad en aplicaciones de página única o formularios complejos."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Evaluar prácticas recomendadas de menús y elementos de la IU ocultos detrás de la interacción."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Medir los cambios de diseño y el tiempo de ejecución de JavaScript en una serie de interacciones."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descubrir oportunidades de rendimiento para mejorar la experiencia en aplicaciones de página única y en páginas abiertas por largo tiempo."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Mayor <PERSON>o"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} auditoría informativa}other{{numInformative} auditorías informativas}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Para dispositivos móviles"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Carga de página"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Los informes de navegación analizan la carga de una sola página, de la misma manera que los informes originales de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Informe de navegación"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} informe de navegación}other{{numNavigation} informes de navegación}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} auditoría con posibilidades de aprobar}other{{numPassableAudits} auditorías con posibilidades de aprobar}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} auditoría aprobada}other{{numPassed} auditorías aprobadas}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Promedio"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON>e"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Bueno"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Guardar"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Estado de la página en un momento específico"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Los informes de instantáneas analizan la página en un estado particular, por lo general, después de las interacciones de un usuario."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Informe de un momento específico"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} informe de un momento específico}other{{numSnapshot} informes de un momento específico}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Resumen"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interacciones del usuario"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Los informes de períodos analizan un período arbitrario, el cual por lo general incluye interacciones de los usuarios."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Informe del período"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} informe del período}other{{numTimespan} informes del período}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Informe del flujo de usuarios de Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "En el caso de contenido animado, utiliza [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) para minimizar el uso de CPU mientras el contenido no aparezca en pantalla."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Cuando especifiques un resguardo adecuado para otros navegadores, se recomienda mostrar todos los componentes de [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) en formato WebP. [Obtén más información](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Asegúrate de usar [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) para las imágenes que realizan una carga diferida. [Obtén más información](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Utiliza herramientas como [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) para [procesar diseños de AMP en el servidor](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulta el [documento AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) para garantizar la compatibilidad de todos los estilos."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "El elemento de [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) es compatible con el atributo de [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) para especificar los recursos de imagen que deben usarse en función del tamaño de la pantalla. [Obtén más información](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Si se están procesando listas muy grandes, se recomienda utilizar el desplazamiento virtual con el kit de desarrollo de componentes (CDK). [Obtén más información](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Aplica la [división de código en rutas](https://web.dev/route-level-code-splitting-in-angular/) para minimizar el tamaño de los paquetes de JavaScript. Además, se recomienda almacenar previamente los activos en caché con el [service worker de Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Si utilizas Angular CLI, asegúrate de que las compilaciones se generen en el modo de producción. [Obtén más información](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Si utilizas Angular CLI, incluye mapas de origen en la compilación de producción para inspeccionar tus paquetes. [Obtén más información](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Precarga las rutas con anticipación para mejorar la velocidad de la navegación. [Obtén más información](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Se recomienda usar la utilidad de `BreakpointObserver` en el kit de desarrollo de componentes (CDK) para administrar las interrupciones de imágenes. [Obtén más información](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Puedes subir tu GIF a un servicio que permita insertarlo como un video HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Especifica `@font-display` cuando definas fuentes personalizadas en tu tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> configurar [formatos de imagen WebP con un estilo conversor de imágenes ](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) en tu sitio."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instala [un módulo de Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) que pueda cargar imágenes de forma diferida. Esos módulos tienen la capacidad de postergar las imágenes fuera de pantalla para mejorar el rendimiento."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Se recomienda usar un módulo para intercalar instancias críticas de CSS y JavaScript, o cargar de manera potencial los elementos de forma asíncrona a través de JavaScript, como el módulo de [Agregación avanzada de CSS/JS](https://www.drupal.org/project/advagg). Ten en cuenta que las optimizaciones que ofrece este módulo pueden generar fallas en el sitio, por lo que seguramente tengas que hacer cambios en el código."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Los temas, los módulos y las especificaciones del servidor afectan al tiempo de respuesta. <PERSON><PERSON><PERSON> buscar un tema más optimizado, seleccionar un módulo de optimización o actualizar tu servidor. Los servidores de hosting deben usar el almacenamiento en caché del código de operación PHP, el almacenamiento de memoria en caché para reducir los tiempos de búsqueda en la base de datos como Redis o Memcached y la lógica optimizada de la app para preparar páginas más rápido."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Se recomienda usar [estilos de imágenes responsivas](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) para reducir el tamaño de las imágenes que se cargan en la página. Si usas Views para mostrar elementos de contenido múltiple en una página, puedes implementar la paginación para limitar el número de elementos de contenido que se muestran en una página en particular."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Asegúrate de habilitar \"Agregar archivos CSS\" en la página \"Administración » Configuración » Desarrollo\". También puedes configurar opciones más avanzadas de agregación en los [módulos adicionales](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) a fin de mejorar la velocidad del sitio. Para lograrlo, puedes concatenar, reducir y comprimir los estilos CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Asegúrate de habilitar \"Agregar archivos JavaScript\" en la página \"Administración » Configuración » Desarrollo\". También puedes configurar opciones más avanzadas de agregación en los [módulos adicionales](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) a fin de mejorar la velocidad del sitio. Para lograrlo, puedes concatenar, reducir y comprimir los elementos de JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Se recomienda quitar las reglas de CSS no utilizadas y solo adjuntar las bibliotecas de Drupal necesarias al componente o a la página pertinentes. Para obtener información, consulta el [vínculo a la documentación de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Para identificar las bibliotecas adjuntas que agregan instancias innecesarias de CSS, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o el módulo concreto en la URL de la hoja de estilo cuando está inhabilitada la agregación de CSS en tu sitio de Drupal. Presta atención a los temas o módulos que tengan varias hojas de estilo en la lista con muchos elementos en rojo en la cobertura de código. Los temas o módulos solo deberían poner en cola hojas de estilo que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Se recomienda quitar los elementos de JavaScript no utilizados y solo adjuntar las bibliotecas de Drupal necesarias al componente o a la página pertinentes. Para obtener información, consulta el [vínculo a la documentación de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Para identificar las bibliotecas adjuntas que agregan instancias innecesarias de JavaScript, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o el módulo concreto en la URL de la secuencia de comandos cuando está inhabilitada la agregación de JavaScript en tu sitio de Drupal. Presta atención a los temas o módulos que tengan varias secuencias de comandos en la lista con muchos elementos en rojo en la cobertura de código. Los temas o módulos solo deberían poner en cola secuencias de comandos que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Establece el \"tiempo máximo de almacenamiento en caché del navegador y el proxy\" en la página \"Administración » Configuración » Desarrollo\". Obtén información sobre [el almacenamiento en caché de Drupal y cómo mejorar el rendimiento](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Se recomienda usar [un módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) que optimice y reduzca de forma automática el tamaño de las imágenes que se cargan en el sitio y conserve la calidad. Además, asegúrate de usar los [estilos de imágenes responsivas](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) que proporciona Drupal (disponibles en Drupal 8 y versiones posteriores) para todas las imágenes renderizadas en el sitio."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Para agregar optimizaciones de recursos preconnect o dns-prefetch mediante DNS, instala y configura [un módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) que proporcione elementos para las optimizaciones de recursos del usuario-agente."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Asegúrate de usar los [estilos de imágenes responsivas](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) que proporciona Drupal (disponibles en Drupal 8 y versiones posteriores). Utiliza los estilos de imágenes responsivas cuando renderices campos de imágenes a través de modos de vista, vistas o imágenes subidas a través del editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Optimize Fonts` para que se aplique de forma automática la función de CSS `font-display`. De esta manera, te aseguras de que el texto sea visible para los usuarios mientras se cargan las fuentes para sitios web."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Next-Gen Formats` para convertir imágenes a WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Lazy Load Images` para posponer la carga de imágenes que no aparecen en pantalla hasta que las necesites."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Critical CSS` y `Script Delay` para posponer los elementos de JS/CSS que no sean críticos."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Utiliza [Ezoic Cloud Catching](https://pubdash.ezoic.com/speed/caching) para almacenar tu contenido en caché en nuestra red mundial, lo que reduce el tiempo de carga."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Minify CSS` para reducir automáticamente tu CSS. De esta manera, se disminuye el tamaño de la carga útil en la red."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Minify Javascript` para reducir automáticamente tu JS. De esta manera, se disminuye el tamaño de la carga útil en la red."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Remove Unused CSS` para obtener ayuda con este problema. De esta manera, se identificarán las clases de CSS que realmente se utilizan en cada página del sitio y se quitará el resto para que el archivo se conserve pequeño."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Efficient Static Cache Policy` para establecer los valores recomendados en el encabezado del almacenamiento en caché de los elementos estáticos."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Next-Gen Formats` para convertir imágenes a WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Pre-Connect Origins` para agregar automáticamente sugerencias de recursos `preconnect`. De esta manera, se establecen conexiones anticipadas con orígenes de terceros importantes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Preload Fonts` y `Preload Background Images` para agregar vínculos `preload`. De esta manera, le darás prioridad a la obtención de recursos que se requerirán más adelante durante la carga de páginas."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Utiliza [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Resize Images` para cambiar el tamaño de las imágenes a uno apropiado y así disminuir el tamaño de la carga útil en la red."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Puedes subir tu GIF a un servicio que permita insertarlo como un video HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Puedes utilizar un [complemento](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) o servicio que convierta automáticamente las imágenes que subas a los formatos óptimos."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instala un [complemento de Joomla de carga diferida](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) que proporcione la capacidad de postergar las imágenes fuera de pantalla o bien cambia a una plantilla que proporcione esa funcionalidad. A partir de Joomla 4.0, todas las imágenes nuevas obtendrán el atributo `loading` [de forma automática](https://github.com/joomla/joomla-cms/pull/30748) desde el núcleo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Existen varios complementos de Joomla que pueden ayudarte a [intercalar elementos fundamentales](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) o a [postergar recursos menos importantes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Ten en cuenta que las optimizaciones que ofrecen estos complementos pueden interferir con funciones de tus plantillas o complementos, por lo que deberás probarlas de forma exhaustiva."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Las plantillas, las extensiones y las especificaciones del servidor afectan al tiempo de respuesta. <PERSON>uedes buscar una plantilla más optimizada, seleccionar cuidadosamente una extensión de optimización o actualizar el servidor."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Puedes mostrar fragmentos en las categorías de artículos (por ejemplo, mediante el vínculo \"Más información\"), reducir la cantidad de artículos que se muestran en cada página, dividir tus entradas más largas en múltiples páginas o usar un complemento para postergar la carga de los comentarios."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Hay varias [extensiones de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que pueden concatenar, reducir y comprimir los estilos CSS para acelerar tu sitio web. También hay plantillas que proporcionan esta funcionalidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Hay varias [extensiones de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que pueden concatenar, reducir y comprimir las secuencias de comandos para acelerar tu sitio web. También hay plantillas que proporcionan esta funcionalidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Puedes reducir o cambiar la cantidad de [extensiones de Joomla](https://extensions.joomla.org/) que cargan elementos de CSS que tu página no usa. Para identificar las extensiones que agregan elementos de CSS innecesarios, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la hoja de estilo. Presta atención a los complementos que tengan varias hojas de estilo en la lista con muchos elementos en rojo en la cobertura de código. Los complementos solo deberían poner en cola hojas de estilo que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Puedes reducir o cambiar la cantidad de [extensiones de Joomla](https://extensions.joomla.org/) que cargan secuencias de JavaScript que tu página no usa. Para identificar los complementos que agregan secuencias JS innecesarias, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar la extensión concreta en la URL de la secuencia de comandos. Presta atención a las extensiones que tengan varias secuencias de comandos en la lista con muchos elementos en rojo en la cobertura de código. Las extensiones solo deberían poner en cola secuencias de comandos que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Consulta información sobre el [almacenamiento en caché del navegador en Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Puedes usar un [complemento de optimización de imágenes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que comprima las imágenes y conserve la calidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Se recomienda usar un [complemento de imágenes responsivas](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) para poder usar este tipo de imágenes en tu contenido."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Para habilitar la compresión de texto, habilita la compresión de páginas en gzip, en Joomla (Sistema > Configuración general > Servidor)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Si no estás creando un paquete con los elementos de JavaScript, se recomienda usar [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Inhabilita [el empaquetado y la reducción de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) y analiza utilizar [baler](https://github.com/magento/baler/) en su lugar."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Specifica `@font-display` cuando [definas fuentes personalizadas](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Para utilizar formatos de imagen más nuevos, se recomienda el [mercado de Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) donde encontrarás una amplia variedad de extensiones de terceros."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Se recomienda modificar las plantillas de productos y catálogos para utilizar la función de [carga diferida](https://web.dev/native-lazy-loading) de la plataforma web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Util<PERSON> la [integración de Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Habilita la opción \"Minify CSS Files\" en las opciones de configuración para desarrolladores de la tienda. [Obtén más información](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [Terser](https://www.npmjs.com/package/terser) para reducir todos los elementos de JavaScript de la implementación de contenido estático y, luego, inhabilita la función de reducción integrada."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Inhabilita el [empaquetado de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrado en Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Para optimizar las imágenes, se recomienda el [mercado de Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) donde encontrarás una amplia variedad de extensiones de terceros."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Para agregar optimizaciones de recursos preconnect o dns-prefetch mediante DNS, [modifica un diseño de temas](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Para agregar las etiquetas de `<link rel=preload>`, [modifica el diseño de los temas](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Utiliza el componente `next/image` en lugar de `<img>` para optimizar el formato de imagen automáticamente. [Obtén más información](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Utiliza el componente `next/image` en lugar de `<img>` a fin de realizar cargas diferidas de imágenes automáticamente. [Obtén más información](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Utiliza el componente `next/image` y establece \"prioridad\" como verdadero para precargar imágenes LCP. [Obtén más información](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Utiliza el componente `next/script` para posponer la carga de secuencias de comandos no críticas de terceros. [Obtén más información](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Utiliza el componente `next/image` para asegurarte de que las imágenes tengan siempre el tamaño correcto. [Obtén más información](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON> configurar `PurgeCSS` en la configuración de `Next.js` para quitar las reglas que no se usan de las hojas de estilo. [Obtén más información](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Utiliza `Webpack Bundle Analyzer` para detectar códigos de JavaScript sin uso. [Más información](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON><PERSON> utilizar `Next.js Analytics` para calcular el rendimiento real de tu app. [Obtén más información](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configura el almacenamiento en caché para elementos inmutables y páginas `Server-side Rendered` (SSR). [Obtén más información](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Utiliza el componente `next/image` en lugar de `<img>` para modificar la calidad de la imagen. [Obtén más información](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Utiliza el componente `next/image` para establecer las `sizes` correspondientes. [Obtén más información](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Habilita la compresión en tu servidor Next.js. [Obtén más información](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Utiliza el componente `nuxt/image` y establece `format=\"webp\"`. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Utiliza el componente `nuxt/image` y establece `loading=\"lazy\"` para las imágenes fuera de pantalla. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Utiliza el componente `nuxt/image` y especifica `preload` para la imagen LCP. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Utiliza el componente `nuxt/image` y especifica `width` y `height` explícitos. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Utiliza el componente `nuxt/image` y establece las `quality` correspondientes. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Utiliza el componente `nuxt/image` y establece las `sizes` correspondientes. [Obtén más información](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Reemplaza los GIF animados con video](https://web.dev/replace-gifs-with-videos/) para que se cargue la página web más rápido y considera utilizar formatos de archivo modernos, como [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) o [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) para mejorar la eficiencia de la compresión en más de un 30% con respecto al VP9, el códec de video de vanguardia actual."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Puedes utilizar un [complemento](https://octobercms.com/plugins?search=image) o servicio que convierta automáticamente las imágenes que subes en los formatos óptimos. [Las imágenes WebP sin pérdidas](https://developers.google.com/speed/webp) son un 26% más pequeñas en comparación con las PNG y entre un 25% y un 34% más pequeñas que las imágenes JPEG comparables con índices de calidad SSIM equivalentes. Otro formato de imagen de última generación que se debe tener en cuenta es [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Puedes instalar un [complemento de carga diferida de imágenes](https://octobercms.com/plugins?search=lazy) que permita diferir las imágenes fuera de pantalla. Otra alternativa es cambiar a un tema que proporcione esa funcionalidad. También puedes utilizar [el complemento AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Existen varios complementos que permiten [intercalar elementos fundamentales](https://octobercms.com/plugins?search=css). Estos complementos pueden interferir con otros, por lo que deberás probarlos de forma exhaustiva."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Los temas, los complementos y las especificaciones afectan al tiempo de respuesta del servidor. Considera buscar un tema más optimizado, elegir cuidadosamente un complemento de optimización o actualizar el servidor. El CMS October también permite que los desarrolladores utilicen [`Queues`](https://octobercms.com/docs/services/queues) para diferir el procesamiento de una tarea que requiera mucho tiempo, como enviar un correo electrónico. De esta manera, se aceleran drásticamente las solicitudes web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Considera mostrar extractos en las listas de publicaciones (p. ej., cuando se presiona el botón `show more`), reducir el número de publicaciones que se muestran en una página determinada, dividir publicaciones largas entre varias páginas web o utilizar un complemento para los comentarios de carga diferida."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Existen varios [complementos](https://octobercms.com/plugins?search=css) que concatenan, reducen y comprimen los estilos para acelerar un sitio web. Se puede acelerar el desarrollo utilizando un proceso de compilación para reducir los estilos de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Existen varios [complementos](https://octobercms.com/plugins?search=javascript) que concatenan, reducen y comprimen las secuencias de comandos para acelerar el sitio web. Se puede acelerar el desarrollo utilizando un proceso de compilación para reducir los estilos de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Considera revisar los [complementos](https://octobercms.com/plugins) que carguen instancias de CSS no utilizadas en el sitio web. Para identificar los complementos que agregan instancias de CSS innecesarias, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en Herramientas para desarrolladores de Chrome. Identifica el tema o el complemento responsable desde la URL de la hoja de estilo. Busca los complementos que tengan varias hojas de estilo con muchos elementos en rojo en la cobertura de código. Los complementos solo deben agregar hojas de estilo que se usen en la página web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Considera revisar los [complementos](https://octobercms.com/plugins?search=javascript) que cargan instancias de JavaScript no utilizadas en la página web. Para identificar los complementos que agregan instancias de JavaScript innecesarias, ejecuta [la cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en Herramientas para desarrolladores de Chrome. Identifica el tema o el complemento responsable desde la URL de la secuencia de comandos. Busca complementos que tengan varias secuencias de comandos con muchos elementos en rojo en la cobertura de código. Los complementos solo deben agregar secuencias de comandos que se usen en la página web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Obtén información para [no enviar solicitudes de red innecesarias gracias a la caché HTTP](https://web.dev/http-cache/#caching-checklist). Existen varios [complementos](https://octobercms.com/plugins?search=Caching) para acelerar el almacenamiento en caché."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Puedes utilizar un [complemento de optimización de imágenes](https://octobercms.com/plugins?search=image) para comprimir las imágenes sin modificar su calidad."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Sube las imágenes directamente en el administrador de contenido multimedia para asegurarte de contar con los tamaños de imagen requeridos. Considera utilizar el [filtro de cambio de tamaño](https://octobercms.com/docs/markup/filter-resize) o un [complemento para cambiar el tamaño de las imágenes](https://octobercms.com/plugins?search=image) a fin de asegurarte de que se utilicen los tamaños óptimos."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Puedes habilitar la compresión de texto en la configuración de tu servidor web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Si renderizas muchos elementos repetidos en la página, considera utilizar una biblioteca de \"sistema de ventanas\" como `react-window` para minimizar la cantidad de nodos DOM que se crean. [Obtén más información](https://web.dev/virtualize-long-lists-react-window/). Además, minimiza la cantidad de renderizaciones repetidas e innecesarias mediante [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) o [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo), y [omite efectos](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) solo hasta que hayan cambiado ciertas dependencias si usas el enlace de `Effect` para mejorar el rendimiento del tiempo de ejecución."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Si utilizas React Router, minimiza el uso del componente de `<Redirect>` para las [navegaciones de ruta](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Si procesas en el servidor cualquier componente de React, se recomienda usar `renderToPipeableStream()` o `renderToStaticNodeStream()` para permitir que el cliente reciba diferentes partes del lenguaje de marcado y las complete en lugar de recibir todas al mismo tiempo. [Obtén más información](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Si el sistema de compilación reduce los archivos CSS de forma automática, asegúrate de implementar la compilación de producción de tu app. Puedes comprobarlo con la extensión de las Herramientas para desarrolladores de React. [Obtén más información](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Si el sistema de compilación reduce los archivos JS de forma automática, asegúrate de implementar la compilación de producción de tu app. Puedes comprobarlo con la extensión de las Herramientas para desarrolladores de React. [Obtén más información](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Si no estás procesando datos en el servidor, [divide los paquetes de JavaScript](https://web.dev/code-splitting-suspense/) con `React.lazy()`. De lo contrario, divide el código con una biblioteca de terceros como [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Utiliza el generador de perfiles de las Herramientas para desarrolladores de React, que emplea la API de Profiler para medir el rendimiento de procesamiento de tus componentes. [Obtén más información](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Puedes subir tu GIF a un servicio que permita insertarlo como un video HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Te recomendamos usar el complemento [Performance Lab](https://wordpress.org/plugins/performance-lab/) para convertir las imágenes JPEG que hayas subido a WebP de forma automática, siempre que sea compatible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instala un [complemento de carga diferida de WordPress](https://wordpress.org/plugins/search/lazy+load/) con la capacidad de postergar las imágenes fuera de pantalla o bien cambia a un tema que incluya esa función. También puedes usar [el complemento AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Existen varios complementos de WordPress que pueden ayudarte a [insertar elementos fundamentales](https://wordpress.org/plugins/search/critical+css/) o a [postergar recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). Ten en cuenta que las optimizaciones que ofrecen estos complementos pueden interferir con funciones de tu tema o complementos, por lo que seguramente tengas que hacer cambios en el código."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Los temas, los complementos y las especificaciones del servidor afectan al tiempo de respuesta. Puedes buscar un tema más optimizado, seleccionar un complemento de optimización o actualizar tu servidor."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Puedes mostrar fragmentos en tus listas de entradas (por ejemplo, mediante la etiqueta \"<more>\"), reducir la cantidad de entradas que se muestran en cada página, dividir tus entradas más largas en múltiples páginas o usar un complemento para postergar la carga de los comentarios."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+css/) que pueden concatenar, reducir y comprimir los estilos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de compilación para reducir los estilos de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+javascript/) que pueden concatenar, reducir y comprimir las secuencias de comandos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de compilación para realizar la reducción de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan hojas de estilo CSS que tu página no usa. Para identificar los complementos que agregan hojas de estilo CSS innecesarias, prueba ejecutar la [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la hoja de estilo. Presta atención a los complementos que tengan varias hojas de estilo en la lista con muchos elementos en rojo en la cobertura de código. Los complementos solo deberían poner en cola hojas de estilo que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan secuencias JavaScript que tu página no usa. Para identificar los complementos que agregan secuencias JS innecesarias, prueba ejecutar la [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la secuencia de comandos. Presta atención a los complementos que tengan varias secuencias de comandos en la lista con muchos elementos en rojo en la cobertura de código. Los complementos solo deberían poner en cola secuencias de comandos que se usen en la página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Consulta información sobre el [almacenamiento en la memoria caché del navegador en WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Puedes usar un [complemento de optimización de imágenes de WordPress](https://wordpress.org/plugins/search/optimize+images/) que comprima tus imágenes y conserve la calidad."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Carga imágenes directamente a través de la [biblioteca de medios](https://wordpress.org/support/article/media-library-screen/) para garantizar que estén disponibles los tamaños de imagen requeridos y, luego, insértalas desde la biblioteca de medios, o bien usa el widget de imágenes para asegurarte de que se utilicen los tamaños de imagen óptimos (incluidos los que se emplean para interrupciones receptivas). Evita usar imágenes `Full Size`, a menos que las dimensiones sean adecuadas para su empleo. [Obtén más información](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Puedes habilitar la compresión de texto en la configuración de tu servidor web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Habilita \"Imagify\" en la pestaña de optimización de imágenes en \"WP Rocket\" para convertir tus imágenes a WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Habilita [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) en \"WP Rocket\" para seguir esta recomendación. Esta función retrasa la carga de las imágenes hasta que el visitante se desplace hacia abajo en la página y necesite verlas."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Habilita [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) y [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) en \"WP Rocket\" para abordar esta recomendación. Estas funciones optimizarán respectivamente los archivos CSS y JavaScript para que no bloqueen la renderización de tu página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Habilita [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) en \"WP Rocket\" para solucionar este problema. Se quitarán los espacios y comentarios de los archivos CSS de tu sitio a fin de reducir el tamaño de archivo y acelerar la descarga."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Habilita [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) en \"WP Rocket\" para solucionar este problema. Se quitarán los espacios vacíos y los comentarios de los archivos JavaScript para reducir su tamaño y acelerar la descarga."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Habilita la opción [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) en \"WP Rocket\" para solucionar el problema. Reduce el tamaño de la página, ya que quita todas las CSS y hojas de estilo que no se usan, a la vez que mantiene solo las CSS que se usan en cada página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Habilita [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) en \"WP Rocket\" para solucionar este problema. Mejorará la carga de tu página, ya que retrasa la ejecución de las secuencias de comandos hasta la interacción del usuario. Si tu sitio tiene iframes, puedes usar [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) de \"WP Rocket\" y [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Habilita \"Imagify\" en la pestaña de optimización de imágenes de \"WP Rocket\" y ejecuta Bulk Optimization para comprimir las imágenes."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Usa [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) en \"WP Rocket\" para agregar \"dns-prefetch\" y acelerar la conexión con dominios externos. Además, \"WP Rocket\" agrega automáticamente la opción \"preconnect\" al [dominio de Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) y a cualquier CNAME que se agregue mediante la función [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Para solucionar este problema, habilita [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) en \"WP Rocket\". Las fuentes más importantes de tu sitio se precargarán con prioridad."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Consulta la calculadora."}, "report/renderer/report-utils.js | collapseView": {"message": "Contraer vista"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navegación inicial"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latencia de ruta crítica máxima:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copiar JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Activar o desactivar el Tema oscuro"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Se expandió la impresión"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Resumen de impresión"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Guardar como Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Guardar como HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Guardar como JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON><PERSON> en el lector"}, "report/renderer/report-utils.js | errorLabel": {"message": "Error"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Error del informe: No hay información de la auditoría"}, "report/renderer/report-utils.js | expandView": {"message": "Expandir vista"}, "report/renderer/report-utils.js | footerIssue": {"message": "Informa sobre un problema"}, "report/renderer/report-utils.js | hide": {"message": "Ocultar"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Datos de prueba"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> de [Lighthouse](https://developers.google.com/web/tools/lighthouse/) de la página actual en una red móvil emulada. Los valores son estimados y pueden variar."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Elementos adicionales que se deben comprobar manualmente"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "No aplicable"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunidad"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON> estimado"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Auditorías aprobadas"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Carga inicial de la página"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitación personalizada"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Escritorio emulado"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Sin emulación"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versión de axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potencia de memoria/CPU ilimitada"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitación de la CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositivo"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitación de la red"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulación de pantallas"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Usuario-agente (red)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Carga de una única página"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Estos datos se obtienen de la carga de una única página, a diferencia de los datos de campos que sintetizan la información de muchas sesiones."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitación de la red 4G lenta"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Desconocido"}, "report/renderer/report-utils.js | show": {"message": "Mostrar"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostrar las auditorías relevantes para la métrica:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Contraer fragmento"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Expandir fragmento"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos de terceros"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Proporcionado por el entorno"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Algunos problemas afectaron la ejecución de Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Los valores son estimados y pueden variar. La [medición del rendimiento se calcula](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directamente a partir de estas métricas."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Ver registro original"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Ver registro"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Ver diagrama de árbol"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Auditorías aprobadas con advertencias"}, "report/renderer/report-utils.js | warningHeader": {"message": "Advertencias: "}, "treemap/app/src/util.js | allLabel": {"message": "Todo"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Todas las secuencias de comandos"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Cobertura"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes de recursos"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nombre"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Activar o desactivar tabla"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Bytes sin usar"}}