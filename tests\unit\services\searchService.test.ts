import { SearchService } from '@/lib/services/searchService'

// Mock de los datos JSON
jest.mock('@/tramites_chia_optimo.json', () => [
  {
    Nombre: 'Licencia de construcción',
    dependencia: 'Secretaría de Planeación',
    subdependencia: 'Dirección de Planeación',
    '¿Tiene pago?': 'Sí - $419.000',
    'Tiempo de respuesta': '45 días hábiles',
    'Visualización trámite en el SUIT': 'https://suit.gov.co/test',
    'Visualización trámite en GOV.CO': 'https://gov.co/test',
    Formulario: 'Sí'
  },
  {
    Nombre: 'Certificado de residencia',
    dependencia: 'Secretaría General',
    subdependencia: 'Dirección Administrativa',
    '¿Tiene pago?': 'No',
    'Tiempo de respuesta': '1 día hábil',
    'Visualización trámite en el SUIT': 'https://suit.gov.co/test2',
    'Visualización trámite en GOV.CO': 'https://gov.co/test2',
    Formulario: 'No'
  }
])

jest.mock('@/OPA-chia-optimo.json', () => ({
  dependencias: {
    '001': {
      nombre: 'Despacho Alcalde',
      subdependencias: {
        '001': {
          nombre: 'Despacho del Alcalde',
          OPA: [
            { codigo_OPA: 'OPA001', OPA: 'Atención ciudadana general' },
            { codigo_OPA: 'OPA002', OPA: 'Audiencias públicas' }
          ]
        }
      }
    }
  }
}))

describe('SearchService', () => {
  let searchService: SearchService

  beforeEach(() => {
    searchService = new SearchService()
  })

  describe('search', () => {
    it('debería retornar resultados vacíos para query vacío', async () => {
      const results = await searchService.search('')
      expect(results).toEqual([])
    })

    it('debería encontrar trámites por nombre exacto', async () => {
      const results = await searchService.search('licencia construcción')
      
      expect(results).toHaveLength(1)
      expect(results[0].name).toBe('Licencia de construcción')
      expect(results[0].type).toBe('TRAMITE')
      expect(results[0].dependency).toBe('Secretaría de Planeación')
    })

    it('debería encontrar trámites por coincidencia parcial', async () => {
      const results = await searchService.search('certificado')
      
      expect(results.length).toBeGreaterThan(0)
      expect(results[0].name).toContain('Certificado')
    })

    it('debería encontrar OPAs', async () => {
      const results = await searchService.search('atención ciudadana')
      
      const opaResults = results.filter(r => r.type === 'OPA')
      expect(opaResults.length).toBeGreaterThan(0)
    })

    it('debería ordenar resultados por relevancia', async () => {
      const results = await searchService.search('licencia')
      
      // El primer resultado debería tener el score más alto
      if (results.length > 1) {
        expect(results[0].matchScore).toBeGreaterThanOrEqual(results[1].matchScore!)
      }
    })

    it('debería respetar el límite de resultados', async () => {
      const results = await searchService.search('certificado', {}, { limit: 1 })
      
      expect(results).toHaveLength(1)
    })

    it('debería filtrar por tipo', async () => {
      const tramiteResults = await searchService.search('licencia', { type: 'TRAMITE' })
      const opaResults = await searchService.search('atención', { type: 'OPA' })
      
      tramiteResults.forEach(result => {
        expect(result.type).toBe('TRAMITE')
      })
      
      opaResults.forEach(result => {
        expect(result.type).toBe('OPA')
      })
    })

    it('debería filtrar por dependencia', async () => {
      const results = await searchService.search('licencia', { 
        dependency: 'Secretaría de Planeación' 
      })
      
      results.forEach(result => {
        expect(result.dependency).toContain('Planeación')
      })
    })

    it('debería incluir highlighting cuando se solicite', async () => {
      const results = await searchService.search('licencia', {}, { 
        includeHighlight: true 
      })
      
      if (results.length > 0) {
        expect(results[0].highlightedName).toBeDefined()
        expect(results[0].highlightedName).toContain('<mark>')
      }
    })
  })

  describe('getPopularSearches', () => {
    it('debería retornar lista de búsquedas populares', async () => {
      const popular = await searchService.getPopularSearches()
      
      expect(Array.isArray(popular)).toBe(true)
      expect(popular.length).toBeGreaterThan(0)
      expect(typeof popular[0]).toBe('string')
    })

    it('debería respetar el límite especificado', async () => {
      const popular = await searchService.getPopularSearches(3)
      
      expect(popular).toHaveLength(3)
    })
  })

  describe('getDependencies', () => {
    it('debería retornar lista de dependencias únicas', async () => {
      const dependencies = await searchService.getDependencies()
      
      expect(Array.isArray(dependencies)).toBe(true)
      expect(dependencies.length).toBeGreaterThan(0)
      
      // Verificar que no hay duplicados
      const uniqueDeps = [...new Set(dependencies)]
      expect(dependencies).toEqual(uniqueDeps)
    })

    it('debería retornar dependencias ordenadas', async () => {
      const dependencies = await searchService.getDependencies()
      
      const sorted = [...dependencies].sort()
      expect(dependencies).toEqual(sorted)
    })
  })

  describe('getSearchStats', () => {
    it('debería retornar estadísticas correctas', async () => {
      const stats = await searchService.getSearchStats()
      
      expect(stats).toHaveProperty('totalTramites')
      expect(stats).toHaveProperty('totalOPAs')
      expect(stats).toHaveProperty('totalDependencies')
      
      expect(typeof stats.totalTramites).toBe('number')
      expect(typeof stats.totalOPAs).toBe('number')
      expect(typeof stats.totalDependencies).toBe('number')
      
      expect(stats.totalTramites).toBeGreaterThan(0)
      expect(stats.totalOPAs).toBeGreaterThan(0)
      expect(stats.totalDependencies).toBeGreaterThan(0)
    })
  })
})
