import { supabase } from '@/lib/supabase/client'
import tramitesData from '@/tramites_chia_optimo.json'
import opasData from '@/OPA-chia-optimo.json'

// Interfaces para tipos de datos
export interface Dependency {
  id: string
  name: string
  sigla: string
  tramitesCount: number
  opasCount: number
  totalProcedures: number
  subdependenciasCount: number
  description?: string
  icon?: string
  color?: string
}

export interface DependencyStats {
  totalDependencies: number
  totalTramites: number
  totalOPAs: number
  totalProcedures: number
  averageProceduresPerDependency: number
}

export interface ProceduresByDependency {
  dependency: Dependency
  tramites: any[]
  opas: any[]
}

/**
 * Servicio para gestionar datos de dependencias municipales
 * Procesa información de trámites y OPAs agrupados por dependencia
 */
class DependencyService {
  private static instance: DependencyService
  private dependencies: Map<string, Dependency> = new Map()
  private initialized = false

  private constructor() {}

  static getInstance(): DependencyService {
    if (!DependencyService.instance) {
      DependencyService.instance = new DependencyService()
    }
    return DependencyService.instance
  }

  /**
   * Inicializa el servicio procesando los datos de dependencias
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await this.processDependencies()
      this.initialized = true
    } catch (error) {
      console.error('Error inicializando DependencyService:', error)
      throw error
    }
  }

  /**
   * Procesa los datos de trámites y OPAs para extraer información de dependencias
   */
  private async processDependencies(): Promise<void> {
    const dependencyMap = new Map<string, {
      name: string
      sigla: string
      tramites: any[]
      opas: any[]
    }>()

    // Procesar trámites
    tramitesData.forEach((tramite: any) => {
      const depCode = tramite.codigo_dependencia
      const depName = tramite.dependencia
      
      if (!dependencyMap.has(depCode)) {
        dependencyMap.set(depCode, {
          name: depName,
          sigla: this.extractSigla(depName),
          tramites: [],
          opas: [],
          subdependenciasCount: 0
        })
      }
      
      dependencyMap.get(depCode)!.tramites.push(tramite)
    })

    // Procesar OPAs
    Object.entries(opasData.dependencias).forEach(([depCode, depData]: [string, any]) => {
      if (!dependencyMap.has(depCode)) {
        dependencyMap.set(depCode, {
          name: depData.nombre,
          sigla: depData.sigla,
          tramites: [],
          opas: [],
          subdependenciasCount: 0
        })
      }

      // Contar OPAs en todas las subdependencias
      const opasCount = Object.values(depData.subdependencias).reduce((total: number, subDep: any) => {
        return total + (subDep.OPA ? subDep.OPA.length : 0)
      }, 0)

      // Contar subdependencias
      const subdependenciasCount = Object.keys(depData.subdependencias || {}).length

      const existingDep = dependencyMap.get(depCode)!
      existingDep.name = depData.nombre // Usar nombre de OPAs si está disponible
      existingDep.sigla = depData.sigla
      existingDep.opas = this.extractOPAsFromDependency(depData)
      existingDep.subdependenciasCount = subdependenciasCount
    })

    // Convertir a formato Dependency
    dependencyMap.forEach((depData, depCode) => {
      const dependency: Dependency = {
        id: depCode,
        name: depData.name,
        sigla: depData.sigla,
        tramitesCount: depData.tramites.length,
        opasCount: depData.opas.length,
        totalProcedures: depData.tramites.length + depData.opas.length,
        subdependenciasCount: depData.subdependenciasCount || 0,
        description: this.generateDescription(depData.name),
        icon: this.assignIcon(depData.name),
        color: this.assignColor(depCode)
      }

      this.dependencies.set(depCode, dependency)
    })
  }

  /**
   * Extrae OPAs de una dependencia y sus subdependencias
   */
  private extractOPAsFromDependency(depData: any): any[] {
    const opas: any[] = []
    
    Object.entries(depData.subdependencias).forEach(([subDepCode, subDepData]: [string, any]) => {
      if (subDepData.OPA) {
        subDepData.OPA.forEach((opa: any) => {
          opas.push({
            ...opa,
            dependencia: depData.nombre,
            subdependencia: subDepData.nombre,
            codigo_dependencia: depData.codigo || subDepCode.substring(0, 3),
            codigo_subdependencia: subDepCode
          })
        })
      }
    })

    return opas
  }

  /**
   * Extrae sigla de un nombre de dependencia
   */
  private extractSigla(name: string): string {
    if (name.includes('Secretaría')) {
      const words = name.replace('Secretaría de ', '').split(' ')
      return words.map(word => word.charAt(0).toUpperCase()).join('')
    }
    
    const words = name.split(' ')
    if (words.length === 1) return words[0].substring(0, 3).toUpperCase()
    return words.map(word => word.charAt(0).toUpperCase()).join('').substring(0, 4)
  }

  /**
   * Genera descripción para una dependencia
   */
  private generateDescription(name: string): string {
    const descriptions: { [key: string]: string } = {
      'Despacho Alcalde': 'Oficina principal del Alcalde Municipal',
      'Secretaría de Gobierno': 'Gestión del orden público y convivencia ciudadana',
      'Secretaría de Hacienda': 'Administración financiera y tributaria municipal',
      'Secretaría de Planeación': 'Ordenamiento territorial y desarrollo urbano',
      'Secretaría de Desarrollo Social': 'Programas sociales y atención a población vulnerable',
      'Secretaría de Educación': 'Administración del sistema educativo municipal',
      'Secretaría de Salud': 'Vigilancia y control en salud pública',
      'Secretaría de Movilidad': 'Gestión del tránsito y transporte público',
      'Descentralizados': 'Entidades descentralizadas del municipio'
    }

    return descriptions[name] || `Gestión de procedimientos de ${name}`
  }

  /**
   * Asigna icono representativo para una dependencia
   */
  private assignIcon(name: string): string {
    const icons: { [key: string]: string } = {
      'Despacho Alcalde': 'crown',
      'Secretaría de Gobierno': 'shield',
      'Secretaría de Hacienda': 'banknote',
      'Secretaría de Planeación': 'map',
      'Secretaría de Desarrollo Social': 'users',
      'Secretaría de Educación': 'graduation-cap',
      'Secretaría de Salud': 'heart-pulse',
      'Secretaría de Movilidad': 'car',
      'Descentralizados': 'building'
    }

    return icons[name] || 'folder'
  }

  /**
   * Asigna color temático para una dependencia
   */
  private assignColor(depCode: string): string {
    const colors = [
      'bg-chia-blue-100 border-chia-blue-300 hover:bg-chia-blue-200',
      'bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200',
      'bg-blue-100 border-blue-300 hover:bg-blue-200',
      'bg-green-100 border-green-300 hover:bg-green-200',
      'bg-purple-100 border-purple-300 hover:bg-purple-200',
      'bg-orange-100 border-orange-300 hover:bg-orange-200',
      'bg-teal-100 border-teal-300 hover:bg-teal-200',
      'bg-indigo-100 border-indigo-300 hover:bg-indigo-200',
      'bg-pink-100 border-pink-300 hover:bg-pink-200'
    ]

    const index = parseInt(depCode) % colors.length
    return colors[index]
  }

  /**
   * Obtiene todas las dependencias
   */
  async getAllDependencies(): Promise<Dependency[]> {
    await this.initialize()
    return Array.from(this.dependencies.values()).sort((a, b) => 
      b.totalProcedures - a.totalProcedures
    )
  }

  /**
   * Obtiene una dependencia por ID
   */
  async getDependencyById(id: string): Promise<Dependency | null> {
    await this.initialize()
    return this.dependencies.get(id) || null
  }

  /**
   * Obtiene estadísticas generales de dependencias
   */
  async getDependencyStats(): Promise<DependencyStats> {
    await this.initialize()
    
    const dependencies = Array.from(this.dependencies.values())
    const totalTramites = dependencies.reduce((sum, dep) => sum + dep.tramitesCount, 0)
    const totalOPAs = dependencies.reduce((sum, dep) => sum + dep.opasCount, 0)
    const totalProcedures = totalTramites + totalOPAs

    return {
      totalDependencies: dependencies.length,
      totalTramites,
      totalOPAs,
      totalProcedures,
      averageProceduresPerDependency: Math.round(totalProcedures / dependencies.length)
    }
  }

  /**
   * Busca dependencias por nombre
   */
  async searchDependencies(query: string): Promise<Dependency[]> {
    await this.initialize()
    
    const searchTerm = query.toLowerCase().trim()
    if (!searchTerm) return this.getAllDependencies()

    return Array.from(this.dependencies.values())
      .filter(dep => 
        dep.name.toLowerCase().includes(searchTerm) ||
        dep.sigla.toLowerCase().includes(searchTerm) ||
        (dep.description && dep.description.toLowerCase().includes(searchTerm))
      )
      .sort((a, b) => b.totalProcedures - a.totalProcedures)
  }

  /**
   * Obtiene procedimientos de una dependencia específica
   */
  async getProceduresByDependency(dependencyId: string): Promise<ProceduresByDependency | null> {
    await this.initialize()
    
    const dependency = this.dependencies.get(dependencyId)
    if (!dependency) return null

    // Obtener trámites de esta dependencia
    const tramites = tramitesData.filter((tramite: any) => 
      tramite.codigo_dependencia === dependencyId
    )

    // Obtener OPAs de esta dependencia
    const opasDepData = opasData.dependencias[dependencyId]
    const opas = opasDepData ? this.extractOPAsFromDependency(opasDepData) : []

    return {
      dependency,
      tramites,
      opas
    }
  }

  /**
   * Obtiene las dependencias más populares (con más procedimientos)
   */
  async getTopDependencies(limit: number = 6): Promise<Dependency[]> {
    await this.initialize()
    
    return Array.from(this.dependencies.values())
      .sort((a, b) => b.totalProcedures - a.totalProcedures)
      .slice(0, limit)
  }
}

export default DependencyService.getInstance()
