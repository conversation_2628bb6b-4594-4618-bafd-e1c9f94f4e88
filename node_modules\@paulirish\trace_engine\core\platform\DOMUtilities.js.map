{"version": 3, "file": "DOMUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/DOMUtilities.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAC,GAAa;IAC7C,IAAI,aAAa,GAAiB,GAAG,CAAC,aAAa,CAAC;IACpD,OAAO,aAAa,IAAI,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QAC3F,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC;IACzD,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,6BAA6B,CAAC,IAAU;IACtD,IAAI,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC/C,OAAO,UAAU,EAAE,CAAC;QAClB,IAAI,UAAU,YAAY,UAAU,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,UAAU,GAAG,UAAU,CAAC,sBAAsB,EAAE,CAAC;IACnD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,WAAW,CACvB,QAAc,EAAE,MAAc,EAAE,cAAsB,EAAE,cAAoB,EAAE,SAAkB;IAClG,IAAI,SAAS,CAAC;IACd,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,OAAO,CAAC;IACZ,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,cAAc,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;QACnE,IAAI,IAAI,GAAc,QAAQ,CAAC;QAC/B,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,SAAS,GAAG,cAAc,CAAC;gBAC7B,CAAC;gBACD,MAAM;YACR,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC/E,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;oBAChC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACrD,SAAS,GAAG,IAAI,CAAC;wBACjB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;wBACpB,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM;YACR,CAAC;YAED,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS,GAAG,cAAc,CAAC;YAC3B,WAAW,GAAG,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,QAAQ,CAAC;QACrB,WAAW,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;QAClE,IAAI,IAAI,GAAqB,QAAQ,CAAC;QACtC,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO,GAAG,cAAc,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACR,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;oBACnD,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACrD,OAAO,GAAG,IAAI,CAAC;wBACf,SAAS,GAAG,CAAC,CAAC;wBACd,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM;YACR,CAAC;YAED,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,cAAc,CAAC;YACzB,SAAS,GAAG,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;gBACvC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC;QAC5F,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,QAAQ,CAAC;QACnB,SAAS,GAAG,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;IACpD,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACxC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAElC,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * `document.activeElement` will not enter shadow roots to find the element\n * that has focus; use this method if you need to traverse through any shadow\n * roots to find the actual, specific focused element.\n */\nexport function deepActiveElement(doc: Document): Element|null {\n  let activeElement: Element|null = doc.activeElement;\n  while (activeElement && activeElement.shadowRoot && activeElement.shadowRoot.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\n\nexport function getEnclosingShadowRootForNode(node: Node): Node|null {\n  let parentNode = node.parentNodeOrShadowHost();\n  while (parentNode) {\n    if (parentNode instanceof ShadowRoot) {\n      return parentNode;\n    }\n    parentNode = parentNode.parentNodeOrShadowHost();\n  }\n  return null;\n}\n\nexport function rangeOfWord(\n    rootNode: Node, offset: number, stopCharacters: string, stayWithinNode: Node, direction?: string): Range {\n  let startNode;\n  let startOffset = 0;\n  let endNode;\n  let endOffset = 0;\n\n  if (!stayWithinNode) {\n    stayWithinNode = rootNode;\n  }\n\n  if (!direction || direction === 'backward' || direction === 'both') {\n    let node: Node|null = rootNode;\n    while (node) {\n      if (node === stayWithinNode) {\n        if (!startNode) {\n          startNode = stayWithinNode;\n        }\n        break;\n      }\n\n      if (node.nodeType === Node.TEXT_NODE && node.nodeValue !== null) {\n        const start = (node === rootNode ? (offset - 1) : (node.nodeValue.length - 1));\n        for (let i = start; i >= 0; --i) {\n          if (stopCharacters.indexOf(node.nodeValue[i]) !== -1) {\n            startNode = node;\n            startOffset = i + 1;\n            break;\n          }\n        }\n      }\n\n      if (startNode) {\n        break;\n      }\n\n      node = node.traversePreviousNode(stayWithinNode);\n    }\n\n    if (!startNode) {\n      startNode = stayWithinNode;\n      startOffset = 0;\n    }\n  } else {\n    startNode = rootNode;\n    startOffset = offset;\n  }\n\n  if (!direction || direction === 'forward' || direction === 'both') {\n    let node: (Node|null)|Node = rootNode;\n    while (node) {\n      if (node === stayWithinNode) {\n        if (!endNode) {\n          endNode = stayWithinNode;\n        }\n        break;\n      }\n\n      if (node.nodeType === Node.TEXT_NODE && node.nodeValue !== null) {\n        const start = (node === rootNode ? offset : 0);\n        for (let i = start; i < node.nodeValue.length; ++i) {\n          if (stopCharacters.indexOf(node.nodeValue[i]) !== -1) {\n            endNode = node;\n            endOffset = i;\n            break;\n          }\n        }\n      }\n\n      if (endNode) {\n        break;\n      }\n\n      node = node.traverseNextNode(stayWithinNode);\n    }\n\n    if (!endNode) {\n      endNode = stayWithinNode;\n      endOffset = stayWithinNode.nodeType === Node.TEXT_NODE ? stayWithinNode.nodeValue?.length || 0 :\n                                                               stayWithinNode.childNodes.length;\n    }\n  } else {\n    endNode = rootNode;\n    endOffset = offset;\n  }\n\n  if (!rootNode.ownerDocument) {\n    throw new Error('No `ownerDocument` found for rootNode');\n  }\n  const result = rootNode.ownerDocument.createRange();\n  result.setStart(startNode, startOffset);\n  result.setEnd(endNode, endOffset);\n\n  return result;\n}\n"]}