/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/error/ErrorBoundary.tsx */ \"(app-pages-browser)/./components/error/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/accessibility.tsx */ \"(app-pages-browser)/./lib/accessibility.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true,\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/accessibility.tsx":
/*!*******************************!*\
  !*** ./lib/accessibility.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: function() { return /* binding */ SkipLink; },\n/* harmony export */   announceToScreenReader: function() { return /* binding */ announceToScreenReader; },\n/* harmony export */   calculateContrastRatio: function() { return /* binding */ calculateContrastRatio; },\n/* harmony export */   getButtonProps: function() { return /* binding */ getButtonProps; },\n/* harmony export */   getFormFieldProps: function() { return /* binding */ getFormFieldProps; },\n/* harmony export */   getLinkProps: function() { return /* binding */ getLinkProps; },\n/* harmony export */   getModalProps: function() { return /* binding */ getModalProps; },\n/* harmony export */   getTableProps: function() { return /* binding */ getTableProps; },\n/* harmony export */   meetsContrastRequirement: function() { return /* binding */ meetsContrastRequirement; },\n/* harmony export */   useAriaLiveRegion: function() { return /* binding */ useAriaLiveRegion; },\n/* harmony export */   useFocusManagement: function() { return /* binding */ useFocusManagement; },\n/* harmony export */   useHighContrast: function() { return /* binding */ useHighContrast; },\n/* harmony export */   useKeyboardNavigation: function() { return /* binding */ useKeyboardNavigation; },\n/* harmony export */   useReducedMotion: function() { return /* binding */ useReducedMotion; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ calculateContrastRatio,meetsContrastRequirement,useKeyboardNavigation,announceToScreenReader,useFocusManagement,SkipLink,useAriaLiveRegion,useReducedMotion,useHighContrast,getFormFieldProps,getTableProps,getModalProps,getButtonProps,getLinkProps auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n/**\n * Accessibility utilities for WCAG 2.1 AA compliance\n * Includes keyboard navigation, screen reader support, and color contrast utilities\n */ \n// WCAG 2.1 AA color contrast ratios\nconst CONTRAST_RATIOS = {\n    AA_NORMAL: 4.5,\n    AA_LARGE: 3,\n    AAA_NORMAL: 7,\n    AAA_LARGE: 4.5\n};\n// Color contrast calculation\nfunction calculateContrastRatio(color1, color2) {\n    const getLuminance = (color)=>{\n        // Convert hex to RGB\n        const hex = color.replace(\"#\", \"\");\n        const r = parseInt(hex.substr(0, 2), 16) / 255;\n        const g = parseInt(hex.substr(2, 2), 16) / 255;\n        const b = parseInt(hex.substr(4, 2), 16) / 255;\n        // Calculate relative luminance\n        const getRGB = (c)=>{\n            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n        };\n        return 0.2126 * getRGB(r) + 0.7152 * getRGB(g) + 0.0722 * getRGB(b);\n    };\n    const lum1 = getLuminance(color1);\n    const lum2 = getLuminance(color2);\n    const brightest = Math.max(lum1, lum2);\n    const darkest = Math.min(lum1, lum2);\n    return (brightest + 0.05) / (darkest + 0.05);\n}\n// Check if color combination meets WCAG standards\nfunction meetsContrastRequirement(foreground, background) {\n    let level = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"AA\", isLargeText = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n    const ratio = calculateContrastRatio(foreground, background);\n    if (level === \"AA\") {\n        return ratio >= (isLargeText ? CONTRAST_RATIOS.AA_LARGE : CONTRAST_RATIOS.AA_NORMAL);\n    } else {\n        return ratio >= (isLargeText ? CONTRAST_RATIOS.AAA_LARGE : CONTRAST_RATIOS.AAA_NORMAL);\n    }\n}\n// Keyboard navigation hook\nfunction useKeyboardNavigation(containerRef) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    _s();\n    const { focusableSelector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])', loop = true, autoFocus = false } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const container = containerRef.current;\n        if (!container) return;\n        const focusableElements = container.querySelectorAll(focusableSelector);\n        if (autoFocus && focusableElements.length > 0) {\n            focusableElements[0].focus();\n        }\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Tab\") {\n                const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);\n                if (e.shiftKey) {\n                    // Shift + Tab (previous)\n                    if (currentIndex <= 0) {\n                        if (loop) {\n                            e.preventDefault();\n                            focusableElements[focusableElements.length - 1].focus();\n                        }\n                    }\n                } else {\n                    // Tab (next)\n                    if (currentIndex >= focusableElements.length - 1) {\n                        if (loop) {\n                            e.preventDefault();\n                            focusableElements[0].focus();\n                        }\n                    }\n                }\n            }\n            // Arrow key navigation for lists and grids\n            if ([\n                \"ArrowUp\",\n                \"ArrowDown\",\n                \"ArrowLeft\",\n                \"ArrowRight\"\n            ].includes(e.key)) {\n                const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);\n                let nextIndex = currentIndex;\n                switch(e.key){\n                    case \"ArrowDown\":\n                    case \"ArrowRight\":\n                        nextIndex = currentIndex + 1;\n                        break;\n                    case \"ArrowUp\":\n                    case \"ArrowLeft\":\n                        nextIndex = currentIndex - 1;\n                        break;\n                }\n                if (nextIndex >= 0 && nextIndex < focusableElements.length) {\n                    e.preventDefault();\n                    focusableElements[nextIndex].focus();\n                } else if (loop) {\n                    e.preventDefault();\n                    if (nextIndex < 0) {\n                        focusableElements[focusableElements.length - 1].focus();\n                    } else {\n                        focusableElements[0].focus();\n                    }\n                }\n            }\n            // Home/End navigation\n            if (e.key === \"Home\") {\n                e.preventDefault();\n                focusableElements[0].focus();\n            } else if (e.key === \"End\") {\n                e.preventDefault();\n                focusableElements[focusableElements.length - 1].focus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>container.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        containerRef,\n        focusableSelector,\n        loop,\n        autoFocus\n    ]);\n}\n_s(useKeyboardNavigation, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n// Screen reader announcements\nfunction announceToScreenReader(message) {\n    let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"polite\";\n    const announcement = document.createElement(\"div\");\n    announcement.setAttribute(\"aria-live\", priority);\n    announcement.setAttribute(\"aria-atomic\", \"true\");\n    announcement.className = \"sr-only\";\n    announcement.textContent = message;\n    document.body.appendChild(announcement);\n    // Remove after announcement\n    setTimeout(()=>{\n        document.body.removeChild(announcement);\n    }, 1000);\n}\n// Focus management hook\nfunction useFocusManagement() {\n    _s1();\n    const previousFocusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const saveFocus = ()=>{\n        previousFocusRef.current = document.activeElement;\n    };\n    const restoreFocus = ()=>{\n        if (previousFocusRef.current) {\n            previousFocusRef.current.focus();\n        }\n    };\n    const trapFocus = (containerRef)=>{\n        const container = containerRef.current;\n        if (!container) return;\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Tab\") {\n                if (e.shiftKey) {\n                    if (document.activeElement === firstElement) {\n                        e.preventDefault();\n                        lastElement.focus();\n                    }\n                } else {\n                    if (document.activeElement === lastElement) {\n                        e.preventDefault();\n                        firstElement.focus();\n                    }\n                }\n            }\n            if (e.key === \"Escape\") {\n                restoreFocus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus();\n        return ()=>{\n            container.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    };\n    return {\n        saveFocus,\n        restoreFocus,\n        trapFocus\n    };\n}\n_s1(useFocusManagement, \"vBCYOLWFINdoBTu9HyUIFvLuFyE=\");\n// Skip link component for keyboard navigation\nfunction SkipLink(param) {\n    let { href, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-md focus:shadow-lg\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\lib\\\\accessibility.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_c = SkipLink;\n// ARIA live region hook for dynamic content updates\nfunction useAriaLiveRegion() {\n    _s2();\n    const [liveRegion, setLiveRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const region = document.createElement(\"div\");\n        region.setAttribute(\"aria-live\", \"polite\");\n        region.setAttribute(\"aria-atomic\", \"true\");\n        region.className = \"sr-only\";\n        document.body.appendChild(region);\n        setLiveRegion(region);\n        return ()=>{\n            if (document.body.contains(region)) {\n                document.body.removeChild(region);\n            }\n        };\n    }, []);\n    const announce = (message)=>{\n        if (liveRegion) {\n            liveRegion.textContent = message;\n        }\n    };\n    return announce;\n}\n_s2(useAriaLiveRegion, \"psDMvue3ZEM4PZahFBEKMnMYJnA=\");\n// Reduced motion detection\nfunction useReducedMotion() {\n    _s3();\n    const [prefersReducedMotion, setPrefersReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n        setPrefersReducedMotion(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setPrefersReducedMotion(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, []);\n    return prefersReducedMotion;\n}\n_s3(useReducedMotion, \"c2o+PeDo1dLruq/wbnW+Z6a6rIY=\");\n// High contrast mode detection\nfunction useHighContrast() {\n    _s4();\n    const [prefersHighContrast, setPrefersHighContrast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mediaQuery = window.matchMedia(\"(prefers-contrast: high)\");\n        setPrefersHighContrast(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setPrefersHighContrast(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, []);\n    return prefersHighContrast;\n}\n_s4(useHighContrast, \"5lg4err4w/R33O8pH8TDXog9KIs=\");\n// Form accessibility utilities\nfunction getFormFieldProps(id, label, error, description, required) {\n    const describedBy = [];\n    if (description) describedBy.push(\"\".concat(id, \"-description\"));\n    if (error) describedBy.push(\"\".concat(id, \"-error\"));\n    return {\n        field: {\n            id,\n            \"aria-describedby\": describedBy.length > 0 ? describedBy.join(\" \") : undefined,\n            \"aria-invalid\": error ? \"true\" : undefined,\n            \"aria-required\": required ? \"true\" : undefined\n        },\n        label: {\n            htmlFor: id,\n            children: label + (required ? \" *\" : \"\")\n        },\n        description: description ? {\n            id: \"\".concat(id, \"-description\"),\n            children: description\n        } : null,\n        error: error ? {\n            id: \"\".concat(id, \"-error\"),\n            role: \"alert\",\n            \"aria-live\": \"polite\",\n            children: error\n        } : null\n    };\n}\n// Table accessibility utilities\nfunction getTableProps(caption, summary) {\n    return {\n        table: {\n            role: \"table\",\n            \"aria-label\": caption\n        },\n        caption: {\n            children: caption,\n            className: \"sr-only\"\n        },\n        summary: summary ? {\n            children: summary,\n            className: \"sr-only\"\n        } : null\n    };\n}\n// Modal accessibility utilities\nfunction getModalProps(titleId, descriptionId, onClose) {\n    return {\n        modal: {\n            role: \"dialog\",\n            \"aria-modal\": \"true\",\n            \"aria-labelledby\": titleId,\n            \"aria-describedby\": descriptionId,\n            onKeyDown: (e)=>{\n                if (e.key === \"Escape\" && onClose) {\n                    onClose();\n                }\n            }\n        },\n        title: {\n            id: titleId\n        },\n        description: descriptionId ? {\n            id: descriptionId\n        } : null,\n        closeButton: {\n            \"aria-label\": \"Cerrar modal\",\n            onClick: onClose\n        }\n    };\n}\n// Button accessibility utilities\nfunction getButtonProps(label) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { expanded, controls, pressed, disabled } = options;\n    return {\n        \"aria-label\": label,\n        \"aria-expanded\": expanded !== undefined ? expanded : undefined,\n        \"aria-controls\": controls,\n        \"aria-pressed\": pressed !== undefined ? pressed : undefined,\n        disabled,\n        type: \"button\"\n    };\n}\n// Link accessibility utilities\nfunction getLinkProps(text) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { external, download, current } = options;\n    return {\n        \"aria-label\": text + (external ? \" (abre en nueva ventana)\" : \"\") + (download ? \" (descarga archivo)\" : \"\"),\n        \"aria-current\": current ? \"page\" : undefined,\n        target: external ? \"_blank\" : undefined,\n        rel: external ? \"noopener noreferrer\" : undefined,\n        download: download ? true : undefined\n    };\n}\nvar _c;\n$RefreshReg$(_c, \"SkipLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/accessibility.tsx\n"));

/***/ })

});