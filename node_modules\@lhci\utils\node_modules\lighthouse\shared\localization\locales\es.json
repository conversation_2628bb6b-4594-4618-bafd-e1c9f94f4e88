{"core/audits/accessibility/accesskeys.js | description": {"message": "Las claves de acceso permiten a los usuarios dirigirse rápidamente a una parte concreta de la página. Para facilitar una navegación correcta, las claves de acceso deben ser únicas. [Más información sobre las claves de acceso](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Los valores de `[accesskey]` no son únicos"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Los valores de `[accesskey]` son únicos"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> `role` de ARIA admite un subconjunto determinado de atributos `aria-*`. Si no coinciden, los atributos `aria-*` se invalidarán. [Consulta cómo asociar atributos ARIA a sus funciones](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no se corresponden con sus funciones"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Los atributos `[aria-*]` coinciden con sus funciones"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Si un elemento no tiene un nombre accesible, los lectores de pantalla lo leen diciendo un nombre genérico, lo que hace que el elemento no resulte útil a los usuarios que necesitan lectores de pantalla. [Consulta cómo hacer que los elementos de comando sean más accesibles](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Los elementos `button`, `link` y `menuitem` no tienen nombres accesibles"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Los elementos `button`, `link` y `menuitem` tienen nombres accesibles"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Las tecnologías asistenciales, como los lectores de pantalla, funcionan de forma inestable cuando se establece `aria-hidden=\"true\"` en el documento `<body>`. [Consulta cómo afecta `aria-hidden` al cuerpo del documento](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` se encuentra en el documento `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` no se encuentra en el documento `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Los elementos inferiores enfocables que están dentro de un elemento `[aria-hidden=\"true\"]` evitan que esos elementos interactivos estén disponibles para los usuarios de tecnologías asistenciales, como lectores de pantalla. [Consulta cómo `aria-hidden` afecta a los elementos enfocables](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Los elementos `[aria-hidden=\"true\"]` contienen elementos descendientes seleccionables"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Los elementos `[aria-hidden=\"true\"]` no contienen ningún elemento inferior seleccionable"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Si un campo de entrada no tiene un nombre accesible, los lectores de pantalla lo leerán en voz alta con un nombre genérico, lo que hace que el campo no resulte útil a los usuarios que necesitan lectores de pantalla. [Más información sobre las etiquetas de campos de entrada](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Los campos de entrada de ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Los campos de entrada ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Si un elemento medidor no tiene un nombre accesible, los lectores de pantalla lo leen diciendo un nombre genérico, lo que hace que no resulte útil para los usuarios que necesitan lectores de pantalla. [Consulta cómo asignar un nombre a los elementos `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Los elementos `meter` de ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Los elementos `meter` de ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Si un elemento `progressbar` no tiene un nombre accesible, los lectores de pantalla lo leen diciendo un nombre genérico, lo que hace que no resulte útil a los usuarios que necesitan lectores de pantalla. [Consulta cómo etiquetar elementos `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Los elementos `progressbar` de ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Los elementos `progressbar` de ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Algunas funciones de ARIA incluyen atributos obligatorios que describen el estado del elemento a los lectores de pantalla. [Más información sobre las funciones y los atributos obligatorios](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Los elementos `[role]` no incluyen todos los atributos `[aria-*]` necesarios"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Todos los elementos `[role]` tienen los atributos `[aria-*]` obligatorios"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Algunas funciones principales de ARIA deben contener funciones secundarias específicas para llevar a cabo las funciones de accesibilidad correspondientes. [Más información sobre las funciones y los elementos secundarios obligatorios](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "A los elementos con un `[role]` ARIA que requieren que los elementos secundarios contengan un `[role]` específico les faltan algunos o todos los elementos secundarios necesarios."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Los elementos con un `[role]` ARIA que requieren que los elementos secundarios contengan un `[role]` específico tienen todos los elementos secundarios necesarios."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Algunas funciones secundarias de ARIA se deben incluir dentro de funciones principales concretas para poder llevar a cabo las funciones de accesibilidad correspondientes. [Más información sobre las funciones de ARIA y el elemento superior obligatorio](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Los atributos `[role]` no están incluidos dentro de los elementos principales obligatorios"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Los atributos `[role]` están incluidos en los elementos principales correspondientes"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Las funciones de ARIA deben tener valores válidos para realizar las funciones de accesibilidad correspondientes. [Más información sobre las funciones de ARIA válidas](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Los valores de `[role]` no son v<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Los valores de `[role]` son v<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Si un campo de interruptor no tiene un nombre accesible, los lectores de pantalla lo leerán en voz alta con un nombre genérico, lo que hace que el campo no resulte útil a los usuarios que necesitan lectores de pantalla. [Más información sobre los campos de interruptores](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Los campos de interruptores ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Los campos de interruptores ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Si un elemento de descripción emergente no tiene un nombre accesible, los lectores de pantalla lo leen diciendo un nombre genérico, lo que hace que no resulte útil para los usuarios que necesitan lectores de pantalla. [Consulta cómo asignar un nombre a los elementos `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Los elementos `tooltip` de ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Los elementos `tooltip` de ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Si un elemento `treeitem` no tiene un nombre accesible, los lectores de pantalla lo leen diciendo un nombre genérico, lo que hace que no resulte útil a los usuarios que necesitan lectores de pantalla. [Más información sobre cómo etiquetar elementos `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Los elementos `treeitem` de ARIA no tienen nombres accesibles"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Los elementos `treeitem` de ARIA tienen nombres accesibles"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Las tecnologías asistenciales, como los lectores de pantalla, no pueden interpretar los atributos ARIA cuyos valores no sean válidos. [Más información sobre los valores válidos de los atributos ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Los atributos `[aria-*]` no tienen valores válidos"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Los atributos `[aria-*]` tienen valores válidos"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Las tecnologías asistenciales, como los lectores de pantalla, no pueden interpretar los atributos ARIA con nombres no válidos. [Más información sobre los atributos ARIA válidos](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no son válidos o no están bien escritos"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Los atributos `[aria-*]` son válidos y están bien escritos"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos con errores"}, "core/audits/accessibility/button-name.js | description": {"message": "Si un botón no tiene un nombre accesible, los lectores de pantalla lo leerán en voz alta como \"botón\", lo que hace que el botón no resulte útil a los usuarios que necesitan lectores de pantalla. [Consulta cómo hacer que los botones sean más accesibles](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Los botones no tienen nombres accesibles"}, "core/audits/accessibility/button-name.js | title": {"message": "Los botones tienen nombres accesibles"}, "core/audits/accessibility/bypass.js | description": {"message": "Incluir maneras de omitir el contenido repetitivo permite a los usuarios con teclado navegar por la página de forma más eficaz. [Más información sobre cómo omitir bloques](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "La página no contiene ningún en<PERSON>o, enlace de omisión ni región de punto de referencia"}, "core/audits/accessibility/bypass.js | title": {"message": "La página contiene un encabezado, un enlace de omisión o una región de punto de referencia"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Los textos con poco contraste resultan difíciles o imposibles de leer para muchos usuarios. [Consulta cómo crear suficiente contraste de color](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Los colores de fondo y de primer plano no tienen una relación de contraste adecuada."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Los colores de fondo y de primer plano tienen una relación de contraste adecuada"}, "core/audits/accessibility/definition-list.js | description": {"message": "Si las listas de definiciones no están bien marcadas, es posible que los lectores de pantalla las interpreten de forma confusa o imprecisa. [Consulta cómo estructurar las listas de definición correctamente](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Los `<dl>` no contienen únicamente grupos de `<dt>` y `<dd>` ordenados correctamente o elementos `<script>`, `<template>` o `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Los elementos `<dl>` contienen únicamente grupos de `<dt>` y `<dd>` o elementos `<script>`, `<template>` o `<div>` ordenados correctamente."}, "core/audits/accessibility/dlitem.js | description": {"message": "Los elementos de la lista de definiciones (`<dt>` y `<dd>`) deben estar incluidos en un elemento `<dl>` superior para asegurarte de que los lectores de pantalla puedan leerlos en voz alta correctamente. [Consulta cómo estructurar las listas de definición correctamente](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Los elementos de la lista de definiciones están incluidos dentro de elementos `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Los elementos de la lista de definiciones están incluidos dentro de elementos `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Los títulos proporcionan una idea general sobre la página a los usuarios de lectores de pantalla. Además, los usuarios de buscadores se basan principalmente en los títulos para determinar si una página es relevante para su búsqueda o no. [Más información sobre los títulos de documentos](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "El documento no contiene un elemento `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "El documento tiene un elemento `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Todos los elementos seleccionables deben tener un `id` único para asegurar que son visibles para las tecnologías asistenciales. [Consulta cómo corregir `id`s duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Los atributos `[id]` de los elementos activos seleccionables no son únicos"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Los atributos `[id]` de los elementos activos seleccionables son únicos"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "El valor de un ID de ARIA debe ser único para evitar que las tecnologías asistenciales omitan otras instancias. [Consulta cómo corregir IDs de ARIA duplicados](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Los ID de ARIA no son únicos"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Los ID de ARIA son únicos"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Las tecnologías asistenciales, como los lectores de pantalla, pueden leer de forma confusa los campos de formulario que tienen varias etiquetas, ya que pueden usar la primera etiqueta, la última o todas. [Consulta cómo utilizar las etiquetas de formularios](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Los campos de formulario tienen varias etiquetas"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Ningún campo de formulario tiene varias etiquetas"}, "core/audits/accessibility/frame-title.js | description": {"message": "Los usuarios de lectores de pantalla confían en que los títulos describan el contenido de los marcos. [Más información sobre los títulos de los marcos](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Los elementos `<frame>` o `<iframe>` no tienen título"}, "core/audits/accessibility/frame-title.js | title": {"message": "Los elementos `<frame>` o `<iframe>` tienen un título"}, "core/audits/accessibility/heading-order.js | description": {"message": "Los títulos ordenados correctamente que no saltan niveles transmiten la estructura semántica de la página, lo que facilita la navegación y la comprensión para los usuarios que usan tecnologías asistenciales. [Más información sobre el orden de los encabezados](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Los elementos de encabezado no aparecen en orden secuencial descendente"}, "core/audits/accessibility/heading-order.js | title": {"message": "Los elementos de encabezado aparecen en orden secuencial descendente"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Si no se especifica ningún atributo `lang` en una página, el lector de pantalla asumirá que la página está en el idioma predeterminado que el usuario eligió al configurarlo. Si el idioma de la página es diferente del predeterminado, es posible que el lector de pantalla no lea correctamente el texto de la página. [Más información sobre el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "El elemento `<html>` no tiene un atributo `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "El elemento `<html>` tiene un atributo `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Especificar un [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido permite a los lectores de pantalla leer el texto correctamente en voz alta. [Consulta cómo utilizar el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "El valor del atributo `[lang]` del elemento `<html>` no es válido."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "El atributo `[lang]` del elemento `<html>` tiene un valor válido"}, "core/audits/accessibility/image-alt.js | description": {"message": "Los elementos informativos deberían incluir textos alternativos cortos y descriptivos. Los elementos decorativos se pueden omitir usando un atributo \"alt\" vacío. [Más información sobre el atributo `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Los elementos de imagen no tienen ningún atributo `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Los elementos de imagen tienen atributos `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Cuando se usa una imagen como botón `<input>`, resulta útil proporcionar un texto alternativo para permitir que los usuarios de lectores de pantalla entiendan cuál es la función del botón. [Más información sobre añadir texto alternativo a las imágenes](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Los elementos `<input type=\"image\">` no tienen texto `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Los elementos `<input type=\"image\">` tienen texto `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Las etiquetas facilitan que las tecnologías asistenciales, como los lectores de pantalla, puedan leer los controles de los formularios de forma correcta. [Más información sobre las etiquetas de elementos de formulario](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Los elementos de formulario no tienen ninguna etiqueta asociada"}, "core/audits/accessibility/label.js | title": {"message": "Los elementos de formulario tienen etiquetas asociadas"}, "core/audits/accessibility/link-name.js | description": {"message": "Usar textos de enlace (y textos alternativos para las imágenes, si estas se usan como enlaces) que sean reconocibles, únicos y que se puedan seleccionar mejora la experiencia de navegación de los usuarios de lectores de pantalla. [Consulta cómo hacer que los enlaces sean accesibles](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Los enlaces no tienen nombres reconocibles"}, "core/audits/accessibility/link-name.js | title": {"message": "Los enlaces tienen nombres reconocibles"}, "core/audits/accessibility/list.js | description": {"message": "Los lectores de pantalla leen las listas en voz alta de una forma concreta. Se recomienda utilizar una estructura de listas adecuada para que los lectores de pantalla puedan leer las listas de forma correcta. [Más información sobre estructuras de listas adecuadas](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Las listas no contienen únicamente elementos `<li>` y elementos que admiten secuencias de comandos (`<script>` y `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Las listas contienen únicamente elementos `<li>` y elementos que admiten secuencias de comandos (`<script>` y `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Los lectores de pantalla requieren que los elementos de lista (`<li>`) estén incluidos dentro de un elemento superior`<ul>`, `<ol>` o `<menu>` para poder leerlos correctamente en voz alta. [Más información sobre estructuras de listas adecuadas](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Los elementos de lista (`<li>`) no están incluidos dentro de elementos superiores `<ul>`, `<ol>` o `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Los elementos de lista (`<li>`) están incluidos dentro de elementos superiores `<ul>`, `<ol>` o `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Los usuarios no esperan que las páginas se actualicen automáticamente. Si es así, se les volverá a dirigir a la parte superior de la página. Esto puede dar lugar a una experiencia frustrante o confusa. [Más información sobre la etiqueta meta de actualización](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "El documento usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "El documento no usa `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Inhabilitar el zoom provoca problemas a los usuarios con baja visión que necesitan ampliar la pantalla para poder ver correctamente el contenido de las páginas web. [Más información sobre la etiqueta meta de viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "El atributo `[user-scalable=\"no\"]` se usa en el elemento `<meta name=\"viewport\">` o el valor del atributo `[maximum-scale]` es inferior a 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` no se utiliza en el elemento `<meta name=\"viewport\">` y el valor del atributo `[maximum-scale]` no es inferior a 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Los lectores de pantalla no pueden traducir contenido que no sea texto. Si añades texto alternativo a los elementos `<object>`, los lectores de pantalla podrán transmitir su significado a los usuarios. [Más información sobre el texto alternativo de los elementos `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Los elementos `<object>` no tienen texto alternativo"}, "core/audits/accessibility/object-alt.js | title": {"message": "Los elementos `<object>` tienen texto alternativo"}, "core/audits/accessibility/tabindex.js | description": {"message": "Si el valor es superior a 0, significa que el orden de navegación es explícito. Aunque técnicamente es válido, esto suele producir experiencias frustrantes para los usuarios que necesitan usar tecnologías asistenciales. [Más información sobre el atributo `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Algunos elementos tienen un valor de `[tabindex]` superior a 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "No hay ningún elemento con un valor de `[tabindex]` superior a 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Asegurarse de que las celdas `<td>` que usan el atributo `[headers]` solo hacen referencia a otras celdas de la misma tabla mejora la experiencia de los usuarios de lectores de pantalla. [Más información sobre el atributo `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Las celdas de un elemento `<table>` que usan el atributo `[headers]` hacen referencia a un elemento `id` que no se encuentra en la misma tabla."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Las celdas de un elemento `<table>` que usan el atributo `[headers]` hacen referencia a otras celdas de la misma tabla."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Si te aseguras de que los encabezados de las tablas siempre hagan referencia a un conjunto de celdas, puedes mejorar la experiencia de los usuarios de lectores de pantalla. [Más información sobre los encabezados de tabla](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Los elementos `<th>` y los elementos con `[role=\"columnheader\"/\"rowheader\"]` no contienen las celdas de datos que describen."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Los elementos `<th>` y los elementos con atributos `[role=\"columnheader\"/\"rowheader\"]` contienen las celdas de datos que describen."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Especificar un [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) en los elementos ayuda a asegurar que los lectores de pantalla pronuncien correctamente las palabras del texto. [Consulta cómo utilizar el atributo `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Los atributos `[lang]` no tienen un valor válido"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Los atributos `[lang]` tienen un valor válido"}, "core/audits/accessibility/video-caption.js | description": {"message": "Si un vídeo tiene subtítulos, los usuarios sordos o con problemas auditivos pueden acceder a la información con más facilidad. [Más información sobre los subtítulos de los vídeos](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Los elementos `<video>` no contienen ningún elemento `<track>` con el atributo `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Los elementos `<video>` contienen un elemento `<track>` con el atributo `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Valor actual"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Token sugerido"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` ayuda a los usuarios a enviar formularios más rápido. Para reducir el esfuerzo de los usuarios, considera habilitarlo definiendo un valor válido para el atributo `autocomplete`. [Más información sobre `autocomplete` en formularios](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Los elementos `<input>` no tienen los atributos `autocomplete` correctos"}, "core/audits/autocomplete.js | manualReview": {"message": "Requiere revisión manual"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Revisa el orden de los tokens"}, "core/audits/autocomplete.js | title": {"message": "Los elementos `<input>` usan `autocomplete` correctamente"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Tokens de `autocomplete`: \"{token}\" no es válido en {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Revisa el orden de tokens: \"{tokens}\" en {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Se puede solucionar"}, "core/audits/bf-cache.js | description": {"message": "Muchos desplazamientos consisten en volver a una página anterior o regresar a una página posterior. La caché de páginas completas (bfcache) puede acelerar estos desplazamientos de regreso. [Más información sobre la caché de páginas completas](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo del error}other{# motivos del error}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivo del error"}, "core/audits/bf-cache.js | failureTitle": {"message": "La página ha impedido la restauración de la caché de páginas completas"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Tipo de error"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "No se puede solucionar"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Compatibilidad del navegador pendiente"}, "core/audits/bf-cache.js | title": {"message": "La página no ha impedido la restauración de la caché de páginas completas"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Las extensiones de Chrome han afectado de forma negativa al rendimiento de carga de esta página. Prueba a auditarla en modo incógnito o desde un perfil de Chrome sin extensiones."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Evaluación de la secuencia de comandos"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Análisis de la secuencia de comandos"}, "core/audits/bootup-time.js | columnTotal": {"message": "Tiempo de CPU total"}, "core/audits/bootup-time.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de JavaScript. Para ello, puedes utilizar cargas útiles de JavaScript más pequeñas. [Consulta cómo reducir el tiempo de ejecución de JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduce el tiempo de ejecución de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Tiempo de ejecución de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Quita los módulos de JavaScript grandes y duplicados de los paquetes para reducir el número de bytes innecesarios que consume la actividad de red. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Quita los módulos duplicados de los paquetes de JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Los GIFs de gran tamaño no son eficientes para mostrar contenido animado. Para usar menos bytes de la red, te recomendamos que utilices los formatos de vídeo MPEG4 o WebM para incluir animaciones y los formatos PNG o WebP para añadir imágenes estáticas en lugar del formato GIF. [Más información sobre formatos de vídeo eficientes](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Usa formatos de vídeo para incluir contenido animado"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Los polyfills y los transforms permiten que los navegadores antiguos utilicen las nuevas funciones de JavaScript. Sin embargo, muchos de estos polyfills y transforms no son necesarios para los navegadores modernos. Para tu JavaScript empaquetado, adopta una estrategia moderna de implementación de secuencias de comandos usando la detección de funciones module/nomodule para reducir la cantidad de código que se envía a los navegadores modernos sin perder la compatibilidad con los navegadores antiguos. [Consulta cómo utilizar JavaScript moderno](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Evita usar JavaScript antiguo en navegadores modernos"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Los formatos como WebP y AVIF suelen comprimir mejor las imágenes que los formatos PNG o JPEG, lo que hace que se descarguen más rápido y consuman menos datos. [Más información sobre formatos de imagen modernos](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Publica imágenes con formatos de próxima generación"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Te recomendamos que uses la carga en diferido con imágenes ocultas y que no aparecen en pantalla una vez que todos los recursos críticos hayan terminado de cargarse para reducir el tiempo que pasa hasta que la página es interactiva. [Consulta cómo posponer las imágenes que no aparecen en pantalla](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Pospón la carga de imágenes que no aparecen en pantalla"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Hay recursos que bloquean el primer renderizado de tu página. Te recomendamos que muestres los elementos de JavaScript y CSS críticos insertados y pospongas todos los que no sean esenciales. [Consulta cómo eliminar recursos que bloquean el renderizado](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimina los recursos que bloqueen el renderizado"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Si la carga útil de la red es muy grande, los usuarios consumen más datos móviles y las páginas tardan más en cargarse. [Consulta cómo reducir el tamaño de la carga útil](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Tamaño total: {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evita cargas útiles de red de gran tamaño"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita cargas útiles de red de gran tamaño"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Si minificas los archivos CSS, se puede reducir el tamaño de la carga útil de la red. [Consulta cómo minificar archivos CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifica los archivos CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Si minificas los archivos de JavaScript, se puede reducir el tamaño de la carga útil y el tiempo de análisis de la secuencia de comandos. [Consulta cómo minificar JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifica los recursos JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reduce las reglas que no se usen de las hojas de estilo y retrasa las reglas CSS que no se utilicen para el contenido de la parte visible a primera vista. Así, se reducirán los bytes consumidos por la actividad de red. [Consulta cómo reducir el contenido de CSS que no se use](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduce el contenido CSS que no se use"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reduce el contenido JavaScript que no se use y retrasa la carga de secuencias de comandos hasta que se necesiten. Así, se reducirán los bytes consumidos por la actividad de red. [Consulta cómo reducir el contenido de JavaScript que no se use](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduce el contenido JavaScript que no se use"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Una duración en caché más larga puede aumentar el número de visitas repetidas a tu página. [Más información sobre las políticas de caché eficaces](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Se ha encontrado 1 recurso}other{Se han encontrado # recursos}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Publica recursos estáticos con una política de caché eficaz"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Usa una política de caché eficaz en recursos estáticos"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Las imágenes optimizadas se cargan más rápido y consumen menos datos móviles. [Consulta cómo codificar imágenes de forma eficaz](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifica las imágenes de forma eficaz"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensiones reales"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensiones mostradas"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Las imágenes eran más grandes que el tamaño mostrado"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Las imágenes eran adecuadas para el tamaño mostrado"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Muestra imágenes con un tamaño adecuado para ahorrar datos móviles y mejorar el tiempo de carga. [Consulta cómo cambiar el tamaño de las imágenes](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Usa un tamaño adecuado para las imágenes"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Los recursos de texto se deberían publicar comprimidos (gzip, deflate o brotli) para minimizar el total de bytes de la red. [Más información sobre la compresión de texto](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Habilita la compresión de texto"}, "core/audits/content-width.js | description": {"message": "Si el ancho del contenido de tu aplicación no coincide con el ancho del viewport, es posible que no esté optimizada para pantallas de dispositivos móviles. [Consulta cómo definir el tamaño del contenido en el viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "El tamaño del viewport es de {innerWidth} px y no coincide con el de la ventana, que es de {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "El contenido no tiene el tamaño adecuado para el viewport"}, "core/audits/content-width.js | title": {"message": "El contenido tiene el tamaño adecuado para el viewport"}, "core/audits/critical-request-chains.js | description": {"message": "Las cadenas de solicitud crítica que se muestran a continuación indican qué recursos son de alta prioridad. Te recomendamos que reduzcas la longitud de las cadenas, disminuyas el tamaño de los recursos o pospongas la descarga de recursos innecesarios para mejorar la carga de la página. [Consulta cómo evitar encadenar solicitudes críticas](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Se ha encontrado 1 cadena}other{Se han encontrado # cadenas}}"}, "core/audits/critical-request-chains.js | title": {"message": "Evita encadenar solicitudes críticas"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravedad"}, "core/audits/csp-xss.js | description": {"message": "Una política de seguridad de contenido (CSP) sólida reduce considerablemente el riesgo de ataques de cross-site scripting (XSS). [Consulta cómo usar una CSP para prevenir XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxis"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "La página contiene una CSP definida en una etiqueta <meta>. Considera mover la CSP a un encabezado HTTP o definir otra CSP estricta en un encabezado HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "No se ha encontrado ninguna CSP en el modo obligatorio"}, "core/audits/csp-xss.js | title": {"message": "Asegura que la CSP sea efectiva frente a ataques XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Desactivación/Advertencia"}, "core/audits/deprecations.js | columnLine": {"message": "Lín<PERSON>"}, "core/audits/deprecations.js | description": {"message": "Las APIs obsoletas se eliminarán del navegador en el futuro. [Más información sobre las APIs obsoletas](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Se ha encontrado 1 advertencia}other{Se han encontrado # advertencias}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Usa API obsoletas"}, "core/audits/deprecations.js | title": {"message": "Evita las API obsoletas"}, "core/audits/dobetterweb/charset.js | description": {"message": "Es necesario declarar una codificación de caracteres. Puedes hacerlo utilizando una etiqueta `<meta>` situada en los primeros 1024 bytes del código HTML o en el encabezado de respuesta HTTP Content-Type. [Más información sobre cómo declarar la codificación de caracteres](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Falta la declaración del conjunto de caracteres o se ha definido demasiado tarde en el código HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "El conjunto de caracteres está definido correctamente"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Especificar un DOCTYPE evita que el navegador cambie al modo Quirks. [Más información sobre la declaración de DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "El nombre del DOCTYPE debe ser la cadena `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "El documento contiene un `doctype` que activa `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "El documento debe contener un elemento DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Se esperaba que publicId fuera una cadena vacía"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Se esperaba que systemId fuera una cadena vacía"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "El documento contiene un `doctype` que activa `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "A la página le falta el DOCTYPE de HTML, por lo que se ha activado el modo Quirks"}, "core/audits/dobetterweb/doctype.js | title": {"message": "La página tiene el DOCTYPE de HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estadística"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Los DOM de gran tamaño aumentan el uso de memoria, hacen que los [cálculos de estilo](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) duren más y generan costosas [redistribuciones del diseño](https://developers.google.com/speed/articles/reflow). [Consulta cómo evitar un tamaño de DOM excesivo](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}other{# elementos}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evita un tamaño excesivo de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidad máxima de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Número máximo de elementos secundarios"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evita un tamaño excesivo de DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Los usuarios dudan o desconfían de los sitios web que solicitan su ubicación sin contexto. Como alternativa, puedes vincular la solicitud a una acción del usuario. [Más información sobre el permiso de geolocalización](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita el permiso de geolocalización al cargar la página"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita solicitar el permiso de geolocalización al cargar la página"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipo de problema"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Los problemas registrados en el panel `Issues` de las herramientas para desarrolladores de Chrome indican que hay errores sin resolver. <PERSON><PERSON><PERSON> proceder de solicitudes fallidas de red, controles de seguridad insuficientes y otros errores del navegador. Abre el panel Issues de las herramientas para desarrolladores de Chrome para obtener más información sobre cada error."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Se han registrado problemas en el panel `Issues` de las herramientas para desarrolladores de Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloqueado por la política de orígenes cruzados"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Uso intenso de recursos por parte de anuncios"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "No hay ningún problema en el panel `Issues` de las herramientas para desarrolladores de Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versión"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Todas las bibliotecas frontend de JavaScript detectadas en la página. [Más información sobre esta auditoría de diagnóstico de detección de bibliotecas de JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Bibliotecas de JavaScript detectadas"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Las secuencias de comandos externas inyectadas dinámicamente mediante `document.write()` pueden retrasar la carga de la página varias decenas de segundos en conexiones lentas. [Consulta cómo evitar el elemento document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evitar `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Los usuarios dudan o desconfían de los sitios que solicitan enviar notificaciones sin contexto. Como alternativa, puedes vincular la solicitud a los gestos de los usuarios. [Más información sobre cómo obtener permiso para recibir notificaciones de forma responsable](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita el permiso de notificación al cargar la página"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita solicitar el permiso de notificación al cargar la página"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ofrece muchas ventajas con respecto a HTTP/1.1, como los encabezados binarios y la multiplexación. [Más información sobre HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitud no atendida mediante HTTP/2}other{# solicitudes no atendidas mediante HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Usa HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Se recomienda que los procesadores de eventos táctiles y de la rueda sean `passive` para mejorar el desplazamiento de tu página. [Más información sobre cómo adoptar procesadores de eventos pasivos](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "No usa listeners pasivos para mejorar el desplazamiento"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Usa listeners pasivos para mejorar el desplazamiento"}, "core/audits/errors-in-console.js | description": {"message": "Los errores registrados en la consola indican que hay problemas sin resolver. Pueden proceder de solicitudes fallidas de la red y otros errores del navegador. [Más información sobre estos errores en la auditoría de diagnóstico de la consola](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Se han registrado errores del navegador en la consola"}, "core/audits/errors-in-console.js | title": {"message": "No se ha registrado en la consola ningún error del navegador"}, "core/audits/font-display.js | description": {"message": "Usa la función CSS `font-display` para asegurarte de que los usuarios puedan ver el texto mientras se cargan las fuentes web. [Más información sobre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "Asegúrate de que el texto permanece visible mientras se carga la fuente web"}, "core/audits/font-display.js | title": {"message": "Todo el texto permanece visible mientras se carga la fuente web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse no ha podido comprobar automáticamente el valor `font-display` de la URL de origen: {fontOrigin}.}other{Lighthouse no ha podido comprobar automáticamente los valores `font-display` de la URL de origen: {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Relación de aspecto (real)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Relación de aspecto (mostrada)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Las dimensiones de las imágenes mostradas deberían mantener su relación de aspecto natural. [Más información sobre la relación de aspecto de imagen](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Muestra imágenes con una relación de aspecto incorrecta"}, "core/audits/image-aspect-ratio.js | title": {"message": "Muestra imágenes con la relación de aspecto adecuada"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Tamaño real"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Tam<PERSON><PERSON> esperado"}, "core/audits/image-size-responsive.js | description": {"message": "Para maximizar la claridad de la imagen, las dimensiones normales de la imagen deberían ser proporcionales al tamaño de la pantalla y a la proporción de píxeles. [Consulta cómo proporcionar imágenes adaptables](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Muestra imágenes con una resolución baja"}, "core/audits/image-size-responsive.js | title": {"message": "Muestra imágenes con una resolución adecuada"}, "core/audits/installable-manifest.js | already-installed": {"message": "La aplicación ya está instalada"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "No se ha podido descargar un icono necesario del archivo de manifiesto"}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivo del error"}, "core/audits/installable-manifest.js | description": {"message": "Service worker es la tecnología que te permite usar en tu aplicación muchas de las funciones de las aplicaciones web progresivas, como el modo Sin conexión, poder añadirlas a la pantalla de inicio y las notificaciones push. Si el service worker y el archivo de manifiesto se implementan de forma adecuada, los navegadores pueden preguntar a los usuarios de forma proactiva si quieren añadir tu aplicación a su pantalla de inicio, lo que puede incrementar el número de interacciones. [Consulta más información sobre los requisitos de instalación del archivo de manifiesto](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo}other{# motivos}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "El archivo de manifiesto de la aplicación web o el service worker no cumplen los requisitos de instalación"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "La URL de Play Store de la aplicación y el ID de Play Store no coinciden"}, "core/audits/installable-manifest.js | in-incognito": {"message": "La página se ha cargado en una ventana de Incógnito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "La propiedad \"display\" del archivo de manifiesto debe tener el valor \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "El archivo de manifiesto contiene el campo \"display_override\", y el primer modo de visualización admitido debe ser \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "El archivo de manifiesto no se ha podido obtener, está vacío o no se ha podido analizar"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "La URL del archivo de manifiesto ha cambiado mientras se estaba obteniendo el archivo de manifiesto."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "El archivo de manifiesto no contiene ningún campo \"name\" ni \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "El archivo de manifiesto no contiene un icono apto. Debe estar en formato PNG, SVG o WebP de al menos {value0} píxeles, debe definirse el atributo \"sizes\" y, si se define el atributo \"purpose\", debe tener el valor \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Ninguno de los iconos proporcionados tiene al menos {value0} píxeles en formato de imagen cuadrada PNG, SVG o WebP, con el atributo \"purpose\" sin definir o definido con el valor \"any\"."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "El icono descargado estaba vacío o dañado"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "No se ha proporcionado ningún ID de Play Store"}, "core/audits/installable-manifest.js | no-manifest": {"message": "La página no tiene ninguna URL del archivo de manifiesto <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "No se ha detectado ningún service worker que coincida. Es posible que tengas que volver a cargar la página, o comprueba que el alcance del service worker en la página actual incluya el alcance y la URL de inicio del archivo de manifiesto."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "No se ha podido comprobar el service worker porque no hay ningún campo \"start_url\" en el archivo de manifiesto"}, "core/audits/installable-manifest.js | noErrorId": {"message": "No se reconoce el ID de error de instalación \"{errorId}\""}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "La página no está alojada en un origen seguro"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "La página no se ha cargado en el marco principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "La página no funciona sin conexión"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "La PWA se ha desinstalado y las comprobaciones de inestabilidad se están restableciendo."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "La plataforma de aplicaciones especificada no es compatible con Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "El archivo de manifiesto especifica que \"prefer_related_applications\" tiene el valor true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "\"prefer_related_applications\" solo es compatible con Chrome Beta y con canales estables de Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse no ha podido determinar si había un service worker. Inténtalo con una versión más reciente de Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "El esquema de URL del archivo de manifiesto ({scheme}) no es compatible con Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "La URL de inicio del archivo de manifiesto no es válida"}, "core/audits/installable-manifest.js | title": {"message": "El archivo de manifiesto de la aplicación web y el service worker cumplen los requisitos de instalación"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Una URL del archivo de manifiesto contiene un nombre de usuario, una contraseña o un puerto"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "La página no funciona sin conexión. La página no se considerará instalable a partir de Chrome 93, la versión estable de agosto del 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Permitida"}, "core/audits/is-on-https.js | blocked": {"message": "Bloqueada"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL poco segura"}, "core/audits/is-on-https.js | columnResolution": {"message": "Resolución de solicitudes"}, "core/audits/is-on-https.js | description": {"message": "Todos los sitios deberían estar protegidos con el protocolo HTTPS, incluso los que no gestionen datos sensibles. Esto incluye evitar el [contenido mixto](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), en el que algunos recursos se cargan mediante HTTP a pesar de que la solicitud inicial se haya servido mediante HTTPS. HTTPS evita que los intrusos manipulen o escuchen de forma pasiva las comunicaciones entre tu aplicación y tus usuarios. Además, es un requisito previo para poder usar HTTP/2 y las APIs de muchas plataformas web nuevas. [Más información sobre HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Se ha encontrado 1 solicitud poco segura}other{Se han encontrado # solicitudes poco seguras}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "No usa HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Usa HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Actualizada a HTTPS automáticamente"}, "core/audits/is-on-https.js | warning": {"message": "Permitida con advertencia"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Este es el mayor elemento con contenido renderizado en el viewport. [Más información sobre el renderizado del mayor elemento con contenido](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Renderizado del mayor elemento con contenido"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribución al CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Estos elementos DOM son los que más contribuyen al CLS de la página. [Descubre cómo mejorar el CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Evitar cambios de diseño importantes"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Las imágenes de la parte visible a primera vista que se cargan en diferido se renderizan más tarde en el ciclo de vida de la página, lo que puede retrasar el renderizado del mayor elemento con contenido. [Más información sobre la carga en diferido óptima](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "La imagen del renderizado del mayor elemento con contenido se ha cargado en diferido"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "La imagen del renderizado del mayor elemento con contenido no se ha cargado en diferido"}, "core/audits/long-tasks.js | description": {"message": "Enumera las tareas más largas del hilo principal, lo que es útil para identificar las que más contribuyen a la latencia. [Consulta cómo evitar tareas largas del hilo principal](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tarea larga encontrada}other{# tareas largas encontradas}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON> tareas largas del hilo principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoría"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de JavaScript. Para ello, puedes utilizar cargas útiles de JavaScript más pequeñas. [Consulta cómo minimizar el trabajo del hilo principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimiza el trabajo del hilo principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiza el trabajo del hilo principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Para llegar al mayor nú<PERSON><PERSON> de usuarios, los sitios deben funcionar en los navegadores más habituales. [Más información sobre la compatibilidad entre navegadores](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "El sitio web funciona en diferentes navegadores"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Asegúrate de que cada página permita generar un enlace profundo en forma de URL, y de que dichas URLs se utilicen solo para compartir las páginas fácilmente en las redes sociales. [Más información sobre cómo proporcionar enlaces profundos](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tiene una URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Las transiciones deberían ser rápidas al tocar en diferentes partes de la aplicación, incluso si la red es lenta. Esta experiencia es fundamental para la percepción del usuario sobre el funcionamiento de la aplicación. [Más información sobre las transiciones de páginas](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "No parece que se bloqueen las transiciones de la página en la red"}, "core/audits/maskable-icon.js | description": {"message": "Los iconos enmascarables permiten que la imagen ocupe toda su figura sin que se produzca un efecto panorámico al instalar la aplicación en un dispositivo. [Más información sobre los iconos de archivo de manifiesto enmascarables](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "El archivo de manifiesto no tiene un icono adaptable"}, "core/audits/maskable-icon.js | title": {"message": "El archivo de manifiesto tiene un icono adaptable"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Los cambios de diseño acumulados miden el movimiento de los elementos visibles dentro del viewport. [Más información sobre la métrica Cambios de diseño acumulados](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interacción con el siguiente renderizado mide la capacidad de respuesta de la página, es decir, el tiempo que tarda la página en responder de forma visible a la entrada del usuario. [Más información sobre la métrica Interacción con el siguiente renderizado](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "El primer renderizado con contenido indica el momento en el que se renderiza el primer texto o la primera imagen. [Más información sobre la métrica Primer renderizado con contenido](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "El primer renderizado significativo mide el momento en que se muestra el contenido principal de la página. [Más información sobre la métrica Primer renderizado significativo](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "La métrica Time to Interactive mide el tiempo que tarda una página en ser totalmente interactiva. [Más información sobre la métrica Time to Interactive](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "El renderizado del mayor elemento con contenido indica el tiempo que se tarda en dibujar el texto o la imagen de mayor tamaño. [Más información sobre la métrica Renderizado del mayor elemento con contenido](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "La latencia potencial máxima de la primera interacción que podrían experimentar los usuarios es la duración de la tarea más larga. [Más información sobre la métrica Latencia potencial máxima de la primera interacción](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index indica la rapidez con la que se puede ver el contenido de una página. [Más información sobre la métrica Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Suma de los periodos, en milisegundos, entre FCP y Time to Interactive cuando la duración de la tarea excede los 50 ms. [Más información sobre la métrica Total Blocking Time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Los tiempos de ida y vuelta (RTT) de la red afectan mucho al rendimiento. Un RTT alto hasta un origen indica que usar servidores más cercanos al usuario podría mejorar el rendimiento. [Más información sobre el tiempo de ida y vuelta](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "Tiempos de ida y vuelta de la red"}, "core/audits/network-server-latency.js | description": {"message": "La latencia del servidor puede afectar al rendimiento del sitio web. Una latencia del servidor alta en un origen indica que el servidor está sobrecargado o que su rendimiento de backend es bajo. [Más información sobre el tiempo de respuesta del servidor](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Latencias de backend del servidor"}, "core/audits/no-unload-listeners.js | description": {"message": "El evento `unload` no se activa de manera fiable y esperar a que se detecte puede impedir optimizaciones del navegador, como el almacenamiento en caché de páginas completas. Usa los eventos `pagehide` o `visibilitychange` en su lugar. [Más información sobre los procesadores de eventos de descarga](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registra un procesador `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Impide el uso de procesadores de eventos `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Las animaciones no compuestas pueden aparecer entrecortadas e incrementar el CLS. [Consulta cómo evitar las animaciones no compuestas](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# elemento animado encontrado}other{# elementos animados encontrados}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "La propiedad \"filter\" relacionada puede tener píxeles en movimiento"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "El destino contiene otra animación no compatible"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "El efecto tiene un modo compuesto distinto de \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evita las animaciones no compuestas"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "La propiedad \"transform\" relacionada depende del tamaño de la caja"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Propiedad CSS no admitida: {properties}}other{Propiedades CSS no admitidas: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Este efecto incluye parámetros de temporización no compatibles"}, "core/audits/performance-budget.js | description": {"message": "Mantén la cantidad y el tamaño de las solicitudes de red por debajo de los objetivos definidos en los límites de rendimiento. [Más información sobre los límites de rendimiento](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 solicitud}other{# solicitudes}}"}, "core/audits/performance-budget.js | title": {"message": "Límites de rendimiento"}, "core/audits/preload-fonts.js | description": {"message": "Precarga las fuentes `optional` para que los usuarios puedan utilizarlas en su primera visita. [Más información sobre cómo precargar fuentes](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Las fuentes con `font-display: optional` no están precargadas"}, "core/audits/preload-fonts.js | title": {"message": "Se han precargado las fuentes con `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Si el elemento LCP se añade de forma dinámica a la página, tendrás que precargar la imagen para mejorar el LCP. [Más información sobre cómo precargar los elementos LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Precargar la imagen de renderizado del mayor elemento con contenido"}, "core/audits/redirects.js | description": {"message": "Las redirecciones provocan retrasos adicionales antes de que la página se pueda cargar. [Consulta cómo evitar las redirecciones de página](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evita que haya varias redirecciones de página"}, "core/audits/resource-summary.js | description": {"message": "Para definir la cantidad y el tamaño de los recursos de la página, añade un archivo budget.json. [Más información sobre los límites de rendimiento](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 solicitud • {byteCount, number, bytes} KiB}other{# solicitudes • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Reduce el número de solicitudes y el tamaño de las transferencias"}, "core/audits/seo/canonical.js | description": {"message": "Los enlaces canónicos sugieren qué URL se debe mostrar en los resultados de búsqueda. [Más información sobre los enlaces canónicos](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Varias URL en conflicto ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "La URL no es válida ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Apunta a una ubicación de `hreflang` distinta ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "No es una URL absoluta ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Apunta a la URL raíz del dominio (la página principal), en lugar de a una página de contenido equivalente"}, "core/audits/seo/canonical.js | failureTitle": {"message": "El documento no tiene un atributo `rel=canonical` válido"}, "core/audits/seo/canonical.js | title": {"message": "El documento tiene un atributo `rel=canonical` válido"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Enlace no rastreable"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Es posible que los buscadores usen atributos `href` en los enlaces para rastrear sitios web. Asegúrate de que el atributo `href` de los elementos de ancla enlace con un destino adecuado para que se puedan descubrir más páginas del sitio. [Consulta cómo hacer que los enlaces sean rastreables](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Los enlaces no son rastreables"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Los enlaces son rastreables"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Texto ilegible adicional"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Tamaño de fuente"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% de texto de página"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Las fuentes con un tamaño inferior a 12 px son demasiado pequeñas y poco legibles, lo que obliga a los visitantes que acceden con dispositivos móviles a pellizcar la pantalla para ampliarla y poder leer el texto. Intenta que más del 60 % del texto de la página tenga un tamaño igual o superior a 12 px. [Más información sobre los tamaños de fuente legibles](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "El {decimalProportion, number, extendedPercent} del texto es legible"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "El texto es ilegible porque no hay etiquetas meta de viewport optimizadas para pantallas de dispositivos móviles."}, "core/audits/seo/font-size.js | failureTitle": {"message": "El documento no usa tamaños de fuente legibles"}, "core/audits/seo/font-size.js | legibleText": {"message": "Texto legible"}, "core/audits/seo/font-size.js | title": {"message": "El documento usa tamaños de fuente legibles"}, "core/audits/seo/hreflang.js | description": {"message": "Los enlaces \"hreflang\" indican a los buscadores qué versiones de las páginas deben incluir en los resultados de búsqueda de una zona o un idioma determinados. [Más información sobre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "El atributo `hreflang` del documento no es válido"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valor href relativo"}, "core/audits/seo/hreflang.js | title": {"message": "El documento tiene un atributo `hreflang` válido"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Código de idioma inesperado"}, "core/audits/seo/http-status-code.js | description": {"message": "Es posible que las páginas con códigos de estado HTTP no válidos no estén bien indexadas. [Más información sobre los códigos de estado HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "El código de estado HTTP de la página no es válido"}, "core/audits/seo/http-status-code.js | title": {"message": "El código de estado HTTP de la página es válido"}, "core/audits/seo/is-crawlable.js | description": {"message": "Los buscadores no pueden incluir tus páginas en los resultados de búsqueda si no tienen permiso para rastrearlas. [Más información sobre las directivas del rastreador](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Se ha bloqueado la indexación de la página"}, "core/audits/seo/is-crawlable.js | title": {"message": "No se ha bloqueado la indexación de la página"}, "core/audits/seo/link-text.js | description": {"message": "El texto descriptivo de los enlaces ayuda a los buscadores a entender tu contenido. [Consulta cómo hacer que los enlaces sean más accesibles](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 enlace encontrado}other{# enlaces encontrados}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Los enlaces no tienen texto descriptivo"}, "core/audits/seo/link-text.js | title": {"message": "Los enlaces tienen texto descriptivo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Ejecuta la [Herramienta de prueba de datos estructurados](https://search.google.com/structured-data/testing-tool/) y la herramienta [Structured Data Linter](http://linter.structured-data.org/) para validar los datos estructurados. [Más información sobre los datos estructurados](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Los datos estructurados son v<PERSON><PERSON>os"}, "core/audits/seo/meta-description.js | description": {"message": "Se pueden incluir metadescripciones en los resultados de búsqueda para resumir brevemente el contenido de la página. [Más información sobre la metadescripción](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "El texto de la descripción está vacío."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "El documento no tiene una metadescripción"}, "core/audits/seo/meta-description.js | title": {"message": "El documento tiene una metadescripción"}, "core/audits/seo/plugins.js | description": {"message": "Los buscadores no pueden indexar el contenido de los complementos, y muchos dispositivos limitan el uso de complementos o no los admiten. [Más información sobre cómo evitar complementos](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "El documento usa complementos"}, "core/audits/seo/plugins.js | title": {"message": "El documento no usa complementos"}, "core/audits/seo/robots-txt.js | description": {"message": "Si el formato del archivo robots.txt no es correcto, es posible que los rastreadores no puedan interpretar cómo quieres que se rastree o indexe tu sitio web. [Más información sobre robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La solicitud de robots.txt ha devuelto el siguiente código de estado HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 error encontrado}other{# errores encontrados}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse no ha podido descargar un archivo robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt no es válido"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt es válido"}, "core/audits/seo/tap-targets.js | description": {"message": "Los elementos interactivos, como los botones y enlaces, deben ser lo suficientemente grandes (48x48 px) y tener suficiente espacio alrededor para poder tocarlos con facilidad sin superponerse con otros elementos. [Más información sobre los objetivos táctiles](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "El {decimalProportion, number, percent} de los elementos táctiles tiene un tamaño adecuado"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Los elementos táctiles son demasiado pequeños porque no hay etiquetas meta de viewport optimizadas para pantallas de dispositivos móviles"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "El tamaño de los elementos táctiles no es el adecuado"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Elementos superpuestos"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Elemento táctil"}, "core/audits/seo/tap-targets.js | title": {"message": "El tamaño de los elementos táctiles es el adecuado"}, "core/audits/server-response-time.js | description": {"message": "Mantén breve el tiempo de respuesta del servidor para el documento principal, puesto que todas las demás solicitudes dependen de él. [Más información sobre la métrica Tiempo hasta el primer byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "El documento raíz ha tardado {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reducir el tiempo de respuesta inicial del servidor"}, "core/audits/server-response-time.js | title": {"message": "El tiempo de respuesta inicial del servidor fue breve"}, "core/audits/service-worker.js | description": {"message": "El service worker es la tecnología que te permite usar en tu aplicación las funciones de las aplicaciones web progresivas, como el modo sin conexión, poder añadirlas a la pantalla de inicio y las notificaciones push. [Más información sobre los service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Un service worker controla esta página, pero no se ha encontrado ninguna propiedad `start_url` porque el archivo de manifiesto no es un JSON válido"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Un service worker controla esta página, pero la propiedad `start_url` ({startUrl}) está fuera del rango del service worker ({scopeUrl})."}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Un service worker controla esta página, pero no se ha encontrado la propiedad `start_url` porque no se ha recuperado ningún archivo de manifiesto."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Este origen tiene al menos un service worker, pero la página ({pageUrl}) no está dentro del rango."}, "core/audits/service-worker.js | failureTitle": {"message": "No registra un service worker que controle la página y la propiedad `start_url`"}, "core/audits/service-worker.js | title": {"message": "Hay un service worker que controla la página y la propiedad `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Una pantalla de inicio personalizada asegura una experiencia de calidad cuando los usuarios ejecuten tu aplicación desde sus pantallas de inicio. [Más información sobre las pantallas de inicio](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "No se ha configurado para una pantalla de inicio personalizada"}, "core/audits/splash-screen.js | title": {"message": "Se ha configurado para una pantalla de inicio personalizada"}, "core/audits/themed-omnibox.js | description": {"message": "El color de la barra de direcciones del navegador puede adaptarse a tu sitio. [Más información sobre cómo personalizar los temas de la barra de direcciones](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "No establece un color personalizado en la barra de direcciones."}, "core/audits/themed-omnibox.js | title": {"message": "Establece un color personalizado en la barra de direcciones."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (casos de éxito de clientes)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (medios sociales)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vídeo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Producto"}, "core/audits/third-party-facades.js | description": {"message": "Algunas inserciones de terceros se pueden cargar en diferido. Puedes sustituirlas por una fachada hasta que sean necesarias. [Consulta cómo posponer elementos de terceros con una fachada](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternativa de fachada disponible}other{# alternativas de fachada disponibles}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Algunos recursos de terceros se pueden cargar en diferido con una fachada"}, "core/audits/third-party-facades.js | title": {"message": "Cargar recursos de terceros en diferido con fachadas"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Proveedor externo"}, "core/audits/third-party-summary.js | description": {"message": "El código externo puede afectar mucho a la velocidad de carga. Limita el número de proveedores externos redundantes e intenta cargar el código externo cuando se haya completado la carga principal de tu página. [Consulta cómo minimizar el impacto de terceros](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "El código de un tercero ha bloqueado el hilo principal durante {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduce el impacto del código de terceros"}, "core/audits/third-party-summary.js | title": {"message": "Reducir el uso de código de terceros"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Medición"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "M<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Establece límites de tiempo para que te ayuden a controlar el rendimiento de tu sitio. Los sitios eficientes cargan su contenido y responden a los eventos de entrada del usuario con rapidez. [Más información sobre los límites de rendimiento](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Límites de tiempo"}, "core/audits/unsized-images.js | description": {"message": "En los elementos de imagen, especifica un ancho y una altura explícitos para reducir los cambios de diseño y mejorar el CLS. [Consulta cómo configurar las dimensiones de una imagen](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Los elementos de imagen no tienen `width` y `height` explícitos"}, "core/audits/unsized-images.js | title": {"message": "Los elementos de imagen tienen `width` y `height` explícitos"}, "core/audits/user-timings.js | columnType": {"message": "Tipo"}, "core/audits/user-timings.js | description": {"message": "Te recomendamos que uses la API User Timing en tu aplicación para calcular su rendimiento real durante las principales experiencias de usuario. [Más información sobre las marcas de User Timings](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 tiempo de usuario}other{# tiempos de usuario}}"}, "core/audits/user-timings.js | title": {"message": "Medidas y marcas de User Tim<PERSON>"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Se ha encontrado un elemento `<link rel=preconnect>` para \"{securityOrigin}\", pero el navegador no lo ha usado. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON><PERSON><PERSON> a<PERSON> sugerencias de recursos `preconnect` o `dns-prefetch` para establecer conexiones previas con orígenes importantes de terceros. [Consulta cómo establecer conexión previamente con orígenes necesarios](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Establece conexión previamente con los orígenes necesarios"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Se han encontrado más de 2 conexiones `<link rel=preconnect>`. Se deben usar con moderación y solo deben conectar con los orígenes más importantes."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Se ha encontrado un elemento `<link rel=preconnect>` para \"{securityOrigin}\", pero el navegador no lo ha usado. Usa `preconnect` solo para los orígenes importantes que seguramente solicitará la página."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Se ha encontrado un elemento `<link>` de precarga para \"{preloadURL}\", pero el navegador no lo ha usado. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "core/audits/uses-rel-preload.js | description": {"message": "Te recomendamos usar `<link rel=preload>` para dar prioridad a los recursos que se solicitan más tarde al cargar la página. [Consulta cómo precargar solicitudes clave](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Carga previamente las solicitudes clave"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL de mapa"}, "core/audits/valid-source-maps.js | description": {"message": "Los mapas de origen traducen el código minificado al código fuente original. Esto ayuda a los desarrolladores a depurar en producción. Además, Lighthouse puede proporcionar sugerencias adicionales. Plantéate desplegar mapas de origen para disfrutar de estas ventajas. [Más información sobre los mapas de origen](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "No hay mapas de origen para JavaScript propio de gran tamaño"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "La biblioteca de JavaScript de gran tamaño no tiene un mapa de origen"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Advertencia: Falta 1 elemento en `.sourcesContent`}other{Advertencia: Faltan # elementos en `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "La página tiene mapas de origen válidos"}, "core/audits/viewport.js | description": {"message": "El `<meta name=\"viewport\">` no solo optimiza tu aplicación para los tamaños de pantalla de los dispositivos móviles, sino que también evita [el retraso de 300 milisegundos de las entradas del usuario](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Más información sobre cómo usar la etiqueta meta de viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "No se ha encontrado ninguna etiqueta `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "No tiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Contiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Se trata del trabajo de bloqueo del hilo que se produce mientras se aplica la métrica Interacción con el siguiente renderizado. [Más información sobre la métrica Interacción con el siguiente renderizado](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms invertidos en el evento \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Objetivo de evento"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimizar el trabajo durante una interacción clave"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Latencia de la interacción"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Retraso de la presentación"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tiempo de procesamiento"}, "core/audits/work-during-interaction.js | title": {"message": "Minimiza el trabajo durante una interacción clave"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "A continuación se indican consejos para optimizar el uso de ARIA en tu aplicación, lo que puede mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Aquí tienes consejos para proporcionar contenido alternativo para audio y vídeo. Así se puede mejorar la experiencia de los usuarios con dificultades auditivas o visuales."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio y vídeo"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Estos elementos destacan las prácticas recomendadas de accesibilidad más habituales."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Prácticas recomendadas"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Estas comprobaciones incluyen consejos para [mejorar la accesibilidad de tu aplicación web](https://developer.chrome.com/docs/lighthouse/accessibility/). Solo se pueden detectar un subconjunto de problemas de accesibilidad de forma automática. Por eso, te recomendamos realizar pruebas manuales."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Estos elementos se ocupan de áreas que las herramientas de prueba automáticas no pueden analizar. Consulta más información sobre cómo [revisar la accesibilidad](https://web.dev/how-to-review/) en nuestra guía."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accesibilidad"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "A continuación se indican consejos para facilitar la lectura del contenido."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "A continuación se indican consejos para que los usuarios con diversas configuraciones regionales puedan interpretar mejor el contenido de las páginas."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalización y localización"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "A continuación se indican consejos para mejorar la semántica de los controles de tu aplicación. Estos consejos pueden mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nombres y etiquetas"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Aquí tienes consejos para facilitar el desplazamiento con el teclado en tu aplicación."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegación"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "A continuación se indican consejos para mejorar la lectura de datos en tablas o en listas con tecnología asistencial, como los lectores de pantalla."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tablas y listas"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilidad con navegadores"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Prácticas recomendadas"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "General"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Confianza y seguridad"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiencia de usuario"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Los límites de rendimiento definen los estándares de rendimiento de tu sitio web."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Límites"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Consulta más información sobre el rendimiento de tu aplicación. Estos datos no [afectan directamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) a la puntuación del rendimiento."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "El aspecto más importante del rendimiento es la rapidez con la que se renderizan los píxeles en la pantalla. Métricas clave: Primer renderizado con contenido y Primer renderizado significativo"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Mejoras del primer renderizado"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Estas sugerencias pueden ayudar a que tu página cargue más rápido. No [afectan directamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) a la puntuación del rendimiento"}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Mejora la experiencia de carga general para que la página responda bien y se pueda usar lo antes posible. Métricas clave: Tiempo hasta que está interactiva, Índice de velocidad"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Mejoras generales"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Rendimiento"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Estas comprobaciones validan los aspectos de las aplicaciones web progresivas. [Consulta cómo crear una buena aplicación web progresiva](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Estas comprobaciones son necesarias según el documento de referencia [PWA Checklist](https://web.dev/pwa-checklist/) (lista de comprobación para aplicaciones web progresivas), pero Lighthouse no las verifica automáticamente. Es importante que las verifiques manualmente (aunque no afectan a la puntuación)."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalabilidad"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizado para PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Estas comprobaciones aseguran que tu página siga las recomendaciones básicas sobre optimización en buscadores. Hay muchos otros factores adicionales que Lighthouse no valora aquí y que pueden afectar a tu posicionamiento en las búsquedas, como el rendimiento en las [Métricas web principales](https://web.dev/learn-core-web-vitals/). [Consulta más información sobre las Directrices básicas de la Búsqueda de Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Ejecuta estos validadores adicionales en tu sitio web para comprobar más prácticas recomendadas de SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Edita el código HTML de tu página web de forma que los rastreadores puedan entender mejor el contenido de tu aplicación."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Prácticas recomendadas de contenido"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para aparecer en los resultados de búsqueda, los rastreadores necesitan acceder a tu aplicación."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastrear e indexar"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Asegúrate de que tus páginas estén optimizadas para móviles de forma que los usuarios no tengan que pellizcar o ampliar la pantalla para poder leer su contenido. [Consulta cómo optimizar páginas para móviles](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Optimización para móviles"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Parece que el dispositivo probado tiene una CPU más lenta de lo que espera Lighthouse. Esto puede afectar negativamente a la puntuación de rendimiento. Más información sobre cómo [calibrar un multiplicador de ralentización de CPU adecuado](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Es posible que la página no se esté cargando correctamente porque la URL de prueba ({requested}) se ha redirigido a {final}. Prueba directamente la segunda URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "La página ha tardado demasiado en cargar como para terminar dentro del límite de tiempo. Es posible que el resultado esté incompleto."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Se ha agotado el tiempo de espera para borrar la caché del navegador. Prueba a auditar esta página de nuevo e informa del error si el problema persiste."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Es posible que algunos datos almacenados hayan afectado al rendimiento de las subidas en esta ubicación: {locations}. Haz una auditoría de esta página en una ventana de Incógnito para evitar que esos recursos afecten a tus puntuaciones.}other{Es posible que algunos datos almacenados hayan afectado al rendimiento de las subidas en estas ubicaciones: {locations}. Haz una auditoría de esta página en una ventana de Incógnito para evitar que esos recursos afecten a tus puntuaciones.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Se ha agotado el tiempo de espera para borrar los datos de origen. Prueba a auditar esta página de nuevo e informa del error si el problema persiste."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Solo las páginas que se han cargado a través de una solicitud GET pueden usar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Solo las páginas con un código de estado de 2XX se pueden almacenar en caché."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome ha detectado un intento de ejecutar JavaScript mientras estaba en la caché."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Las páginas que han solicitado un AppBanner no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Las marcas inhabilitan la caché de páginas completas. Ve a chrome://flags/#back-forward-cache para habilitarla localmente en este dispositivo."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "La línea de comandos ha inhabilitado la caché de páginas completas."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "La caché de páginas completas se ha inhabilitado porque no hay suficiente memoria."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Caché de páginas completas no admitida por delegado."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "La caché de páginas completas se ha inhabilitado para el prerrenderizador."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "La página no se puede almacenar en caché porque incluye una instancia BroadcastChannel con procesadores registrados."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Las páginas con un encabezado cache-control:no-store no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "La caché se ha borrado intencionadamente."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "La página se ha quitado de la caché para poder almacenar otra."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Las páginas que contienen complementos no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Las páginas que usan la API FileChooser no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Las páginas que usan la API File System Access no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Las páginas que usan Media Device Dispatcher no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Un reproductor multimedia estaba funcionando al salir."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Las páginas que usan la API MediaSession y definen un estado de reproducción no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Las páginas que usan la API MediaSession y establecen controladores de acciones no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "La caché de páginas completas está inhabilitada debido al lector de pantalla."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Las páginas que usan SecurityHandler no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Las páginas que usan la API Serial no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Las páginas que usan la API WebAuthentication no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Las páginas que usan la API WebBluetooth no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Las páginas que usan la API WebUSB no pueden utilizar la caché de páginas completas."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Las páginas que usan un worker o un worklet dedicado no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "El documento no había terminado de cargarse antes de salir."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "El banner de la aplicación estaba presente al salir."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "El Gestor de contraseñas de Chrome estaba presente al salir."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "La extracción del DOM estaba en curso al salir."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer estaba presente al salir."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "La caché de páginas completas está inhabilitada debido a extensiones que usan la API de mensajes."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Las extensiones con conexión de larga duración deben cerrar la conexión antes de acceder a la caché de páginas completas."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Hay extensiones con conexión de larga duración que han intentado enviar mensajes a marcos en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "La caché de páginas completas está inhabilitada debido a las extensiones."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "En la página se mostraba un diálogo modal, como un diálogo de reenvío de formulario o de contraseña HTTP, al salir."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "La página sin conexión se mostraba al salir."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "La barra de intervención en caso de falta de memoria estaba presente al salir."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Había solicitudes de permiso al salir."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "El bloqueador de ventanas emergentes estaba presente al salir."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Se mostraban detalles de Navegación segura al salir."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "La función Navegación segura ha considerado que esta página hace un uso inadecuado y ha bloqueado la ventana emergente."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Se ha activado un service worker mientras la página estaba en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "La caché de páginas completas se ha inhabilitado debido a un error de documento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Las páginas que usan FencedFrames no se pueden almacenar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "La página se ha quitado de la caché para poder almacenar otra."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Las páginas que han otorgado acceso de emisión multimedia no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Las páginas que usan portales no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Las páginas que usan IdleManager no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Las páginas que tienen una conexión IndexedDB abierta no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Se han usado APIs no válidas."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Las páginas en las que las extensiones inyectan JavaScript no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Las páginas en las que las extensiones inyectan StyleSheet no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Error interno."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "La caché de páginas completas se ha inhabilitado debido a una solicitud keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Las páginas que usan el bloqueo de teclado no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | loading": {"message": "Esta página no había terminado de cargarse antes de salir."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Las páginas cuyo recurso principal incluye cache-control:no-cache no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Las páginas cuyo recurso principal incluye cache-control:no-store no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "La navegación se ha cancelado antes de que la página se pudiera restaurar desde la caché de páginas completas."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "La página se ha quitado de la caché porque una conexión de red activa ha recibido demasiados datos. Chrome limita la cantidad de datos que puede recibir una página mientras está almacenada en caché."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Las páginas que tienen fetch() o XHR en curso no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "La página se ha quitado de la caché de páginas completas porque una solicitud de red activa incluía un redireccionamiento."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "La página se ha quitado de la caché porque una conexión de red ha estado abierta demasiado tiempo. Chrome limita el tiempo durante el que una página puede recibir datos mientras está almacenada en caché."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Las páginas que no tienen un encabezado de respuesta válido no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "La navegación se ha producido en un marco que no es el principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Las páginas con transacciones en curso de bases de datos indexadas no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Las páginas con una solicitud de red en curso no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Las páginas con una solicitud de red de obtención en curso no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Las páginas con una solicitud de red en curso no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Las páginas con una solicitud de red XHR en curso no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Las páginas que usan PaymentManager no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Las páginas que usan Picture-in-Picture no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | portal": {"message": "Las páginas que usan portales no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | printing": {"message": "Las páginas que muestran interfaz de impresión no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "La página se ha abierto usando `window.open()` y otra pestaña hace referencia a ella, o la página ha abierto una ventana."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "El proceso de renderizado de la página que está en la caché de páginas completas ha fallado."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "El proceso de renderizado de la página que está en la caché de páginas completas se ha cerrado."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Las páginas que han solicitado permisos de captura de audio no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Las páginas que han solicitado permisos de sensor no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Las páginas que han solicitado la sincronización en segundo plano o permisos de obtención no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Las páginas que han solicitado permisos MIDI no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Las páginas que han solicitado permisos de notificaciones no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Las páginas que han solicitado acceso al almacenamiento no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Las páginas que han solicitado permisos de captura de vídeo no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Solo las páginas cuyo esquema de URL es HTTP/HTTPS se pueden almacenar en caché."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "La página ha sido reclamada por un service worker mientras está en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker ha intentado enviar `MessageEvent` a la página que está en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Se ha anulado el registro de ServiceWorker mientras una página estaba en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "La página se ha quitado de la caché de páginas completas porque se ha activado un service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome se ha reiniciado y se han borrado las entradas de la caché de páginas completas."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Las páginas que usan SharedWorker no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Las páginas que usan SpeechRecognizer no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Las páginas que usan SpeechSynthesis no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Un iframe de la página ha comenzado una navegación que no ha completado."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Las páginas cuyo subrecurso incluye cache-control:no-cache no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Las páginas cuyo subrecurso incluye cache-control:no-store no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | timeout": {"message": "La página ha superado el tiempo máximo que puede estar en la caché de páginas completas y ha caducado."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "La página ha agotado el tiempo de espera al acceder a la caché de páginas completas (probablemente, debido a controladores pagehide de larga duración)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "La página tiene un controlador de descarga en el marco principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "La página tiene un controlador de descarga en un submarco."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "El navegador ha cambiado el encabezado de anulación de user-agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Las páginas que han concedido acceso para grabar vídeo o audio no pueden usar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Las páginas que usan WebDatabase no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Las páginas que usan WebHID no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Las páginas que usan WebLocks no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Las páginas que usan WebNfc no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Las páginas que usan WebOTPService no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Las páginas con WebRTC no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Las páginas que usan WebShare no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Las páginas con WebSocket no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Las páginas con WebTransport no se pueden guardar en la caché de páginas completas."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Las páginas que usan WebXR no pueden utilizar la caché de páginas completas por el momento."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "<PERSON>uedes añadir esquemas de URL https: y http: (los navegadores que admiten strict-dynamic los ignoran) para que sea retrocompatible con navegadores anteriores."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "La directiva disown-opener está obsoleta desde CSP3. Usa el encabezado Cross-Origin-Opener-Policy en su lugar."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "La directiva referrer está obsoleta desde CSP2. Usa el encabezado Referrer-Policy en su lugar."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "La directiva reflected-xss está obsoleta desde CSP2. Usa el encabezado X-XSS-Protection en su lugar."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "La falta de base-uri permite que las etiquetas <base> insertadas establezcan la URL base para todas las URLs relativas (por ejemplo, secuencias de comandos) en un dominio controlado por un atacante. Puedes asignar el valor 'none' o 'self' a base-uri."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "La falta de object-src permite que se inserten complementos que ejecutan secuencias de comandos no seguras. <PERSON> puedes, considera asignar el valor none a object-src."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Falta la directiva script-src. Esta directiva permite que se ejecuten secuencias de comandos que no son seguras."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "¿Te has olvidado del punto y coma? <PERSON>rece que {keyword} es una directiva, no una palabra clave."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Los nonces deben usar el conjunto de caracteres base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Los nonces deben tener un mínimo de 8 caracteres."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Evita usar esquemas de URL simples ({keyword}) en esta directiva. Los esquemas de URL simples permiten obtener secuencias de comandos de dominios no seguros."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Evita usar comodines simples ({keyword}) en esta directiva. Los comodines simples permiten obtener secuencias de comandos de dominios no seguros."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "El destino de los informes solo se configura con la directiva report-to. Esta directiva solo la admiten los navegadores Chromium, así que se recomienda usar también una directiva report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Ninguna CSP proporciona un destino de informes. <PERSON><PERSON> <PERSON><PERSON>, es más difícil mantener la CSP a lo largo del tiempo y monitorizar las vulnerabilidades."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Habitualmente, las listas de hosts permitidos se pueden sortear. Considera usar nonces o hashes de la CSP junto con strict-dynamic si es necesario."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Directiva CSP desconocida."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON>re<PERSON> que {keyword} no es una palabra clave válida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "La directiva unsafe-inline permite que se ejecuten secuencias de comandos in-page y gestores de eventos que no son seguros. Puedes usar nonces o hashes de CSP para permitir secuencias de comandos individualmente."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Puedes añadir la directiva unsafe-inline (los navegadores que admiten nonces o hashes la ignoran) para que sea retrocompatible con navegadores anteriores."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "El carácter comodín (*) no cubrirá la autorización en la manipulación CORS de `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Las solicitudes de recursos cuyas URLs contienen caracteres eliminados `(n|r|t)` de espacio y \"menor que\" (`<`) se bloquean. Elimina los saltos de línea y codifica los signos \"menor que\" de sitios como valores de atributos de elementos para cargar estos recursos."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` está obsoleto. En su lugar, usa la API estandarizada Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` está obsoleto. En su lugar, usa la API estandarizada Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` está obsoleto. En su lugar, usa la API estandarizada `nextHopProtocol` en Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Las cookies que contengan un carácter `(0|r|n)` se rechazarán en lugar de truncarse."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "La relajación de la política con el mismo origen estableciendo `document.domain` está obsoleta y se inhabilitará de forma predeterminada. Esta advertencia de desactivación se refiere a un acceso de orígenes cruzados que se habilitó estableciendo `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "La activación de {PH1} desde iframes de orígenes cruzados está obsoleta y se eliminará más adelante."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "El atributo `disableRemotePlayback` debería usarse para inhabilitar la integración predeterminada de Cast, en lugar de usar el selector `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} está obsoleto. Usa {PH2} en su lugar."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Este es un ejemplo de un mensaje traducido sobre un problema de desactivación."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "La relajación de la política con el mismo origen estableciendo `document.domain` está obsoleta y se inhabilitará de forma predeterminada. Para seguir usando esta función, inhabilita los clústeres de agentes con clave de origen enviando un encabezado `Origin-Agent-Cluster: ?0` junto con la respuesta HTTP del documento y los marcos. Consulta https://developer.chrome.com/blog/immutable-document-domain/ para obtener más información."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` está obsoleto y se eliminará. Usa `Event.composedPath()` en su lugar."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "El encabezado `Expect-CT` está obsoleto y se eliminará. Chrome exige transparencia en todos los certificados públicos de confianza emitidos después del 30 de abril del 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Consulta la página de estado de la función para ver más detalles."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` y `watchPosition()` ya no funcionan en orígenes no seguros. Para usar esta función, deberías plantearte cambiar tu aplicación a un origen seguro, como HTTPS. Consulta https://goo.gle/chrome-insecure-origins para obtener más información."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` y `watchPosition()` no están disponibles en orígenes no seguros. Para usar esta función, deberías plantearte cambiar tu aplicación a un origen seguro, como HTTPS. Consulta https://goo.gle/chrome-insecure-origins para obtener más información."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ya no funciona en orígenes no seguros. Para usar esta función, deberías plantearte cambiar tu aplicación a un origen seguro, como HTTPS. Consulta https://goo.gle/chrome-insecure-origins para obtener más información."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` está obsoleto. Usa `RTCPeerConnectionIceErrorEvent.address` o `RTCPeerConnectionIceErrorEvent.port` en su lugar."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "El origen del comercio y los datos arbitrarios del evento de service worker `canmakepayment` han quedado obsoletos y se eliminarán: `topOrigin`, `paymentRequestOrigin`, `methodData` y `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "El sitio web ha solicitado un subrecurso de una red a la que pudo acceder únicamente por la posición de red privilegiada de sus usuarios. Estas solicitudes exponen dispositivos y servidores no públicos a Internet, lo que aumenta el riesgo de que se produzca un ataque de falsificación de solicitud entre sitios (CSRF) o de que se filtre información. Para reducir estos riesgos, Chrome desasiste las solicitudes a subrecursos no públicos cuando se inician desde contextos no seguros, y empezará a bloquearlas."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "No puede cargarse el CSS desde URLs `file:` a no ser que terminen con la extensión de archivo `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "El uso de `SourceBuffer.abort()` para anular la eliminación de intervalo asíncrona de `remove()` está obsoleto debido al cambio de la especificación. La compatibilidad se eliminará más adelante. Deberías procesar el evento `updateend` en su lugar. `abort()` solo está pensado para anular una subida de contenido multimedia o restablecer el estado del analizador."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "La asignación a `MediaSource.duration` de valores situados por debajo de la marca de tiempo de presentación más alta de cualquier marco codificado en búfer está obsoleta debido al cambio de la especificación. La compatibilidad con la retirada implícita de contenido multimedia truncado en búfer se eliminará más adelante. En su lugar, deberías realizar `remove(newDuration, oldDuration)` de forma explícita en todos los `sourceBuffers`, donde se cumpla que `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Este cambio se aplicará con la versión principal {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI solicitará permiso de uso aunque no se especifique el SysEx en `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "La API Notification ya no puede usarse desde orígenes no seguros. Deberías plantearte cambiar tu aplicación a un origen seguro, como HTTPS. Consulta https://goo.gle/chrome-insecure-origins para obtener más información."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "El permiso para la API Notification ya no puede solicitarse desde un iframe de orígenes cruzados. En su lugar, deberías plantearte solicitar permiso desde un marco de nivel superior o abrir una nueva ventana."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Tu partner está negociando una versión obsoleta de (D)TLS. Habla con tu partner para solucionarlo."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL ya no está disponible en contextos no seguros y se eliminará pronto. Utiliza Web Storage o Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Si se especifica `overflow: visible` en las etiquetas img, video y canvas, estas podrían generar contenido visual fuera de los límites del elemento. Consulta https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` está obsoleta. Usa la instalación Just-In-Time para los controladores de pagos."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Tu llamada de `PaymentRequest` ha eludido la directiva `connect-src` sobre política de seguridad de contenido (CSP). Esta elusión está obsoleta. Añade el identificador del método de pago de la API `PaymentRequest` (en el campo `supportedMethods`) a tu directiva `connect-src` de CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` está obsoleto. Usa una versión estandarizada de `navigator.storage` en su lugar."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` con un `<picture>` superior no es válido y, por tanto, se ignorará. Usa `<source srcset>` en su lugar."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` está obsoleto. Usa una versión estandarizada de `navigator.storage` en su lugar."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Las solicitudes de subrecursos cuyas URLs contienen credenciales insertadas (por ejemplo, `**********************/`) se bloquean."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "La restricción `DtlsSrtpKeyAgreement` se ha eliminado. Le has asignado el valor `false` a esta restricción, lo que se interpreta como un intento de usar el método eliminado `SDES key negotiation`. Esta función se ha eliminado. Usa un servicio compatible con `DTLS key negotiation` en su lugar."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "La restricción `DtlsSrtpKeyAgreement` se ha eliminado. Le has asignado el valor `true` a esta restricción, lo que no ha tenido ningún efecto, pero puedes quitar esta restricción para organizarte mejor."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` detectado. Este dialecto de `Session Description Protocol` ya no se admite. Usa `Unified Plan SDP` en su lugar."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, que se utiliza al crear `RTCPeerConnection` con `{sdpSemantics:plan-b}`, es una versión antigua no estándar de `Session Description Protocol`, que se ha eliminado permanentemente de la plataforma web. Sigue disponible al programar con `IS_FUCHSIA`, pero tenemos intención de eliminarlo cuanto antes. Es necesario que dejes de depender de él. Consulta https://crbug.com/1302249 para ver el estado."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "La opción `rtcpMuxPolicy` está obsoleta y se eliminará."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` necesitará aislamiento de otros orígenes. Consulta https://developer.chrome.com/blog/enabling-shared-array-buffer/ para obtener más información."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` sin la activación del usuario está obsoleto y se eliminará."}, "core/lib/deprecations-strings.js | title": {"message": "Función obsoleta usada"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Las extensiones deben aceptar el aislamiento de otros orígenes para seguir usando `SharedArrayBuffer`. Consulta https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} es específica de un proveedor. Usa la función estándar {PH2} en su lugar."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF‑16 no es compatible con response.json en `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "El atributo síncrono `XMLHttpRequest` en el hilo principal está obsoleto por sus efectos negativos en la experiencia del usuario final. Para recibir más ayuda, consulta https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` está obsoleto. En su lugar, usa `isSessionSupported()` y comprueba el valor booleano resuelto."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tiempo de bloqueo del hilo principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Tiempo de vida en caché"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Descripción"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Duración"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementos con errores"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Ubicación"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nombre"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Por encima del límite"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Solicitudes"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Tamaño del recurso"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Fuente"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Hora de inicio"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Duración"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Tamaño de la transferencia"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Ahorro potencial"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Ahorro potencial"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cambios de diseño acumulados"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Ahorro potencial de {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elemento encontrado}other{# elementos encontrados}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Ahorro potencial de {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Primer <PERSON><PERSON><PERSON> significativo"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Fuente"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagen"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interacción con el siguiente renderizado"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alta"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Baja"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Media"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Renderizado del mayor elemento con contenido"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Latencia potencial máxima de la primera interacción"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Contenido multimedia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON>s recursos"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Secuencia de comandos"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Hoja de estilo"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Recursos externos"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Se ha producido un error al registrar el rastro durante la carga de la página. Ejecuta Lighthouse de nuevo. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Se ha agotado el tiempo de espera de la conexión inicial del protocolo del depurador."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome no ha recopilado ninguna captura de pantalla al cargar la página. Comprueba que haya contenido visible en la página e intenta ejecutar Lighthouse de nuevo. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Los servidores DNS no han podido resolver el dominio proporcionado."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "No se ha podido recuperar el recurso {artifactName} necesario: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Se ha producido un error interno de Chrome. Reinicia Chrome y prueba a ejecutar Lighthouse de nuevo."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "No se ha podido recuperar el recurso {artifactName} necesario."}, "core/lib/lh-error.js | noFcp": {"message": "La página no ha renderizado ningún contenido. Comprueba que la ventana del navegador se mantenga en primer plano durante la carga y vuelve a intentarlo. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "La página no ha mostrado contenido que se considere renderizado del mayor elemento con contenido (LCP). Comprueba que la página tenga un elemento LCP válido y vuelve a intentarlo. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "La página proporcionada no es HTML (publicada en tipo de MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Esta versión de Chrome es demasiado antigua y no es compatible con {featureName}. Usa una versión más reciente para ver resultados completos."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse no ha podido cargar correctamente la página que has solicitado. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse no ha podido cargar correctamente la URL que has solicitado porque la página ha dejado de responder."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "La URL que has proporcionado no tiene un certificado de seguridad válido. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ha evitado la carga de la página con un intersticial. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse no ha podido cargar correctamente la página que has solicitado. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes. (Detalles: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse no ha podido cargar correctamente la página que has solicitado. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes. (Código de estado: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "La página ha tardado demasiado tiempo en cargarse. Sigue los consejos del informe para reducir el tiempo de carga de la página y prueba a ejecutar Lighthouse de nuevo. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Se ha superado el tiempo asignado para la respuesta de protocolo de DevTools. (Método: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Se ha superado el tiempo asignado para obtener el contenido de los recursos"}, "core/lib/lh-error.js | urlInvalid": {"message": "La URL que has proporcionado no es válida."}, "core/lib/navigation-error.js | warningXhtml": {"message": "El tipo de MIME de la página es XHTML: Lighthouse no admite este tipo de documento explícitamente"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON>lujo de usuarios ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Informe de navegación ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Informe de un instante ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Informe de tiempo ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Todos los informes"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorías"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accesibilidad"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Prácticas recomendadas"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Rendimiento"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Aplicación web progresiva"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Ordenador"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Interpretar los informes de flujo de Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Interpretar flujos"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Usa los informes de navegación para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Usa los informes de un instante para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Usa los informes de tiempo para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obtener una puntuación del rendimiento de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Medir métricas de rendimiento de carga de la página, como el renderizado del mayor elemento con contenido y el Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Evaluar las funciones de una aplicación web progresiva."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Detectar problemas de accesibilidad en aplicaciones de página única o formularios complejos."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Evaluar las prácticas recomendadas para los menús y los elementos de interfaz de usuario ocultos tras las interacciones."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Medir los cambios de diseño y el tiempo de ejecución de JavaScript en una serie de interacciones."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descubrir oportunidades de rendimiento para mejorar la experiencia en páginas de larga duración y en aplicaciones de página única."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Mayor <PERSON>o"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} auditoría informativa}other{{numInformative} auditorías informativas}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Móvil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Carga de la página"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Los informes de navegación analizan la carga de una sola página, exactamente igual que los informes originales de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Informe de navegación"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} informe de navegación}other{{numNavigation} informes de navegación}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} auditoría aceptable}other{{numPassableAudits} auditorías aceptables}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} auditoría superada}other{{numPassed} auditorías superadas}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Ni buena ni mala"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Mala"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Buena"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Guardar"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Captura del estado de la página"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Los informes de un instante analizan la página en un estado concreto, normalmente tras las interacciones de los usuarios."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Informe de un instante"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} informe de un instante}other{{numSnapshot} informes de un instante}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Resumen"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interacciones del usuario"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Los informes de tiempo analizan un periodo de tiempo arbitrario, que normalmente contiene interacciones de usuario."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Informe de tiempo"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} informe de tiempo}other{{numTimespan} informes de tiempo}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Informe de flujo de usuarios de Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "En el caso del contenido animado, usa [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) para reducir el uso de la CPU mientras el contenido no aparece en pantalla."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Considera mostrar todos los componentes [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) en formatos WebP y especificar una alternativa adecuada para otros navegadores. [Más información](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Comprueba que estés usando [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) para cargar imágenes en diferido. [Más información](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Utiliza herramientas como [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) para [renderizar diseños AMP en el servidor](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulta la [documentación de AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) para asegurarte de que todos los estilos sean compatibles."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "El componente [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) admite el atributo [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) para especificar las imágenes que se deben usar según el tamaño de la pantalla. [Más información](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Si se están renderizando listas muy largas, puedes usar el desplazamiento virtual con el Component Dev Kit (CDK). [Más información](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "[Divide el código de ruta](https://web.dev/route-level-code-splitting-in-angular/) para reducir el tamaño de los paquetes de JavaScript. También puedes almacenar elementos en caché previamente con el [service worker de Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Si utilizas Angular CLI, asegúrate de que las compilaciones se generen en el modo de producción. [Más información](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Si utilizas Angular CLI, incluye mapas de origen en tu compilación de producción para inspeccionar tus paquetes. [Más información](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Precarga rutas para acelerar la navegación. [Más información](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Puedes usar la utilidad `BreakpointObserver` del Component Dev Kit (CDK) para gestionar los puntos de interrupción de imágenes. [Más información](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Considera subir tu GIF a un servicio que permita insertarlo como un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Especifica `@font-display` al definir fuentes personalizadas en el tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Considera configurar [los formatos de imagen WebP con un estilo para convertir imágenes](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) en tu sitio."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instala [un módulo de Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) que pueda cargar imágenes en diferido. Estos módulos proporcionan la capacidad de posponer las imágenes que no aparecen en pantalla para mejorar el rendimiento."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Considera utilizar un módulo para insertar CSS y JavaScript crítico, o cargar recursos de forma asíncrona mediante JavaScript, como el módulo [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Ten en cuenta que las optimizaciones que ofrece este módulo pueden hacer que tu sitio no funcione correctamente, por lo que es probable que tengas que hacer cambios en el código."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Los temas, los módulos y las especificaciones del servidor afectan al tiempo de respuesta. Considera buscar un tema más optimizado, seleccionar un módulo de optimización pertinente o actualizar tu servidor. Tus servidores de alojamiento deben utilizar almacenamiento en caché de opcode PHP y almacenamiento en caché de memoria para reducir los tiempos de consulta a la base de datos como Redis o Memcached, así como una lógica de aplicaciones optimizada para preparar más rápido las páginas."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Considera utilizar [Estilos de imagen adaptable](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) para reducir el tamaño de las imágenes que se cargan en tu página. Si utilizas Vistas para mostrar varios elementos de contenido en una página, plantéate implementar la paginación para limitar el número de elementos de contenido que se muestran en una página determinada."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Comprueba que has habilitado \"Agregar archivos de CSS\" en la página Administración > Configuración > Desarrollo. También puedes configurar opciones más avanzadas de agregación mediante [módulos adicionales](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) que pueden concatenar, minificar y comprimir los estilos de CSS para acelerar tu sitio web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Comprueba que has habilitado \"Agregar archivos de JavaScript\" en la página Administración > Configuración > Desarrollo. También puedes configurar opciones más avanzadas de agregación mediante [módulos adicionales](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) que pueden concatenar, minificar y comprimir los recursos de JavaScript para acelerar tu sitio web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Considera eliminar las reglas de CSS no utilizadas y adjunta únicamente las bibliotecas de Drupal necesarias para la página o el componente relevantes de una página. Consulta el [enlace a la documentación de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para obtener información más detallada. Para identificar las bibliotecas adjuntas que añaden archivos CSS no pertinentes, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o módulo responsable en la URL de la hoja de estilos cuando la agregación de CSS está inhabilitada en tu sitio web de Drupal. Presta atención a los temas o módulos con varias hojas de estilo en la lista y con muchos elementos en rojo en la cobertura de código. Un tema o módulo solo debería poner en cola una hoja de estilos si esta se usa en la página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Considera eliminar los recursos de JavaScript no utilizados y adjunta únicamente las bibliotecas de Drupal necesarias para la página o el componente correspondientes de una página. Consulta el [enlace a la documentación de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para obtener información más detallada. Para identificar las bibliotecas adjuntas que añaden archivos de JavaScript no pertinentes, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o módulo responsable en la URL de la secuencia de comandos cuando la agregación de JavaScript está inhabilitada en tu sitio web de Drupal. Presta atención a los temas o módulos con varias secuencias de comandos en la lista y con muchos elementos en rojo en la cobertura de código. Un tema o módulo solo debería poner en cola una secuencia de comandos si esta se usa en la página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Configura la \"Antigüedad máxima de la caché del navegador y el proxy\" en la página Administración > Configuración > Desarrollo. Más información sobre [la caché de Drupal y optimizar para el rendimiento](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Puedes utilizar [un módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) que optimice y reduzca automáticamente el tamaño de las imágenes subidas en el sitio web sin perder calidad. Comprueba también que estás utilizando los [estilos de imágenes adaptables](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) proporcionados por Drupal (disponibles en Drupal 8 y versiones posteriores)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "<PERSON><PERSON><PERSON> añadir sugerencias de recursos Preconnect o dns-prefetch instalando y configurando [un módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) que proporcione facilidades de sugerencias de recursos para el user-agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Comprueba que estás utilizando los [estilos de imágenes adaptables](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) proporcionados por Drupal (disponibles en Drupal 8 y versiones posteriores). Utiliza los estilos de imágenes adaptables al renderizar campos de imagen en modos de vista, vistas o imágenes subidas a través del editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Optimize Fonts` para utilizar automáticamente la función CSS `font-display` y asegurarte de que los usuarios puedan ver el texto mientras se cargan las fuentes web."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Next-Gen Formats` para convertir imágenes al formato WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Lazy Load Images` para posponer la carga de imágenes que no aparecen en pantalla hasta que sean necesarias."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Critical CSS` y `Script Delay` para posponer los elementos de JavaScript y CSS que no sean esenciales."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Usa [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) para almacenar tu contenido en la caché de nuestra red internacional y reducir el tiempo que tarda en cargarse el primer byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Minify CSS` para minificar tus elementos CSS automáticamente y reducir el tamaño de la carga útil en la red."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Minify Javascript` para minificar el JS automáticamente y reducir los tamaños de cargas útiles en la red."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Remove Unused CSS` para que te ayude con este problema. Identificará las clases de CSS que se estén usando en ese momento en cada página de tu sitio y quitará todas las demás para mantener un tamaño de archivo reducido."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Efficient Static Cache Policy` para establecer los valores recomendados en el encabezado del almacenamiento en caché de los recursos estáticos."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Next-Gen Formats` para convertir imágenes al formato WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Pre-Connect Origins` para añadir automáticamente sugerencias de recursos `preconnect` y establecer conexiones previas con orígenes importantes de terceros."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Preload Fonts` y `Preload Background Images` para añadir enlaces de `preload` y dar prioridad a los recursos que se solicitan más tarde al cargar la página."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Usa [Ezoic Leap](https://pubdash.ezoic.com/speed) y habilita `Resize Images` para cambiar el tamaño de las imágenes de forma que tengan un tamaño adecuado para cada dispositivo, haciendo que se reduzca el tamaño de carga útil de la red."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Considera subir tu GIF a un servicio que permita insertarlo como un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Puedes utilizar un [complemento](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) o servicio que convierta automáticamente las imágenes que subas en los formatos óptimos."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instala un [complemento de carga en diferido de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) con la capacidad de posponer imágenes que no aparecen en pantalla, o cambia a una plantilla que incluya esa función. A partir de Joomla 4.0, todas las imágenes nuevas obtendrán el atributo `loading` [automáticamente](https://github.com/joomla/joomla-cms/pull/30748) del núcleo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Existen varios complementos de Joomla que pueden ayudarte a [insertar recursos fundamentales](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) o a [posponer recursos menos importantes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Ten en cuenta que las optimizaciones que ofrecen estos complementos pueden romper funciones de tus plantillas o complementos, por lo que asegúrate de verificarlas completamente."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Las plantillas, las extensiones y las especificaciones del servidor afectan al tiempo de respuesta. <PERSON>uedes buscar una plantilla más optimizada, seleccionar una extensión de optimización pertinente o actualizar tu servidor."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Puedes mostrar fragmentos en tus categorías de artículos (por ejemplo, mediante un enlace \"leer más\"), reducir la cantidad de artículos que se muestran en cada página, dividir tus entradas más largas en varias páginas o usar un complemento para posponer la carga de los comentarios."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Hay varias [extensiones de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que pueden concatenar, minificar y comprimir tus estilos para acelerar tu sitio web. También hay plantillas que proporcionan esta funcionalidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Hay varias [extensiones de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que pueden concatenar, minificar y comprimir tus secuencias de comandos para acelerar tu sitio web. También hay plantillas que proporcionan esta funcionalidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Puedes reducir o cambiar la cantidad de [extensiones de Joomla](https://extensions.joomla.org/) que cargan archivos CSS sin usar en tu página. Para identificar las extensiones que añaden archivos CSS no pertinentes, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o complemento responsable en la URL de la hoja de estilo. Presta atención a los complementos con varias hojas de estilo en la lista y con muchos elementos en rojo en la cobertura de código. Un complemento solo debería poner en cola una hoja de estilo (si esta se usa en la página)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Considera reducir o cambiar la cantidad de [extensiones de Joomla](https://extensions.joomla.org/) que cargan código de JavaScript sin usar en tu página. Para identificar los complementos que añaden código de JavaScript no pertinente, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar la extensión responsable en la URL de la secuencia de comandos. Presta atención a las extensiones con varias secuencias de comandos en la lista y con muchos elementos en rojo en la cobertura de código. Una extensión solo debería poner en cola una secuencia de comandos si esta se usa en la página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Consulta más información sobre el [almacenamiento en la memoria caché del navegador en Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Considera utilizar un [complemento de optimización de imágenes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que comprima tus imágenes conservando la calidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Considera utilizar un [complemento de imágenes adaptables](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) para utilizar imágenes adaptables en tu contenido."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Puedes habilitar la compresión de texto activando la compresión de página Gzip en Joomla (Sistema > Configuración global > Servidor)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Si no vas a agrupar los elementos de JavaScript, puedes usar [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Inhabilita los [paquetes de JavaScript y la minificación](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrados de Magento y, en su lugar, utiliza [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Especifica `@font-display` al [definir fuentes personalizadas](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "En [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp), puedes buscar extensiones de terceros para utilizar formatos de imagen más recientes."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> cambiar tus plantillas de producto y de catálogo para usar la función [carga en diferido](https://web.dev/native-lazy-loading) de la plataforma web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Usa la [integración Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Habilita la opción Minificar archivos CSS en los ajustes para desarrolladores de tu tienda. [Más información](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON>a [Terser](https://www.npmjs.com/package/terser) para minificar todos los recursos de JavaScript procedentes de implementaciones de contenido estático e inhabilitar la función de minificación integrada."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Permite inhabilitar los [paquetes de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrados de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "En [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image), puedes encontrar diferentes extensiones de terceros para mejorar imágenes."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "<PERSON><PERSON><PERSON> a<PERSON> sugerencias de recursos Preconnect o dns-prefetch [modificando el diseño de un tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Es posible añadir etiquetas `<link rel=preload>` [cambiando el diseño de un tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Usa el componente `next/image` en lugar de `<img>` para optimizar el formato de la imagen automáticamente. [Más información](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Usa el componente `next/image` en lugar de `<img>` para cargar imágenes en diferido automáticamente. [Más información](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Usa el componente `next/image` y asigna el valor true a \"priority\" para precargar imágenes LCP. [Más información](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Usa el componente `next/script` para posponer la carga de secuencias de comandos de terceros que no sean esenciales. [Más información](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Usa el componente `next/image` para asegurarte de que las imágenes siempre tengan el tamaño adecuado. [Más información](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Considera usar `PurgeCSS` en la configuración de `Next.js` para quitar las reglas que no se usen de las hojas de estilo. [Más información](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Usa `Webpack Bundle Analyzer` para detectar código de JavaScript sin usar. [Más información](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Considera usar `Next.js Analytics` para medir el rendimiento real de tu aplicación. [Más información](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configura el almacenamiento en caché de recursos que no se pueden modificar y páginas `Server-side Rendered` (SSR). [Más información](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Usa el componente `next/image` en lugar de `<img>` para ajustar la calidad de imagen. [Más información](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Usa el componente `next/image` para establecer `sizes` de manera adecuada. [Más información](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Habilita la compresión en tu servidor Next.js. [Más información](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Usa el componente `nuxt/image` y establece `format=\"webp\"`. [Más información](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Usa el componente `nuxt/image` y establece `loading=\"lazy\"` para las imágenes que no aparecen en pantalla. [Más información](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Usa el componente `nuxt/image` y asigna el valor `preload` a la imagen de LCP. [Más información](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Usa el componente `nuxt/image` y asigna valores específicos de `width` y `height`. [Más información](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Usa el componente `nuxt/image` y asigna el valor adecuado a `quality`. [Más información](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Usa el componente `nuxt/image` y asigna el valor adecuado a `sizes`. [Más información](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Sustituye los GIF animados por vídeos](https://web.dev/replace-gifs-with-videos/) para cargar las páginas web más rápidamente. También puedes usar formatos de archivo modernos como [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) o [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) para mejorar la eficiencia de compresión más de un 30 % en comparación con VP9, el códec de vídeo de última generación."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Te recomendamos que utilices un [complemento](https://octobercms.com/plugins?search=image) o un servicio que convierta automáticamente a los formatos óptimos las imágenes que subas. Las [imágenes WebP sin pérdida](https://developers.google.com/speed/webp) tienen un tamaño un 26 % inferior en comparación con el formato PNG y entre un 25 % y un 34 % inferior en comparación con las imágenes JPEG con el índice de calidad SSIM equivalente. [AVIF](https://jakearchibald.com/2020/avif-has-landed/) es otro formato de imagen de nueva generación que deberías tener en cuenta."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Te recomendamos que instales un [complemento de carga en diferido de imágenes](https://octobercms.com/plugins?search=lazy) con la capacidad de posponer imágenes que no aparecen en pantalla, o que cambies a un tema que incluya esa función. También puedes usar el [complemento AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Hay muchos complementos que ayudan a [insertar recursos fundamentales](https://octobercms.com/plugins?search=css). Estos complementos pueden hacer que otros complementos no funcionen correctamente, por lo que deberías probarlos minuciosamente."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Los temas, los complementos y las especificaciones del servidor afectan al tiempo de respuesta. Te recomendamos que busques un tema más optimizado, que selecciones un complemento de optimización pertinente o que actualices el servidor. El CMS October también permite a los desarrolladores usar [`Queues`](https://octobercms.com/docs/services/queues) para posponer el procesamiento de una tarea que consuma mucho tiempo, como enviar un correo. Esto acelera considerablemente las solicitudes web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Puedes mostrar fragmentos en tus listas de publicaciones (por ejemplo, mediante un botón `show more`), reducir la cantidad de publicaciones que se muestran en cada página web, dividir las publicaciones largas en varias páginas web o usar un complemento para cargar los comentarios en diferido."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Hay muchos [complementos](https://octobercms.com/plugins?search=css) que pueden acelerar un sitio web mediante la concatenación, la minificación y la compresión de estilos. Usar un proceso de compilación para realizar la minificación de forma anticipada puede acelerar el desarrollo."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Hay muchos [complementos](https://octobercms.com/plugins?search=javascript) que pueden acelerar un sitio web mediante la concatenación, la minificación y la compresión de secuencias de comandos. Usar un proceso de compilación para realizar la minificación de forma anticipada puede acelerar el desarrollo."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Puedes comprobar los [complementos](https://octobercms.com/plugins) que cargan archivos CSS sin usar en el sitio web. Para identificar los complementos que añaden archivos CSS innecesarios, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en las herramientas para desarrolladores de Chrome. Identifica el tema o el complemento responsable en la URL de la hoja de estilo. Busca complementos con varias hojas de estilo y muchos elementos en rojo en la cobertura de código. Un complemento solo debería añadir una hoja de estilo si esta se usa en la página web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Te recomendamos que revises los [complementos](https://octobercms.com/plugins?search=javascript) que cargan código de JavaScript sin usar en la página web. Para identificar los complementos que añaden código de JavaScript innecesario, ejecuta la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en las herramientas para desarrolladores de Chrome. Identifica el tema o el complemento responsable en la URL de la secuencia de comandos. Busca complementos con varias secuencias de comandos y muchos elementos en rojo en la cobertura de código. Un complemento solo debería añadir una secuencia de comandos si se usa en la página web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Consulta más información sobre cómo [evitar solicitudes de red innecesarias con la caché de HTTP](https://web.dev/http-cache/#caching-checklist). Hay muchos [complementos](https://octobercms.com/plugins?search=Caching) que se pueden usar para acelerar el almacenamiento en caché."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Te recomendamos que utilices un [complemento de optimización de imágenes](https://octobercms.com/plugins?search=image) que comprima imágenes conservando la calidad."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Sube imágenes directamente al administrador de medios para asegurarte de que los tamaños de imagen necesarios estén disponibles. Te recomendamos que uses el [filtro de modificación de tamaño](https://octobercms.com/docs/markup/filter-resize) o un [complemento de modificación de tamaño de imágenes](https://octobercms.com/plugins?search=image) para asegurarte de que se utilicen los tamaños de imagen óptimos."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Habilita la compresión de texto en la configuración del servidor web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Puedes usar una biblioteca \"windowing\", como `react-window`, para reducir el número de nodos de DOM creados si vas a renderizar muchos elementos repetidos en una página. [Más información](https://web.dev/virtualize-long-lists-react-window/) También puedes reducir las renderizaciones repetidas e innecesarias usando [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) o [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo), y [evitar los efectos](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) solo hasta que ciertas dependencias hayan cambiado (si estás usando el hook `Effect` para mejorar el rendimiento del tiempo de ejecución)."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Si usas React Router, minimiza el uso del componente `<Redirect>` en el caso de las [navegaciones de ruta](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Si estás renderizando componentes React en el servidor, puedes usar `renderToPipeableStream()` o `renderToStaticNodeStream()` para permitir al cliente recibir e hidratar distintas partes de las etiquetas en vez de todas a la vez. [Más información](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Si tu sistema de compilación minifica los archivos CSS automáticamente, comprueba que estés implementando la compilación de producción de tu aplicación. Puedes comprobarlo con la extensión de las herramientas de desarrollo de React. [Más información](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Si tu sistema de compilación minifica los archivos JS automáticamente, comprueba que estés implementando la compilación de producción de tu aplicación. Puedes comprobarlo con la extensión de las herramientas de desarrollo de React. [Más información](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Si no vas a renderizar en el servidor, [divide tus paquetes de JavaScript](https://web.dev/code-splitting-suspense/) con `React.lazy()`. También puedes dividir el código con una biblioteca de terceros, como [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Usa DevTools Profiler de React, que utiliza la API del profiler, para medir el rendimiento de tus componentes al renderizar. [Más información](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Considera subir tu GIF a un servicio que permita insertarlo como un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Puedes usar el complemento [Performance Lab](https://wordpress.org/plugins/performance-lab/) para convertir automáticamente las imágenes JPEG que subas a WebP siempre que sea posible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instala un [complemento de carga en diferido de WordPress](https://wordpress.org/plugins/search/lazy+load/) con la capacidad de posponer imágenes que no aparecen en la pantalla, o cambia a un tema con esa función. También puedes usar el [complemento de AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Existen varios complementos de WordPress que pueden ayudarte a [insertar recursos fundamentales](https://wordpress.org/plugins/search/critical+css/) o [posponer recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). Ten en cuenta que las optimizaciones que ofrecen estos complementos pueden bloquear funciones de tu tema o tus complementos, así que seguramente tengas que hacer cambios en el código."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Los temas, los complementos y las especificaciones del servidor afectan al tiempo de respuesta. Puedes buscar un tema más optimizado, seleccionar un complemento de optimización o actualizar tu servidor."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Puedes mostrar fragmentos en tus listas de entradas (por ejemplo, mediante la etiqueta \"more\"), reducir la cantidad de entradas que se muestran en cada página, dividir tus entradas más largas en múltiples páginas o usar un complemento para posponer la carga de los comentarios."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+css/) que pueden concatenar, minificar y comprimir tus estilos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de creación para realizar la minificación de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+javascript/) que pueden concatenar, minificar y comprimir tus secuencias de comandos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de creación para realizar la minificación de forma anticipada."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan archivos CSS sin usar en tu página. Para identificar los complementos que añaden archivos CSS externos, ejecuta la [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la hoja de estilo. Presta atención a los complementos con varias hojas de estilo en la lista y con muchos elementos en rojo en la cobertura de código. Un complemento solo debería poner en cola una hoja de estilo (si esta se usa en la página)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan código de JavaScript sin usar en tu página. Para identificar los complementos que añaden código de JavaScript externo, ejecuta la [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la secuencia de comandos. Presta atención a los complementos con varias secuencias de comandos en la lista y con muchos elementos en rojo en la cobertura de código. Un complemento solo debería poner en cola una secuencia de comandos (si esta se usa en la página)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Consulta más información sobre el [almacenamiento en la memoria caché del navegador en WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Puedes utilizar un [complemento de optimización de imágenes de WordPress](https://wordpress.org/plugins/search/optimize+images/) que comprima tus imágenes conservando la calidad."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Sube imágenes directamente a la [biblioteca multimedia](https://wordpress.org/support/article/media-library-screen/) para asegurarte de que estén disponibles los tamaños de imagen necesarios y, después, insértalas desde esa biblioteca multimedia o utiliza el widget de imagen para usar los tamaños de imagen óptimos (incluidos los tamaños de los puntos de interrupción adaptables). Evita usar imágenes `Full Size`, a no ser que las dimensiones sean las adecuadas para su uso. [Más información](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Puedes habilitar la compresión de texto en la configuración de tu servidor web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Habilita Imagify en la pestaña de optimización de imágenes de WP Rocket para convertir tus imágenes al formato WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Habilita la función de [carga diferida](https://docs.wp-rocket.me/article/1141-lazyload-for-images) en WP Rocket para seguir esta recomendación. Esta función retrasa la carga de las imágenes hasta que el visitante se desplaza hacia abajo y necesita verlas."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Habilita la función para [quitar CSS no utilizado](https://docs.wp-rocket.me/article/1529-remove-unused-css) y [cargar JavaScript con retraso](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) en WP Rocket para seguir esta recomendación. Estas funciones optimizan, respectivamente, los archivos CSS y JavaScript para que no bloqueen el renderizado de tu página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Habilita la función para [minificar archivos CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) en WP Rocket para solucionar este problema. Se eliminarán todos los espacios y comentarios de los archivos CSS de tu sitio para reducir el tamaño de los archivos y aumentar la velocidad a la que se descargan."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Habilita la función para [minificar archivos JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) en WP Rocket para solucionar este problema. Se eliminarán los espacios y comentarios de los archivos JavaScript para reducir su tamaño y aumentar la velocidad a la que se descargan."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Habilita la función para [quitar CSS no utilizado](https://docs.wp-rocket.me/article/1529-remove-unused-css) en WP Rocket para solucionar este problema. Esto reduce el tamaño de la página quitando todos los archivos CSS y hojas de estilo que no se utilizan y conservando solo el CSS usado para cada página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Habilita la función para [retrasar la ejecución de JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) en WP Rocket para solucionar este problema. Esto mejorará la carga de tu página retrasando la ejecución de las secuencias de comandos hasta que el usuario interactúe con la página. Si tu sitio tiene iframes, puedes usar la función de [carga diferida para iframes y vídeos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) de WP Rocket, así como la función para [reemplazar el iframe de YouTube por la imagen de vista previa](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Habilita Imagify en la pestaña de optimización de imágenes de WP Rocket y ejecuta Bulk Optimization para comprimir tus imágenes."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Usa la función para [precargar solicitudes DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) en WP Rocket para añadir \"dns-prefetch\" y acelerar la conexión con dominios externos. Además, WP Rocket añade automáticamente \"preconnect\" al [dominio de Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) y a todos los CNAMEs añadidos mediante la función para [habilitar CDNs](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Para solucionar este problema en las fuentes, habilita la opción para [quitar CSS no utilizado](https://docs.wp-rocket.me/article/1529-remove-unused-css) en WP Rocket. Las fuentes esenciales de tu sitio se precargarán con prioridad."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Ver calculadora."}, "report/renderer/report-utils.js | collapseView": {"message": "Contraer vista"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navegación inicial"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latencia de ruta crítica máxima:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copiar JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Activar o desactivar el tema oscuro"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Imprimir versión ampliada"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Imprimir resumen"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Guardar como gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Guardar como HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Guardar como JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Abrir en el visor"}, "report/renderer/report-utils.js | errorLabel": {"message": "Error"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Error del informe: no hay información de la auditoría"}, "report/renderer/report-utils.js | expandView": {"message": "Ampliar vista"}, "report/renderer/report-utils.js | footerIssue": {"message": "Notificar un problema"}, "report/renderer/report-utils.js | hide": {"message": "Ocultar"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Datos de experimentos"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) ha analizado la página actual en una red móvil emulada. Los valores son estimaciones y pueden variar."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Elementos adicionales que se deben comprobar manualmente"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "No aplicable"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunidad"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON> estimado"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Auditorías aprobadas"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Carga inicial de la página"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitación personalizada"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Escritorio emulado"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Sin emulación"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versión de Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potencia de la CPU o de la memoria no limitada"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitación de CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositivo"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitación de red"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulación de pantalla"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User-agent (red)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Carga de una única página"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Estos datos se obtienen a partir de la carga de una única página, mientras que los datos de campo ofrecen un resumen de muchas sesiones."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitación de 4G lenta"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Desconocido"}, "report/renderer/report-utils.js | show": {"message": "Mostrar"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostrar auditorías relacionadas con:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Ocultar fragmento"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Mostrar fragmento"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos externos"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Proporcionado por el entorno"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Algunos problemas han afectado a la ejecución de Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Los valores son estimaciones y pueden variar. La [puntuación del rendimiento se calcula](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directamente a partir de estas métricas."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Ver rastro original"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON>er rastro"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Ver gráfico de rectángulos"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Auditorías aprobadas con advertencias"}, "report/renderer/report-utils.js | warningHeader": {"message": "Advertencias: "}, "treemap/app/src/util.js | allLabel": {"message": "Todo"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Todas las secuencias de comandos"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Cobertura"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes del recurso"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nombre"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Mostrar/Ocultar tabla"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Bytes sin usar"}}