/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/error/ErrorBoundary.tsx */ \"(app-pages-browser)/./components/error/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/accessibility.tsx */ \"(app-pages-browser)/./lib/accessibility.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/mobile.tsx */ \"(app-pages-browser)/./lib/mobile.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true,\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cerror%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Caccessibility.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Clib%5C%5Cmobile.tsx%22%2C%22ids%22%3A%5B%22MobileOptimized%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mobile.tsx":
/*!************************!*\
  !*** ./lib/mobile.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAKPOINTS: function() { return /* binding */ BREAKPOINTS; },\n/* harmony export */   MobileOptimized: function() { return /* binding */ MobileOptimized; },\n/* harmony export */   getMobileImageSizes: function() { return /* binding */ getMobileImageSizes; },\n/* harmony export */   getResponsiveGridCols: function() { return /* binding */ getResponsiveGridCols; },\n/* harmony export */   useDeviceDetection: function() { return /* binding */ useDeviceDetection; },\n/* harmony export */   useMobilePerformance: function() { return /* binding */ useMobilePerformance; },\n/* harmony export */   useMobileScroll: function() { return /* binding */ useMobileScroll; },\n/* harmony export */   useResponsiveValue: function() { return /* binding */ useResponsiveValue; },\n/* harmony export */   useSafeAreaInsets: function() { return /* binding */ useSafeAreaInsets; },\n/* harmony export */   useTouchGestures: function() { return /* binding */ useTouchGestures; },\n/* harmony export */   useViewportHeight: function() { return /* binding */ useViewportHeight; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BREAKPOINTS,useDeviceDetection,useResponsiveValue,useTouchGestures,useViewportHeight,useSafeAreaInsets,useMobileScroll,useMobilePerformance,MobileOptimized,getResponsiveGridCols,getMobileImageSizes auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$();\n/**\n * Mobile optimization utilities\n * Includes responsive design helpers, touch interactions, and mobile-specific optimizations\n */ \n// Breakpoint definitions\nconst BREAKPOINTS = {\n    xs: 0,\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    \"2xl\": 1536\n};\n// Device detection\nfunction useDeviceDetection() {\n    _s();\n    const [deviceInfo, setDeviceInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isMobile: false,\n        isTablet: false,\n        isDesktop: false,\n        isTouchDevice: false,\n        orientation: \"portrait\",\n        screenSize: \"md\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDeviceInfo = ()=>{\n            const width = window.innerWidth;\n            const height = window.innerHeight;\n            const isTouchDevice = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n            // Determine screen size\n            let screenSize = \"xs\";\n            for (const [breakpoint, minWidth] of Object.entries(BREAKPOINTS).reverse()){\n                if (width >= minWidth) {\n                    screenSize = breakpoint;\n                    break;\n                }\n            }\n            // Device type detection\n            const isMobile = width < BREAKPOINTS.md;\n            const isTablet = width >= BREAKPOINTS.md && width < BREAKPOINTS.lg && isTouchDevice;\n            const isDesktop = width >= BREAKPOINTS.lg;\n            // Orientation\n            const orientation = width > height ? \"landscape\" : \"portrait\";\n            setDeviceInfo({\n                isMobile,\n                isTablet,\n                isDesktop,\n                isTouchDevice,\n                orientation,\n                screenSize\n            });\n        };\n        updateDeviceInfo();\n        window.addEventListener(\"resize\", updateDeviceInfo);\n        window.addEventListener(\"orientationchange\", updateDeviceInfo);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateDeviceInfo);\n            window.removeEventListener(\"orientationchange\", updateDeviceInfo);\n        };\n    }, []);\n    return deviceInfo;\n}\n_s(useDeviceDetection, \"X8vltGlqFLsGZXF+xESHDqeSLqM=\");\n// Responsive value hook\nfunction useResponsiveValue(values, defaultValue) {\n    _s1();\n    const { screenSize } = useDeviceDetection();\n    // Find the appropriate value for current screen size\n    const breakpointOrder = [\n        \"2xl\",\n        \"xl\",\n        \"lg\",\n        \"md\",\n        \"sm\",\n        \"xs\"\n    ];\n    const currentIndex = breakpointOrder.indexOf(screenSize);\n    for(let i = currentIndex; i < breakpointOrder.length; i++){\n        const breakpoint = breakpointOrder[i];\n        if (values[breakpoint] !== undefined) {\n            return values[breakpoint];\n        }\n    }\n    return defaultValue;\n}\n_s1(useResponsiveValue, \"+5xsDCCTTPJ3rFPLvds5dxH/Llo=\", false, function() {\n    return [\n        useDeviceDetection\n    ];\n});\n// Touch gesture detection\nfunction useTouchGestures(elementRef) {\n    _s2();\n    const [gestures, setGestures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isSwipeLeft: false,\n        isSwipeRight: false,\n        isSwipeUp: false,\n        isSwipeDown: false,\n        isPinching: false,\n        scale: 1\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        let startX = 0;\n        let startY = 0;\n        let startDistance = 0;\n        const handleTouchStart = (e)=>{\n            if (e.touches.length === 1) {\n                startX = e.touches[0].clientX;\n                startY = e.touches[0].clientY;\n            } else if (e.touches.length === 2) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                startDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n            }\n        };\n        const handleTouchMove = (e)=>{\n            if (e.touches.length === 2) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                const currentDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                const scale = currentDistance / startDistance;\n                setGestures((prev)=>({\n                        ...prev,\n                        isPinching: true,\n                        scale\n                    }));\n            }\n        };\n        const handleTouchEnd = (e)=>{\n            if (e.changedTouches.length === 1) {\n                const endX = e.changedTouches[0].clientX;\n                const endY = e.changedTouches[0].clientY;\n                const deltaX = endX - startX;\n                const deltaY = endY - startY;\n                const minSwipeDistance = 50;\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {\n                    if (deltaX > 0) {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeRight: true\n                            }));\n                    } else {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeLeft: true\n                            }));\n                    }\n                } else if (Math.abs(deltaY) > minSwipeDistance) {\n                    if (deltaY > 0) {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeDown: true\n                            }));\n                    } else {\n                        setGestures((prev)=>({\n                                ...prev,\n                                isSwipeUp: true\n                            }));\n                    }\n                }\n                // Reset gestures after a short delay\n                setTimeout(()=>{\n                    setGestures({\n                        isSwipeLeft: false,\n                        isSwipeRight: false,\n                        isSwipeUp: false,\n                        isSwipeDown: false,\n                        isPinching: false,\n                        scale: 1\n                    });\n                }, 100);\n            }\n        };\n        element.addEventListener(\"touchstart\", handleTouchStart, {\n            passive: true\n        });\n        element.addEventListener(\"touchmove\", handleTouchMove, {\n            passive: true\n        });\n        element.addEventListener(\"touchend\", handleTouchEnd, {\n            passive: true\n        });\n        return ()=>{\n            element.removeEventListener(\"touchstart\", handleTouchStart);\n            element.removeEventListener(\"touchmove\", handleTouchMove);\n            element.removeEventListener(\"touchend\", handleTouchEnd);\n        };\n    }, [\n        elementRef\n    ]);\n    return gestures;\n}\n_s2(useTouchGestures, \"RgPtzo2iQJ9XkLZri0pS/DXYKM8=\");\n// Viewport height fix for mobile browsers\nfunction useViewportHeight() {\n    _s3();\n    const [vh, setVh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateVh = ()=>{\n            const vh = window.innerHeight * 0.01;\n            setVh(vh);\n            document.documentElement.style.setProperty(\"--vh\", \"\".concat(vh, \"px\"));\n        };\n        updateVh();\n        window.addEventListener(\"resize\", updateVh);\n        window.addEventListener(\"orientationchange\", updateVh);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateVh);\n            window.removeEventListener(\"orientationchange\", updateVh);\n        };\n    }, []);\n    return vh;\n}\n_s3(useViewportHeight, \"rrC13ZmKsm31sjhcrZWSfmpc/0A=\");\n// Safe area insets for devices with notches\nfunction useSafeAreaInsets() {\n    _s4();\n    const [insets, setInsets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateInsets = ()=>{\n            const computedStyle = getComputedStyle(document.documentElement);\n            setInsets({\n                top: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-top)\") || \"0\"),\n                right: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-right)\") || \"0\"),\n                bottom: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-bottom)\") || \"0\"),\n                left: parseInt(computedStyle.getPropertyValue(\"env(safe-area-inset-left)\") || \"0\")\n            });\n        };\n        updateInsets();\n        window.addEventListener(\"orientationchange\", updateInsets);\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", updateInsets);\n        };\n    }, []);\n    return insets;\n}\n_s4(useSafeAreaInsets, \"gE3oLOyCaiUJXP8AYr5WTUr1Tto=\");\n// Mobile-optimized scroll behavior\nfunction useMobileScroll() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s5();\n    const { threshold = 10, onScrollUp, onScrollDown } = options;\n    const [scrollDirection, setScrollDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let lastScrollY = window.scrollY;\n        let ticking = false;\n        const updateScrollDirection = ()=>{\n            const currentScrollY = window.scrollY;\n            const difference = Math.abs(currentScrollY - lastScrollY);\n            if (difference > threshold) {\n                const direction = currentScrollY > lastScrollY ? \"down\" : \"up\";\n                setScrollDirection(direction);\n                setScrollY(currentScrollY);\n                if (direction === \"up\" && onScrollUp) {\n                    onScrollUp();\n                } else if (direction === \"down\" && onScrollDown) {\n                    onScrollDown();\n                }\n                lastScrollY = currentScrollY;\n            }\n            ticking = false;\n        };\n        const onScroll = ()=>{\n            if (!ticking) {\n                requestAnimationFrame(updateScrollDirection);\n                ticking = true;\n            }\n        };\n        window.addEventListener(\"scroll\", onScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", onScroll);\n    }, [\n        threshold,\n        onScrollUp,\n        onScrollDown\n    ]);\n    return {\n        scrollDirection,\n        scrollY\n    };\n}\n_s5(useMobileScroll, \"Oo2T7UHdQ0SxXDNpFliM0wcMfaw=\");\n// Performance optimization for mobile\nfunction useMobilePerformance() {\n    _s6();\n    const [isLowEndDevice, setIsLowEndDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Detect low-end devices\n        const checkDeviceCapabilities = ()=>{\n            const memory = navigator.deviceMemory;\n            const cores = navigator.hardwareConcurrency;\n            const connection = navigator.connection;\n            let isLowEnd = false;\n            // Memory check (less than 4GB)\n            if (memory && memory < 4) {\n                isLowEnd = true;\n            }\n            // CPU cores check (less than 4 cores)\n            if (cores && cores < 4) {\n                isLowEnd = true;\n            }\n            // Network check (slow connection)\n            if (connection && (connection.effectiveType === \"slow-2g\" || connection.effectiveType === \"2g\")) {\n                isLowEnd = true;\n            }\n            setIsLowEndDevice(isLowEnd);\n        };\n        checkDeviceCapabilities();\n    }, []);\n    return {\n        isLowEndDevice\n    };\n}\n_s6(useMobilePerformance, \"sTipxbcXS5iHF7d22xpPF3C2FHU=\");\n// Mobile-friendly component wrapper\nfunction MobileOptimized(param) {\n    let { children, fallback, enableTouchOptimizations = true } = param;\n    _s7();\n    const { isMobile, isTouchDevice } = useDeviceDetection();\n    const { isLowEndDevice } = useMobilePerformance();\n    useViewportHeight();\n    // Apply mobile-specific optimizations\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && enableTouchOptimizations) {\n            // Disable hover effects on touch devices\n            document.body.classList.add(\"touch-device\");\n            // Optimize scrolling\n            document.body.style.webkitOverflowScrolling = \"touch\";\n            // Prevent zoom on input focus\n            const viewport = document.querySelector(\"meta[name=viewport]\");\n            if (viewport) {\n                viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");\n            }\n        }\n        return ()=>{\n            if (isMobile) {\n                document.body.classList.remove(\"touch-device\");\n            }\n        };\n    }, [\n        isMobile,\n        enableTouchOptimizations\n    ]);\n    // Show fallback for low-end devices if provided\n    if (isLowEndDevice && fallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s7(MobileOptimized, \"tb0nskgBUFvNptXAq7XVZcLNzg0=\", false, function() {\n    return [\n        useDeviceDetection,\n        useMobilePerformance,\n        useViewportHeight\n    ];\n});\n_c = MobileOptimized;\n// Responsive grid utility\nfunction getResponsiveGridCols(screenSize) {\n    const gridCols = {\n        xs: 1,\n        sm: 2,\n        md: 2,\n        lg: 3,\n        xl: 4,\n        \"2xl\": 4\n    };\n    return gridCols[screenSize] || 1;\n}\n// Mobile-optimized image loading\nfunction getMobileImageSizes(screenSize) {\n    const sizes = {\n        xs: \"100vw\",\n        sm: \"50vw\",\n        md: \"33vw\",\n        lg: \"25vw\",\n        xl: \"20vw\",\n        \"2xl\": \"16vw\"\n    };\n    return sizes[screenSize] || \"100vw\";\n}\nvar _c;\n$RefreshReg$(_c, \"MobileOptimized\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mobile.tsx\n"));

/***/ })

});