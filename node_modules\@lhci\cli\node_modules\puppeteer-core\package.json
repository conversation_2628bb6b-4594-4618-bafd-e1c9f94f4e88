{"name": "puppeteer-core", "version": "19.11.1", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "keywords": ["puppeteer", "chrome", "headless", "automation"], "type": "commonjs", "main": "./lib/cjs/puppeteer/puppeteer-core.js", "types": "./lib/types.d.ts", "exports": {".": {"types": "./lib/types.d.ts", "import": "./lib/esm/puppeteer/puppeteer-core.js", "require": "./lib/cjs/puppeteer/puppeteer-core.js"}, "./internal/*": {"import": "./lib/esm/puppeteer/*", "require": "./lib/cjs/puppeteer/*"}, "./*": {"import": "./*", "require": "./*"}}, "repository": {"type": "git", "url": "https://github.com/puppeteer/puppeteer/tree/main/packages/puppeteer-core"}, "engines": {"node": ">=14.14.0"}, "scripts": {"build:docs": "wireit", "build:tsc": "wireit", "build:types": "wireit", "build": "wireit", "check": "tsx tools/ensure-correct-devtools-protocol-package", "clean": "tsc -b --clean && rm -rf lib src/generated", "generate:package-json": "wireit", "generate:sources": "wireit", "prepack": "wireit"}, "wireit": {"prepack": {"command": "tsx ../../tools/cp.ts ../../README.md README.md", "files": ["../../README.md"], "output": ["README.md"]}, "build": {"dependencies": ["build:tsc", "build:types"]}, "generate:sources": {"command": "tsx tools/generate_sources.ts", "clean": "if-file-deleted", "files": ["../../versions.js", "src/{injected,templates}/**", "tools/generate_sources.ts"], "output": ["src/generated/*.ts"]}, "generate:package-json": {"command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json", "files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"]}, "build:docs": {"command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "files": ["api-extractor.docs.json", "lib/esm/puppeteer/puppeteer-core.d.ts", "tsconfig.json"], "dependencies": ["build:tsc"]}, "build:tsc": {"command": "tsc -b && rollup --config rollup.third_party.config.mjs", "clean": "if-file-deleted", "dependencies": ["generate:package-json", "generate:sources", "../browsers:build"], "files": ["{compat,src,third_party}/**", "rollup.third_party.config.mjs"], "output": ["lib/{cjs,esm}/**", "!lib/esm/package.json"]}, "build:types": {"command": "api-extractor run --local && eslint --cache-location .eslintcache --cache --ext=ts --no-ignore --no-eslintrc -c=../../.eslintrc.types.cjs --fix lib/types.d.ts", "files": ["../../.eslintrc.types.cjs", "api-extractor.json", "lib/esm/puppeteer/types.d.ts", "tsconfig.json"], "output": ["lib/types.d.ts"], "dependencies": ["build:tsc"]}}, "files": ["lib", "!*.tsbuil<PERSON><PERSON>"], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"chromium-bidi": "0.4.7", "cross-fetch": "3.1.5", "debug": "4.3.4", "devtools-protocol": "0.0.1107588", "extract-zip": "2.0.1", "https-proxy-agent": "5.0.1", "proxy-from-env": "1.1.0", "tar-fs": "2.1.1", "unbzip2-stream": "1.4.3", "ws": "8.13.0", "@puppeteer/browsers": "0.5.0"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "devDependencies": {"mitt": "3.0.0", "parsel-js": "1.1.0"}}