import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { FAQSection } from '@/components/faq/FAQSection'
import faqService from '@/lib/services/faqService'

// Mock del servicio FAQ
jest.mock('@/lib/services/faqService', () => ({
  __esModule: true,
  default: {
    getCategories: jest.fn(),
    getPopularFAQs: jest.fn(),
    getFAQStats: jest.fn(),
    searchFAQs: jest.fn(),
    getFAQsByCategory: jest.fn(),
  }
}))

// Mock de los datos JSON
jest.mock('@/tramites_chia_optimo.json', () => [], { virtual: true })
jest.mock('@/OPA-chia-optimo.json', () => [], { virtual: true })

const mockFaqService = faqService as jest.Mocked<typeof faqService>

// Datos de prueba
const mockCategories = [
  {
    id: 'impuestos',
    name: 'Impuestos y Tributos',
    description: 'Preguntas sobre impuestos municipales',
    icon: 'Receipt',
    color: 'bg-blue-500',
    count: 3
  },
  {
    id: 'licencias',
    name: 'Licencias y Permisos',
    description: 'Construcción y funcionamiento',
    icon: 'FileCheck',
    color: 'bg-green-500',
    count: 2
  }
]

const mockFAQs = [
  {
    id: 'faq-1',
    question: '¿Qué es el impuesto predial?',
    answer: 'El impuesto predial es un tributo municipal que grava la propiedad inmueble.',
    category: 'impuestos',
    tags: ['impuesto', 'predial', 'propiedad'],
    relatedProcedures: ['Impuesto predial unificado'],
    popularity: 95,
    lastUpdated: new Date()
  },
  {
    id: 'faq-2',
    question: '¿Cómo obtengo una licencia de construcción?',
    answer: 'Debe presentar los documentos técnicos ante la Secretaría de Planeación.',
    category: 'licencias',
    tags: ['licencia', 'construcción', 'planeación'],
    relatedProcedures: ['Licencia de construcción'],
    popularity: 90,
    lastUpdated: new Date()
  }
]

const mockStats = {
  totalFAQs: 10,
  totalCategories: 6,
  averagePopularity: 85,
  mostPopularCategory: 'Impuestos y Tributos'
}

describe('FAQSection', () => {
  beforeEach(() => {
    // Reset de todos los mocks
    jest.clearAllMocks()
    
    // Configurar respuestas por defecto
    mockFaqService.getCategories.mockResolvedValue(mockCategories)
    mockFaqService.getPopularFAQs.mockResolvedValue(mockFAQs)
    mockFaqService.getFAQStats.mockResolvedValue(mockStats)
    mockFaqService.searchFAQs.mockResolvedValue([])
    mockFaqService.getFAQsByCategory.mockResolvedValue([])
  })

  describe('Renderizado inicial', () => {
    it('debería renderizar correctamente', async () => {
      render(<FAQSection />)
      
      expect(screen.getByText('Cargando preguntas frecuentes...')).toBeInTheDocument()
      
      await waitFor(() => {
        expect(screen.getByText('Preguntas Frecuentes')).toBeInTheDocument()
      })
    })

    it('debería mostrar título y descripción personalizados', async () => {
      const customTitle = 'FAQ Personalizado'
      const customDescription = 'Descripción personalizada'
      
      render(
        <FAQSection 
          title={customTitle}
          description={customDescription}
        />
      )
      
      await waitFor(() => {
        expect(screen.getByText(customTitle)).toBeInTheDocument()
        expect(screen.getByText(customDescription)).toBeInTheDocument()
      })
    })

    it('debería mostrar estadísticas cuando showStats es true', async () => {
      render(<FAQSection showStats={true} />)
      
      await waitFor(() => {
        expect(screen.getByText('10 preguntas')).toBeInTheDocument()
        expect(screen.getByText('6 categorías')).toBeInTheDocument()
        expect(screen.getByText('Más consultada: Impuestos y Tributos')).toBeInTheDocument()
      })
    })

    it('debería ocultar estadísticas cuando showStats es false', async () => {
      render(<FAQSection showStats={false} />)
      
      await waitFor(() => {
        expect(screen.queryByText('10 preguntas')).not.toBeInTheDocument()
      })
    })
  })

  describe('Búsqueda', () => {
    it('debería mostrar campo de búsqueda cuando showSearch es true', async () => {
      render(<FAQSection showSearch={true} />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })
    })

    it('debería ocultar campo de búsqueda cuando showSearch es false', async () => {
      render(<FAQSection showSearch={false} />)
      
      await waitFor(() => {
        expect(screen.queryByPlaceholderText('Buscar en preguntas frecuentes...')).not.toBeInTheDocument()
      })
    })

    it('debería realizar búsqueda al escribir', async () => {
      const user = userEvent.setup()
      mockFaqService.searchFAQs.mockResolvedValue([mockFAQs[0]])

      await act(async () => {
        render(<FAQSection />)
      })

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')

      await act(async () => {
        await user.type(searchInput, 'impuesto')
      })

      await waitFor(() => {
        expect(mockFaqService.searchFAQs).toHaveBeenCalledWith('impuesto', {
          category: undefined,
          limit: 10
        })
      })
    })

    it('debería mostrar botón para limpiar búsqueda', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })
      
      const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')
      await user.type(searchInput, 'test')
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: '' })).toBeInTheDocument()
      })
    })
  })

  describe('Filtros por categoría', () => {
    it('debería mostrar filtros cuando showCategoryFilter es true', async () => {
      await act(async () => {
        render(<FAQSection showCategoryFilter={true} />)
      })

      await waitFor(() => {
        expect(screen.getByText('Filtrar por categoría:')).toBeInTheDocument()
        expect(screen.getByText('Todas')).toBeInTheDocument()
        // Usar getAllByText para manejar elementos duplicados
        expect(screen.getAllByText('Impuestos y Tributos')).toHaveLength(2)
        expect(screen.getAllByText('Licencias y Permisos')).toHaveLength(2)
      })
    })

    it('debería ocultar filtros cuando showCategoryFilter es false', async () => {
      render(<FAQSection showCategoryFilter={false} />)
      
      await waitFor(() => {
        expect(screen.queryByText('Filtrar por categoría:')).not.toBeInTheDocument()
      })
    })

    it('debería filtrar por categoría al hacer clic', async () => {
      const user = userEvent.setup()
      mockFaqService.getFAQsByCategory.mockResolvedValue([mockFAQs[0]])

      await act(async () => {
        render(<FAQSection />)
      })

      await waitFor(() => {
        expect(screen.getAllByText('Impuestos y Tributos')).toHaveLength(2)
      })

      // Hacer clic en el botón de filtro (no en el texto de estadísticas)
      const filterButtons = screen.getAllByText('Impuestos y Tributos')
      const filterButton = filterButtons.find(el => el.closest('button'))

      await act(async () => {
        await user.click(filterButton!)
      })

      await waitFor(() => {
        expect(mockFaqService.getFAQsByCategory).toHaveBeenCalledWith('impuestos', 10)
      })
    })

    it('debería mostrar contador de FAQs por categoría', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('3')).toBeInTheDocument() // Contador para impuestos
        expect(screen.getByText('2')).toBeInTheDocument() // Contador para licencias
      })
    })
  })

  describe('Lista de FAQs', () => {
    it('debería mostrar FAQs cargadas', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('¿Qué es el impuesto predial?')).toBeInTheDocument()
        expect(screen.getByText('¿Cómo obtengo una licencia de construcción?')).toBeInTheDocument()
      })
    })

    it('debería expandir FAQ al hacer clic', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('¿Qué es el impuesto predial?')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('¿Qué es el impuesto predial?'))
      
      await waitFor(() => {
        expect(screen.getByText('El impuesto predial es un tributo municipal que grava la propiedad inmueble.')).toBeInTheDocument()
      })
    })

    it('debería mostrar tags cuando FAQ está expandida', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('¿Qué es el impuesto predial?')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('¿Qué es el impuesto predial?'))
      
      await waitFor(() => {
        expect(screen.getByText('impuesto')).toBeInTheDocument()
        expect(screen.getByText('predial')).toBeInTheDocument()
        expect(screen.getByText('propiedad')).toBeInTheDocument()
      })
    })

    it('debería mostrar procedimientos relacionados', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('¿Qué es el impuesto predial?')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('¿Qué es el impuesto predial?'))
      
      await waitFor(() => {
        expect(screen.getByText('Trámites relacionados:')).toBeInTheDocument()
        expect(screen.getByText('• Impuesto predial unificado')).toBeInTheDocument()
      })
    })

    it('debería mostrar popularidad de FAQ', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('95% popular')).toBeInTheDocument()
        expect(screen.getByText('90% popular')).toBeInTheDocument()
      })
    })
  })

  describe('Estados vacíos', () => {
    it('debería mostrar mensaje cuando no hay FAQs', async () => {
      mockFaqService.getPopularFAQs.mockResolvedValue([])
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByText('No se encontraron preguntas')).toBeInTheDocument()
      })
    })

    it('debería mostrar mensaje específico para búsqueda sin resultados', async () => {
      const user = userEvent.setup()
      mockFaqService.searchFAQs.mockResolvedValue([])
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })
      
      const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')
      await user.type(searchInput, 'término inexistente')
      
      await waitFor(() => {
        expect(screen.getByText('No hay resultados para "término inexistente"')).toBeInTheDocument()
      })
    })
  })

  describe('Funcionalidad "Ver más"', () => {
    it('debería mostrar botón "Ver más" cuando hay más FAQs', async () => {
      const manyFAQs = Array.from({ length: 15 }, (_, i) => ({
        ...mockFAQs[0],
        id: `faq-${i}`,
        question: `Pregunta ${i}`
      }))
      
      mockFaqService.getPopularFAQs.mockResolvedValue(manyFAQs)
      
      render(<FAQSection initialLimit={5} />)
      
      await waitFor(() => {
        expect(screen.getByText('Ver más preguntas (10 restantes)')).toBeInTheDocument()
      })
    })

    it('debería expandir lista al hacer clic en "Ver más"', async () => {
      const user = userEvent.setup()
      const manyFAQs = Array.from({ length: 15 }, (_, i) => ({
        ...mockFAQs[0],
        id: `faq-${i}`,
        question: `Pregunta ${i}`
      }))
      
      mockFaqService.getPopularFAQs.mockResolvedValue(manyFAQs)
      
      render(<FAQSection initialLimit={5} />)
      
      await waitFor(() => {
        expect(screen.getByText('Ver más preguntas (10 restantes)')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Ver más preguntas (10 restantes)'))
      
      await waitFor(() => {
        expect(screen.getByText('Ver menos preguntas')).toBeInTheDocument()
      })
    })
  })

  describe('Limpieza de filtros', () => {
    it('debería mostrar botón limpiar filtros cuando hay filtros activos', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })
      
      const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')
      await user.type(searchInput, 'test')
      
      await waitFor(() => {
        expect(screen.getByText('Limpiar filtros')).toBeInTheDocument()
      })
    })

    it('debería limpiar filtros al hacer clic', async () => {
      const user = userEvent.setup()
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar en preguntas frecuentes...')).toBeInTheDocument()
      })
      
      const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')
      await user.type(searchInput, 'test')
      
      await waitFor(() => {
        expect(screen.getByText('Limpiar filtros')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Limpiar filtros'))
      
      await waitFor(() => {
        expect(searchInput).toHaveValue('')
      })
    })
  })

  describe('Accesibilidad', () => {
    it('debería tener estructura de encabezados correcta', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(screen.getByRole('heading', { level: 2, name: 'Preguntas Frecuentes' })).toBeInTheDocument()
      })
    })

    it('debería tener botones accesibles', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        const buttons = screen.getAllByRole('button')
        expect(buttons.length).toBeGreaterThan(0)
      })
    })

    it('debería tener campos de entrada etiquetados', async () => {
      render(<FAQSection />)
      
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('Buscar en preguntas frecuentes...')
        expect(searchInput).toBeInTheDocument()
      })
    })
  })

  describe('Manejo de errores', () => {
    it('debería manejar errores de carga graciosamente', async () => {
      mockFaqService.getCategories.mockRejectedValue(new Error('Error de red'))
      mockFaqService.getPopularFAQs.mockRejectedValue(new Error('Error de red'))
      mockFaqService.getFAQStats.mockRejectedValue(new Error('Error de red'))
      
      // Spy en console.error para verificar que se registra el error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      render(<FAQSection />)
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalled()
      })
      
      consoleSpy.mockRestore()
    })
  })
})
