{"core/audits/accessibility/accesskeys.js | description": {"message": "Тастери за приступ омогућавају корисницима да брзо фокусирају део странице. Да би навигација радила исправно, сваки тастер за приступ мора да буде јединствен. [Сазнајте више о тастерима за приступ](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Вредности за `[accesskey]` нису јединствене"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Вредности за `[accesskey]` су јединствене"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Сваки ARIA елемент `role` подржава одређени подскуп атрибута `aria-*`. Ако се ови елементи не подударају, атрибути `aria-*` ће бити неважећи. [Сазнајте како да се ARIA атрибути подударају са улогама](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` се не подударају са својим улогама"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибути `[aria-*]` се подударају са својим улогама"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Када елемент нема приступачан назив, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте како да учините елементе команди приступачнијим](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Елементи `button`, `link` и `menuitem` немају називе прилагођене функцијама приступачности."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Елементи `button`, `link` и `menuitem` имају називе прилагођене функцијама приступачности"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана, не раде доследно када се `aria-hidden=\"true\"` подеси за `<body>` документа. [Сазнајте како `aria-hidden` утиче на садржај документа](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Елемент `[aria-hidden=\"true\"]` је присутан за `<body>` документа"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Елемент `[aria-hidden=\"true\"]` није присутан за `<body>` документа"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Елементи по опадајућем редоследу који могу да се фокусирају у оквиру елемента `[aria-hidden=\"true\"]` спречавају те интерактивне елементе да буду доступни корисницима технологија за помоћ особама са инвалидитетом, попут читача екрана. [Сазнајте како `aria-hidden` утиче на елементе који могу да се фокусирају](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Елементи `[aria-hidden=\"true\"]` садрже елементе по опадајућем редоследу који могу да се фокусирају"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Елементи `[aria-hidden=\"true\"]` не обухватају елементе по опадајућем редоследу који могу да се фокусирају"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Када поље за унос нема назив прилагођен функцији приступачности, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте више о ознакама поља за унос](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA поља за унос немају називе прилагођене функцији приступачности"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA поља за унос имају називе прилагођене функцији приступачности"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Када елемент мерача нема приступачан назив, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте како да именујете `meter` елементе](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA елементи `meter` немају називе прилагођене функцијама приступачности."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA елементи `meter` имају називе прилагођене функцијама приступачности"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Када `progressbar` нема назив прилагођен функцији приступачности, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте како да означите елементе `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA елементи `progressbar` немају називе прилагођене функцијама приступачности."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA елементи `progressbar` имају називе прилагођене функцијама приступачности"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Поједине ARIA улоге имају обавезне атрибуте који статус елемента описују читачима екрана. [Сазнајте више о улогама и обавезним атрибутима](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Улоге `[role]` немају све обавезне атрибуте `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Улоге `[role]` имају све обавезне атрибуте `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Поједине надређене ARIA улоге морају да обухватају одређене подређене улоге да би правилно обављале намењене функције приступачности. [Сазнајте више о улогама и обавезним подређеним елементима](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Елементима са ARIA улогом `[role]` који захтевају да подређени елементи садрже конкретни елемент `[role]` недостају неки или сви ти потребни подређени елементи."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Елементи са ARIA улогом `[role]` који захтевају да подређени елементи садрже конкретни елемент `[role]` имају све потребне подређене елементе."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Поједине подређене ARIA улоге морају да буду обухваћене одређеним надређеним улогама да би правилно обављале намењене функције приступачности. [Сазнајте више о ARIA улогама и обавезном надређеном елементу](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Улоге `[role]` нису обухваћене својим обавезним надређеним елементом"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Улоге `[role]` су обухваћене својим обавезним надређеним елементом"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Вредности ARIA улога морају да буду важеће да би правилно обављале намењене функције приступачности. [Сазнајте више о важећим ARIA улогама](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Вредности за `[role]` нису важеће"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Вредности за `[role]` су важеће"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Када поље за прекидач нема назив прилагођен функцији приступачности, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте више о укључивању или искључивању поља](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA поља прекидача немају називе прилагођене функцији приступачности"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA поља прекидача имају називе прилагођене функцији приступачности"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Када елемент објашњења нема приступачан назив, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте како да именујете `tooltip` елементе](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA елементи `tooltip` немају називе прилагођене функцијама приступачности."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA елементи `tooltip` имају називе прилагођене функцијама приступачности"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Када `treeitem` нема назив прилагођен функцији приступачности, читачи екрана га најављују помоћу генеричког назива, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте више о означавању елемената `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA елементи `treeitem` немају називе прилагођене функцијама приступачности."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA елементи `treeitem` имају називе прилагођене функцијама приступачности"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана, не могу да протумаче ARIA атрибуте са неважећим вредностима. [Сазнајте више о важећим вредностима за ARIA атрибуте](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Вредности атрибута `[aria-*]` нису важеће"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Вредности атрибута `[aria-*]` су важеће"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана, не могу да протумаче ARIA атрибуте са неважећим називима. [Сазнајте више о важећим ARIA атрибутима](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` нису важећи или су погрешно написани"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибути `[aria-*]` су важећи и нису погрешно написани"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи који нису прошли проверу"}, "core/audits/accessibility/button-name.js | description": {"message": "Када дугме нема назив прилагођен функцији приступачности, читачи екрана га најављују као „дугме“, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте како да учините дугмад приступачнијом](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Дугмад нема називе прилагођене функцијама приступачности"}, "core/audits/accessibility/button-name.js | title": {"message": "Дугмад има називе прилагођене функцијама приступачности"}, "core/audits/accessibility/bypass.js | description": {"message": "Када се додају начини за заобилажење садржаја који се понавља, корисници тастатуре могу ефикасније да се крећу по страници. [Сазнајте више о заобилажењу блокова](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Страница не обухвата наслов, линк за прескакање нити регион оријентира"}, "core/audits/accessibility/bypass.js | title": {"message": "Страница обухвата наслов, линк за прескакање или регион оријентира"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Многи корисници веома тешко читају текст са малим контрастом или уопште не могу да га читају. [Сазнајте како да обезбедите довољно велики контраст боја](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Боје у позадини и у првом плану немају задовољавајући однос контраста."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Боје у позадини и у првом плану имају задовољавајући однос контраста"}, "core/audits/accessibility/definition-list.js | description": {"message": "Када листе дефиниција нису правилно означене, читачи екрана могу да пружају збуњујући или нетачан излаз. [Сазнајте како да направите правилну структуру листа дефиниција](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` не садржи само правилно поређане групе `<dt>` и `<dd>`, елементе `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` садржи само правилно поређане групе `<dt>` и `<dd>`, елементе `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Ставке листе дефиниција (`<dt>` и `<dd>`) морају да буду упаковане у надређени елемент`<dl>` да би читачи екрана могли да их правилно читају. [Сазнајте како да направите правилну структуру листа дефиниција](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Ставке листе дефиниција су упаковане у елементе `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Ставке листе дефиниција су упаковане у елементе`<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Наслов корисницима читача екрана пружа преглед странице, а корисници претраживача се на њега ослањају да би утврдили да ли је страница релевантна за њихову претрагу. [Сазнајте више о насловима докумената](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Документ нема елемент `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Документ има елемент `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Да бисте се уверили да су видљиви за технологије за помоћ особама са инвалидитетом, сви елементи који могу да се фокусирају морају да имају јединствени `id`. [Сазнајте како да решите дуплиране `id`-ове](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Атрибути `[id]` на активним елементима који могу да се фокусирају нису јединствени"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Атрибути `[id]` на активним елементима који могу да се фокусирају су јединствени"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Вредност ARIA ИД-а мора да буде јединствена да би се спречило да технологије за помоћ особама са инвалидитетом пропусте друге инстанце. [Сазнајте како да отклоните дупликате ARIA ИД-ова](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ИД-ови нису јединствени"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ИД-ови су јединствени"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана који користе прву, последњу или све ознаке, могу да читају поља обрасца са више ознака на начин који збуњује кориснике. [Сазнајте како да користите ознаке образаца](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Поља обрасца имају више ознака"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Ниједно поље обрасца нема више ознака"}, "core/audits/accessibility/frame-title.js | description": {"message": "Корисници читача екрана очекују од наслова оквира да им опишу садржај оквира. [Сазнајте више о насловима оквира](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Елементи `<frame>` или `<iframe>` немају наслов"}, "core/audits/accessibility/frame-title.js | title": {"message": "Елементи `<frame>` или `<iframe>` имају наслов"}, "core/audits/accessibility/heading-order.js | description": {"message": "Наслови са правилним редоследом који не прескачу нивое преносе семантичку структуру странице, па је чине лакшом за кретање и разумевање при коришћењу технологија за помоћ особама са инвалидитетом. [Сазнајте више о редоследу наслова](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Елементи наслова се не приказују опадајућим редоследом"}, "core/audits/accessibility/heading-order.js | title": {"message": "Елементи наслова се приказују опадајућим редоследом"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ако за страницу није наведен атрибут `lang`, читач екрана претпоставља да је страница на подразумеваном језику који је корисник одабрао током подешавања читача екрана. Ако страница заправо није на подразумеваном језику, читач екрана можда неће правилно читати текст са странице. [Сазнајте више о атрибуту `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елемент `<html>` нема атрибут `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Елемент `<html>` има атрибут `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Навођењем важећег кода [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) омогућава се да читач екрана правилно чита текст. [Сазнајте како да користите атрибут `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елемент `<html>` нема важећу вредност за свој атрибут `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Елемент `<html>` има важећу вредност за свој атрибут `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Информативни елементи треба да садрже кратки, описни алтернативни текст. Декоративни елементи могу да се занемаре празним атрибутом alt. [Сазнајте више о атрибуту `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Елементи слике немају атрибуте `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Елементи слика имају атрибуте `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Када се слика користи као дугме `<input>`, навођење алтернативног текста може да помогне корисницима да разумеју сврху дугмета. [Сазнајте више о алтернативном тексту слике за унос](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Елементи `<input type=\"image\">` не садрже текст `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Елементи `<input type=\"image\">` садрже текст `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Ознаке омогућавају да технологије за помоћ особама са инвалидитетом, попут читача екрана, правилно најављују контроле образаца. [Сазнајте више о ознакама елемената обрасца](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Елементи образаца немају повезане ознаке"}, "core/audits/accessibility/label.js | title": {"message": "Елементи образаца имају повезане ознаке"}, "core/audits/accessibility/link-name.js | description": {"message": "Текст линка (и алтернативни текст за слике када се користи за линкове) који је препознатљив, јединствен и може да се фокусира олакшава кретање за кориснике читача екрана. [Сазнајте како да учините линкове доступним.](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Назив линкова не може да се препозна"}, "core/audits/accessibility/link-name.js | title": {"message": "Назив линкова може да се препозна"}, "core/audits/accessibility/list.js | description": {"message": "Читачи екрана читају листе на посебан начин. Правилна структура листе олакшава разумевање читача екрана. [Сазнајте више о правилној структури листе](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Листе не садрже искључиво елементе `<li>` и елементе који подржавају скрипте (`<script>` и`<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Листе садрже искључиво елементе `<li>` и елементе који подржавају скрипте (`<script>` и `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Читачи екрана захтевају да ставке листе (`<li>`) буду обухваћене надређеним елементима `<ul>`, `<ol>` или `<menu>` да би могле да се правилно читају. [Сазнајте више о правилној структури листе](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Ставке листе (`<li>`) нису обухваћене надређеним елементима`<ul>`, `<ol>` или `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Ставке листе (`<li>`) су обухваћене надређеним елементима `<ul>`, `<ol>` или `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Корисници не очекују да се страница аутоматски освежава и тиме се фокус премешта на почетак странице. То може да фрустира или збуњује кориснике. [Сазнајте више о освежавању метаознаке](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документ користи метаознаку `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Документ не користи метаознаку `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Онемогућавање зумирања представља проблем за слабовиде кориснике који се ослањају на увећавање приказа екрана да би могли да виде садржај веб-странице. [Сазнајте више о метаознаци области приказа](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` се користи у елементу `<meta name=\"viewport\">` или је вредност атрибута `[maximum-scale]` мања од 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` се не користи у елементу `<meta name=\"viewport\">`, а вредност атрибута `[maximum-scale]` није мања од 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Читачи екрана не могу да преводе садржај који није текст. Додавање алтернативног текста елементима `<object>` омогућава да читачи екрана лакше пренесу значење корисницима. [Сазнајте више о алтернативном тексту за елементе `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Елементи `<object>` немају алтернативни текст"}, "core/audits/accessibility/object-alt.js | title": {"message": "Елементи `<object>` имају алтернативни текст"}, "core/audits/accessibility/tabindex.js | description": {"message": "Вредност већа од 0 означава експлицитни распоред навигације. Иако је технички исправно, то често фрустрира кориснике који се ослањају на технологије за помоћ особама са инвалидитетом. [Сазнајте више о атрибуту `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Неки елементи имају вредност за `[tabindex]` која је већа од 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Вредност ниједног елемента `[tabindex]` није већа од 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Читачи екрана имају функције које олакшавају кретање кроз табеле. Ако се побринете да се ћелије `<td>` које користе атрибут `[headers]` односе само на друге ћелије у истој табели, можете да побољшате доживљај за кориснике читача екрана. [Сазнајте више о атрибуту `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ћелије у елементу `<table>` које користе атрибут `[headers]` односе се на елемент `id` који се не налази у истој табели."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Ћелије у елементу `<table>` које користе атрибут `[headers]` односе се на ћелије табеле у истој табели."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Читачи екрана имају функције које олакшавају кретање кроз табеле. Ако се побринете да се наслови табела увек односе на неку групу ћелија, можете да побољшате доживљај за кориснике читача екрана. [Сазнајте више о заглављима табела](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Елементи `<th>` и елементи са атрибутом`[role=\"columnheader\"/\"rowheader\"]` немају ћелије са подацима које описују."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементи `<th>` и елементи са атрибутом `[role=\"columnheader\"/\"rowheader\"]` имају ћелије са подацима које описују."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Навођењем важећег кода [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) у елементима омогућава се да читач екрана правилно чита текст. [Сазнајте како да користите атрибут `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Вредност атрибута `[lang]` није важећа"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Атрибути `[lang]` имају важећу вредност"}, "core/audits/accessibility/video-caption.js | description": {"message": "Када је доступан титл за видео, глуви корисници и они са оштећењем слуха лакше могу да приступају информацијама које видео обухвата. [Сазнајте више о титловима видео снимака](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Елементи `<video>` не обухватају елемент `<track>` са атрибутом `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Елементи `<video>` садрже елемент `<track>` са атрибутом `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Акту<PERSON><PERSON>на вредност"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Предложени токен"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` помаже корисницима да брже шаљу обрасце. Да би корисницима било лакше, не би било лоше да подесите атрибут `autocomplete` на важећу вредност и тиме омогућите ову ставку. [Сазнајте више о атрибуту `autocomplete` у обрасцима](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Елементи `<input>` немају исправне атрибуте `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Захтева ручни преглед"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Прегледајте редослед токена"}, "core/audits/autocomplete.js | title": {"message": "Елементи `<input>` исправно користе `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Токени за `autocomplete`: „{token}“ није важећи за {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Прегледајте редослед токена: „{tokens}“ у елементу {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Може нешто да се предузме"}, "core/audits/bf-cache.js | description": {"message": "Многе навигације се обављају враћањем на претходну страницу или поновним прослеђивањем. Кеширање целе странице (bfcache) може да убрза ове повратне навигације. [Сазнајте више о bfcache-у](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 разлог неуспеха}one{# разлог неуспеха}few{# разлога неуспеха}other{# разлога неуспеха}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Разлог неуспеха"}, "core/audits/bf-cache.js | failureTitle": {"message": "Страница је спречила враћање кеширања целе странице"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Тип неуспеха"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Не може ништа да се предузме"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Подршка за прегледач на чекању"}, "core/audits/bf-cache.js | title": {"message": "Страница није спречила враћање кеширања целе странице"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Додаци за Chrome су негативно утицали на брзину учитавања ове странице. Пробајте да проверите страницу у режиму без архивирања или са Chrome профила без додатака."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Процена скрипта"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Рашчлањивање скрипта"}, "core/audits/bootup-time.js | columnTotal": {"message": "Укупно CPU време"}, "core/audits/bootup-time.js | description": {"message": "Препоручујемо вам да смањите време потребно за рашчлањивање, компајлирање и извршавање JS фајлова. Приказивање мањих JS ресурса ће вам можда помоћи у томе. [Сазнајте како да смањите време извршавања JavaScript-а](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Смањите време извршавања JavaScript датотека"}, "core/audits/bootup-time.js | title": {"message": "Време извршавања JavaScript-а"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Уклоните велике, дуплиране JavaScript модуле из пакета да бисте смањили непотребну потрошњу података током мрежних активности. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Уклоните дуплиране модуле из JavaScript пакета"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Велики GIF-ови нису корисни за приказивање анимираног садржаја. Препоручујемо вам да уместо GIF-ова користите MPEG4/WebM видео снимке за анимације и PNG/WebP за статичне слике да бисте уштедели мрежне податке. [Сазнајте више о ефикасним видео форматима](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Користите видео формате за анимирани садржај"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Полифили и трансформације омогућавају да старе верзије прегледача користе нове функције JavaScript-а. Међутим, многи нису неопходни за модерне прегледаче. За JavaScript пакете користите модерну стратегију примене скрипте помоћу откривања функција модулом/без модула да бисте смањили количину кода који се шаље модерним прегледачима, притом задржавајући подршку за старе верзије прегледача. [Сазнајте како се користи модерни JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Избегавајте приказивање застарелог JavaScript-а модерним прегледачима"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Формати слика као што су WebP и AVIF често пружају бољу компресију од PNG-а или JPEG-а, што омогућава бржа преузимања и мању потрошњу података. [Сазнајте више о модерним форматима слика](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Приказујте слике у форматима следеће генерације"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Препоручујемо вам да одложите учитавање слика ван екрана и скривених слика док се сви веома важни ресурси не учитају како бисте смањили време до почетка интеракције. [Сазнајте како да одложите слике ван екрана](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Одложите слике ван екрана"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокирају прво приказивање странице. Препоручујемо вам да приказујете све важне JS/CSS фајлове у тексту и да одложите све JS фајлове/стилове који нису толико важни. [Сазнајте како да елиминишете ресурсе који блокирају приказивање](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Елиминишите ресурсе који блокирају приказивање"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Велике мрежне ресурсе корисници морају да плате стварним новцем и они су веома повезани са дугим временима учитавања. [Сазнајте како да смањите величине ресурса](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Укупна величина је била {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Избегавајте огромне мрежне ресурсе"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Избегава огромне мрежне ресурсе"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Умањивањем CSS фајлова можете да смањите величине мрежних ресурса. [Сазнајте како да умањите CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Умањите CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Умањивање JavaScript фајлова може да смањи величине ресурса и време рашчлањивања скрипта. [Сазнајте како да умањите JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Умањите JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Смањите некоришћена правила из описа стилова и одложите CSS који се не користи за садржај изнад прелома да бисте смањили потрошњу бајтова током мрежних активности. [Сазнајте како да смањите CSS који се не користи](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Смањите некоришћени CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Смањите некоришћени JavaScript и одложите учитавање скрипти док не буду потребне да бисте смањили потрошњу бајтова током мрежних активности. [Сазнајте како да смањите JavaScript који се не користи](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Смањите некоришћени JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Дуго трајање кеша може да убрза поновне посете страници. [Сазнајте више о ефикасним смерницама за кеш](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 ресурс}one{Пронађен је # ресурс}few{Пронађена су # ресурса}other{Пронађено је # ресурса}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Приказујте статичне елементе са ефикасним смерницама кеша"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Користи ефикасне смернице кеша на статичним елементима"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимизоване слике се учитавају брже и троше мање мобилних података. [Сазнајте како да ефикасно кодирате слике](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Ефикасно кодирајте слике"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Стварне димензије"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Приказане димензије"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Слике су веће од приказане величине"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Слике су одговарајуће за приказану величину"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Приказујте слике одговарајуће величине да бисте уштедели мобилне податке и побољшали време учитавања. [Сазнајте како да подесите величину слика](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Одредите одговарајућу величину слика"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Ресурсе засноване на тексту треба да приказујете у компримованом формату (gzip, deflate или brotli) да бисте смањили укупну количину потрошених мрежних података. [Сазнајте више о компримовању текста](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Омогућите компресију текста"}, "core/audits/content-width.js | description": {"message": "Ако се ширина садржаја апликације не подудара са ширином области приказа, апликација можда није оптимизована за екране на мобилним уређајима. [Сазнајте како да одредите величину садржаја за област приказа](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Величина области приказа од {innerWidth} пиксела се не подудара са величином прозора од {outerWidth} пиксела."}, "core/audits/content-width.js | failureTitle": {"message": "Садржај није одговарајуће величине за област приказа"}, "core/audits/content-width.js | title": {"message": "Садржај је одговарајуће величине за област приказа"}, "core/audits/critical-request-chains.js | description": {"message": "Ланци веома важних захтева у наставку вам приказују који ресурси се учитавају са високим приоритетом. Препоручујемо вам да смањите дужину ланаца, да смањите величину преузимања за ресурсе или да одложите преузимање ресурса који нису неопходни ради бржег учитавања странице. [Сазнајте како да избегнете прављење ланаца критичних захтева](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 ланац}one{Пронађен је # ланац}few{Пронађена су # ланца}other{Пронађено је # ланаца}}"}, "core/audits/critical-request-chains.js | title": {"message": "Избегавајте прављење ланаца критичних захтева"}, "core/audits/csp-xss.js | columnDirective": {"message": "Директива"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Озбиљност"}, "core/audits/csp-xss.js | description": {"message": "Снажне смернице за безбедност садржаја (CSP) знатно смањују ризик од напада скриптовањем динамички генерисаних веб-страница (XSS). [Сазнајте како да користите CSP да бисте спречили XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Синтакса"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Страница садржи CSP дефини<PERSON><PERSON><PERSON> у <meta> ознаци. Размотрите премештање CSP-а у HTTP заглавље или дефинисање другог строгог CSP-а у HTTP заглављу."}, "core/audits/csp-xss.js | noCsp": {"message": "Није пронађен ниједан CSP у режиму примене"}, "core/audits/csp-xss.js | title": {"message": "Уверите се да је CSP ефикасан у борби против XSS напада"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Застарело/упозорење"}, "core/audits/deprecations.js | columnLine": {"message": "Ред"}, "core/audits/deprecations.js | description": {"message": "Застарели API-ји ће на крају бити уклоњени из прегледача. [Сазнајте више о застарелим API-јима](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Откривено је 1 упозорење}one{Откривено је # упозорење}few{Откривена су # упозорења}other{Откривено је # упозорења}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Користи застареле API-је"}, "core/audits/deprecations.js | title": {"message": "Избегава застареле API-је"}, "core/audits/dobetterweb/charset.js | description": {"message": "Утврђивање кодирања знакова је обавезно. То може да се уради помоћу ознаке `<meta>` у прва 1024 бајта HTML-а или у HTTP заглављу одговора Content-Type. [Сазнајте више о декларисању кодирања знакова](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Charset није утврђен или се утврђује прекасно у HTML-у"}, "core/audits/dobetterweb/charset.js | title": {"message": "Правилно дефини<PERSON><PERSON><PERSON> charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Навођењем doctype-а спречава се прелазак на архајски режим прегледача. [Сазнајте више о декларацији doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Назив за doctype мора да буде стринг `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Документ садржи `doctype` који покреће `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документ мора да садржи doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Очекивани publicId ће бити празан стринг"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Очекивани systemId ће бити празан стринг"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Документ садржи `doctype` који покреће `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Страници недостаје HTML doctype, па се активира архајски режим"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Страница има HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистика"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Вредност"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Велики DOM ће повећати потрошњу меморије, изазвати дужа [израчунавања стилова](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) и довести до скупих [преобликовања изгледа](https://developers.google.com/speed/articles/reflow). [Сазнајте како да избегнете прекомерну величину DOM-а](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}one{# елемент}few{# елемента}other{# елемената}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Избегавајте превелику величину DOM-а"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимална дубина DOM-а"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Укупан број DOM елемената"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Максималан број подређених елемената"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Избегава превелику величину DOM-а"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Корисници немају поверења у сајтове који траже њихову локацију без контекста или их такви сајтови збуњују. Препоручујемо вам да уместо тога повежете захтев са радњом коју обавља корисник. [Сазнајте више о дозволи за геолоцирање](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Тражи дозволу за геолоцирање при учитавању странице"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Избегавајте тражење дозволе за геолоцирање при учитавању странице"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Тип проблема"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Проблеми евидентирани у окну `Issues` у Chrome алаткама за програмере указују на нерешене проблеме. Они могу да буду резултат неуспелих мрежних захтева, недовољних безбедносних контрола и других проблема у вези са прегледачем. Отворите окно Проблеми у Chrome алаткама за програмере да бисте пронашли више детаља о сваком проблему."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Проблеми су евидентирани у окну `Issues` у Chrome алаткама за програмере"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Блокирају смернице за захтеве из других извора"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Огласи користе много ресурса"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Нема проблема у окну `Issues` у Chrome алаткама за програмере"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Верзија"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Све корисничке JavaScript библиотеке откривене на овој страници. [Сазнајте више о овој дијагностичком надзору откривања JavaScript библиотеке](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Откривене су JavaScript библиотеке"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Ако корисници имају споре везе, спољне скрипте које се динамички убацују помоћу атрибута `document.write()` могу да одложе учитавање странице за десетине секунди. [Сазнајте како да избегнете document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Избегните `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Избегава атрибут `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Корисници немају поверења у сајтове који траже дозволу за слање обавештења без контекста или их такви сајтови збуњују. Препоручујемо вам да уместо тога повежете захтев са покретима корисника. [Сазнајте више о одговорном добијању дозволе за обавештења](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Тражи дозволу за обавештења при учитавању странице"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Избегавајте тражење дозволе за обавештења при учитавању странице"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 има бројне предности у односу на HTTP/1.1, укључујући бинарна заглавља и мултиплексирање. [Сазнајте више о протоколу HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 захтев није испоручен преко протокола HTTP/2}one{# захтев није испоручен преко протокола HTTP/2}few{# захтева нису испоручена преко протокола HTTP/2}other{# захтева није испоручено преко протокола HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Користите HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Препоручујемо вам да пасивне обрађиваче догађаја означите као `passive` да бисте побољшали резултате померања. [Сазнајте више о усвајању пасивних ослушкивача догађаја](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не користите пасивне обрађиваче да бисте побољшали учинак померања"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Користите пасивне обрађиваче да бисте побољшали учинак померања"}, "core/audits/errors-in-console.js | description": {"message": "Грешке евидентиране у конзоли указују на нерешене проблеме. Оне су резултат неуспелих мрежних захтева и других проблема у вези са прегледачем. [Сазнајте више о овим грешкама у дијагностичком надзору конзоле](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Грешке прегледача су евидентиране у конзоли"}, "core/audits/errors-in-console.js | title": {"message": "Ниједна грешка прегледача није евидентирана у конзоли"}, "core/audits/font-display.js | description": {"message": "Искористите CSS функцију `font-display` да би текст био видљив корисницима док се веб-фонтови учитавају. [Сазнајте више о функцији `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Побрините се да текст остане видљив током учитавања веб-фонтова"}, "core/audits/font-display.js | title": {"message": "Сав текст остаје видљив током учитавања веб-фонтова"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse није успео да аутоматски провери вредност `font-display` за полазни URL {fontOrigin}.}one{Lighthouse није успео да аутоматски провери `font-display` вредности за полазни URL {fontOrigin}.}few{Lighthouse није успео да аутоматски провери `font-display` вредности за полазни URL {fontOrigin}.}other{Lighthouse није успео да аутоматски провери `font-display` вредности за полазни URL {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Размера (стварна)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Размера (приказана)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Димензије приказа слике треба да се подударају са природном размером. [Сазнајте више о размери слике](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Приказује слике са погрешном размером"}, "core/audits/image-aspect-ratio.js | title": {"message": "Приказује слике са тачном размером"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Стварна величина"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Приказана величина"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Очекивана величина"}, "core/audits/image-size-responsive.js | description": {"message": "Природне димензије слике треба да буду пропорционалне размери величине екрана и пиксела да би слике биле што јасније. [Сазнајте како да пружате прилагодљиве слике](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Приказује слике са ниском резолуцијом"}, "core/audits/image-size-responsive.js | title": {"message": "Приказује слике са одговарајућом резолуцијом"}, "core/audits/installable-manifest.js | already-installed": {"message": "Апликација је већ инсталирана"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Преузимање обавезне иконе из манифеста није успело"}, "core/audits/installable-manifest.js | columnValue": {"message": "Разлог неуспеха"}, "core/audits/installable-manifest.js | description": {"message": "Сервисер је технологија која омогућава апликацији да користи многе функције прогресивних веб-апликација, попут офлајн рада, додавања на почетни екран и искачућих обавештења. Уз одговарајуће примене сервисера и манифеста прегледачи могу проактивно да траже од корисника да додају апликацију на почетни екран, што може да доведе до већег ангажовања. [Сазнајте више о условима за инсталирање за манифест](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 разлог}one{# разлог}few{# разлога}other{# разлога}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Манифест веб-апликације или сервисер не задовољава услове за инсталирање"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL апликације у Play продавници и ИД у Play продавници се не подударају"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Страница се учитава у прозору без архивирања"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Својство „display“ у манифесту мора да буде „standalone“, „fullscreen“ или „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Манифест садржи поље „display_override“, а први подржани начин приказивања мора да буде „standalone“, „fullscreen“ или „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Преузимање или рашчлањивање манифеста није успело или је он празан"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL манифеста је промењен током преузимања манифеста."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Манифест не садржи поље „name“ или „short_name“"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Манифест не садржи одговарајућу икону – обавезан је PNG, SVG или WebP формат који има најмање {value0} пиксела, атрибут за величине мора да буде подешен, а атрибут за сврху, ако је подешен, мора да обухвата „Било који“."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Ниједна од наведених икона није у облику квадрата од најмање {value0} пиксела у PNG, SVG или WebP формату, са атрибутом сврхе који није подешен или је подешен на „Било који“"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Преузета икона је била празна или оштећена"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Није наведен ИД у Play продавници"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Страница не садржи <link> URL манифеста"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Није откривен ниједан одговарајући сервисер. Можда ћете морати поново да учитате страницу или да проверите да ли опсег сервисера за актуелну страницу обухвата опсег и почетни URL из манифеста."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Провера сервисера без поља „start_url“ у манифесту није успела"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ИД грешке у вези са могућношћу инсталирања „{errorId}“ није препознат"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Страница се не приказује из безбедног извора"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Страница се не учитава у главном оквиру"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Страница не ради офлајн"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA је деинсталирана и провере нестабилности се ресетују."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Наведена платформа за апликације није подржана на Android-у"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Манифест наводи prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Ставка prefer_related_applications је подржана само у Chrome-у бета и на Стабилним каналима на Android-у."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse није успео да утврди да ли је постојао сервисер. Пробајте са новијом верзијом Chrome-а."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Шема URL-а манифеста ({scheme}) није подржана на Android-у."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Почетни URL манифеста није важећи"}, "core/audits/installable-manifest.js | title": {"message": "Манифест веб-апликације и сервисер задовољавају услове за инсталирање"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL у манифесту садржи корисничко име, лозинку или порт"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Страница не ради офлајн. Сматраће се да страница не може да се инсталира после Chrome-а 93, чија стабилна верзија излази у августу 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Дозвољен"}, "core/audits/is-on-https.js | blocked": {"message": "Блокиран"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Небезбедан URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Разрешење захтева"}, "core/audits/is-on-https.js | description": {"message": "Сви сајтови треба да буду заштићени HTTPS-ом, чак и они који не обрађују осетљиве податке. То обухвата избегавање [мешовитог садржаја](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), односно учитавање неких ресурса преко HTTP-а иако се почетни захтев приказује преко HTTPS-а. HTTPS спречава уљезе да неовлашћено приступају комуникацији између апликације и корисника или да је пасивно слушају. Он је предуслов за HTTP/2 i API-је бројних нових веб-платформи. [Сазнајте више о протоколу HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 небезбедан захтев}one{Пронађен је # небезбедан захтев}few{Пронађена су # небезбедна захтева}other{Пронађено је # небезбедних захтева}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Не користи HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Користи HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Аутоматски надограђен на HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Дозвољен уз упозорење"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Ово је највећи елемент садржаја који је приказан у области приказа. [Сазнајте више о елементу са највећим приказивањем садржаја](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Елемент са највећим приказивањем садржаја"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS допринос"}, "core/audits/layout-shift-elements.js | description": {"message": "Ови DOM елементи највише доприносе CLS-у странице. [Сазнајте како да побољшате CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Избегавајте велике промене распореда"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Слике изнад прелома које се споро учитавају приказују се касније у циклусу странице, што може да изазове кашњење највећег приказивања садржаја. [Сазнајте више о оптималном спором учитавању](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Слика највећег приказивања садржаја је споро учитана"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Слика највећег приказивања садржаја није споро учитана"}, "core/audits/long-tasks.js | description": {"message": "Наводи најдуже задатке у главној нити, што је корисно за идентификовање оних који највише доприносе кашњењу приказа. [Сазнајте како да избегнете дуге задатке у главној нити](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је # дуги задатак}one{Пронађен је # дуги задатак}few{Пронађена су # дуга задатка}other{Пронађено је # дугих задатака}}"}, "core/audits/long-tasks.js | title": {"message": "Избегавајте дуге задатке у главној нити"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категорија"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Препоручујемо вам да смањите време потребно за рашчлањивање, компајлирање и извршавање JS фајлова. Приказивање мањих JS ресурса ће вам можда помоћи у томе. [Сазнајте како да смањите рад главне нити](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Смањите рад главне нити"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Смањује рад главне нити"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Да би имали што више корисника, сајтови треба да раде у свим значајнијим прегледачима. [Сазнајте више о компатибилности са више прегледача](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Сајт ради у различитим веб-прегледачима"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Уверите се да до појединачних страница воде прецизни линкови преко URL-ова и да су URL-ови јединствени у сврху дељења на друштвеним медијима. [Сазнајте више о пружању прецизних линкова](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Свака страница има URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Корисници треба да имају утисак да су прелази брзи док додирују ставке, чак и на спорој мрежи. Овај доживљај је кључан за утисак који ће корисник имати о учинку. [Сазнајте више о преласцима са странице на страницу](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Преласци са странице на страницу не делују као да се блокирају због мреже"}, "core/audits/maskable-icon.js | description": {"message": "Икона која може да се маскира омогућава да слика прекрије цео облик без водоравног оивичења када се апликација инсталира на уређају. [Сазнајте више о иконама манифеста које могу да се маскирају](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Манифест не садржи икону која може да се маскира"}, "core/audits/maskable-icon.js | title": {"message": "Манифест садржи икону која може да се маскира"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Кумулативни помак садржаја странице мери кретање видљивих елемената унутар области приказа. [Сазнајте више о показатељу Кумулативни помак садржаја странице](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Интеракција до следећег приказивања мери брзину одговора странице, колико дуго страници треба да видљиво одговори на унос корисника. [Сазнајте више о показатељу Интеракција до следећег приказивања](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Прво приказивање садржаја означава време када се приказују први текст или слика. [Сазнајте више о показатељу Прво приказивање садржаја](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Прво значајно приказивање означава време када примарни садржај странице постаје видљив. [Сазнајте више о показатељу Прво значајно приказивање](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Време до почетка интеракције је количина времена која је потребна да би страница постала потпуно интерактивна. [Сазнајте више о показатељу Време до почетка интеракције](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Највеће приказивање садржаја означава тренутак у којем се приказују највећи текст или слика. [Сазнајте више о показатељу Највеће приказивање садржаја](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Максимално потенцијално кашњење првог приказа које може да се деси корисницима је трајање најдужег задатка. [Сазнајте више о показатељу Максимално потенцијално кашњење првог приказа](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Индекс брзине приказује колико брзо садржај странице постаје видљив за кориснике. [Сазнајте више о показатељу Индекс брзине](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Збир свих периода између FCP-а и времена до почетка интеракције, када задатак траје дуже од 50 ms, изражено у милисекундама. [Сазнајте више о показатељу Укупно време блокирања](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Трајања повратног пута (RTT) мреже знатно утичу на учинак. Ако је трајање повратног пута до почетне локације велико, то значи да би сервери који су ближи кориснику могли да побољшају учинак. [Сазнајте више о трајању повратног пута](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Трајања повратног пута мреже"}, "core/audits/network-server-latency.js | description": {"message": "Кашњења сервера могу да утичу на учинак веба. Ако је кашњење сервера за почетну локацију велико, то значи да је сервер преоптерећен или да има слаб позадински учинак. [Сазнајте више о времену одговора сервера](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Позадинска кашњења сервера"}, "core/audits/no-unload-listeners.js | description": {"message": "Догађај `unload` се не покреће поуздано и ако се он обрађује, то може да спречи оптимизације прегледача попут кеша за кретање уназад и унапред. Боље да користите догађаје `pagehide` или `visibilitychange`. [Сазнајте више о уклањању ослушкивача догађаја](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Региструје обрађивача догађаја `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Избегава обрађиваче догађаја `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Анимације које нису композитне могу да буду проблематичне и да повећавају кумулативни помак садржаја странице. [Сазнајте како да избегнете анимације које нису композитне](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је # анимиран елемент}one{Пронађен је # анимиран елемент}few{Пронађена су # анимирана елемента}other{Пронађено је # анимираних елемената}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Својство које се односи на филтере може да помера пикселе"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Циљ има другу анимацију која је некомпатибилна"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Ефекат има композитни режим који није „replace“"}, "core/audits/non-composited-animations.js | title": {"message": "Избегавајте анимације које нису композитне"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Својство везано за трансформацију зависи од величине оквира"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Неподржано CSS својство: {properties}}one{Неподржана CSS својства: {properties}}few{Неподржана CSS својства: {properties}}other{Неподржана CSS својства: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Ефекат има неподржане параметре времена"}, "core/audits/performance-budget.js | description": {"message": "Захтеве за количину и величину мреже одржавајте испод граница одређених циљевима за учинак. [Сазнајте више о ограничењима учинка](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 захтев}one{# захтев}few{# захтева}other{# захтева}}"}, "core/audits/performance-budget.js | title": {"message": "Циљ за учинак"}, "core/audits/preload-fonts.js | description": {"message": "Предучитајте `optional` фонтове да би нови посетиоци могли да их користе. [Сазнајте више о предучитавању фонтова](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Фонтови који користе `font-display: optional` нису предучитани"}, "core/audits/preload-fonts.js | title": {"message": "Фонтови који користе `font-display: optional` су предучитани"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ако се LCP елемент динамички додаје на страницу, треба да предучитате слику да бисте побољшали LCP (највеће приказивање садржаја). [Сазнајте више о учитавању LCP елемената](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Предучитајте слику са највећим приказивањем садржаја"}, "core/audits/redirects.js | description": {"message": "Преусмеравања доводе до додатних кашњења пре учитавања странице. [Сазнајте како да избегнете преусмеравања странице](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Избегавајте вишеструка преусмеравања странице"}, "core/audits/resource-summary.js | description": {"message": "Да бисте подесили циљеве за количину и величину ресурса странице, додајте датотеку budget.json file. [Сазнајте више о ограничењима учинка](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 захтев • {byteCount, number, bytes} Ki<PERSON>}one{# захтев • {byteCount, number, bytes} Ki<PERSON>}few{# захтева • {byteCount, number, bytes} Ki<PERSON>}other{# захтева • {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "Омогући да број захтева и величине преноса буду мали"}, "core/audits/seo/canonical.js | description": {"message": "Канонички линкови предлажу који URL треба да се прикаже у резултатима претраге. [Сазнајте више о каноничким линковима](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Више неусаглашених URL-ова ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Неважећи URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Усмерава на другу `hreflang` локацију ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Није апсолутни URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Усмерава на основни URL домена (почетну страницу), уместо еквивалентне странице садржаја"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Документ нема важећу вредност `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Документ има важећи атрибут `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Линк који не може да се попише"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Претраживачи могу да користе `href` атрибуте на линковима за пописивање веб-сајтова. Уверите се да `href` атрибут елемената сидра води на одговарајуће одредиште како би више страница са сајта могло да се открије. [Сазнајте како да омогућите да се линкови пописују](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Линкови не могу да се попишу"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Линкови могу да се попишу"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Додатан нечитљив текст"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Величина фонта"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Проценат текста на страници"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Величине фонтова испод 12 пиксела су премале да би биле читљиве и због њих корисници на мобилним уређајима морају да „зумирају прстима“ како би могли да читају садржај. Потрудите се да >60% текста странице буде ≥12 пиксела. [Сазнајте више о читљивим величинама фонтова](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} читљивог текста"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Текст није читљив јер не постоји метаознака области приказа која је оптимизована за екране на мобилним уређајима."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Документ не користи читљиве величине фонтова"}, "core/audits/seo/font-size.js | legibleText": {"message": "Читљив текст"}, "core/audits/seo/font-size.js | title": {"message": "Документ користи читљиве величине фонтова"}, "core/audits/seo/hreflang.js | description": {"message": "Линкови hreflang обавештавају претраживаче коју верзију странице треба да наведу у резултатима претраге за дати језик или регион. [Сазнајте више о линковима `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Документ нема важећи атрибут `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Рела<PERSON>ивна href вредност"}, "core/audits/seo/hreflang.js | title": {"message": "Документ има важећи атрибут `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Неочекивани кôд језика"}, "core/audits/seo/http-status-code.js | description": {"message": "Странице са неуспешним HTTP кодовима статуса можда неће бити правилно индексиране. [Сазнајте више о HTTP кодовима статуса](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Страница има неуспешан HTTP кôд статуса"}, "core/audits/seo/http-status-code.js | title": {"message": "Страница има успешан HTTP кôд статуса"}, "core/audits/seo/is-crawlable.js | description": {"message": "Претраживачи не могу да уврсте странице у резултате претраге ако немају дозволу да их пописују. [Сазнајте више о директивама пописивача](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Индексирање странице је блокирано"}, "core/audits/seo/is-crawlable.js | title": {"message": "Индексирање странице није блокирано"}, "core/audits/seo/link-text.js | description": {"message": "Описни текст у линковима помаже претраживачима да разумеју садржај. [Сазнајте како да учините линкове приступачнијим](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 линк}one{Пронађен је # линк}few{Пронађена су # линка}other{Пронађено је # линкова}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Линкови немају описни текст"}, "core/audits/seo/link-text.js | title": {"message": "Линкови имају описни текст"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Покрените [алатку за тестирање структурираних података](https://search.google.com/structured-data/testing-tool/) и [алатку за анализирање структурираних података](http://linter.structured-data.org/) да бисте проценили структуриране податке. [Сазнајте више о структурираним подацима](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Структурирани подаци су важећи"}, "core/audits/seo/meta-description.js | description": {"message": "Метаописи могу да буду уврштени у резултате претраге да би пружили сажети резиме садржаја странице. [Сазнајте више о метаопису](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Поље за текст описа је празно."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Документ нема метаопис"}, "core/audits/seo/meta-description.js | title": {"message": "Документ има метаопис"}, "core/audits/seo/plugins.js | description": {"message": "Претраживачи не могу да индексирају садржај додатних компонената, а многи уређаји ограничавају додатне компоненте или их не подржавају. [Сазнајте више о избегавању додатних компоненти](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Документ користи додатне компоненте"}, "core/audits/seo/plugins.js | title": {"message": "Документ избегава додатне компоненте"}, "core/audits/seo/robots-txt.js | description": {"message": "Ако фајл robots.txt није правилно направљен, пописивачи можда неће моћи да разумеју како желите да се веб-сајт попише или индексира. [Сазнајте више о фајлу robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Захтев за датотеку robots.txt вратио је HTTP статус: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Пронађена је 1 грешка}one{Пронађена је # грешка}few{Пронађене су # грешке}other{Пронађено је # грешака}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse није успео да преузме датотеку robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Датотека robots.txt није важећа"}, "core/audits/seo/robots-txt.js | title": {"message": "Датотека robots.txt је важећа"}, "core/audits/seo/tap-targets.js | description": {"message": "Интерактивни елементи попут дугмади и линкова треба да буду довољно велики (48×48 пиксела) и да имају довољно простора око себе да би било лако да се додирну без преклапања са другим елементима. [Сазнајте више о циљевима за додир](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} циљева додиривања има одговарајућу величину"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Циљеви додиривања су премали јер не постоји метаознака области приказа која је оптимизована за екране на мобилним уређајима"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Циљеви додиривања немају одговарајућу величину"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Циљ који се преклапа"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Циљ додиривања"}, "core/audits/seo/tap-targets.js | title": {"message": "Циљеви додиривања имају одговарајућу величину"}, "core/audits/server-response-time.js | description": {"message": "Време одговора сервера за главни документ треба да буде кратко јер сви други захтеви зависе од њега. [Сазнајте више о показатељу Време до првог бајта](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Основном документу је требало {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Скратите почетно време одговора сервера"}, "core/audits/server-response-time.js | title": {"message": "Почетно време одговора сервера је било кратко"}, "core/audits/service-worker.js | description": {"message": "Сервисер је технологија која омогућава апликацији да користи многе функције прогресивних веб-апликација, попут офлајн рада, додавања на почетни екран и искачућих обавештења. [Сазнајте више о сервисерима](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Ову страницу контролише сервисер, али није пронађен ниједан `start_url` јер није успело рашчлањивање манифеста као важеће JSON датотеке"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Ову страницу контролише сервисер, али `start_url` ({startUrl}) не спада у опсег сервисера ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Ову страницу контролише сервисер, али није пронађен ниједан `start_url` јер ниједан манифест није преузет."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Овај извор има један или више сервисера, али страница ({pageUrl}) није у опсегу."}, "core/audits/service-worker.js | failureTitle": {"message": "Не региструје сервисер који контролише страницу и `start_url`"}, "core/audits/service-worker.js | title": {"message": "Региструје сервисер који контролише страницу и `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Тематски уводни екран обезбеђује квалитетан доживљај када корисници покрећу апликацију са почетних екрана. [Сазнајте више о уводним екранима](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Није конфигурисано за прилагођени уводни екран"}, "core/audits/splash-screen.js | title": {"message": "Конфигурисано за прилагођени уводни екран"}, "core/audits/themed-omnibox.js | description": {"message": "Трака за адресу прегледача може да има тему која одговара сајту. [Сазнајте више о томе како се тематизује трака за адресу](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Не подешава боју теме за траку за адресу."}, "core/audits/themed-omnibox.js | title": {"message": "Подешава боју теме за траку за адресу."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (успех клијената)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (маркетинг)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (друштвене мреже)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (видео)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Производ"}, "core/audits/third-party-facades.js | description": {"message": "Нека уграђивања трећих страна могу споро да се учитавају. Предлажемо да их замените фасадом док не буду потребна. [Сазнајте како да одложите треће стране помоћу фасаде](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Доступна је # алтернативна фасада}one{Доступна је # алтернативна фасада}few{Доступне су # алтернативне фасаде}other{Доступно је # алтернативних фасада}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Неки ресурси трећих страна могу одложено да се учитавају помоћу фасаде"}, "core/audits/third-party-facades.js | title": {"message": "Одложено учитавајте ресурсе трећих страна помоћу фасада"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Независни добављач"}, "core/audits/third-party-summary.js | description": {"message": "Кôд независног добављача може значајно да утиче на учинак учитавања. Ограничите број сувишних независних добављача услуге и пробајте да учитате кôд независног добављача када страница примарно заврши са учитавањем. [Сазнајте како да смањите утицај треће стране](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Кôд независног добављача је блокирао главну нит {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Смањите утицај кода независног добављача"}, "core/audits/third-party-summary.js | title": {"message": "Смањите коришћење садржаја треће стране"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Мерење"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Показатељ"}, "core/audits/timing-budget.js | description": {"message": "Подесите буџет за брзину учитавања да бисте лакше пратили учинак сајта. Сајтови са добрим учинком се учитавају за кратко време и брзо реагују на догађаје уноса корисника. [Сазнајте више о ограничењима учинка](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Буџет за брзину учитавања"}, "core/audits/unsized-images.js | description": {"message": "Подесите експлицитну ширину и висину у елементима слика ради смањења прелаза изгледа и побољшања кумулативног помака садржаја странице. [Сазнајте како да подесите димензије слике](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Елементи слика немају експлицитне: `width` и `height`"}, "core/audits/unsized-images.js | title": {"message": "Елементи слика имају експлицитне: `width` и `height`"}, "core/audits/user-timings.js | columnType": {"message": "Тип"}, "core/audits/user-timings.js | description": {"message": "Препоручујемо вам да опремите апликацију API-јем за време корисника да бисте измерили учинак апликације у реалном свету током кључних корисничких доживљаја. [Сазнајте више о ознакама Време корисника](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 време корисника}one{# време корисника}few{# времена корисника}other{# времена корисника}}"}, "core/audits/user-timings.js | title": {"message": "Ознаке и мере Времена корисника"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Повезивање унапред `<link rel=preconnect>` је пронађено за „{securityO<PERSON><PERSON>}“, али га прегледач није употребио. Проверите да ли правилно користите атрибут `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Размислите о томе да додате савете за ресурсе `preconnect` или `dns-prefetch` како бисте успоставили ране везе са важним изворима трећих страна. [Сазнајте како да се унапред повежете са обавезним изворима](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Повежите се унапред са потребним изворима"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Пронађено је више од 2 повезивања `<link rel=preconnect>`. Она треба да се користе ретко и само до најважнијих извора."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Повезивање унапред `<link rel=preconnect>` је пронађено за „{securityOrigin}“, али га прегледач није употребио. Користите `preconnect` само за важне изворе које ће страница сигурно захтевати."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Предучитавање `<link>` је пронађено за „{preloadURL}“, али га прегледач није употребио. Проверите да ли правилно користите атрибут `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Препоручујемо вам да користите `<link rel=preload>` како бисте касније током учитавања странице дали приоритет преузимању ресурса који се тренутно траже. [Сазнајте како да унапред учитате кључне захтеве](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Унапред учитајте најважније захтеве"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL мапе"}, "core/audits/valid-source-maps.js | description": {"message": "Изворне мапе преводе умањен кôд у оригинални изворни кôд. То помаже програмерима при отклањању грешака у производњи. Поред тога, Lighthouse садржи додатни увид. Не би било лоше да примените изворне мапе да бисте могли да користите те предности. [Сазнајте више о изворним мапама](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Недостају изворне мапе за велики JavaScript прве стране"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Великом JavaScript фајлу недостаје изворна мапа"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Упозорење: недостаје 1 ставка у атрибуту `.sourcesContent`}one{Упозорење: недостаје # ставка у атрибуту `.sourcesContent`}few{Упозорење: недостају # ставке у атрибуту `.sourcesContent`}other{Упозорење: недостаје # ставки у атрибуту `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Страница има важеће изворне мапе"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` оптимизује апликацију за величине екрана мобилних телефона, али и спречава [кашњење од 300 милисекунди при уносу корисника](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Сазнајте више о коришћењу метаознаке области приказа](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Није пронађена ознака `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Нема ознаку `<meta name=\"viewport\">` са ознакама `width` или `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Има ознаку `<meta name=\"viewport\">` са ознаком `width` или `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "То је рад на блокирању нити које се одвија током мерења интеракције до следећег приказивања. [Сазнајте више о показатељу Интеракција до следећег приказивања](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms је потрошено на догађај „{interactionType}“"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Циљ догађаја"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Умањите рад током кључне интеракције"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Кашњење уноса"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Кашњење презентације"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Време обраде"}, "core/audits/work-during-interaction.js | title": {"message": "Смањује рад током кључне интеракције"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "То су прилике да побољшате коришћење ARIA улога у апликацији, чиме може да побољша доживљај корисника технологије за помоћ особама са инвалидитетом, као што је читач екрана."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "То су прилике да пружите алтернативни садржај за аудио и видео датотеке. То може да побољша доживљај за кориснике са оштећеним слухом или видом."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Звук и видео"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ове ставке истичу уобичајене најбоље праксе у вези са приступачношћу."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Најбоље праксе"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ове провере истичу прилике за [побољшање приступачности веб-апликације](https://developer.chrome.com/docs/lighthouse/accessibility/). Аутоматски може да се открије само један подскуп проблема са приступачношћу, па препоручујемо да обављате и ручно тестирање."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ове ставке обрађују области које алатка за аутоматизовано тестирање не може да обухвати. Сазнајте више у водичу о [спровођењу прегледа приступачности](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Приступачност"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "То су прилике да побољшате читљивост садржаја."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "То су прилике да побољшате тумачење свог садржаја за кориснике на различитим језицима."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Интернационализација и локализација"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "То су прилике да побољшате семантику контрола у апликацији. То може да побољша доживљај корисника технологије за помоћ особама са инвалидитетом, као што је читач екрана."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Називи и ознаке"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ово су прилике да побољшате кретање по тастатури у апликацији."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навигација"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ово су прилике за побољшање доживљаја при читању података из табела или листа помоћу технологије за помоћ особама са инвалидитетом, попут читача екрана."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Табеле и листе"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Компатибилност прегледача"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Најбоље праксе"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Опште"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Поверење и безбедност"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Кориснички доживљај"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Циљевима за учинак одређују се стандарди за учинак сајта."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Циљеви"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Више информација о учинку апликације. Ови бројеви не [утичу директно](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) на оцену учинка."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Дијагностика"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Најважнији аспект учинка је брзина којом се пиксели приказују на екрану. Кључни показатељи: Прво приказивање садржаја, Прво значајно приказивање"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Побољшања првог приказивања"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ови предлози могу да вам помогну да се страница учитава брже. Не [утичу директно](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) на оцену учинка."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Могућности"}, "core/config/default-config.js | metricGroupTitle": {"message": "Показатељи"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Побољшајте општи доживљај учитавања да би страница почела да се одазива и да би била спремна за коришћење у најкраћем могућем року. Кључни показатељи: Време почетка интеракције, Индекс брзине"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Општа побољшања"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Учинак"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Овим проверама се проверавају аспекти прогресивне веб-апликације. [Сазнајте шта чини добру прогресивну веб-апликацију](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ове провере захтева основна [Контролна листа за прогресивне веб-апликације](https://web.dev/pwa-checklist/), али их Lighthouse не спроводи аутоматски. Оне не утичу на ваш резултат, али је важно да их ручно потврдите."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Може да се инсталира"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимизовано за PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Захваљујући овим проверама страница ће сигурно бити у складу са основним саветима за оптимизацију за претраживаче. Има много додатних фактора које Lighthouse овде не узима у обзир, а који могу да утичу на рангирање у претрази, укључујући учинак у [Основним показатељима за веб](https://web.dev/learn-core-web-vitals/). [Сазнајте више о основама Google претраге](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Покрећите ове додатне валидаторе на сајту да бисте проверили додатне најбоље праксе оптимизације за претраживаче."}, "core/config/default-config.js | seoCategoryTitle": {"message": "Оптимизација за претраживаче"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Форматирајте HTML садржај на начин који омогућава пописивачима да боље разумеју садржај апликације."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Најбоље праксе за садржај"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Да би се апликација појавила у резултатима претраге, пописивачи треба да имају приступ до ње."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Пописивање и индексирање"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Уверите се да су странице прилагођене мобилним уређајима да корисници не би морали да умањују или увећавају приказ како би читали странице са садржајем. [Сазнајте како да прилагодите странице мобилним уређајима](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Прилагођено мобилним уређајима"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Изгледа да тестирани уређај има спорији процесор него што  Lighthouse очекује. То може негативно да утиче на оцену учинка. Сазнајте више о [калибрисању одговарајућег множиоца у вези са успоравањем процесора](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Страница се можда не учитава на очекиван начин зато што је пробни URL ({requested}) преусмерен на {final}. Пробајте директно да тестирате други URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Учитавање странице је било преспоро да би се довршило у оквиру временског ограничења. Резултати могу да буду непотпуни."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Временско ограничење за брисање кеша прегледача је истекло. Пробајте поново да проверите ову страницу и пријавите грешку ако се проблем настави."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{На овој локацији можда има сачуваних података који утичу на ефикасност учитавања: {locations}. Проверите ту страницу у прозору без архивирања да бисте спречили да ти ресурси утичу на резултате.}one{На овим локацијама можда има сачуваних података који утичу на ефикасност учитавања: {locations}. Проверите ту страницу у прозору без архивирања да бисте спречили да ти ресурси утичу на резултате.}few{На овим локацијама можда има сачуваних података који утичу на ефикасност учитавања: {locations}. Проверите ту страницу у прозору без архивирања да бисте спречили да ти ресурси утичу на резултате.}other{На овим локацијама можда има сачуваних података који утичу на ефикасност учитавања: {locations}. Проверите ту страницу у прозору без архивирања да бисте спречили да ти ресурси утичу на резултате.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Временско ограничење за брисање изворних података је истекло. Пробајте поново да проверите ову страницу и пријавите грешку ако се проблем настави."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Само странице учитане преко GET захтева испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Само странице са кодом статуса 2XX могу да се кеширају."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome је открио покушај извршавања JavaScript-а док је био у кешу."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Странице које су захтевале AppBanner тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Ознаке су онемогућиле кеширање целе странице. Посетите chrome://flags/#back-forward-cache да бисте га омогућили локално на уређају."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Командна линија је онемогућила кеширање целе странице."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Кеширање целе странице је онемогућено због недовољно меморије."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Делегат не подржава кеширање целе странице."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Функција за приказивање унапред је онемогућила кеширање целе странице."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Ова страница не може да се кешира јер има инстанцу BroadcastChannel са регистрованим слушаоцима."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Странице са заглављем cache-control:no-store не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Кеш је намерно обрисан."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Страница је уклоњена из кеша да би се дозволило кеширање друге странице."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Странице које имају додатне компоненте тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Странице које користе FileChooser API тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Странице које користе API за приступ систему фајлова тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Странице које користе диспечер за медијски уређај не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Репродукција из медија плејера је била у току при напуштању странице."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Странице које користе MediaSession API и подешавају статус репродукције не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Странице које користе MediaSession API и подешавају обрађиваче радњи не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Чит<PERSON>ч екрана је онемогућио кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Странице које користе SecurityHandler тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Странице које користе Serial API тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Странице које користе WebAuthetication API тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Странице које користе WebBluetooth API тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Странице које користе WebUSB API тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Странице које користе предвиђени обрађивач или радни задатак тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Документ није довршио учитавање пре напуштања документа."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "App Banner је био активан при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome менаџер лозинки је био активан при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM дестилација је била у току при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer је био активан при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Кеширање целе странице је онемогућено јер су додаци користили API за размену порука."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Додаци са трајном везом треба да затворе везу пре кеширања целе странице."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Додаци са трајном везом су покушали да шаљу поруке оквирима у кеширању целе странице."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Кеширање целе странице је онемогућено због додатака."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Модални дијалог као што је поновно слање обрасца или дијалог за HTTP лозинку је приказан при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Оф<PERSON><PERSON><PERSON>н страница је приказана при напуштању."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Трака за интервенцију у вези са недостатком меморије је била присутна при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Дошло је до слања захтева за дозволе при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Откривен је блокатор искачућих прозора при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Приказани су детаљи о Безбедном прегледању при напуштању странице."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Безбедно прегледање је открило да ова страница садржи злоупотребу и блокирало је искачући прозор."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Сервисер је активиран док је страница била у процесу кеширања целе странице."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Кеширање целе странице је онемогућено због грешке у документу."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Странице које користе FencedFrames не могу да се складиште у кешу целе странице."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Страница је уклоњена из кеша да би се дозволило кеширање друге странице."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Странице које су одобриле приступ за стримовање медија тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Странице које користе портале тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Странице које користе IdleManager тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Странице које имају отворену IndexedDB везу тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Користе се API-ји који не испуњавају услове."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Странице на којима се StyleSheet умеће помоћу додатака тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Странице на којима се StyleSheet умеће помоћу додатака тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Интерна грешка."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Кеширање целе странице је онемогућено због захтева за одржавање линка."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Странице које користе закључавање тастатуре тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | loading": {"message": "Страница није довршила учитавање пре напуштања странице."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Странице чији главни ресурс има cache-control:no-cache не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Странице чији главни ресурс има cache-control:no-store не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Кретање је отказано пре него што је страница могла да буде враћена из кеша целе странице."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Страница је уклоњена из кеша јер је активна мрежна веза примила превише података. Chrome ограничава количину података коју страница може да прими док је кеширана."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Странице које имају преузимање() или XHR у току не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Страница је уклоњена из кеширања целе странице јер је активан мрежни захтев обухватао преусмеравање."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Страница је уклоњена из кеша јер је мрежна веза била предуго отворена. Chrome ограничава време које страница има за примање података док је кеширана."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Странице које немају исправно заглавље одговора не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Кретање се десило у оквиру који није главни оквир."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Странице са активним индексираним DB трансакцијама тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Странице са активним захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Странице са активним захтевом за преузимање мреже тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Странице са активним захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Странице са активним XHR захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Странице које користе PaymentManager тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Странице које користе функцију Слика у слици тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | portal": {"message": "Странице које користе портале тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | printing": {"message": "Странице које приказују кориснички интерфејс за штампање тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Страница је отворена помоћу метода `window.open()`, а друга картица садржи референцу на њу или је страница отворила прозор."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Отказао је процес рендеровања за страницу у кешу целе странице."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Процес рендеровања за страницу у кешу целе странице је прекинут."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Странице које захтевају дозволе за снимање аудио садржаја тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Странице које захтевају дозволе за сензоре тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Странице које захтевају синхронизацију у позадини или дозволе за преузимање тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Странице које захтевају дозволе за MIDI тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Странице које захтевају дозволе за обавештења тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Странице које захтевају приступ меморијском простору тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Странице које захтевају дозволе за снимање видеа тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Само странице чија шема URL-а је HTTP или HTTPS могу да се кеширају."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Страницу је преузео сервисер док је кеширање целе странице у току."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Сервисер је покушао да пошаље страницу која је у процесу кеширања целе странице атрибуту `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Регистрација за ServiceWorker је опозвана док је било у току кеширање целе странице."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Страница је уклоњена из кеширања целе странице због активације сервисера."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome је рестартовао и обрисао уносе кеширања целе странице."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Странице које користе SharedWorker тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Странице које користе SpeechRecognizer тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Странице које користе SpeechSynthesis тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "iframe на страници је започео кретање које се није завршило."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Странице чији подресурс има cache-control:no-cache не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Странице чији подресурс има cache-control:no-store не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Страница је премашила максимално време за кеширање целе странице и истекла је."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Истекло је време да страница приступи кеширању целе странице (вероватно због обрађивача сакривања странице који су дуго били покренути)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Страница има unload обрађивач у главном оквиру."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Страница има unload обрађивач у подоквиру."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Прегледач је променио заглавље замене корисничког агента."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Странице које су одобриле приступ за снимање видео или аудио садржаја тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Странице које користе WebDatabase тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Странице које користе WebHID тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Странице које користе WebLocks тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Странице које користе WebNfc тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Странице које користе WebOTPService тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Странице са WebRTC-ом не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Странице које користе WebShare тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Кеширање целих страница са WebSocket-ом није могуће."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Странице са WebTransport-ом не могу да приступе кеширању целе странице."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Странице које користе WebXR тренутно не испуњавају услове за кеширање целе странице."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Можете да додате https: и http: URL шеме (које игноришу прегледачи који подржавају strict-dynamic) ради компатибилности уназад са старијим прегледачима."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Директива disown-opener је застарела од смерница CSP3. Користите заглавље за Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Директива referrer је застарела од смерница CSP2. Користите заглавље за Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Директива reflected-xss је застарела од смерница CSP2. Користите заглавље за X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Base-uri који недостаје омогућава уметнутим ознакама <base> да подесе основни URL за све релативне URL-ове (нпр. скрипте) до домена који контролише нападач. Саветујемо вам да подесите base-uri на „none“ или „self“."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Ако директива object-src недостаје, дозвољено је уметање додатних компоненти које извршавају небезбедне скрипте. Саветујемо вам да подесите директиву object-src на „none“ ако можете."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Директива script-src недостаје. То може да омогући извршавање небезбедних скрипти."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Да ли сте заборавили тачку и зараз? Изгледа да је {keyword} дирекива, а не кључна реч."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Једнократни кључеви морају да користе base64 charset."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Једнократни кључеви морају да имају бар 8 знакова."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Избегавајте да користите обичне URL шеме ({keyword}) у овој директиви. Обичне URL шеме дозвољавају да извор скрипти буде небезбедан домен."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Избегавајте да користите обичне џокерске знаке ({keyword}) у овој директиви. Обични џокерски знаци омогућавају да извор скрипти буде небезбедан домен."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Одредиште за извештаје се конфигурише само преко директиве report-to. Ова директива је подржана само у прегледачима заснованим на Chromium-у, па се препоручује да користите директиву report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Ниједан CSP не конфигурише одредиште за извештаје. Тиме се отежава одржавање CSP-а током времена и праћење отказивања."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Листе дозвољених хостова често могу да се заобиђу. Саветујемо вам да користите CSP једнократне кључеве или хешеве, уз „strict-dynamic“ ако је потребно."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Непозната CSP директива."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "Изгледа да је {keyword} неважећа кључна реч."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Директива unsafe-inline омогућава извршавање небезбедних скрипти и обрађивача догађаја на страници. Саветујемо вам да користите CSP једнократне кључеве или хешеве да бисте појединачно дозвољавали скрипте."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Саветујемо вам да додате директиву unsafe-inline (коју игноришу прегледачи који подржавају једнократне кључеве/хешове) ради компатибилности уназад са старијим прегледачима."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Овлашћење неће бити покривено симболом џокерског знака (*) у оквиру CORS управљања атрибутом `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Захтеви за ресурсе чији URL-ови су садржали уклоњене знакове за размак (`(n|r|t)`) и знакове „мање-од“ (`<`) су блокирани. Уклоните нове редове и кодирајте знакове „мање-од“ из извора као што су вредности атрибута елемената да би се учитали ти ресурси."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "Атрибут `chrome.loadTimes()` је застарео, па користите стандардизовани API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "Атрибут `chrome.loadTimes()` је застарео, па користите стандардизовани API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` је застарели тип, па користите стандардизовани API: `nextHopProtocol` за Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Колачићи који садрже знак `(0|r|n)` ће бити одбијени, а не скраћени."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Попуштање смерница за исто порекло подешавањем атрибута `document.domain` је застарело и биће подразумевано онемогућено. Ово упозорење о застаревању се односи на приступ различитог порекла који је омогућен подешавањем атрибута `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Покретање {PH1} из iframe-ова различитог порекла је застарело и уклониће се у будућности."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Атрибут `disableRemotePlayback` треба да се користи да би се онемогућила подразумевана интеграција за пребацивање уместо коришћења бирача `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} је застарели тип. Уместо њега користите {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Ово је пример преведене поруке о проблему са застаревањем."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Попуштање смерница за исто порекло подешавањем атрибута `document.domain` је застарело и биће подразумевано онемогућено. Да бисте наставили да користите ову функцију онемогућите групе агената са кључевима према пореклу тако што ћете послати заглавље атрибута `Origin-Agent-Cluster: ?0` заједно са HTTP одговором за документ и оквире. Погледајте https://developer.chrome.com/blog/immutable-document-domain/ за више детаља."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Атрибут `Event.path` је застарео и уклониће се. Уместо њега користите `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Заглавље `Expect-CT` је застарело и биће уклоњено. Chrome захтева транспарентност сертификата за све јавно поуздане сертификате издате после 30. априла 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Више детаља потражите на страници за статус функције."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` и`watchPosition()` више не раде са небезбедним пореклом. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` и`watchPosition()` су застарели или небезбедни извори. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` више не ради са небезбедним пореклом. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` је застарели тип. Уместо њега користите `RTCPeerConnectionIceErrorEvent.address` или `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Порекло продавца и произвољни подаци из догађаја сервисeра `canmakepayment` су застарели и биће уклоњени: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Веб-сајт је затражио подизвор са мреже којој је могао да приступи само због привилеговане мрежне позиције својих корисника. Ови захтеви излажу уређаје и сервере који нису јавни интернету, чиме се повећава ризик од напада фалсификовањем захтева са других сајтова (CSRF) и/или цурења информација. Да би ублажио ове ризике, Chrome застарева захтеве ка подизворима који нису јавни када се покрену из небезбедног контекста и почеће да их блокира."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS не може да се учита са `file:` URL-ова ако се не завршавају екстензијом фајла `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Коришћење атрибута `SourceBuffer.abort()` да би се отказало уклањање асинхроног опсега за `remove()` је застарело због промене спецификације. Подршка ће се уклонити у будућности. Уместо њега треба да слушате догађај `updateend`. `abort()` има за циљ само да откаже додавање асинхроних медија или да ресетује стање рашчлањивача."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Подешавање атрибута `MediaSource.duration` испод највише временске ознаке презентације било којих баферованих кодираних оквира је застарело због промене спецификације. Подршка за имплицитно уклањање скраћеног баферованог медијског садржаја ће се уклонити у будућности. Уместо тога треба да извршите експлицитни `remove(newDuration, oldDuration)` на све `sourceBuffers`, где је `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Ова измена ће ступити на снагу са циљем {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI ће затражити дозволу за коришћење чак и ако SysEx није наведен у атрибуту `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "API за обавештења више не сме да се користи из незбезбедног порекла. Размислите о пребацивању апликације на безбедно порекло, као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Дозвола за API за обавештења више не може да се тражи од iframe-а различитог порекла. Размислите о томе да затражите дозволу од оквира највишег нивоа или да отворите нови прозор."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Ваш партнер користи застарелу (D)TLS верзију. Проверите са партнером да бисте исправили ово."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL у небезбедним контекстима је застарео и ускоро ће бити уклоњен. Користите меморијски простор на вебу или индексирану базу података."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Ако наведете `overflow: visible` на ознакама img, video и canvas, оне могу да доведу до прављења визуелног садржаја ван граница елемента. Погледајте https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` је застарели тип. Боље да користите једнократно инсталирање за обрађиваче плаћања."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest` позив је заобишао директиву `connect-src` за смернице за безбедност садржаја (CSP). Ово заобилажење је застарело. Додајте идентификатор начина плаћања из API-ја `PaymentRequest` (у пољу `supportedMethods`) у директиву `connect-src` за CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` је застарели тип. Користите стандардизовани `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Атрибут `<source src>` са надређеним елементом `<picture>` је неважећи, па се игнорише. Уместо њега користите `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` је застарели тип. Користите стандардизовани `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Блокирани су захтеви за подресурсе чији URL-ови садрже уграђене акредитиве (нпр. `**********************/`)."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Ограничење `DtlsSrtpKeyAgreement` је уклоњено. Навели сте вредност `false` за ово ограничење, што се тумачи као покушај коришћења уклоњеног метода `SDES key negotiation`. Ова функција је уклоњена, па користите услугу која подржава `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Ограничење `DtlsSrtpKeyAgreement` је уклоњено. Навели сте вредност `true` за ово ограничење, што није имало ефекта, али можете да уклоните ово ограничење ради прегледности."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Откривен је атрибут `Complex Plan B SDP`. Ди<PERSON><PERSON><PERSON>ект за `Session Description Protocol` више није подржан. Уместо њега користите `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, који се користи при изради атрибута `RTCPeerConnection` уз `{sdpSemantics:plan-b}` је застарела нестандардна верзија атрибута `Session Description Protocol` који је трајно избрисан са веб-платформе. И даље је доступан при изради уз `IS_FUCHSIA`, али намеравамо да га избришемо чим то буде могуће. Обуставите зависност од њега. Погледајте https://crbug.com/1302249 за статус."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Опција `rtcpMuxPolicy` је застарела и биће уклоњена."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` ће захтевати изолацију од приступа из других извора. Погледајте https://developer.chrome.com/blog/enabling-shared-array-buffer/ за више детаља."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Атрибут `speechSynthesis.speak()` без активације корисника је застарео и уклониће се."}, "core/lib/deprecations-strings.js | title": {"message": "Користи се застарела функција"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Додаци треба да омогуће изолацију од приступа из других извора да бисте и даље користили `SharedArrayBuffer`. Погледајте https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} зависи од продавца. Користите стандардни {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "JSON за одговор не подржава UTF-16 у атрибуту `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхрони `XMLHttpRequest` у главној нити је застарео због негативног утицаја на доживљај крајњег корисника. Додатну помоћ потражите на https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` је застарели тип. Користите `isSessionSupported()` и проверите решену логичку вредност."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Период блокирања главне нити"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Време преживљавања кеша"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Траја<PERSON>е"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Елемент"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Елементи који нису прошли проверу"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Локација"}, "core/lib/i18n/i18n.js | columnName": {"message": "Назив"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Премашује циљ"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Захтеви"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Величина ресурса"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурса"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Величина"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Извор"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Време почетка"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Проведено време"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Величина преноса"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенциј<PERSON><PERSON>на уштеда"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенциј<PERSON><PERSON>на уштеда"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенциј<PERSON><PERSON>на уштеда од {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Пронађен је 1 елемент}one{Пронађен је # елемент}few{Пронађена су # елемента}other{Пронађено је # елемената}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенција<PERSON>на уштеда од {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Прво значајно приказивање"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Фонт"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Слика"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Интеракција до следећег приказивања"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Висока"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Ниска"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Средња"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Макс. потенцијално кашњење првог приказа"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Мед<PERSON><PERSON>и"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Друго"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Остали ресурси"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Скрипта"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} сек"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> стила"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Независни ресурси"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Укупно"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Дошло је до грешке при евидентирању трага током учитавања странице. Поново покрените Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Временско ограничење чекања на иницијалну везу за протокол програма за отклањање грешака."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome није прикупио ниједан снимак екрана током учитавања странице. Уверите се да је садржај видљив на страници, па пробајте да поново покренете Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS сервери нису могли да разреше наведени домен."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Прикупљач за обавезни ресурс {artifactName} је наишао на грешку: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Дошло је до интерне грешке у Chrome-у. Поново покрените Chrome и пробајте да поново покренете Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Прикупљач за обавезни ресурс {artifactName} се није покренуо."}, "core/lib/lh-error.js | noFcp": {"message": "Страница није приказала никакав садржај. Уверите се да је прозор прегледача у првом плану током учитавања и пробајте поново. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "На страници се није приказао садржај који се квалификује као највеће приказивање садржаја (LCP). Уверите се да страница има важећи LCP елемент и пробајте поново. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Наведена страница није HTML (приказује се као {mimeType} MIME типа)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ова верзија Chrome-а је престара да би подржавала „{featureName}“. Користите новију верзију да бисте видели комплетне резултате."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse није успео да поуздано учита URL који сте захтевали јер је страница престала да реагује."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL који сте навели нема важећи безбедносни сертификат. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome је спречио учитавање странице са транзитивним огласом. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве. (Детаљи: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве. (К<PERSON>д статуса: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Учитавање странице је трајало предуго. Пратите прилике у извештају да бисте скратили време учитавања странице, па поново покрените Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Одговор протокола DevTools се чека дуже од додељеног периода. (Метод: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Преузимање садржаја ресурса траје дуже од додељеног периода."}, "core/lib/lh-error.js | urlInvalid": {"message": "Изгледа да је URL који сте навели неважећи."}, "core/lib/navigation-error.js | warningXhtml": {"message": "MIME тип странице је XHTML: Lighthouse не подржава изричито овај тип документа"}, "core/user-flow.js | defaultFlowName": {"message": "Кориснички ток ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Извештај о навигацији ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Извештај са прегледом ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Извештај за период ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Сви извештаји"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Категорије"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Приступачност"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Најбоље праксе"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Учинак"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Прогре<PERSON>и<PERSON>на веб-апликација"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "Оптимизација за претраживаче"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Разумевање извештаја о току за Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Разумевање токова"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Користите извештаје о навигацији за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Користите извештаје са прегледом за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Користите извештаје за период за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Преузмите Lighthouse оцену учинка."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Измерите показатеље учинка за учитавање странице, као што су највеће приказивање садржаја и индекс брзине."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Приступите могућностима прогресивних веб-апликација."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Пронађите проблеме са приступачношћу у апликацијама са једном страницом или комплексним формама."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Процените најбоље праксе за меније и елементе корисничког интерфејса сакривене иза интеракције."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Измерите време извршавања прелаза изгледа и JavaScript-а за серију интеракција."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Откријте прилике за учинак да бисте побољшали доживљај за дугорочне странице и апликације са једном страницом."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Највећи утицај"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} информативна провера}one{{numInformative} информативна провера}few{{numInformative} информативне провере}other{{numInformative} информативних провера}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Мобилни уређај"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Учитавање странице"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Извештаји о навигацији анализирају учитавање појединачне странице, потпуно исто као оригинални Lighthouse извештаји."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Извештај о навигацији"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} извештај о навигацији}one{{numNavigation} извештај о навигацији}few{{numNavigation} извештаја о навигацији}other{{numNavigation} извештаја о навигацији}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} провера која може да се прође}one{{numPassableAudits} провера која може да се прође}few{{numPassableAudits} провере које могу да се прођу}other{{numPassableAudits} провера које могу да се прођу}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{Прошли сте {numPassed} проверу}one{Прошли сте{numPassed} проверу}few{Прошли сте{numPassed} провере}other{Прошли сте{numPassed} провера}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Просек"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Грешка"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Слабо"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Добро"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Сачувај"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Снимљено стање странице"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Извештаји са прегледом анализирају страницу у посебном стању, обично после интеракције са корисницима."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Извештај са прегледом"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} извештај са прегледом}one{{numSnapshot} извештај са прегледом}few{{numSnapshot} извештаја са прегледом}other{{numSnapshot} извештаја са прегледом}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Резиме"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Корисничке интеракције"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Извештаји за период анализирају насумични период, који обично садржи интеракције корисника."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Извештај за период"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} извештај за период}one{{numTimespan} извештај за период}few{{numTimespan} извештаја за период}other{{numTimespan} извештаја за период}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Извештај о корисничком току за Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Када је у питању анимирани садржај, користите [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) да бисте смањили коришћење процесора када садржај није на екрану."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Размислите о томе да прикажете све компоненте [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) у WebP форматима уз навођење прикладног резервног решења за друге прегледаче. [Сазнајте више](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Уверите се да користите ознаке [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) за слике како би се оне аутоматски одложено учитавале. [Сазнајте више](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Користите алатке као што је [AMP оптимизатор](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) да бисте [приказивали AMP распореде на серверу](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Погледајте [документацију за AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) да бисте се уверили да су сви стилови подржани."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Компонента [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) подржава атрибут [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) да би назначила које сликовне елементе да користи на основу величине екрана. [Сазнајте више](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Размислите о томе да користите виртуелно померање помоћу Component Dev Kit-а (CDK) ако се приказују веома велике листе. [Сазнајте више](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Примените [раздвајање кода на нивоу руте](https://web.dev/route-level-code-splitting-in-angular/) да бисте смањили величину JavaScript пакета. Такође размислите о томе да унапред кеширате елементе помоћу [сервисер<PERSON> Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Ако користите Angular CLI, уверите се да су верзије генерисане у производном режиму. [Сазнајте више](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Ако користите Angular CLI, уврстите мапе извора у производну верзију да бисте прегледали пакете. [Сазнајте више](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Учитајте руте унапред да бисте убрзали навигацију. [Сазнајте више](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Размислите о томе да користите услужни програм `BreakpointObserver` у Component Dev Kit-у (CDK) да бисте управљали преломним тачкама слика. [Сазнајте више](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Предлажемо да отпремите GIF у услугу која ће га кодирати за уградњу као HTML5 видео."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Наведите `@font-display` кад дефинишете прилагођене фонтове у теми."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Предлажемо да конфигуришете [WebP формате слика помоћу стила Конверзија слике](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) на сајту."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Инсталирајте [Drupal модул](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) који може одложено да учитава слике. Такви модули пружају могућност одлагања свих слика ван екрана да би се побољшао учинак."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Предлажемо да користите модул за уградњу кључног CSS-а и JavaScipt-а или потенцијално асинхроно учитавање елемената путем JavaScript-а, попут модула [Напредно CSS/JS груписање](https://www.drupal.org/project/advagg). Имајте на уму да оптимизације које пружа овај модул могу да оштете сајт, па ћете вероватно морати да унесете неке промене у кôд."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Теме, модули и спецификације сервера доприносе времену одговора сервера. Предлажемо да пронађете оптимизованију тему, пажљиво изаберете модул за оптимизацију и/или надоградите сервер. Сервери за хостовање треба да користе кеширање PHP опкода, кеширање меморије ради смањења времена одговарања на упит из базе података, попут Redis-а или Memcached-а, као и оптимизовану логику апликације ради брже припреме страница."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Предлажемо да користите [прилагодљиве стилове слика](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) како бисте смањили величину слика које се учитавају на страници. Ако користите Views (прикази) да бисте приказали више ставки садржаја на страници, размислите о примени нумерисања страница да бисте ограничили број ставки садржаја које се приказују на одређеној страници."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Уверите се да сте омогућили Aggregate CSS files (Груписање CSS датотека) на страници Administration (Администрација) » Configuration (Конфигурација) » Development (Развој). Можете и да конфигуришете напредније опције груписања путем [додатних модула](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) како бисте убрзали сајт надовезивањем, умањивањем и компримовањем CSS стилова."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Уверите се да сте омогућили Aggregate JavaScript files (Груписање JavaScript датотека) на страници Administration (Администрација) » Configuration (Конфигурација) » Development (Развој). Можете и да конфигуришете напредније опције груписања путем [додатних модула](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) како бисте убрзали сајт надовезивањем, умањивањем и компримовањем JavaScript елемената."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Предлажемо да уклоните CSS правила која се не користе и приложите само неопохдне Drupal библиотеке релевантној страници или компоненти на страници. Детаље потражите на [линку за Drupal документацију](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Да бисте идентификовали приложене библиотеке које додају сувишан CSS, пробајте да покренете проверу [покривености кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Спорну тему или модул можете да идентификујете у URL-у описа стила кад је на Drupal сајту онемогућено CSS груписање. Потражите теме или модуле који на листи имају много описа стила са доста црвених елемената у покривености кода. Тема или модул треба да ставе опис стила на листу само ако се он стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Предлажемо да уклоните JavaScript елементе који се не користе и приложите само неопходне Drupal библиотеке релевантној страници или компоненти на страници. Детаље потражите на [линку за Drupal документацију](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Да бисте идентификовали приложене библиотеке које додају сувишан JavaScript, пробајте да покренете [просек употребе кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Спорну тему или модул можете да идентификујете у URL-у скрипте кад је на Drupal сајту онемогућено JavaScript груписање. Потражите теме или модуле који на листи имају много скрипти са доста црвених елемената у просеку употребе кода. Тема или модул треба да ставе скрипту на листу само ако се она стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Подесите Browser and proxy cache maximum age (Мак<PERSON>и<PERSON><PERSON><PERSON>на старост кеша прегледача и проксија) на страници Administration (Администрација) » Configuration (Конфигурација) » Development (Развој). Прочитајте више о [Drupal кешу и оптимизацији за боље перформансе](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Предлажемо да користите [модул](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) који аутоматски оптимизује и смањује величину слика које се отпремају преко сајта без губитка квалитета. Уверите се и да користите изворне [стилове прилагодљивих слика](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) које пружа Drupal (доступни у Drupal-у 8 и новијим верзијама) за све слике које се рендерују на сајту."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Савете за ресурсе за повезивање унапред или предучитавање DNS-а можете да додате инсталирањем и конфигурисањем [модула](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) који пружа инфраструктуру за савете за ресурсе корисничког агента."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Уверите се да користите изворне [стилове прилагодљивих слика](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) које пружа Drupal (доступни у Drupal-у 8 и новијим верзијама). Користите стилове прилагодљивих слика при рендеровању поља за слике преко режима приказа, приказа или слика отпремљених путем WYSIWYG уређивача."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Optimize Fonts` да бисте аутоматски искористили CSS функцију `font-display` како би текст био видљив корисницима док се веб-фонтови учитавају."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Next-Gen Formats` да бисте конвертовали слике у WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Lazy Load Images` да бисте одложили учитавање слика ван екрана док не буду потребне."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Critical CSS` и `Script Delay` да бисте одложили некритични JS или CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Користите [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) да бисте кеширали садржај на светској мрежи и побољшали време до првог бајта."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Minify CSS` да бисте аутоматски умањили CSS како би се смањиле величине мрежних ресурса."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Minify Javascript` да бисте аутоматски умањили JS ради смањења величине мрежних ресурса."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Remove Unused CSS` ради лакшег решавања овог проблема. Тиме се идентификују CSS класе које се стварно користе на свакој страници вашег сајта и уклањају се све друге да би величина датотеке остала мала."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Efficient Static Cache Policy` да бисте подесили препоручене вредности у заглављу кеширања за статичне елементе."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Next-Gen Formats` да бисте конвертовали слике у WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Pre-Connect Origins` да бисте аутоматски додали савете за `preconnect` ресурсе ради успостављања раних веза са важним изворима трећих страна."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Preload Fonts` и `Preload Background Images` да бисте додали линкове за `preload` како бисте дали предност преузимању ресурса који се тренутно траже касније током учитавања странице."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Користите [Ezoic Leap](https://pubdash.ezoic.com/speed) и омогућите `Resize Images` да бисте променили величину слика тако да одговара уређају и тиме смањили величине мрежних ресурса."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Предлажемо да отпремите GIF у услугу која ће га кодирати за уградњу као HTML5 видео."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Препоручујемо да користите [додатну компоненту](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) или услугу која аутоматски конвертује отпремљене слике у оптималне формате."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Инсталирајте [Joomla додатну компоненту за одложено учитавање](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) која омогућава да одложите све слике ван екрана или пређите на шаблон који пружа ту функцију. Почев од верзије Joomla 4.0, све нове слике ће [аутоматски](https://github.com/joomla/joomla-cms/pull/30748) добити атрибут `loading` од основних функција."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Многе Joomla додатне компоненте могу да вам помогну да [уградите кључне елементе](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) или [одложите мање важне ресурсе](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Имајте на уму да оптимизације које пружају те додатне компоненте могу да оштете функције шаблона или додатних компоненти, па морате детаљно да их тестирате."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Шаблони, додаци и спецификације сервера доприносе времену одговора сервера. Предлажемо да пронађете оптимизованији шаблон, пажљиво изаберете додатак за оптимизацију и/или надоградите сервер."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Предлажемо да прикажете одломке у категоријама чланака (нпр. преко линка „прочитајте више“), смањите број чланака који се приказују на одређеној страници, раздвојите дугачке постове на више страница или користите додатне компоненте за одложено учитавање коментара."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Неки [<PERSON><PERSON><PERSON> додаци](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) могу да убрзају сајт надовезивањем, умањивањем и компримовањем CSS стилова. Постоје и шаблони који пружају ту функцију."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Многи [<PERSON><PERSON><PERSON> додаци](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) могу да убрзају сајт надовезивањем, умањивањем и компримовањем скрипти. Постоје и шаблони који пружају ту функцију."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Предлажемо да изоставите или замените [Joomla додатке](https://extensions.joomla.org/) који на страници учитавају CSS који се не користи. Да бисте идентификовали додатке који додају сувишан CSS, пробајте да покренете проверу [покривености кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Спорну тему или додатну компоненту можете да идентификујете у URL-у описа стила. Потражите додатне компоненте које на листи имају много описа стилова са доста црвених елемената у покривености кода. Додатна компонента треба да стави опис стила на листу само ако се он стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Предлажемо да изоставите или замените [Joomla додатке](https://extensions.joomla.org/) који на страници учитавају JavaScript који се не користи. Да бисте идентификовали додатне компоненте које додају сувишан JS, пробајте да покренете проверу [покривености кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Спорни додатак можете да идентификујете у URL-у скрипте. Потражите додатке који на листи имају много скрипти са доста црвених елемената у покривености кода. Додатак треба да стави скрипту на листу само ако се она стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Прочитајте више о [кеширању прегледача у систему Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Предлажемо да користите [додатну компоненту за оптимизацију слика](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) која компримује слике без губитка квалитета."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Предлажемо да користите [додатне компоненте за прилагодљиве слике](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) како бисте користили прилагодљиве слике у садржају."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Можете да омогућите компримовање текста тако што ћете омогућити Gzip компримовање страница у систему Joomla (System (Систем) > Global configuration (Глобална конфигурација) > Server (<PERSON><PERSON>рве<PERSON>))."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ако не правите пакете JavaScript елемената, размислите о томе да користите [бејлер](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Онемогућите уграђене [функције прављења пакета и умањивања у JavaScript-у](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) на платформи Magento и размотрите коришћење [бејлера](https://github.com/magento/baler/) уместо тога."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Назначите ознаку `@font-display` када [дефинишете прилагођене фонтове](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Размислите о томе да потражите мноштво додатака трећих страна на [Magento Marketplace-у](https://marketplace.magento.com/catalogsearch/result/?q=webp) да бисте искористили новије формате слика."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Размислите о томе да измените шаблоне производа и каталога да бисте искористили функцију [одложеног учитавања](https://web.dev/native-lazy-loading) на веб-платформи."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Користите [Varnish интеграцију](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) платформе Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Омогућите опцију „Умањи CSS датотеке“ у подешавањима за програмере у продавници. [Сазнајте више](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Користите [Terser](https://www.npmjs.com/package/terser) да бисте умањили све JavaScript елементе из примене статичног садржаја и онемогућили уграђену функцију умањивања."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Онемогућите уграђено [прављење JavaScript пакета](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) на платформи Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Размислите о томе да потражите мноштво додатака трећих страна на [Magento Marketplace-у](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) да бисте оптимизовали слике."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Савете за ресурсе за повезивање унапред или припрему учитавања DNS-а можете да додате ако[измените изглед теме](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Ознаке `<link rel=preload>` могу да се додају [изменом изгледа теме](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Користите компоненту `next/image`, а не `<img>`, да бисте аутоматски оптимизовали формат слика. [Сазнајте више](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Користите компоненту `next/image`, а не `<img>`, да бисте аутоматски споро учитавали слике. [Сазнајте више](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Користите компоненту `next/image` и поставите „priority“ на „true“ да бисте унапред учитали LCP слику. [Сазнајте више](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Користите компоненту `next/script` да бисте одложили учитавање скрипта треће стране које нису толико важне. [Сазнајте више](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Користите компоненту `next/image` да бисте се уверили да су слике увек одговарајуће величине. [Сазнајте више](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Препоручујемо вам да подесите `PurgeCSS` у конфигурацији компоненте `Next.js` да бисте уклонили неискоришћена правила из описа стилова. [Сазнајте више](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Користите `Webpack Bundle Analyzer` да бисте открили неискоришћени JavaScript кôд. [Сазнајте више](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Препоручујемо вам да користите `Next.js Analytics` за мерење учинка апликације у стварном свету. [Сазнајте више](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Конфигуришите кеширање за непроменљиве елементе и `Server-side Rendered` (SSR) странице. [Сазнајте више](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Користите компоненту `next/image`, а не `<img>`, да бисте подесили квалитет слике. [Сазнајте више](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Користите компоненту `next/image` да бисте подесили одговарајуће `sizes`. [Сазнајте више](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Омогућите компримовање на Next.js серверу. [Сазнајте више](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Користите компоненту `nuxt/image` и подесите `format=\"webp\"`. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Користите компоненту `nuxt/image` и подесите `loading=\"lazy\"` за слике ван екрана. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Користите компоненту `nuxt/image` и наведите `preload` за LCP слику. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Користите компоненту `nuxt/image` и наведите експлицитне атрибуте `width` и `height`. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Користите компоненту `nuxt/image` и подесите одговарајући `quality`. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Користите компоненту `nuxt/image` и подесите одговарајуће `sizes`. [Сазнајте више](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Замените анимиране GIF-ове видео садржајем](https://web.dev/replace-gifs-with-videos/) да би се веб-странице брже учитавале, а предлажемо и да користите модерне формате фајлова, као што је [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) или [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), да бисте побољшали ефикасност компримовања за више од 30% у односу на актуелни најсавременији видео кодек, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Предлажемо да користите [додатну компоненту](https://octobercms.com/plugins?search=image) или услугу која аутоматски конвертује отпремљене слике у оптималне формате. Величина [WebP слика без губитака](https://developers.google.com/speed/webp) је за 26% мања од величине PNG слика и за 25–34% мања од величине одговарајућих JPEG слика еквивалентног SSIM индекса квалитета. Још један формат слике следеће генерације који треба да узмете у обзир је [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Предлажемо да инсталирате [додатну компоненту за одложено учитавање слика](https://octobercms.com/plugins?search=lazy) која пружа могућност одлагања свих слика ван екрана или преласка на тему која пружа ту функцију. Предлажемо и да користите [додатну компоненту за AMP странице](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Има много додатних компоненти које вам помажу да [уметнете критичне елементе](https://octobercms.com/plugins?search=css). Те додатне компоненте могу да оштете друге додатне компоненте, па морате детаљно да их тестирате."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Теме, додатне компоненте и спецификације сервера доприносе времену одговора сервера. Предлажемо да пронађете оптимизованију тему, пажљиво изаберете додатну компоненту за оптимизацију и/или надоградите сервер. Систем за управљање садржајем October омогућава програмерима и да користе ставку [`Queues`](https://octobercms.com/docs/services/queues) да би одложили обраду неког задатка који одузима много времена, попут слања имејла. То драстично убрзава веб-захтеве."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Предлажемо да прикажете одломке на листама постова (на пример, помоћу дугмета `show more`), смањите број постова који се приказују на одређеној веб-страници, раздвојите дугачке постове на више веб-страница или користите додатну компоненту за одложено учитавање коментара."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Има много [додатних компоненти](https://octobercms.com/plugins?search=css) које могу да убрзају веб-сајт надовезивањем, умањивањем и компримовањем стилова. Ако користите превођење и повезивање да бисте унапред обавили ово умањивање, то може да убрза програмирање."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Има много [додатних компоненти](https://octobercms.com/plugins?search=javascript) које могу да убрзају веб-сајт надовезивањем, умањивањем и компримовањем скрипти. Ако користите превођење и повезивање да бисте унапред обавили ово умањивање, то може да убрза програмирање."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Предлажемо да прегледате [додатне компоненте](https://octobercms.com/plugins) које на веб-сајту учитавају CSS који се не користи. Да бисте идентификовали додатне компоненте које додају непотребни CSS, покрените [просек употребе кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у Chrome алаткама за програмере. Идентификујте одговорну тему или додатну компоненту на основу URL-а описа стила. Потражите додатне компоненте које имају много описа стилова са доста црвених елемената у просеку употребе кода. Додатна компонента треба да дода опис стила само ако се он стварно користи на веб-страници."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Предлажемо да прегледате [додатне компоненте](https://octobercms.com/plugins?search=javascript) које на веб-страници учитавају JavaScript који се не користи. Да бисте идентификовали додатне компоненте које додају непотребни JavaScript, покрените [просек употребе кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у Chrome алаткама за програмере. Идентификујте одговорну тему или додатну компоненту на основу URL-а скрипте. Потражите додатне компоненте које имају много скрипти са доста црвених елемената у просеку употребе кода. Додатна компонента треба да дода скрипту само ако се она стварно користи на веб-страници."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Прочитајте више о [спречавању непотребних мрежних захтева помоћу HTTP кеша](https://web.dev/http-cache/#caching-checklist). Има много [додатних компоненти](https://octobercms.com/plugins?search=Caching) које могу да се користе за убрзавање кеширања."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Предлажемо да користите [додатну компоненту за оптимизацију слика](https://octobercms.com/plugins?search=image) која компримује слике без губитка квалитета."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Отпремајте слике директно у менаџеру медија да бисте се уверили да су доступне обавезне величине слика. Предлажемо да користите [филтер за промену величине](https://octobercms.com/docs/markup/filter-resize) или [додатну компоненту за промену величине слика](https://octobercms.com/plugins?search=image) да бисте се уверили да се користе оптималне величине слика."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Омогућите компримовање текста у конфигурацији веб-сервера."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Предлажемо да користите библиотеку „са прозорима“ као што је `react-window` како бисте смањили број направљених DOM чворова ако на страници рендерујете много елемената који се понављају. [Сазнајте више](https://web.dev/virtualize-long-lists-react-window/). Исто тако, aко користите копчу `Effect` за побољшање учинка времена извршавања, смањите непотребна поновна рендеровања помоћу компоненти [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) или [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) и [прескочите ефекте](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) само док се одређене зависности не промене."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Ако користите React рутер, смањите коришћење компоненте `<Redirect>` за [навигације помоћу руте](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ако приказујете било какве React компоненте на серверу, размислите о томе да користите `renderToPipeableStream()` или `renderToStaticNodeStream()` како бисте омогућили клијенту да прима и попуњава различите делове ознаке уместо свих делова одједном. [Сазнајте више](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ако систем за превођење и повезивање аутоматски умањује CSS фајлове, уверите се да примењујете производну верзију апликације. То можете да проверите помоћу додатка React Developer Tools. [Сазнајте више](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ако систем за превођење и повезивање аутоматски умањује JS фајлове, уверите се да примењујете производну верзију апликације. То можете да проверите помоћу додатка React Developer Tools. [Сазнајте више](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Ако не приказујете на серверу, [раздвојите JavaScript пакете](https://web.dev/code-splitting-suspense/) помоћу ознаке `React.lazy()`. У супротном, раздвојите кôд помоћу библиотеке треће стране попут [компонената које могу да се учитавају](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Користите React DevTools Profiler, ко<PERSON><PERSON> кор<PERSON><PERSON>ти API Profiler, да бисте мерили перформансе приказивања компонената. [Сазнајте више.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Предлажемо да отпремите GIF у услугу која ће га кодирати за уградњу као HTML5 видео."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Предлажемо да користите додатну компоненту [ Performance Lab](https://wordpress.org/plugins/performance-lab/) за аутоматско конвертовање отпремљених JPEG слика у WebP увек када је то подржано."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Инсталирајте [WordPress додатну компоненту за лако учитавање](https://wordpress.org/plugins/search/lazy+load/) која омогућава да одложите све слике ван екрана или да пређете на тему која пружа ту функцију. Препоручујемо и да користите [додатну компоненту за AMP странице](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Неке WordPress додатне компоненте могу да вам помогну да [уметнете критичне елементе](https://wordpress.org/plugins/search/critical+css/) или [одложите мање важне ресурсе](https://wordpress.org/plugins/search/defer+css+javascript/). Имајте на уму да оптимизације које пружају ове додатне компоненте могу да оштете функције или теме додатних компоненти, па ћете вероватно морати да уносите промене у кôд."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Теме, додатне компоненте и спецификације сервера доприносе времену одговора сервера. Препоручујемо да пронађете оптимизованију тему, пажљиво изаберете додатну компоненту за оптимизацију и/или надоградите сервер."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Препоручујемо да прикажете одломке у листама постова (на пример, преко још ознака), смањите број постова који се приказују на одређеној страници, раздвојите дугачке постове на више странциа или користите додатну компоненту за лако учитавање коментара."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Неке [WordPress додатне компоненте](https://wordpress.org/plugins/search/minify+css/) могу да убрзају сајт тако што повезују, умањују и компримују стилове. Ово умањивање можете да обавите и унапред помоћу процеса дизајнирања ако је могуће."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Неке [WordPress додатне компоненте](https://wordpress.org/plugins/search/minify+javascript/) могу да убрзају сајт тако што повезују, умањују и компримују скрипте. Ово умањивање можете да обавите и унапред помоћу процеса дизајнирања ако је могуће."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Препоручујемо да умањите или промените број [WordPress додатних компоненти](https://wordpress.org/plugins/) које на страници учитавају CSS који се не користи. Да бисте идентификовали додатне компоненте које додају сувишан CSS, пробајте да покренете [покривеност кода](https://developer.chrome.com/docs/devtools/coverage/) у алатки Chrome DevTools. Можете да идентификујете одговорну тему/додатну компоненту у URL-у стилске странице. Потражите додатне компоненте које на листи имају много стилских страница са доста црвенила у покривености кода. Додатна компонента треба да стави стилску страницу на листу само ако се стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Препоручујемо да умањите или промените број [WordPress додатних компоненти](https://wordpress.org/plugins/) које на страници учитавају JavaScript који се не користи. Да бисте идентификовали додатне компоненте које додају сувишан JS, пробајте да покренете [покривеност кода](https://developer.chrome.com/docs/devtools/coverage/) у алатки Chrome DevTools. Можете да идентификујете одговорну тему/додатну компоненту у URL-у скрипте. Потражите додатне компоненте које на листи имају много скрипти са доста црвенила у покривености кода. Додатна компонента треба да стави скрипту на листу само ако се стварно користи на страници."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Прочитајте више о [кеширању прегледача у WordPress-у](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Препоручујемо да користите [WordPress додатну компоненту за оптимизацију слика](https://wordpress.org/plugins/search/optimize+images/) која компримује слике без губитка квалитета."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Отпремајте слике директно помоћу [библиотеке медија](https://wordpress.org/support/article/media-library-screen/) да бисте се уверили да су доступне обавезне величине слика, па их уметните у библиотеку медија или користите виџет странице да бисте се уверили да се користе оптималне величине слика (укључујући оне за преломне тачке које се одазивају). Избегавајте коришћење слика `Full Size` ако димензије нису адекватне за њихово коришћење. [Сазнајте више](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Можете да омогућите компримовање текста у конфигурацији веб-сервера."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Омогућите Imagify на картици Оптимизација слика у WP Rocket-у да бисте конвертовали слике у WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Омогућите [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) у WP Rocket-у да бисте поправили ову препоруку. Ова функција одлаже учитавање слика док посетилац не скролује надоле на страници и мора да их види."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Омогућите [Уклони некоришћени CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) и [Учитај одложени JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) у WP Rocket-у да бисте одговорили на ову препоруку. Ове функције ће оптимизовати CSS и JavaScript датотеке тако да не блокирају приказивање странице."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Омогућите [Умањите CSS датотеке](https://docs.wp-rocket.me/article/1350-css-minify-combine) у WP Rocket-у да бисте решили овај проблем. Сви простори и коментари у CSS датотекама сајта биће уклоњени како би се величина датотеке смањила и како би се убрзало преузимање."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Омогућите [Умањи JavaScript датотеке](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) у WP Rocket-у да бисте решили овај проблем. Празни простори и коментари ће бити уклоњени из JavaScript датотека ради смањивања величине и бржег преузимања."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Омогућите [Уклони некоришћени CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) у „WP Rocket-у“ да бисте решили овај проблем. Смањује величину странице уклањањем свих CSS-ова и описа стилова који се не користе, а задржава само CSS који се користи за сваку страницу."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Да бисте решили овај проблем, омогућите [Одложи извршавање JavaScript-а](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) у WP Rocket-у. То ће побољшати учитавање странице одлагањем извршавања скрипти до интеракције корисника. Ако сајт садржи iframe-ове, можете да користите и [LazyLoad за iframe-ове и видео снимке](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) WP Rocket-а, као и [Замени YouTube iframe сликом прегледа](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Омогућите Imagify на картици Оптимизација слика у WP Rocket-у и покрените групну оптимизацију да бисте компримовали слике."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Користите [Предучитавај DNS захтеве](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) у WP Rocket-у да бисте додали „dns-fetch“ и убрзали везу са спољним доменима. Уз то, WP Rocket аутоматски додаје „повезивање унапред“ у [домен Google фонтова](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) и CNAME елементе додате преко функције [Омогући CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Да бисте решили овај проблем са фонтовима, омогућите [Уклоните некоришћени CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) у WP Rocket-у. Најважнији фонтови сајта ће се унапред учитати са приоритетом."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Погледајте калкулатор."}, "report/renderer/report-utils.js | collapseView": {"message": "Скупи приказ"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Почетна навигација"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Максимално кашњење критичне путање:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Копирај JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Укључи/искључи тамну тему"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Прошири дијалог за штампање"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Штампај резиме"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Сачувај као Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Сачувај као HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Сачувај као JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Отвори у приказивачу"}, "report/renderer/report-utils.js | errorLabel": {"message": "Грешка!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Пријављивање грешке: нема информација о провери"}, "report/renderer/report-utils.js | expandView": {"message": "Прошири приказ"}, "report/renderer/report-utils.js | footerIssue": {"message": "Пријавите проблем"}, "report/renderer/report-utils.js | hide": {"message": "Сакриј"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Подаци о експерименталним функцијама"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) анализа актуелне странице емулиране помоћу мобилне мреже. Вредности представљају процене и могу да варирају."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Додатне ставке за ручну проверу"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Није примењиво"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Могућност"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Процењена уштеда"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Провере са задовољавајућом оценом"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Почетно учитавање странице"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Прилагођено ограничавање"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Емулирани рачунар"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Без емулације"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe верзија"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Доступан процесор/меморија"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Ограничавање процесора"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Уређај"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ограничавање мреже"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Емулација екрана"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Кориснички агент (мрежа)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Једно учитавање странице"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ови подаци су добијени на основу једног учитавања странице, насупрот подацима из поља који резимирају многе сесије."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ограничавање попут споре 4G везе"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Непознато"}, "report/renderer/report-utils.js | show": {"message": "Прикажи"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Прикажи ревизије релевантне за:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Скупи фрагмент"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Прошири фрагмент"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Прикажи независне ресурсе"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Пружа окружење"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Било је извесних проблема који су утицали на ово покретање Lighthouse-а:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Вредности представљају процене и могу да варирају. [Оцена учинка са израчунава](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) директно на основу тих показатеља."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Прикажи оригинални траг"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Прикажи траг"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Прик<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Провере са задовољавајућом оценом које садрже упозорења"}, "report/renderer/report-utils.js | warningHeader": {"message": "Упозорења: "}, "treemap/app/src/util.js | allLabel": {"message": "Све"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Све скрипте"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Покривеност"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Дуплирани модули"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Бајтови ресурса"}, "treemap/app/src/util.js | tableColumnName": {"message": "Назив"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Укључи/искључи табелу"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Неискоришћени бајтови"}}