"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/searchService.ts":
/*!***************************************!*\
  !*** ./lib/services/searchService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchService: function() { return /* binding */ SearchService; },\n/* harmony export */   searchService: function() { return /* binding */ searchService; }\n/* harmony export */ });\n/* harmony import */ var _tramites_chia_optimo_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/tramites_chia_optimo.json */ \"(app-pages-browser)/./tramites_chia_optimo.json\");\n/* harmony import */ var _OPA_chia_optimo_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/OPA-chia-optimo.json */ \"(app-pages-browser)/./OPA-chia-optimo.json\");\n\n\n/**\r\n * Servicio de búsqueda inteligente para trámites y OPAs\r\n */ class SearchService {\n    /**\r\n   * Inicializar datos de trámites y OPAs\r\n   */ async initializeData() {\n        if (this.initialized) return;\n        try {\n            // Cargar trámites desde JSON\n            this.tramites = _tramites_chia_optimo_json__WEBPACK_IMPORTED_MODULE_0__.map((tramite, index)=>({\n                    id: \"tramite-\".concat(index),\n                    name: tramite.Nombre,\n                    type: \"TRAMITE\",\n                    description: \"Tr\\xe1mite de \".concat(tramite.Nombre),\n                    dependency: tramite.dependencia,\n                    subdependency: tramite.subdependencia,\n                    cost: tramite[\"\\xbfTiene pago?\"] === \"No\" ? \"Gratuito\" : tramite[\"\\xbfTiene pago?\"],\n                    responseTime: tramite[\"Tiempo de respuesta\"],\n                    suitUrl: tramite[\"Visualizaci\\xf3n tr\\xe1mite en el SUIT\"],\n                    govUrl: tramite[\"Visualizaci\\xf3n tr\\xe1mite en GOV.CO\"],\n                    formRequired: tramite.Formulario === \"S\\xed\",\n                    popularity: Math.floor(Math.random() * 100) + 1 // Simulado por ahora\n                }));\n            // Cargar OPAs desde JSON\n            this.opas = this.flattenOPAs(_OPA_chia_optimo_json__WEBPACK_IMPORTED_MODULE_1__);\n            this.initialized = true;\n        } catch (error) {\n            console.error(\"Error inicializando datos de b\\xfasqueda:\", error);\n        }\n    }\n    /**\r\n   * Aplanar estructura jerárquica de OPAs\r\n   */ flattenOPAs(opasData) {\n        const flattened = [];\n        let opaIndex = 0;\n        Object.entries(opasData.dependencias).forEach((param)=>{\n            let [depCode, dep] = param;\n            Object.entries(dep.subdependencias).forEach((param)=>{\n                let [subDepCode, subDep] = param;\n                var _subDep_OPA;\n                (_subDep_OPA = subDep.OPA) === null || _subDep_OPA === void 0 ? void 0 : _subDep_OPA.forEach((opa)=>{\n                    flattened.push({\n                        id: \"opa-\".concat(opaIndex++),\n                        name: opa.OPA.length > 100 ? opa.OPA.substring(0, 100) + \"...\" : opa.OPA,\n                        type: \"OPA\",\n                        description: opa.OPA,\n                        dependency: dep.nombre,\n                        subdependency: subDep.nombre,\n                        cost: \"Gratuito\",\n                        responseTime: \"1 d\\xeda h\\xe1bil\",\n                        popularity: Math.floor(Math.random() * 50) + 1 // Simulado\n                    });\n                });\n            });\n        });\n        return flattened;\n    }\n    /**\r\n   * Realizar búsqueda inteligente\r\n   */ async search(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        await this.initializeData();\n        const { limit = 10, offset = 0, includeHighlight = true, sortBy = \"relevance\", sortOrder = \"desc\" } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        const searchTerm = query.toLowerCase().trim();\n        let allProcedures = [\n            ...this.tramites,\n            ...this.opas\n        ];\n        // Aplicar filtros\n        if (filters.type && filters.type !== \"ALL\") {\n            allProcedures = allProcedures.filter((proc)=>proc.type === filters.type);\n        }\n        if (filters.dependency) {\n            allProcedures = allProcedures.filter((proc)=>proc.dependency.toLowerCase().includes(filters.dependency.toLowerCase()));\n        }\n        if (filters.hasCost !== undefined) {\n            allProcedures = allProcedures.filter((proc)=>{\n                const isFree = proc.cost === \"Gratuito\" || proc.cost === \"No\";\n                return filters.hasCost ? !isFree : isFree;\n            });\n        }\n        // Realizar búsqueda con scoring\n        const results = allProcedures.map((proc)=>{\n            const matchScore = this.calculateMatchScore(proc, searchTerm);\n            if (matchScore === 0) return null;\n            return {\n                ...proc,\n                matchScore,\n                highlightedName: includeHighlight ? this.highlightMatch(proc.name, searchTerm) : proc.name\n            };\n        }).filter(Boolean);\n        // Ordenar resultados\n        results.sort((a, b)=>{\n            switch(sortBy){\n                case \"name\":\n                    return sortOrder === \"asc\" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);\n                case \"popularity\":\n                    return sortOrder === \"asc\" ? (a.popularity || 0) - (b.popularity || 0) : (b.popularity || 0) - (a.popularity || 0);\n                case \"relevance\":\n                default:\n                    return (b.matchScore || 0) - (a.matchScore || 0);\n            }\n        });\n        return results.slice(offset, offset + limit);\n    }\n    /**\r\n   * Calcular score de relevancia para un procedimiento\r\n   */ calculateMatchScore(procedure, searchTerm) {\n        let score = 0;\n        const name = procedure.name.toLowerCase();\n        const description = (procedure.description || \"\").toLowerCase();\n        const dependency = procedure.dependency.toLowerCase();\n        // Coincidencia exacta en el nombre (peso alto)\n        if (name === searchTerm) {\n            score += 100;\n        } else if (name.startsWith(searchTerm)) {\n            score += 80;\n        } else if (name.includes(searchTerm)) {\n            score += 60;\n        }\n        // Coincidencia en la descripción\n        if (description.includes(searchTerm)) {\n            score += 30;\n        }\n        // Coincidencia en la dependencia\n        if (dependency.includes(searchTerm)) {\n            score += 20;\n        }\n        // Bonus por popularidad\n        if (procedure.popularity) {\n            score += Math.floor(procedure.popularity / 10);\n        }\n        // Bonus por palabras múltiples\n        const searchWords = searchTerm.split(\" \").filter((word)=>word.length > 2);\n        if (searchWords.length > 1) {\n            const wordMatches = searchWords.filter((word)=>name.includes(word) || description.includes(word)).length;\n            score += wordMatches / searchWords.length * 20;\n        }\n        return score;\n    }\n    /**\r\n   * Resaltar coincidencias en el texto\r\n   */ highlightMatch(text, searchTerm) {\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        return text.replace(regex, \"<mark>$1</mark>\");\n    }\n    /**\r\n   * Obtener sugerencias populares\r\n   */ async getPopularSearches() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        await this.initializeData();\n        // En producción, esto vendría de analytics/métricas reales\n        const popularTerms = [\n            \"licencia construcci\\xf3n\",\n            \"certificado residencia\",\n            \"impuesto predial\",\n            \"licencia comercial\",\n            \"certificado libertad\",\n            \"permiso eventos\",\n            \"paz y salvo\",\n            \"certificado estratificaci\\xf3n\",\n            \"licencia funcionamiento\",\n            \"registro mercantil\"\n        ];\n        return popularTerms.slice(0, limit);\n    }\n    /**\r\n   * Obtener dependencias disponibles para filtros\r\n   */ async getDependencies() {\n        await this.initializeData();\n        const dependencies = new Set();\n        this.tramites.forEach((tramite)=>{\n            if (tramite.dependency) {\n                dependencies.add(tramite.dependency);\n            }\n        });\n        this.opas.forEach((opa)=>{\n            if (opa.dependency) {\n                dependencies.add(opa.dependency);\n            }\n        });\n        return Array.from(dependencies).sort();\n    }\n    /**\r\n   * Obtener estadísticas de búsqueda\r\n   */ async getSearchStats() {\n        await this.initializeData();\n        const dependencies = await this.getDependencies();\n        return {\n            totalTramites: this.tramites.length,\n            totalOPAs: this.opas.length,\n            totalDependencies: dependencies.length\n        };\n    }\n    constructor(){\n        this.supabase = createClient();\n        this.tramites = [];\n        this.opas = [];\n        this.initialized = false;\n        this.initializeData();\n    }\n}\n// Instancia singleton del servicio de búsqueda\nconst searchService = new SearchService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/searchService.ts\n"));

/***/ })

});