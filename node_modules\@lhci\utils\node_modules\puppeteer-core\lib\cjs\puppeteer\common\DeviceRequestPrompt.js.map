{"version": 3, "file": "DeviceRequestPrompt.js", "sourceRoot": "", "sources": ["../../../../src/common/DeviceRequestPrompt.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAKH,iDAAyC;AACzC,mEAGoC;AAKpC;;;;GAIG;AACH,MAAa,yBAAyB;IAWpC;;OAEG;IACH,YAAY,EAAU,EAAE,IAAY;QAClC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AAlBD,8DAkBC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,mBAAmB;IAgB9B;;OAEG;IACH,YACE,MAAkB,EAClB,eAAgC,EAChC,UAA4D;;QArB9D,8CAA2B;QAC3B,uDAAkC;QAClC,0CAAY;QACZ,uCAAW,KAAK,EAAC;QACjB,mDAAuB,uBAAA,IAAI,0EAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC;QACtD,qDAAyB,IAAI,GAAG,EAG5B,EAAC;QAEL;;WAEG;QACH,YAAO,GAAgC,EAAE,CAAC;QAUxC,uBAAA,IAAI,+BAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,wCAAoB,eAAe,MAAA,CAAC;QACxC,uBAAA,IAAI,2BAAO,UAAU,CAAC,EAAE,MAAA,CAAC;QAEzB,uBAAA,IAAI,mCAAQ,CAAC,EAAE,CACb,oCAAoC,EACpC,uBAAA,IAAI,gDAAqB,CAC1B,CAAC;QACF,uBAAA,IAAI,mCAAQ,CAAC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YAChD,uBAAA,IAAI,+BAAW,IAAI,MAAA,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,uBAAA,IAAI,0EAAe,MAAnB,IAAI,EAAgB,UAAU,CAAC,CAAC;IAClC,CAAC;IA8BD;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,MAAsD,EACtD,UAA8B,EAAE;QAEhC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;gBAClB,OAAO,MAAM,CAAC;aACf;SACF;QAED,MAAM,EAAC,OAAO,GAAG,uBAAA,IAAI,4CAAiB,CAAC,OAAO,EAAE,EAAC,GAAG,OAAO,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAA,0CAAqB,EAA4B;YAC/D,OAAO,EAAE,qDAAqD,OAAO,aAAa;YAClF,OAAO;SACR,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,EAAC,MAAM,EAAE,OAAO,EAAC,CAAC;QACjC,uBAAA,IAAI,kDAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI;YACF,OAAO,MAAM,OAAO,CAAC;SACtB;gBAAS;YACR,uBAAA,IAAI,kDAAuB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC5C;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,MAAiC;QAC5C,IAAA,kBAAM,EACJ,uBAAA,IAAI,mCAAQ,KAAK,IAAI,EACrB,gDAAgD,CACjD,CAAC;QACF,IAAA,kBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,+BAA+B,CAAC,CAAC;QACvE,IAAA,kBAAM,EACJ,CAAC,uBAAA,IAAI,oCAAS,EACd,6DAA6D,CAC9D,CAAC;QACF,uBAAA,IAAI,mCAAQ,CAAC,GAAG,CACd,oCAAoC,EACpC,uBAAA,IAAI,gDAAqB,CAC1B,CAAC;QACF,uBAAA,IAAI,gCAAY,IAAI,MAAA,CAAC;QACrB,OAAO,uBAAA,IAAI,mCAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACpD,EAAE,EAAE,uBAAA,IAAI,+BAAI;YACZ,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAA,kBAAM,EACJ,uBAAA,IAAI,mCAAQ,KAAK,IAAI,EACrB,gDAAgD,CACjD,CAAC;QACF,IAAA,kBAAM,EACJ,CAAC,uBAAA,IAAI,oCAAS,EACd,6DAA6D,CAC9D,CAAC;QACF,uBAAA,IAAI,mCAAQ,CAAC,GAAG,CACd,oCAAoC,EACpC,uBAAA,IAAI,gDAAqB,CAC1B,CAAC;QACF,uBAAA,IAAI,gCAAY,IAAI,MAAA,CAAC;QACrB,OAAO,uBAAA,IAAI,mCAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAC,EAAE,EAAE,uBAAA,IAAI,+BAAI,EAAC,CAAC,CAAC;IACxE,CAAC;CACF;AAzID,kDAyIC;ibAlGgB,KAAuD;IACpE,IAAI,KAAK,CAAC,EAAE,KAAK,uBAAA,IAAI,+BAAI,EAAE;QACzB,OAAO;KACR;IAED,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE;QACrC,IACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACzB,OAAO,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC;QACpC,CAAC,CAAC,EACF;YACA,SAAS;SACV;QAED,MAAM,SAAS,GAAG,IAAI,yBAAyB,CAC7C,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,IAAI,CACf,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE7B,KAAK,MAAM,oBAAoB,IAAI,uBAAA,IAAI,kDAAuB,EAAE;YAC9D,IAAI,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBAC1C,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aACjD;SACF;KACF;AACH,CAAC;AA0EH;;GAEG;AACH,MAAa,0BAA0B;IAOrC;;OAEG;IACH,YAAY,MAAkB,EAAE,eAAgC;;QAThE,qDAA2B;QAC3B,8DAAkC;QAClC,kEAA+B,IAAI,GAAG,EAEnC,EAAC;QAMF,uBAAA,IAAI,sCAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,+CAAoB,eAAe,MAAA,CAAC;QAExC,uBAAA,IAAI,0CAAQ,CAAC,EAAE,CAAC,oCAAoC,EAAE,KAAK,CAAC,EAAE;YAC5D,uBAAA,IAAI,kGAAyB,MAA7B,IAAI,EAA0B,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,uBAAA,IAAI,0CAAQ,CAAC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YAChD,uBAAA,IAAI,sCAAW,IAAI,MAAA,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB,CACvB,UAA8B,EAAE;QAEhC,IAAA,kBAAM,EACJ,uBAAA,IAAI,0CAAQ,KAAK,IAAI,EACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,GAAG,uBAAA,IAAI,+DAA6B,CAAC,IAAI,KAAK,CAAC,CAAC;QACjE,IAAI,aAAwC,CAAC;QAC7C,IAAI,WAAW,EAAE;YACf,aAAa,GAAG,uBAAA,IAAI,0CAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC1D;QAED,MAAM,EAAC,OAAO,GAAG,uBAAA,IAAI,mDAAiB,CAAC,OAAO,EAAE,EAAC,GAAG,OAAO,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAA,0CAAqB,EAAsB;YACzD,OAAO,EAAE,+CAA+C,OAAO,aAAa;YAC5E,OAAO;SACR,CAAC,CAAC;QACH,uBAAA,IAAI,+DAA6B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI;YACF,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;YAC7D,OAAO,MAAM,CAAC;SACf;gBAAS;YACR,uBAAA,IAAI,+DAA6B,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACnD;IACH,CAAC;CAuBF;AA3ED,gEA2EC;oWAjBG,KAAuD;IAEvD,IAAI,CAAC,uBAAA,IAAI,+DAA6B,CAAC,IAAI,EAAE;QAC3C,OAAO;KACR;IAED,IAAA,kBAAM,EAAC,uBAAA,IAAI,0CAAQ,KAAK,IAAI,CAAC,CAAC;IAC9B,MAAM,YAAY,GAAG,IAAI,mBAAmB,CAC1C,uBAAA,IAAI,0CAAQ,EACZ,uBAAA,IAAI,mDAAiB,EACrB,KAAK,CACN,CAAC;IACF,KAAK,MAAM,OAAO,IAAI,uBAAA,IAAI,+DAA6B,EAAE;QACvD,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;KAC/B;IACD,uBAAA,IAAI,+DAA6B,CAAC,KAAK,EAAE,CAAC;AAC5C,CAAC"}